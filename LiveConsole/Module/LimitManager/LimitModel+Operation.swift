//
//  LimitModel+Operation.swift
//  LivePlus
//
//  Created by 郭炜 on 2022/6/15.
//

import Foundation



extension LimitModel {
    func showVip(option: VipAlertEnum = .keywordReply) {
        HUD.showFail("升级帐户权益后立即解锁")
        return
    }
    
    /// 非会员是否限制关键词回复
    func supportKeywordReplyt(showAlert: Bool = false) -> Bool {
        if !UserInfo.isMember, UserInfo.isTrialVip {
            if showAlert {
                showVip(option: .keywordReply)
            }
            return false
        }
        
        if self.vipFunction.keywordReply {
            if showAlert {
                showVip(option: .keywordReply)
            }
            return false
        }
        return true
    }
    
    
    // 会员
    static func gotoMember() {
        NotificationCenter.default.post(name: LCKey.noti_gotoMember, object: nil)
        LCTools.shared.valiationLogin {
            let ovc = LimitPurchaseVC2()
            AppDelegate.curDisplayVC().navigationController?.pushViewController(ovc)
        }
    }
    
}
