//
//  LimitModel+Alert.swift
//  LivePlus
//
//  Created by 郭炜 on 2022/6/20.
//

/**
 VIP弹窗
 */
import Foundation
import UIKit

enum VipAlertEnum {
    // 键词是否限制
    case keywordReply
    
    var image: UIImage? {
        switch self {
        case .keywordReply:
            return UIImage(named: "relation_vip")
        }
    }
    
    var title: String {
        switch self {
        case .keywordReply:
            return "键词是否"
        }
    }
    
    var detail: String {
        switch self {
        case .keywordReply:
            return "您尚未开通VIP权益"
        }
    }
}
