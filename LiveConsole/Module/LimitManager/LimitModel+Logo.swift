//
//  LimitModel+Logo.swift
//  LivePlus
//
//  Created by 郭炜 on 2022/6/15.
//

/** 判断是否需要使用vip标识 */
import Foundation

/// 可能需要显示viplogo的地方
enum VIPLogoOption {
    ///
    case keywordReply
   
    
    var showLogo: Bool {
        if UserInfo.currentUser()?.vipLevelId == .svip {
            return false
        }
        let limitModel = LimitManager.limits
       
        return false
    }
}

extension UIView {
    /// 自适应vip图标
    func adaptVipLogo(option: VIPLogoOption) {
        if !option.showLogo {
            for view in self.subviews {
                if let view = view as? UIImageView {
                    if view.image == UIImage(named: "vip_logo1") || view.image == UIImage(named: "vip_logo2") {
                        view.removeFromSuperview()
                    }
                }
            }
            return
        }
        
        var logo: UIImageView
        switch option {
        case .keywordReply:
            logo = UIImageView(image: UIImage(named: "vip_logo1"))
        }
        self.addSubview(logo)
    }
}
