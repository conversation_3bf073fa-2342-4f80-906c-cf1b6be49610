//
//  VIPAlert.swift
//  LivePlus
//
//  Created by 郭炜 on 2022/6/20.
//

import UIKit

typealias VIPAction = () -> Void

class VIPAlert: UIView {
    
    private lazy var contentView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        return view
    }()
    
    private lazy var backView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.white
        view.cornerRadius = 20
        return view
    }()
    
    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.textColor = UIColor("#1E1F20")
        label.font = LCDevice.DIN_Font_PF_M(18)
        label.textAlignment = .left
        return label
    }()
    
    private lazy var messageLabel: UITextView = {
        let textview = UITextView()
        textview.font = LCDevice.DIN_Font_PF_M(14)
        textview.textColor = UIColor("#4D4E52")
        textview.isUserInteractionEnabled = false
        return textview
    }()
    
    private lazy var imageView: UIImageView = {
        let imageView = UIImageView()
        return imageView
    }()
    
    private lazy var sureButton: UIButton = {
        let button = UIButton { [weak self] in
            guard let self = self else { return }
            if let action = self.sureAction {
                action()
            }
            self.dismiss()
        }
        button.setTitle("成为会员立即解锁", for: .normal)
        button.cornerRadius = 19
        button.titleLabel?.font = LCDevice.DIN_Font_PF_M(14)
        button.setTitleColor(.white, for: .normal)
        return button
    }()
    
    /// 渐变层
    private var gradientLayer: CAGradientLayer = {
        let leftColor = UIColor("#6974F2")
        let rightColor = UIColor("#AC63F9")
        let gradientColors = [leftColor.cgColor, rightColor.cgColor]
        let gradientLocations: [NSNumber] = [0.0, 1.0]
        let gradientLayer = CAGradientLayer()
        gradientLayer.startPoint = CGPoint(x: 0, y: 0)
        gradientLayer.endPoint = CGPoint(x: 1, y: 0)
        gradientLayer.colors = gradientColors
        gradientLayer.locations = gradientLocations
        return gradientLayer
    }()
    
    private lazy var cancelButton: UIButton = {
        let button = UIButton { [weak self] in
            guard let self = self else { return }
            if let action = self.cancelAction {
                action()
            }
            self.dismiss()
        }
        button.setImage(UIImage(named: "close_vip"), for: .normal)
        return button
    }()
    
    private var title: String = ""
    private var message: String = ""
    private var image: UIImage?
    private var cancelAction: VIPAction?
    private var sureAction: VIPAction?
    
    override init(frame: CGRect) {
        super.init(frame: frame)
    }

    convenience init(image: UIImage?,
                     title: String,
                     message: String,
                     cancelAction: VIPAction?,
                     sureAction: VIPAction?) {
        self.init(frame: CGRect(x: 0, y: 0, width: LCDevice.screenW, height: LCDevice.screenH))
        self.title = title
        self.message = message
        self.image = image
        self.cancelAction = cancelAction
        self.sureAction = sureAction
        makeUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func makeUI() {
        backgroundColor = UIColor(white: 0.0, alpha: 0.5)
        addSubviews([contentView])
        contentView.addSubviews([backView, cancelButton])
        backView.addSubviews([titleLabel, messageLabel, imageView, sureButton])
        let imageHeight: CGFloat = 200
        let backWidth: CGFloat = 300
        let messageHeight = message.sizeLineFeedWithFont(font: messageLabel.font ?? LCDevice.DIN_Font_PF_R(14), width: CGFloat(backWidth - 56))
        let backHeight: CGFloat = imageHeight + 14 + 30 + 6 + messageHeight + 26 + 38 + 20
        contentView.snp.makeConstraints { make in
            make.width.equalTo(backWidth)
            make.height.equalTo(backHeight + 50)
            make.center.equalToSuperview()
        }
        backView.snp.makeConstraints { make in
            make.height.equalTo(backHeight)
            make.top.equalToSuperview()
            make.leading.trailing.equalToSuperview()
        }
        imageView.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview()
            make.height.equalTo(imageHeight)
        }
        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(imageView.snp.bottom).offset(14)
            make.leading.trailing.equalToSuperview().inset(28)
            make.height.equalTo(30)
        }
        messageLabel.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview().inset(25)
            make.top.equalTo(titleLabel.snp.bottom).offset(6)
            make.height.equalTo(messageHeight)
        }
        sureButton.snp.makeConstraints { make in
            make.top.equalTo(messageLabel.snp.bottom).offset(26)
            make.leading.trailing.equalToSuperview().inset(28)
            make.height.equalTo(38)
        }
        cancelButton.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalTo(backView.snp.bottom).offset(20)
            make.width.height.equalTo(30)
        }
        
        imageView.image = image
        titleLabel.text = title
        messageLabel.text = message
        
        sureButton.layer.insertSublayer(gradientLayer, at: 0)
        gradientLayer.frame = CGRect(x: 0, y: 0, width: backWidth - 56, height: 38)
        
        addGestureRecognizer(UITapGestureRecognizer(actionBlock: { [weak self] _ in
            guard let self = self else { return }
            self.dismiss()
        }))
    }
    
    func dismiss() {
        UIView.animate(withDuration: 0.2) {
            self.alpha = 0
        } completion: { _ in
            self.removeFromSuperview()
        }
    }
}

// MARK: - VIPAlert 会员弹窗
extension VIPAlert {
    public static func show(option: VipAlertEnum,
                            cancelAction: VIPAction?,
                            sureAction: VIPAction?) {
        guard let appDel = UIApplication.shared.delegate as? AppDelegate else { return }
        let showingView = VIPAlert(image: option.image, title: option.title, message: option.detail, cancelAction: cancelAction, sureAction: sureAction)
        showingView.alpha = 0.0
        appDel.wd.addSubview(showingView)
        UIView.animate(withDuration: 0.2) {
            showingView.alpha = 1.0
        }
    }
}



// MARK: - 会员弹窗直播间的弹出
class LiveVipAlert: UIView {
    
    private var sureAction: VIPAction?
    
    lazy var contentView: UIView = {
        let view = UIView()
        view.cornerRadius = 8
        view.backgroundColor = UIColor.white
        return view
    }()
    
    lazy var bgView: UIImageView = {
        let view = UIImageView()
        view.cornerRadius = 8
        view.contentMode = .scaleAspectFill
        view.image = UIImage(named: "roomalertbg")
        view.backgroundColor = UIColor.clear
        view.isUserInteractionEnabled = true
        return view
    }()
    

    lazy var title: UILabel = {
        let label = UILabel()
        label.text = "温馨提示"
        label.font = UIFont.systemFont(ofSize: 20, weight: .medium)
        label.textColor = UIColor("#333333")
        return label
    }()
    
    lazy var vipLab: UILabel = {
        let label = UILabel()
        
        let text = "您的免费体验时长或会员已到期，请升级会员后再使用。"
        
        let paraph = NSMutableParagraphStyle()
        paraph.lineSpacing = 6
        let attributes = [NSAttributedString.Key.paragraphStyle: paraph, NSAttributedString.Key.foregroundColor: UIColor("#4D4E52"), NSAttributedString.Key.font: UIFont.systemFont(ofSize: 15, weight: .medium)]
        
        let attributedText = NSMutableAttributedString(string: text, attributes: attributes)
        
        attributedText.setAttributes([NSAttributedString.Key.foregroundColor: UIColor("#6974F2"), NSAttributedString.Key.font: UIFont.systemFont(ofSize: 15, weight: .medium)], range: NSRange(location: 16, length: 4))
        label.attributedText = attributedText
        label.numberOfLines = 0
        return label
    }()
    
    private lazy var cancelBtn: UIButton = {
        let btn = UIButton(frame: CGRect(x: 0.0, y: 0.0, width: 170, height: 40))
        btn.backgroundColor = UIColor("#F6F5F8")
        btn.setTitle("我再想想", for: .normal)
        btn.setTitleColor(UIColor("#4D4E52"), for: .normal)
        btn.titleLabel?.font = UIFont.systemFont(ofSize: 15, weight: .medium)
        btn.addTarget(self, action: #selector(cancelBtnAction(sender:)), for: .touchUpInside)
        btn.cornerRadius = 20
        return btn
    }()
    
    
    private lazy var okBtn: UIButton = {
        let btn = UIButton(frame: CGRect(x: 0.0, y: 0.0, width: 170, height: 40))
        btn.backgroundColor = UIColor("#6974F2")
        btn.setTitle("立刻升级", for: .normal)
        btn.setTitleColor(UIColor.white, for: .normal)
        btn.titleLabel?.font = UIFont.systemFont(ofSize: 15, weight: .medium)
        btn.addTarget(self, action: #selector(okBtnAction(sender:)), for: .touchUpInside)
        btn.cornerRadius = 20
        return btn
    }()
    
    
    override init(frame: CGRect) {
        super.init(frame: frame)
    }

    convenience init( sureAction: VIPAction?) {
        self.init(frame: CGRect(x: 0, y: 0, width: LCDevice.screenW, height: LCDevice.screenH))
        self.sureAction = sureAction
        makeUI()
        business()
    }

    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func makeUI() {
        addSubviews([contentView])
        contentView.addSubviews([bgView, title, okBtn, vipLab, cancelBtn])
        
        contentView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview()
            make.width.equalTo(320)
            make.height.equalTo(274)
        }
       
        bgView.snp.makeConstraints { make in
            make.top.bottom.leading.trailing.equalToSuperview()
        }
        
        title.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalToSuperview().inset(50)
        }
        
        vipLab.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(107)
            make.leading.trailing.equalToSuperview().inset(37)
            make.height.equalTo(50)
        }
        
      
        okBtn.snp.makeConstraints { make in
            make.bottom.equalToSuperview().inset(20)
            make.trailing.equalToSuperview().inset(24)
            make.height.equalTo(40)
            make.width.equalTo(123)
        }
        
        cancelBtn.snp.makeConstraints { make in
            make.bottom.equalToSuperview().inset(20)
            make.leading.equalToSuperview().inset(24)
            make.height.equalTo(40)
            make.width.equalTo(123)
        }
       
    }
    
    func business() {
        
    }
    
    @objc func okBtnAction(sender: UIButton) {
        
        if let action = self.sureAction {
            action()
        }
        self.dismiss()
    }
    
    @objc func cancelBtnAction(sender: UIButton) {
        dismiss()
        
    }
    
    func dismiss() {
        UIView.animate(withDuration: 0.2) {
            self.alpha = 0
        } completion: { _ in
            self.removeFromSuperview()
        }
    }
}


extension LiveVipAlert {
    public static func show(sureAction: VIPAction?) {
        guard let appDel = UIApplication.shared.delegate as? AppDelegate else { return }
        let showingView = LiveVipAlert(sureAction: sureAction)
        showingView.alpha = 0.0
        appDel.wd.addSubview(showingView)
        UIView.animate(withDuration: 0.2) {
            showingView.alpha = 1.0
        }
    }
}


// MARK: - 试用会员弹窗
class TrialVipAlert: UIView {
        
    lazy var contentView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.clear
        return view
    }()
    
    lazy var bgView: UIImageView = {
        let view = UIImageView()
        view.cornerRadius = 8
        view.contentMode = .scaleAspectFill
        view.image = UIImage(named: "免费会员bg")
        view.backgroundColor = UIColor.clear
        view.isUserInteractionEnabled = true
        return view
    }()
    
    
    lazy var vipLab: UILabel = {
        let label = UILabel()
        label.numberOfLines = 0
        if let userModel = UserInfo.currentUser(), let ex = userModel.trialExpireTime{
            let text = "\(LCTools.covertDateString(timeString: ex))"
            let paraph = NSMutableParagraphStyle()
            paraph.lineSpacing = 6
            let attributes = [NSAttributedString.Key.paragraphStyle: paraph, NSAttributedString.Key.foregroundColor: UIColor("#4D4E52"), NSAttributedString.Key.font: UIFont.systemFont(ofSize: 12, weight: .regular)]
            
            let attributedText = NSMutableAttributedString(string: "您的会员权益至\(text)到期", attributes: attributes)
            
            attributedText.setAttributes([NSAttributedString.Key.foregroundColor: UIColor("#AD4423"), NSAttributedString.Key.font: UIFont.systemFont(ofSize: 12, weight: .regular)], range: NSRange(location: 7, length: text.count))
            label.attributedText = attributedText
            label.textAlignment = .center
        }
      
        return label
    }()
    
    private lazy var cancelBtn: UIButton = {
        let btn = UIButton(frame: CGRect(x: 0.0, y: 0.0, width: 170, height: 40))
        btn.backgroundColor = .clear
        btn.setImage(UIImage(named: "关闭_白色"), for: .normal)
        btn.addTarget(self, action: #selector(cancelBtnAction(sender:)), for: .touchUpInside)
        return btn
    }()
    
    
    private lazy var okBtn: UIButton = {
        let btn = UIButton(frame: CGRect(x: 0.0, y: 0.0, width: 170, height: 40))
        btn.backgroundColor = .clear
        btn.setBackgroundImage(UIImage(named: "体验"), for: .normal)
        btn.addTarget(self, action: #selector(okBtnAction(sender:)), for: .touchUpInside)
        return btn
    }()
    
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        makeUI()
        business()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func makeUI() {
        self.backgroundColor = UIColor.black.alpha(value: 0.6)
        
        addSubviews([contentView])
        contentView.addSubviews([bgView, okBtn, vipLab, cancelBtn])
        
        contentView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview()
            make.width.equalTo(243)
            make.height.equalTo(376)
        }
       
        bgView.snp.makeConstraints { make in
            make.top.bottom.leading.trailing.equalToSuperview()
        }
        
        
        vipLab.snp.makeConstraints { make in
            make.bottom.equalToSuperview().inset(72)
            make.leading.trailing.equalToSuperview().inset(24)
            make.height.equalTo(50)
        }
        
      
        okBtn.snp.makeConstraints { make in
            make.bottom.equalToSuperview().inset(24)
            make.centerX.equalToSuperview()
            make.height.equalTo(40)
            make.width.equalTo(209)
        }
        
        cancelBtn.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(24)
            
            make.trailing.equalToSuperview()
            make.height.equalTo(30)
            make.width.equalTo(30)
        }
       
    }
    
    func business() {
        
    }
    
    @objc func okBtnAction(sender: UIButton) {
        
        self.dismiss()
    }
    
    @objc func cancelBtnAction(sender: UIButton) {
        dismiss()
    }
    
    func dismiss() {
        UIView.animate(withDuration: 0.2) {
            self.alpha = 0
        } completion: { _ in
            self.removeFromSuperview()
        }
    }
}


extension TrialVipAlert {
    public static func show() {
        guard let appDel = UIApplication.shared.delegate as? AppDelegate else { return }
        let showingView = TrialVipAlert(frame: CGRect(x: 0, y: 0, width: LCDevice.screenW, height: LCDevice.screenH))
        showingView.alpha = 0.0
        appDel.wd.addSubview(showingView)
        UIView.animate(withDuration: 0.2) {
            showingView.alpha = 1.0
        }
    }
}


// MARK: - iOS16 系统购买弹窗

// 直播间的弹出
class LessiOS16BuyAlert: UIView {
    
    private var sureAction: VIPAction?
        
    lazy var contentView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.clear
        return view
    }()
    
    lazy var bgView: UIImageView = {
        let view = UIImageView()
        view.cornerRadius = 8
        view.contentMode = .scaleAspectFill
        view.image = UIImage(named: "roomalertbg")
        view.backgroundColor = UIColor.clear
        view.isUserInteractionEnabled = true
        return view
    }()
    
    lazy var title: UILabel = {
        let label = UILabel()
        label.text = "温馨提示"
        label.font = UIFont.systemFont(ofSize: 20, weight: .medium)
        label.textColor = UIColor("#333333")
        return label
    }()
    
    
    lazy var vipLab: UILabel = {
        let label = UILabel()
        label.numberOfLines = 0
        let text = "智能场控执行端无法使用"
        let paraph = NSMutableParagraphStyle()
        paraph.lineSpacing = 6
        let attributes = [NSAttributedString.Key.paragraphStyle: paraph, NSAttributedString.Key.foregroundColor: UIColor("#434447"), NSAttributedString.Key.font: UIFont.systemFont(ofSize: 15, weight: .regular)]
        
        let attributedText = NSMutableAttributedString(string: "您当前系统版本＜iOS16.0，\(text)，请务必将您的iOS系统升级到16.0以上再购买", attributes: attributes)
        
        attributedText.setAttributes([NSAttributedString.Key.foregroundColor: UIColor("#6974F2"), NSAttributedString.Key.font: UIFont.systemFont(ofSize: 15, weight: .medium),NSAttributedString.Key.paragraphStyle: paraph], range: NSRange(location: 16, length: text.count))
        label.attributedText = attributedText
        
        return label
    }()
    
    private lazy var cancelBtn: UIButton = {
        let btn = UIButton()
        btn.backgroundColor = UIColor("#6974F2")
        btn.cornerRadius = 20
        btn.setTitle("我知道了", for: .normal)
        btn.setTitleColor(UIColor.white, for: .normal)
        btn.titleLabel?.font = UIFont.systemFont(ofSize: 15, weight: .medium)
        btn.addTarget(self, action: #selector(cancelBtnAction(sender:)), for: .touchUpInside)
        return btn
    }()
    
    
//    private lazy var okBtn: UIButton = {
//        let btn = UIButton()
//        btn.backgroundColor = UIColor("#E5E5E5")
//        btn.cornerRadius = 20
//        btn.setTitle("去购买", for: .normal)
//        btn.setTitleColor(UIColor("#4D4E52"), for: .normal)
//        btn.titleLabel?.font = UIFont.systemFont(ofSize: 15, weight: .medium)
//        btn.addTarget(self, action: #selector(okBtnAction(sender:)), for: .touchUpInside)
//        return btn
//    }()
    
    
    override init(frame: CGRect) {
        super.init(frame: frame)
    }
    
    convenience init( sureAction: VIPAction?) {
        self.init(frame: CGRect(x: 0, y: 0, width: LCDevice.screenW, height: LCDevice.screenH))
        self.sureAction = sureAction
        makeUI()
        business()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func makeUI() {
        self.backgroundColor = UIColor.black.alpha(value: 0.6)
        
        addSubviews([contentView])
        contentView.addSubviews([bgView, title, vipLab, cancelBtn])
        
        contentView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview()
            make.width.equalTo(320)
            make.height.equalTo(274)
        }
       
        bgView.snp.makeConstraints { make in
            make.top.bottom.leading.trailing.equalToSuperview()
        }
        
        title.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalToSuperview().inset(50)
            make.height.equalTo(30)
        }
        
        vipLab.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(107)
            make.leading.trailing.equalToSuperview().inset(30)
            make.height.equalTo(70)
        }
        
      
//        okBtn.snp.makeConstraints { make in
//            make.bottom.equalToSuperview().inset(24)
//            make.left.equalToSuperview().inset(24)
//            make.height.equalTo(40)
//            make.width.equalTo(124)
//        }
        
        cancelBtn.snp.makeConstraints { make in
            make.bottom.equalToSuperview().inset(24)
            make.centerX.equalToSuperview()
            make.height.equalTo(40)
            make.width.equalTo(124)
        }
       
    }
    
    func business() {
        
    }
    
    @objc func okBtnAction(sender: UIButton) {
        self.dismiss()
        if let callback = self.sureAction {
            callback()
        }
    }
    
    @objc func cancelBtnAction(sender: UIButton) {
        dismiss()
    }
    
    func dismiss() {
        UIView.animate(withDuration: 0.2) {
            self.alpha = 0
        } completion: { _ in
            self.removeFromSuperview()
        }
    }
}


extension LessiOS16BuyAlert {
    public static func show(sureAction: VIPAction?) {
        guard let appDel = UIApplication.shared.delegate as? AppDelegate else { return }
        let showingView = LessiOS16BuyAlert(sureAction: sureAction)
        showingView.alpha = 0.0
        appDel.wd.addSubview(showingView)
        UIView.animate(withDuration: 0.2) {
            showingView.alpha = 1.0
        }
    }
}


// MARK: - iOS16 系统升级

// 直播间的弹出
class LessiOS16Alert: UIView {
            
    lazy var contentView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.clear
        return view
    }()
    
    lazy var bgView: UIImageView = {
        let view = UIImageView()
        view.cornerRadius = 8
        view.contentMode = .scaleAspectFill
        view.image = UIImage(named: "roomalertbg")
        view.backgroundColor = UIColor.clear
        view.isUserInteractionEnabled = true
        return view
    }()
    
    lazy var title: UILabel = {
        let label = UILabel()
        label.text = "温馨提示"
        label.font = UIFont.systemFont(ofSize: 20, weight: .medium)
        label.textColor = UIColor("#333333")
        return label
    }()
    
    
    lazy var vipLab: UILabel = {
        let label = UILabel()
        label.numberOfLines = 0
        let text = "需升级手机系统"
        let paraph = NSMutableParagraphStyle()
        paraph.lineSpacing = 6
        let attributes = [NSAttributedString.Key.paragraphStyle: paraph, NSAttributedString.Key.foregroundColor: UIColor("#434447"), NSAttributedString.Key.font: UIFont.systemFont(ofSize: 15, weight: .regular)]
        
        let attributedText = NSMutableAttributedString(string: "您当前系统版本＜iOS16.0，\(text)，再进行使用", attributes: attributes)
        
        attributedText.setAttributes([NSAttributedString.Key.foregroundColor: UIColor("#6974F2"), NSAttributedString.Key.font: UIFont.systemFont(ofSize: 15, weight: .medium),NSAttributedString.Key.paragraphStyle: paraph], range: NSRange(location: 16, length: text.count))
        label.attributedText = attributedText
        
        return label
    }()
    
    private lazy var cancelBtn: UIButton = {
        let btn = UIButton()
        btn.backgroundColor = UIColor("#6974F2")
        btn.cornerRadius = 20
        btn.setTitle("我知道了", for: .normal)
        btn.setTitleColor(UIColor.white, for: .normal)
        btn.titleLabel?.font = UIFont.systemFont(ofSize: 15, weight: .medium)
        btn.addTarget(self, action: #selector(cancelBtnAction(sender:)), for: .touchUpInside)
        return btn
    }()
    
    
    
    override init(frame: CGRect) {
        super.init(frame: frame)
    }
    
    convenience init( sureAction: VIPAction?) {
        self.init(frame: CGRect(x: 0, y: 0, width: LCDevice.screenW, height: LCDevice.screenH))
        makeUI()
        business()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func makeUI() {
        self.backgroundColor = UIColor.black.alpha(value: 0.6)
        
        addSubviews([contentView])
        contentView.addSubviews([bgView, title, vipLab, cancelBtn])
        
        contentView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview()
            make.width.equalTo(320)
            make.height.equalTo(274)
        }
       
        bgView.snp.makeConstraints { make in
            make.top.bottom.leading.trailing.equalToSuperview()
        }
        
        title.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalToSuperview().inset(50)
            make.height.equalTo(30)
        }
        
        vipLab.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(107)
            make.leading.trailing.equalToSuperview().inset(30)
            make.height.equalTo(50)
        }
        
    
        cancelBtn.snp.makeConstraints { make in
            make.bottom.equalToSuperview().inset(24)
            make.centerX.equalToSuperview()
            make.height.equalTo(40)
            make.width.equalTo(124)
        }
       
    }
    
    func business() {
        
    }
    
    @objc func okBtnAction(sender: UIButton) {
        self.dismiss()
    }
    
    @objc func cancelBtnAction(sender: UIButton) {
        dismiss()
    }
    
    func dismiss() {
        UIView.animate(withDuration: 0.2) {
            self.alpha = 0
        } completion: { _ in
            self.removeFromSuperview()
        }
    }
}


extension LessiOS16Alert {
    public static func show(sureAction: VIPAction?) {
        guard let appDel = UIApplication.shared.delegate as? AppDelegate else { return }
        let showingView = LessiOS16Alert(sureAction: sureAction)
        showingView.alpha = 0.0
        appDel.wd.addSubview(showingView)
        UIView.animate(withDuration: 0.2) {
            showingView.alpha = 1.0
        }
    }
}



// MARK: - 免费用户的积分弹窗
class FreePointAlert: UIView {
    
    private var sureAction: VIPAction?
    
    private var cancelAction: VIPAction?
    
    lazy var contentView: UIView = {
        let view = UIView()
        view.cornerRadius = 8
        view.backgroundColor = UIColor.white
        return view
    }()
    
    lazy var iconView: UIImageView = {
        let view = UIImageView()
        view.image = UIImage(named: "roomalert")
        view.backgroundColor = UIColor.clear
        return view
    }()
    
    lazy var bgView: UIImageView = {
        let view = UIImageView()
        view.cornerRadius = 8
        view.contentMode = .scaleAspectFill
        view.image = UIImage(named: "roomalertbg")
        view.backgroundColor = UIColor.clear
        view.isUserInteractionEnabled = true
        return view
    }()
    
    lazy var title: UILabel = {
        let label = UILabel()
        label.text = "温馨提示"
        label.font = UIFont.systemFont(ofSize: 20, weight: .medium)
        label.textColor = UIColor("#333333")
        return label
    }()
    
    lazy var vipLab: UILabel = {
        let label = UILabel()
        let text = "我的积分需开通会员后方可使用～"
        
        let paraph = NSMutableParagraphStyle()
        paraph.lineSpacing = 6
        let attributes = [NSAttributedString.Key.paragraphStyle: paraph, NSAttributedString.Key.foregroundColor: UIColor("#4D4E52"), NSAttributedString.Key.font: UIFont.systemFont(ofSize: 14, weight: .medium)]
        
        let attributedText = NSMutableAttributedString(string: text, attributes: attributes)
        
        attributedText.setAttributes([NSAttributedString.Key.foregroundColor: UIColor("#6974F2"), NSAttributedString.Key.font: UIFont.systemFont(ofSize: 14, weight: .medium)], range: NSRange(location: 5, length: 4))
        label.attributedText = attributedText
        label.numberOfLines = 0
        return label
    }()
    
    private let contentLabel1: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 14, weight: .medium)
        label.textColor = UIColor("#1E1F20")
        label.text = "积分可支持以下功能："
        return label
    }()
    
    private let text11: PointsAlertTextView = {
        let label = PointsAlertTextView()
        label.titleLabel.text = "互动时实时朗读用户昵称"
        return label
    }()
    
    private let text12: PointsAlertTextView = {
        let label = PointsAlertTextView()
        label.titleLabel.text = "AI合成语音"
        return label
    }()
    
    private lazy var cancelBtn: UIButton = {
        let btn = UIButton(frame: CGRect(x: 0.0, y: 0.0, width: 170, height: 40))
        btn.backgroundColor = .white
        btn.setTitle("取消", for: .normal)
        btn.borderColor = UIColor("#E5E3EB")
        btn.borderWidth = 1.5
        btn.setTitleColor(UIColor("#4D4E52"), for: .normal)
        btn.titleLabel?.font = UIFont.systemFont(ofSize: 15, weight: .medium)
        btn.addTarget(self, action: #selector(cancelBtnAction(sender:)), for: .touchUpInside)
        btn.cornerRadius = 20
        return btn
    }()
    
    
    private lazy var okBtn: UIButton = {
        let btn = UIButton(frame: CGRect(x: 0.0, y: 0.0, width: 170, height: 40))
        btn.backgroundColor = UIColor("#6974F2")
        btn.setTitle("去开通", for: .normal)
        btn.setTitleColor(UIColor.white, for: .normal)
        btn.titleLabel?.font = UIFont.systemFont(ofSize: 15, weight: .medium)
        btn.addTarget(self, action: #selector(okBtnAction(sender:)), for: .touchUpInside)
        btn.cornerRadius = 20
        btn.applyGradient()
        return btn
    }()
    
    
    override init(frame: CGRect) {
        super.init(frame: frame)
    }

    convenience init( sureAction: @escaping VIPAction, cancelAction: @escaping VIPAction) {
        self.init(frame: CGRect(x: 0, y: 0, width: LCDevice.screenW, height: LCDevice.screenH))
        self.sureAction = sureAction
        self.cancelAction = cancelAction
        makeUI()
        business()
    }

    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func makeUI() {
        addSubviews([contentView])
        contentView.addSubviews([bgView, iconView, title, okBtn, vipLab, cancelBtn, contentLabel1, text11, text12])
        
        contentView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview()
            make.width.equalTo(303)
            make.height.equalTo(281)
        }
       
        bgView.snp.makeConstraints { make in
            make.top.bottom.leading.trailing.equalToSuperview()
        }
        
        title.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalToSuperview().inset(24)
        }
        
        iconView.snp.makeConstraints { make in
            make.trailing.equalTo(title.snp.leading).offset(-8)
            make.centerY.equalTo(title.snp.centerY)
            make.width.height.equalTo(30)
        }
        
        vipLab.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(76)
            make.leading.trailing.equalToSuperview().inset(24)
            make.height.equalTo(20)
        }
        
        contentLabel1.snp.makeConstraints { make in
            make.top.equalTo(vipLab.snp.bottom).offset(12)
            make.leading.trailing.equalToSuperview().inset(24)
            make.height.equalTo(20)
        }
        
        text11.snp.makeConstraints { make in
            make.height.equalTo(21)
            make.top.equalTo(contentLabel1.snp.bottom).offset(3)
            make.leading.trailing.equalToSuperview().inset(24)
        }
        
        text12.snp.makeConstraints { make in
            make.top.equalTo(text11.snp.bottom)
            make.leading.trailing.equalToSuperview().inset(24)
            make.height.equalTo(45)
        }
      
        okBtn.snp.makeConstraints { make in
            make.bottom.equalToSuperview().inset(20)
            make.trailing.equalToSuperview().inset(24)
            make.height.equalTo(40)
            make.width.equalTo(123)
        }
        
        cancelBtn.snp.makeConstraints { make in
            make.bottom.equalToSuperview().inset(20)
            make.leading.equalToSuperview().inset(24)
            make.height.equalTo(40)
            make.width.equalTo(123)
        }
       
    }
    
    func business() {
        
    }
    
    @objc func okBtnAction(sender: UIButton) {
        
        if let action = self.sureAction {
            action()
        }
        self.dismiss()
    }
    
    @objc func cancelBtnAction(sender: UIButton) {
        dismiss()
        
    }
    
    func dismiss() {
        UIView.animate(withDuration: 0.2) {
            self.alpha = 0
        } completion: { _ in
            self.removeFromSuperview()
        }
    }
}


extension FreePointAlert {
    public static func show(sureAction: @escaping VIPAction, cancelAction: @escaping VIPAction) {
        guard let appDel = UIApplication.shared.delegate as? AppDelegate else { return }
        let showingView = FreePointAlert(sureAction: sureAction, cancelAction: cancelAction)
        showingView.alpha = 0.0
        appDel.wd.addSubview(showingView)
        UIView.animate(withDuration: 0.2) {
            showingView.alpha = 1.0
        }
    }
}



// MARK: - 免费用户的积分弹窗
class FreeAlert: UIView {
    
    private var sureAction: VIPAction?
    
    private var cancelAction: VIPAction?
    
    lazy var contentView: UIView = {
        let view = UIView()
        view.cornerRadius = 8
        view.backgroundColor = UIColor.white
        return view
    }()
    
    lazy var iconView: UIImageView = {
        let view = UIImageView()
        view.image = UIImage(named: "roomalert")
        view.backgroundColor = UIColor.clear
        return view
    }()
    
    lazy var bgView: UIImageView = {
        let view = UIImageView()
        view.cornerRadius = 8
        view.contentMode = .scaleAspectFill
        view.image = UIImage(named: "roomalertbg")
        view.backgroundColor = UIColor.clear
        view.isUserInteractionEnabled = true
        return view
    }()
    
    lazy var title: UILabel = {
        let label = UILabel()
        label.text = "温馨提示"
        label.font = UIFont.systemFont(ofSize: 20, weight: .medium)
        label.textColor = UIColor("#333333")
        return label
    }()
    
    lazy var vipLab: UILabel = {
        let label = UILabel()
        let text = "亲爱的用户，该功能是我们的会员专属服务哦~"
        
        let paraph = NSMutableParagraphStyle()
        paraph.lineSpacing = 6
        let attributes = [NSAttributedString.Key.paragraphStyle: paraph, NSAttributedString.Key.foregroundColor: UIColor("#4D4E52"), NSAttributedString.Key.font: UIFont.systemFont(ofSize: 14, weight: .medium)]
        
        let attributedText = NSMutableAttributedString(string: text, attributes: attributes)
        
        attributedText.setAttributes([NSAttributedString.Key.foregroundColor: UIColor("#6974F2"), NSAttributedString.Key.font: UIFont.systemFont(ofSize: 14, weight: .medium)], range: NSRange(location: 13, length: 4))
        label.attributedText = attributedText
        label.numberOfLines = 0
        return label
    }()
    
    private let contentLabel1: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 14, weight: .medium)
        label.textColor = UIColor("#1E1F20")
        label.text = "开通会员您可获得以下权益："
        return label
    }()
    
    private let text11: PointsAlertTextView = {
        let label = PointsAlertTextView()
        label.titleLabel.text = "商品讲解"
        return label
    }()
    
    private let text12: PointsAlertTextView = {
        let label = PointsAlertTextView()
        label.titleLabel.text = "智能互动"
        return label
    }()
    
    private lazy var cancelBtn: UIButton = {
        let btn = UIButton(frame: CGRect(x: 0.0, y: 0.0, width: 170, height: 40))
        btn.backgroundColor = .white
        btn.setTitle("取消", for: .normal)
        btn.borderColor = UIColor("#E5E3EB")
        btn.borderWidth = 1.5
        btn.setTitleColor(UIColor("#4D4E52"), for: .normal)
        btn.titleLabel?.font = UIFont.systemFont(ofSize: 15, weight: .medium)
        btn.addTarget(self, action: #selector(cancelBtnAction(sender:)), for: .touchUpInside)
        btn.cornerRadius = 20
        return btn
    }()
    
    
    private lazy var okBtn: UIButton = {
        let btn = UIButton(frame: CGRect(x: 0.0, y: 0.0, width: 170, height: 40))
        btn.backgroundColor = UIColor("#6974F2")
        btn.setTitle("去开通", for: .normal)
        btn.setTitleColor(UIColor.white, for: .normal)
        btn.titleLabel?.font = UIFont.systemFont(ofSize: 15, weight: .medium)
        btn.addTarget(self, action: #selector(okBtnAction(sender:)), for: .touchUpInside)
        btn.cornerRadius = 20
        btn.applyGradient()
        return btn
    }()
    
    
    override init(frame: CGRect) {
        super.init(frame: frame)
    }

    convenience init( sureAction: @escaping VIPAction, cancelAction: @escaping VIPAction) {
        self.init(frame: CGRect(x: 0, y: 0, width: LCDevice.screenW, height: LCDevice.screenH))
        self.sureAction = sureAction
        self.cancelAction = cancelAction
        makeUI()
        business()
    }

    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func makeUI() {
        addSubviews([contentView])
        contentView.addSubviews([bgView, iconView, title, okBtn, vipLab, cancelBtn, contentLabel1, text11, text12])
        
        contentView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview()
            make.width.equalTo(303)
            make.height.equalTo(281)
        }
       
        bgView.snp.makeConstraints { make in
            make.top.bottom.leading.trailing.equalToSuperview()
        }
        
        title.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalToSuperview().inset(24)
        }
        
        iconView.snp.makeConstraints { make in
            make.trailing.equalTo(title.snp.leading).offset(-8)
            make.centerY.equalTo(title.snp.centerY)
            make.width.height.equalTo(30)
        }
        
        vipLab.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(76)
            make.leading.trailing.equalToSuperview().inset(24)
            make.height.equalTo(40)
        }
        
        contentLabel1.snp.makeConstraints { make in
            make.top.equalTo(vipLab.snp.bottom).offset(12)
            make.leading.trailing.equalToSuperview().inset(24)
            make.height.equalTo(20)
        }
        
        text11.snp.makeConstraints { make in
            make.height.equalTo(21)
            make.top.equalTo(contentLabel1.snp.bottom).offset(3)
            make.leading.trailing.equalToSuperview().inset(24)
        }
        
        text12.snp.makeConstraints { make in
            make.top.equalTo(text11.snp.bottom)
            make.leading.trailing.equalToSuperview().inset(24)
            make.height.equalTo(45)
        }
      
        okBtn.snp.makeConstraints { make in
            make.bottom.equalToSuperview().inset(20)
            make.trailing.equalToSuperview().inset(24)
            make.height.equalTo(40)
            make.width.equalTo(123)
        }
        
        cancelBtn.snp.makeConstraints { make in
            make.bottom.equalToSuperview().inset(20)
            make.leading.equalToSuperview().inset(24)
            make.height.equalTo(40)
            make.width.equalTo(123)
        }
       
    }
    
    func business() {
        
    }
    
    @objc func okBtnAction(sender: UIButton) {
        
        if let action = self.sureAction {
            action()
        }
        self.dismiss()
    }
    
    @objc func cancelBtnAction(sender: UIButton) {
        dismiss()
        
    }
    
    func dismiss() {
        UIView.animate(withDuration: 0.2) {
            self.alpha = 0
        } completion: { _ in
            self.removeFromSuperview()
        }
    }
}


extension FreeAlert {
    public static func show(sureAction: @escaping VIPAction, cancelAction: @escaping VIPAction) {
        guard let appDel = UIApplication.shared.delegate as? AppDelegate else { return }
        let showingView = FreeAlert(sureAction: sureAction, cancelAction: cancelAction)
        showingView.alpha = 0.0
        appDel.wd.addSubview(showingView)
        UIView.animate(withDuration: 0.2) {
            showingView.alpha = 1.0
        }
    }
}




// MARK: - 开启智能互动弹窗
class HudongOpenLiveAlert: UIView {
    
    private var sureAction: VIPAction?
    
    private var cancelAction: VIPAction?
    
    lazy var contentView: UIView = {
        let view = UIView()
        view.cornerRadius = 8
        view.backgroundColor = UIColor.white
        return view
    }()
    
    lazy var iconView: UIImageView = {
        let view = UIImageView()
        view.image = UIImage(named: "roomalert")
        view.backgroundColor = UIColor.clear
        return view
    }()
    
    lazy var bgView: UIImageView = {
        let view = UIImageView()
        view.cornerRadius = 8
        view.contentMode = .scaleAspectFill
        view.image = UIImage(named: "roomalertbg")
        view.backgroundColor = UIColor.clear
        view.isUserInteractionEnabled = true
        return view
    }()
    
    lazy var title: UILabel = {
        let label = UILabel()
        label.text = "温馨提示"
        label.font = UIFont.systemFont(ofSize: 20, weight: .medium)
        label.textColor = UIColor("#333333")
        return label
    }()
    
    lazy var vipLab: UILabel = {
        let label = UILabel()
        let text = "务必在直播开始后才开启"
        
        let paraph = NSMutableParagraphStyle()
        paraph.lineSpacing = 6
        let attributes = [NSAttributedString.Key.paragraphStyle: paraph, NSAttributedString.Key.foregroundColor: UIColor("#4D4E52"), NSAttributedString.Key.font: UIFont.systemFont(ofSize: 14, weight: .regular)]
        
        let attributedText = NSMutableAttributedString(string: text, attributes: attributes)
        
        attributedText.setAttributes([NSAttributedString.Key.foregroundColor: UIColor("#6974F2"), NSAttributedString.Key.font: UIFont.systemFont(ofSize: 14, weight: .medium)], range: NSRange(location: 3, length: 4))
        label.attributedText = attributedText
        label.numberOfLines = 0
        label.textAlignment = .center
        return label
    }()
    
    private lazy var cancelBtn: UIButton = {
        let btn = UIButton(frame: CGRect(x: 0.0, y: 0.0, width: 170, height: 40))
        btn.backgroundColor = .white
        btn.setTitle("返回", for: .normal)
        btn.borderColor = UIColor("#E5E3EB")
        btn.borderWidth = 1.5
        btn.setTitleColor(UIColor("#4D4E52"), for: .normal)
        btn.titleLabel?.font = UIFont.systemFont(ofSize: 15, weight: .medium)
        btn.addTarget(self, action: #selector(cancelBtnAction(sender:)), for: .touchUpInside)
        btn.cornerRadius = 20
        return btn
    }()
    
    
    private lazy var okBtn: UIButton = {
        let btn = UIButton(frame: CGRect(x: 0.0, y: 0.0, width: 170, height: 40))
        btn.backgroundColor = UIColor("#6974F2")
        btn.setTitle("开启", for: .normal)
        btn.setTitleColor(UIColor.white, for: .normal)
        btn.titleLabel?.font = UIFont.systemFont(ofSize: 15, weight: .medium)
        btn.addTarget(self, action: #selector(okBtnAction(sender:)), for: .touchUpInside)
        btn.cornerRadius = 20
        btn.applyGradient()
        return btn
    }()
    
    
    override init(frame: CGRect) {
        super.init(frame: frame)
    }

    convenience init( sureAction: @escaping VIPAction, cancelAction: @escaping VIPAction) {
        self.init(frame: CGRect(x: 0, y: 0, width: LCDevice.screenW, height: LCDevice.screenH))
        self.sureAction = sureAction
        self.cancelAction = cancelAction
        makeUI()
        business()
    }

    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func makeUI() {
        addSubviews([contentView])
        contentView.addSubviews([bgView, iconView, title, okBtn, vipLab, cancelBtn])
        
        contentView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview()
            make.width.equalTo(303)
            make.height.equalTo(190)
        }
       
        bgView.snp.makeConstraints { make in
            make.top.bottom.leading.trailing.equalToSuperview()
        }
        
        title.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalToSuperview().inset(24)
        }
        
        iconView.snp.makeConstraints { make in
            make.trailing.equalTo(title.snp.leading).offset(-8)
            make.centerY.equalTo(title.snp.centerY)
            make.width.height.equalTo(30)
        }
        
        vipLab.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(76)
            make.leading.trailing.equalToSuperview().inset(24)
            make.height.equalTo(40)
        }
      
        okBtn.snp.makeConstraints { make in
            make.bottom.equalToSuperview().inset(20)
            make.trailing.equalToSuperview().inset(24)
            make.height.equalTo(40)
            make.width.equalTo(123)
        }
        
        cancelBtn.snp.makeConstraints { make in
            make.bottom.equalToSuperview().inset(20)
            make.leading.equalToSuperview().inset(24)
            make.height.equalTo(40)
            make.width.equalTo(123)
        }
       
    }
    
    func business() {
        
    }
    
    @objc func okBtnAction(sender: UIButton) {
        
        if let action = self.sureAction {
            action()
        }
        self.dismiss()
    }
    
    @objc func cancelBtnAction(sender: UIButton) {
        dismiss()
        
    }
    
    func dismiss() {
        UIView.animate(withDuration: 0.2) {
            self.alpha = 0
        } completion: { _ in
            self.removeFromSuperview()
        }
    }
}


extension HudongOpenLiveAlert {
    public static func show(sureAction: @escaping VIPAction, cancelAction: @escaping VIPAction) {
        guard let appDel = UIApplication.shared.delegate as? AppDelegate else { return }
        let showingView = HudongOpenLiveAlert(sureAction: sureAction, cancelAction: cancelAction)
        showingView.alpha = 0.0
        appDel.wd.addSubview(showingView)
        UIView.animate(withDuration: 0.2) {
            showingView.alpha = 1.0
        }
    }
}


// MARK: - 实施朗读开启 一个按钮: 我知道了
class HudongOpenNickTTSAlert_1: UIView {
    
    private var sureAction: VIPAction?
    
    private var cancelAction: VIPAction?
    
    lazy var contentView: UIView = {
        let view = UIView()
        view.cornerRadius = 8
        view.backgroundColor = UIColor.white
        return view
    }()
    
    lazy var iconView: UIImageView = {
        let view = UIImageView()
        view.image = UIImage(named: "roomalert")
        view.backgroundColor = UIColor.clear
        return view
    }()
    
    lazy var bgView: UIImageView = {
        let view = UIImageView()
        view.cornerRadius = 8
        view.contentMode = .scaleAspectFill
        view.image = UIImage(named: "roomalertbg")
        view.backgroundColor = UIColor.clear
        view.isUserInteractionEnabled = true
        return view
    }()
    
    lazy var title: UILabel = {
        let label = UILabel()
        label.text = "温馨提示"
        label.font = UIFont.systemFont(ofSize: 20, weight: .medium)
        label.textColor = UIColor("#333333")
        return label
    }()
    
    lazy var vipLab: UILabel = {
        let label = UILabel()
        let text = "务必在直播开始后才开启"
        
        let paraph = NSMutableParagraphStyle()
        paraph.lineSpacing = 6
        let attributes = [NSAttributedString.Key.paragraphStyle: paraph, NSAttributedString.Key.foregroundColor: UIColor("#4D4E52"), NSAttributedString.Key.font: UIFont.systemFont(ofSize: 14, weight: .medium)]
        
        let attributedText = NSMutableAttributedString(string: text, attributes: attributes)
        
        attributedText.setAttributes([NSAttributedString.Key.foregroundColor: UIColor("#6974F2"), NSAttributedString.Key.font: UIFont.systemFont(ofSize: 14, weight: .medium)], range: NSRange(location: 3, length: 4))
        label.attributedText = attributedText
        label.numberOfLines = 0
        label.textAlignment = .center
        return label
    }()
    
    
    private lazy var okBtn: UIButton = {
        let btn = UIButton(frame: CGRect(x: 0.0, y: 0.0, width: 170, height: 40))
        btn.backgroundColor = UIColor("#6974F2")
        btn.setTitle("我知道了", for: .normal)
        btn.setTitleColor(UIColor.white, for: .normal)
        btn.titleLabel?.font = UIFont.systemFont(ofSize: 15, weight: .medium)
        btn.addTarget(self, action: #selector(okBtnAction(sender:)), for: .touchUpInside)
        btn.cornerRadius = 20
        btn.applyGradient()
        return btn
    }()
    
    
    override init(frame: CGRect) {
        super.init(frame: frame)
    }

    convenience init(sureAction: @escaping VIPAction, cancelAction: @escaping VIPAction, attStr: NSAttributedString) {
        self.init(frame: CGRect(x: 0, y: 0, width: LCDevice.screenW, height: LCDevice.screenH))
        self.sureAction = sureAction
        self.cancelAction = cancelAction
        makeUI()
        business()
        self.vipLab.attributedText = attStr
    }

    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func makeUI() {
        addSubviews([contentView])
        contentView.addSubviews([bgView, iconView, title, okBtn, vipLab])
        
        contentView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview()
            make.width.equalTo(313)
            make.height.equalTo(290)
        }
       
        bgView.snp.makeConstraints { make in
            make.top.bottom.leading.trailing.equalToSuperview()
        }
        
        title.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalToSuperview().inset(24)
        }
        
        iconView.snp.makeConstraints { make in
            make.trailing.equalTo(title.snp.leading).offset(-8)
            make.centerY.equalTo(title.snp.centerY)
            make.width.height.equalTo(30)
        }
        
        vipLab.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(76)
            make.leading.trailing.equalToSuperview().inset(24)
            make.bottom.equalToSuperview().inset(80)
        }
      
        okBtn.snp.makeConstraints { make in
            make.bottom.equalToSuperview().inset(20)
            make.trailing.leading.equalToSuperview().inset(30)
            make.height.equalTo(40)
        }
        
        
       
    }
    
    func business() {
        
    }
    
    @objc func okBtnAction(sender: UIButton) {
        
        if let action = self.sureAction {
            action()
        }
        self.dismiss()
    }
    
    @objc func cancelBtnAction(sender: UIButton) {
        dismiss()
        
    }
    
    func dismiss() {
        UIView.animate(withDuration: 0.2) {
            self.alpha = 0
        } completion: { _ in
            self.removeFromSuperview()
        }
    }
}


extension HudongOpenNickTTSAlert_1 {
    public static func show(sureAction: @escaping VIPAction, cancelAction: @escaping VIPAction, attStr: NSAttributedString) {
        guard let appDel = UIApplication.shared.delegate as? AppDelegate else { return }
        let showingView = HudongOpenNickTTSAlert_1(sureAction: sureAction, cancelAction: cancelAction, attStr: attStr)
        showingView.alpha = 0.0
        appDel.wd.addSubview(showingView)
        UIView.animate(withDuration: 0.2) {
            showingView.alpha = 1.0
        }
    }
}


// MARK: - 实施朗读开启 2个按钮
class HudongOpenNickTTSAlert_2: UIView {
    
    private var sureAction: VIPAction?
    
    private var cancelAction: VIPAction?
    
    lazy var contentView: UIView = {
        let view = UIView()
        view.cornerRadius = 8
        view.backgroundColor = UIColor.white
        return view
    }()
    
    lazy var iconView: UIImageView = {
        let view = UIImageView()
        view.image = UIImage(named: "roomalert")
        view.backgroundColor = UIColor.clear
        return view
    }()
    
    lazy var bgView: UIImageView = {
        let view = UIImageView()
        view.cornerRadius = 8
        view.contentMode = .scaleAspectFill
        view.image = UIImage(named: "roomalertbg")
        view.backgroundColor = UIColor.clear
        view.isUserInteractionEnabled = true
        return view
    }()
    
    lazy var title: UILabel = {
        let label = UILabel()
        label.text = "温馨提示"
        label.font = UIFont.systemFont(ofSize: 20, weight: .medium)
        label.textColor = UIColor("#333333")
        return label
    }()
    
    lazy var vipLab: UILabel = {
        let label = UILabel()
        let text = "务必在直播开始后才开启"
        
        let paraph = NSMutableParagraphStyle()
        paraph.lineSpacing = 6
        let attributes = [NSAttributedString.Key.paragraphStyle: paraph, NSAttributedString.Key.foregroundColor: UIColor("#4D4E52"), NSAttributedString.Key.font: UIFont.systemFont(ofSize: 14, weight: .medium)]
        
        let attributedText = NSMutableAttributedString(string: text, attributes: attributes)
        
        attributedText.setAttributes([NSAttributedString.Key.foregroundColor: UIColor("#6974F2"), NSAttributedString.Key.font: UIFont.systemFont(ofSize: 14, weight: .medium)], range: NSRange(location: 3, length: 4))
        label.attributedText = attributedText
        label.numberOfLines = 0
        label.textAlignment = .center
        return label
    }()
    
     lazy var cancelBtn: UIButton = {
        let btn = UIButton(frame: CGRect(x: 0.0, y: 0.0, width: 170, height: 40))
        btn.backgroundColor = .white
        btn.setTitle("取消", for: .normal)
        btn.borderColor = UIColor("#E5E3EB")
        btn.borderWidth = 1.5
        btn.setTitleColor(UIColor("#4D4E52"), for: .normal)
        btn.titleLabel?.font = UIFont.systemFont(ofSize: 15, weight: .medium)
        btn.addTarget(self, action: #selector(cancelBtnAction(sender:)), for: .touchUpInside)
        btn.cornerRadius = 20
        return btn
    }()
    
    
    private lazy var okBtn: UIButton = {
        let btn = UIButton(frame: CGRect(x: 0.0, y: 0.0, width: 170, height: 40))
        btn.backgroundColor = UIColor("#6974F2")
        btn.setTitle("去充值", for: .normal)
        btn.setTitleColor(UIColor.white, for: .normal)
        btn.titleLabel?.font = UIFont.systemFont(ofSize: 15, weight: .medium)
        btn.addTarget(self, action: #selector(okBtnAction(sender:)), for: .touchUpInside)
        btn.cornerRadius = 20
        btn.applyGradient()
        return btn
    }()
    
    
    override init(frame: CGRect) {
        super.init(frame: frame)
    }

    convenience init( sureAction: @escaping VIPAction, cancelAction: @escaping VIPAction, attStr: NSAttributedString) {
        self.init(frame: CGRect(x: 0, y: 0, width: LCDevice.screenW, height: LCDevice.screenH))
        self.sureAction = sureAction
        self.cancelAction = cancelAction
        makeUI()
        business()
        vipLab.attributedText = attStr
    }

    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func makeUI() {
        addSubviews([contentView])
        contentView.addSubviews([bgView, iconView, title, okBtn, vipLab, cancelBtn])
        
        contentView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview()
            make.width.equalTo(303)
            make.height.equalTo(200)
        }
       
        bgView.snp.makeConstraints { make in
            make.top.bottom.leading.trailing.equalToSuperview()
        }
        
        title.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalToSuperview().inset(24)
        }
        
        iconView.snp.makeConstraints { make in
            make.trailing.equalTo(title.snp.leading).offset(-8)
            make.centerY.equalTo(title.snp.centerY)
            make.width.height.equalTo(30)
        }
        
        vipLab.snp.makeConstraints { make in
            make.top.bottom.equalToSuperview().inset(76)
            make.leading.trailing.equalToSuperview().inset(24)
        }
      
        okBtn.snp.makeConstraints { make in
            make.bottom.equalToSuperview().inset(20)
            make.trailing.equalToSuperview().inset(24)
            make.height.equalTo(40)
            make.width.equalTo(123)
        }
        
        cancelBtn.snp.makeConstraints { make in
            make.bottom.equalToSuperview().inset(20)
            make.leading.equalToSuperview().inset(24)
            make.height.equalTo(40)
            make.width.equalTo(123)
        }
       
    }
    
    func business() {
        
    }
    
    @objc func okBtnAction(sender: UIButton) {
        
        if let action = self.sureAction {
            action()
        }
        self.dismiss()
    }
    
    @objc func cancelBtnAction(sender: UIButton) {
        dismiss()
        if let action = self.cancelAction {
            action()
        }
    }
    
    func dismiss() {
        UIView.animate(withDuration: 0.2) {
            self.alpha = 0
        } completion: { _ in
            self.removeFromSuperview()
        }
    }
}


extension HudongOpenNickTTSAlert_2 {
    public static func show(sureAction: @escaping VIPAction, cancelAction: @escaping VIPAction, attStr: NSAttributedString, leftTitle:String? = nil) {
        guard let appDel = UIApplication.shared.delegate as? AppDelegate else { return }
        let showingView = HudongOpenNickTTSAlert_2(sureAction: sureAction, cancelAction: cancelAction, attStr: attStr)
        if let leftTitle = leftTitle {
            showingView.cancelBtn.setTitle(leftTitle, for: .normal)
        }
        showingView.alpha = 0.0
        appDel.wd.addSubview(showingView)
        UIView.animate(withDuration: 0.2) {
            showingView.alpha = 1.0
        }
    }
}





// MARK: - 示例弹窗 一个按钮: 我知道了
class HudongSampleTextSAlert: UIView {
    
    private var sureAction: VIPAction?
        
    lazy var contentView: UIView = {
        let view = UIView()
        view.cornerRadius = 8
        view.backgroundColor = UIColor.white
        return view
    }()
    
    lazy var title: UILabel = {
        let label = UILabel()
        label.text = "示例"
        label.font = UIFont.systemFont(ofSize: 20, weight: .medium)
        label.textColor = UIColor("#333333")
        return label
    }()
    
    lazy var vipLab: UILabel = {
        let label = UILabel()
       
        label.numberOfLines = 0
        label.textAlignment = .left
        return label
    }()
    
    
    private lazy var okBtn: UIButton = {
        let btn = UIButton(frame: CGRect(x: 0.0, y: 0.0, width: 170, height: 40))
        btn.backgroundColor = UIColor("#6974F2")
        btn.setTitle("我知道了", for: .normal)
        btn.setTitleColor(UIColor.white, for: .normal)
        btn.titleLabel?.font = UIFont.systemFont(ofSize: 15, weight: .medium)
        btn.addTarget(self, action: #selector(okBtnAction(sender:)), for: .touchUpInside)
        btn.cornerRadius = 20
        btn.applyGradient()
        return btn
    }()
    
    
    override init(frame: CGRect) {
        super.init(frame: frame)
    }

    convenience init(sureAction: @escaping VIPAction, attStr: NSAttributedString) {
        self.init(frame: CGRect(x: 0, y: 0, width: LCDevice.screenW, height: LCDevice.screenH))
        self.sureAction = sureAction

        makeUI()
        business()
        self.vipLab.attributedText = attStr
        self.vipLab.numberOfLines = 0
        self.vipLab.textAlignment = .left
    }

    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func makeUI() {
        addSubviews([contentView])
        contentView.addSubviews([title, okBtn, vipLab])
        
        contentView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview()
            make.width.equalTo(313)
            make.bottom.equalTo(okBtn.snp.bottom).offset(24)
        }
       
        
        title.snp.makeConstraints { make in
            make.leading.top.equalToSuperview().inset(24)
            make.height.equalTo(27)
        }
        
        
        vipLab.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(76)
            make.leading.trailing.equalToSuperview().inset(24)
        }
      
        okBtn.snp.makeConstraints { make in
            make.top.equalTo(vipLab.snp.bottom).offset(24)
            make.trailing.leading.equalToSuperview().inset(30)
            make.height.equalTo(40)
        }
       
    }
    
    func business() {
        
    }
    
    @objc func okBtnAction(sender: UIButton) {
        
        if let action = self.sureAction {
            action()
        }
        self.dismiss()
    }
    
    @objc func cancelBtnAction(sender: UIButton) {
        dismiss()
        
    }
    
    func dismiss() {
        UIView.animate(withDuration: 0.2) {
            self.alpha = 0
        } completion: { _ in
            self.removeFromSuperview()
        }
    }
}


extension HudongSampleTextSAlert {
    public static func show(sureAction: @escaping VIPAction, attStr: NSAttributedString) {
        guard let appDel = UIApplication.shared.delegate as? AppDelegate else { return }
        let showingView = HudongSampleTextSAlert(sureAction: sureAction, attStr: attStr)
        showingView.alpha = 0.0
        appDel.wd.addSubview(showingView)
        UIView.animate(withDuration: 0.2) {
            showingView.alpha = 1.0
        }
    }
}



// MARK: - AI合成  积分不够的提示 2个按钮
class TTSPointsAlert_2: UIView {
    
    private var sureAction: VIPAction?
    
    private var cancelAction: VIPAction?
    
    lazy var contentView: UIView = {
        let view = UIView()
        view.cornerRadius = 8
        view.backgroundColor = UIColor.white
        return view
    }()
    
    lazy var iconView: UIImageView = {
        let view = UIImageView()
        view.image = UIImage(named: "roomalert")
        view.backgroundColor = UIColor.clear
        return view
    }()
    
    lazy var bgView: UIImageView = {
        let view = UIImageView()
        view.cornerRadius = 8
        view.contentMode = .scaleAspectFill
        view.image = UIImage(named: "roomalertbg")
        view.backgroundColor = UIColor.clear
        view.isUserInteractionEnabled = true
        return view
    }()
    
    lazy var title: UILabel = {
        let label = UILabel()
        label.text = "温馨提示"
        label.font = UIFont.systemFont(ofSize: 20, weight: .medium)
        label.textColor = UIColor("#333333")
        return label
    }()
    
    lazy var vipLab: UILabel = {
        let label = UILabel()
        let text = "务必在直播开始后才开启"
        
        let paraph = NSMutableParagraphStyle()
        paraph.lineSpacing = 6
        let attributes = [NSAttributedString.Key.paragraphStyle: paraph, NSAttributedString.Key.foregroundColor: UIColor("#4D4E52"), NSAttributedString.Key.font: UIFont.systemFont(ofSize: 14, weight: .medium)]
        
        let attributedText = NSMutableAttributedString(string: text, attributes: attributes)
        
        attributedText.setAttributes([NSAttributedString.Key.foregroundColor: UIColor("#6974F2"), NSAttributedString.Key.font: UIFont.systemFont(ofSize: 14, weight: .medium)], range: NSRange(location: 3, length: 4))
        label.attributedText = attributedText
        label.numberOfLines = 0
        label.textAlignment = .center
        return label
    }()
    
    private lazy var cancelBtn: UIButton = {
        let btn = UIButton(frame: CGRect(x: 0.0, y: 0.0, width: 170, height: 40))
        btn.backgroundColor = .white
        btn.setTitle("取消", for: .normal)
        btn.borderColor = UIColor("#E5E3EB")
        btn.borderWidth = 1.5
        btn.setTitleColor(UIColor("#4D4E52"), for: .normal)
        btn.titleLabel?.font = UIFont.systemFont(ofSize: 15, weight: .medium)
        btn.addTarget(self, action: #selector(cancelBtnAction(sender:)), for: .touchUpInside)
        btn.cornerRadius = 20
        return btn
    }()
    
    
    private lazy var okBtn: UIButton = {
        let btn = UIButton(frame: CGRect(x: 0.0, y: 0.0, width: 170, height: 40))
        btn.backgroundColor = UIColor("#6974F2")
        btn.setTitle("去充值", for: .normal)
        btn.setTitleColor(UIColor.white, for: .normal)
        btn.titleLabel?.font = UIFont.systemFont(ofSize: 15, weight: .medium)
        btn.addTarget(self, action: #selector(okBtnAction(sender:)), for: .touchUpInside)
        btn.cornerRadius = 20
        btn.applyGradient()
        return btn
    }()
    
    
    override init(frame: CGRect) {
        super.init(frame: frame)
    }

    convenience init( sureAction: @escaping VIPAction, cancelAction: @escaping VIPAction, attStr: NSAttributedString) {
        self.init(frame: CGRect(x: 0, y: 0, width: LCDevice.screenW, height: LCDevice.screenH))
        self.sureAction = sureAction
        self.cancelAction = cancelAction
        makeUI()
        business()
        vipLab.attributedText = attStr
        vipLab.textAlignment = .center
    }

    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func makeUI() {
        addSubviews([contentView])
        contentView.addSubviews([bgView, iconView, title, okBtn, vipLab, cancelBtn])
        
        contentView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview()
            make.width.equalTo(303)
            make.height.equalTo(330)
        }
       
        bgView.snp.makeConstraints { make in
            make.top.bottom.leading.trailing.equalToSuperview()
        }
        
        title.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalToSuperview().inset(24)
        }
        
        iconView.snp.makeConstraints { make in
            make.trailing.equalTo(title.snp.leading).offset(-8)
            make.centerY.equalTo(title.snp.centerY)
            make.width.height.equalTo(30)
        }
        
        vipLab.snp.makeConstraints { make in
            make.top.bottom.equalToSuperview().inset(76)
            make.leading.trailing.equalToSuperview().inset(24)
        }
      
        okBtn.snp.makeConstraints { make in
            make.bottom.equalToSuperview().inset(20)
            make.trailing.equalToSuperview().inset(24)
            make.height.equalTo(40)
            make.width.equalTo(123)
        }
        
        cancelBtn.snp.makeConstraints { make in
            make.bottom.equalToSuperview().inset(20)
            make.leading.equalToSuperview().inset(24)
            make.height.equalTo(40)
            make.width.equalTo(123)
        }
       
    }
    
    func business() {
        
    }
    
    @objc func okBtnAction(sender: UIButton) {
        
        if let action = self.sureAction {
            action()
        }
        self.dismiss()
    }
    
    @objc func cancelBtnAction(sender: UIButton) {
        dismiss()
        
    }
    
    func dismiss() {
        UIView.animate(withDuration: 0.2) {
            self.alpha = 0
        } completion: { _ in
            self.removeFromSuperview()
        }
    }
}


extension TTSPointsAlert_2 {
    public static func show(sureAction: @escaping VIPAction, cancelAction: @escaping VIPAction, attStr: NSAttributedString) {
        guard let appDel = UIApplication.shared.delegate as? AppDelegate else { return }
        let showingView = TTSPointsAlert_2(sureAction: sureAction, cancelAction: cancelAction, attStr: attStr)
        showingView.alpha = 0.0
        appDel.wd.addSubview(showingView)
        UIView.animate(withDuration: 0.2) {
            showingView.alpha = 1.0
        }
    }
}


// MARK: - AI克隆  积分不够的提示 2个按钮
class TTSPointsAlert_3: UIView {
    
    private var sureAction: VIPAction?
    
    private var cancelAction: VIPAction?
    
    var isClone: Bool = true
    
    lazy var contentView: UIView = {
        let view = UIView()
        view.cornerRadius = 8
        view.backgroundColor = UIColor.white
        return view
    }()
    
    lazy var iconView: UIImageView = {
        let view = UIImageView()
        view.image = UIImage(named: "roomalert")
        view.backgroundColor = UIColor.clear
        return view
    }()
    
    lazy var bgView: UIImageView = {
        let view = UIImageView()
        view.cornerRadius = 8
        view.contentMode = .scaleAspectFill
        view.image = UIImage(named: "roomalertbg")
        view.backgroundColor = UIColor.clear
        view.isUserInteractionEnabled = true
        return view
    }()
    
    lazy var title: UILabel = {
        let label = UILabel()
        label.text = "温馨提示"
        label.font = UIFont.systemFont(ofSize: 20, weight: .medium)
        label.textColor = UIColor("#333333")
        return label
    }()
    
    lazy var vipLab: UILabel = {
        let label = UILabel()
        let text = ""
        label.numberOfLines = 0
        label.textAlignment = .center
        return label
    }()
    
    private lazy var cancelBtn: UIButton = {
        let btn = UIButton(frame: CGRect(x: 0.0, y: 0.0, width: 170, height: 40))
        btn.backgroundColor = .white
        btn.setTitle("取消", for: .normal)
        btn.borderColor = UIColor("#E5E3EB")
        btn.borderWidth = 1.5
        btn.setTitleColor(UIColor("#4D4E52"), for: .normal)
        btn.titleLabel?.font = UIFont.systemFont(ofSize: 15, weight: .medium)
        btn.addTarget(self, action: #selector(cancelBtnAction(sender:)), for: .touchUpInside)
        btn.cornerRadius = 20
        return btn
    }()
    
    
    private lazy var okBtn: UIButton = {
        let btn = UIButton(frame: CGRect(x: 0.0, y: 0.0, width: 170, height: 40))
        btn.backgroundColor = UIColor("#6974F2")
        btn.setTitle("确定", for: .normal)
        btn.setTitleColor(UIColor.white, for: .normal)
        btn.titleLabel?.font = UIFont.systemFont(ofSize: 15, weight: .medium)
        btn.addTarget(self, action: #selector(okBtnAction(sender:)), for: .touchUpInside)
        btn.cornerRadius = 20
        btn.applyGradient()
        return btn
    }()
    
    
    lazy var checkButton: UIButton = {
        let button = UIButton()
        button.addTarget(self, action: #selector(closeButtonAction), for: .touchUpInside)
        button.setImage(UIImage(named: "登录流程_组件_cell_勾选_nor"), for: .normal)
        button.setImage(UIImage(named: "icon_勾选_sel"), for: .selected)
        button.imageView?.contentMode = .scaleAspectFit
        button.setTitle(" 不再提示", for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 12, weight: .regular)
        button.setTitleColor(UIColor("#A5A7AC"), for: .normal)
        return button
    }()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
    }

    convenience init( sureAction: @escaping VIPAction, cancelAction: @escaping VIPAction, attStr: NSAttributedString, isClone: Bool = true) {
        self.init(frame: CGRect(x: 0, y: 0, width: LCDevice.screenW, height: LCDevice.screenH))
        self.sureAction = sureAction
        self.cancelAction = cancelAction
        self.isClone = isClone
        makeUI()
        business()
        vipLab.attributedText = attStr
        vipLab.textAlignment = .center
    }

    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func makeUI() {
        addSubviews([contentView])
        contentView.addSubviews([bgView, iconView, title, okBtn, vipLab, cancelBtn,checkButton])
        
        contentView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview()
            make.width.equalTo(303)
            make.height.equalTo(350)
        }
       
        bgView.snp.makeConstraints { make in
            make.top.bottom.leading.trailing.equalToSuperview()
        }
        
        title.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalToSuperview().inset(24)
        }
        
        iconView.snp.makeConstraints { make in
            make.trailing.equalTo(title.snp.leading).offset(-8)
            make.centerY.equalTo(title.snp.centerY)
            make.width.height.equalTo(30)
        }
        
        vipLab.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(70)
            make.bottom.equalToSuperview().inset(120)
            make.leading.trailing.equalToSuperview().inset(24)
        }
      
        okBtn.snp.makeConstraints { make in
            make.bottom.equalToSuperview().inset(64)
            make.trailing.equalToSuperview().inset(24)
            make.height.equalTo(40)
            make.width.equalTo(123)
        }
        
        cancelBtn.snp.makeConstraints { make in
            make.bottom.equalToSuperview().inset(64)
            make.leading.equalToSuperview().inset(24)
            make.height.equalTo(40)
            make.width.equalTo(123)
        }
        
        checkButton.snp.makeConstraints { make in
            make.bottom.equalToSuperview().inset(12)
            make.centerX.equalToSuperview()
            make.height.equalTo(40)
            make.width.equalTo(123)
        }
       
    }
    
    func business() {
        
    }
    
    @objc func closeButtonAction() {
        self.checkButton.isSelected = !self.checkButton.isSelected
    }
    
    @objc func okBtnAction(sender: UIButton) {
        if let action = self.sureAction {
            action()
        }
        if isClone {
            UserDefaults.standard.set(self.checkButton.isSelected, forKey: LCKey.UD_Point_Alert_Clone)
        } else {
            UserDefaults.standard.set(self.checkButton.isSelected, forKey: LCKey.UD_Point_Alert_TTS)
        }
        self.dismiss()
    }
    
    @objc func cancelBtnAction(sender: UIButton) {
        dismiss()
    }
    
    func dismiss() {
        UIView.animate(withDuration: 0.2) {
            self.alpha = 0
        } completion: { _ in
            self.removeFromSuperview()
        }
    }
}


extension TTSPointsAlert_3 {
    public static func show(sureAction: @escaping VIPAction, cancelAction: @escaping VIPAction, attStr: NSAttributedString, isClone: Bool = true) {
        guard let appDel = UIApplication.shared.delegate as? AppDelegate else { return }
        let showingView = TTSPointsAlert_3(sureAction: sureAction, cancelAction: cancelAction, attStr: attStr)
        showingView.alpha = 0.0
        appDel.wd.addSubview(showingView)
        UIView.animate(withDuration: 0.2) {
            showingView.alpha = 1.0
        }
    }
}


// MARK: - 积分计费规则
class PointRuleAlert: UIView {
    
    private var sureAction: VIPAction?
    
    private var cancelAction: VIPAction?
    
    lazy var contentView: UIView = {
        let view = UIView()
        view.cornerRadius = 8
        view.backgroundColor = UIColor.white
        return view
    }()
    
    lazy var iconView: UIImageView = {
        let view = UIImageView()
        view.image = UIImage(named: "roomalert")
        view.backgroundColor = UIColor.clear
        return view
    }()
    
    lazy var bgView: UIImageView = {
        let view = UIImageView()
        view.cornerRadius = 8
        view.contentMode = .scaleAspectFill
        view.image = UIImage(named: "roomalertbg")
        view.backgroundColor = UIColor.clear
        view.isUserInteractionEnabled = true
        return view
    }()
    
    lazy var title: UILabel = {
        let label = UILabel()
        label.text = "计费规则"
        label.font = UIFont.systemFont(ofSize: 20, weight: .medium)
        label.textColor = UIColor("#333333")
        return label
    }()
    
    private let text11: PointsAlertTextView = {
        let label = PointsAlertTextView()
        label.titleLabel.text = "每20字消耗1积分"
        return label
    }()
    
    private let text12: PointsAlertTextView = {
        let label = PointsAlertTextView()
        label.titleLabel.text = "不足20字按1积分计算"
        return label
    }()
    
    private lazy var cancelBtn: UIButton = {
        let btn = UIButton()
        btn.backgroundColor = UIColor("#6974F2")
        btn.cornerRadius = 20
        btn.setTitle("我知道了", for: .normal)
        btn.setTitleColor(UIColor.white, for: .normal)
        btn.titleLabel?.font = UIFont.systemFont(ofSize: 15, weight: .medium)
        btn.addTarget(self, action: #selector(cancelBtnAction(sender:)), for: .touchUpInside)
        btn.applyGradient()
        return btn
    }()
    
    
    override init(frame: CGRect) {
        super.init(frame: frame)
    }

    convenience init( sureAction: @escaping VIPAction, cancelAction: @escaping VIPAction) {
        self.init(frame: CGRect(x: 0, y: 0, width: LCDevice.screenW, height: LCDevice.screenH))
        self.sureAction = sureAction
        self.cancelAction = cancelAction
        makeUI()
        business()
    }

    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func makeUI() {
        addSubviews([contentView])
        contentView.addSubviews([bgView, iconView, title, cancelBtn, text11, text12])
        
        contentView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview()
            make.width.equalTo(303)
            make.height.equalTo(220)
        }
       
        bgView.snp.makeConstraints { make in
            make.top.bottom.leading.trailing.equalToSuperview()
        }
        
        title.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalToSuperview().inset(24)
        }
        
        iconView.snp.makeConstraints { make in
            make.trailing.equalTo(title.snp.leading).offset(-8)
            make.centerY.equalTo(title.snp.centerY)
            make.width.height.equalTo(30)
        }
        
    
        text11.snp.makeConstraints { make in
            make.height.equalTo(21)
            make.top.equalToSuperview().inset(76)
            make.leading.trailing.equalToSuperview().inset(24)
        }
        
        text12.snp.makeConstraints { make in
            make.top.equalTo(text11.snp.bottom)
            make.leading.trailing.equalToSuperview().inset(24)
        }
        
        cancelBtn.snp.makeConstraints { make in
            make.bottom.equalToSuperview().inset(20)
            make.leading.trailing.equalToSuperview().inset(24)
            make.height.equalTo(40)
        }
       
    }
    
    func business() {
        
    }
    
    
    @objc func cancelBtnAction(sender: UIButton) {
        dismiss()
    }
    
    func dismiss() {
        UIView.animate(withDuration: 0.2) {
            self.alpha = 0
        } completion: { _ in
            self.removeFromSuperview()
        }
    }
}


extension PointRuleAlert {
    public static func show(sureAction: @escaping VIPAction, cancelAction: @escaping VIPAction) {
        guard let appDel = UIApplication.shared.delegate as? AppDelegate else { return }
        let showingView = PointRuleAlert(sureAction: sureAction, cancelAction: cancelAction)
        showingView.alpha = 0.0
        appDel.wd.addSubview(showingView)
        UIView.animate(withDuration: 0.2) {
            showingView.alpha = 1.0
        }
    }
}
