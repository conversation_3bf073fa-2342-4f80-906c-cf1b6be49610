//
//  LimitMemberCard3.swift
//  LivePlus
//
//  Created by 郭炜 on 2023/7/12.
//

import UIKit

class LimitMemberCard3: UICollectionViewCell {
    
    lazy var background: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor("#282828")
        view.cornerRadius = 12
        view.borderWidth = 2
        return view
    }()
    
    lazy var memberBackground: UIImageView = {
        let imageView = UIImageView(image: UIImage(named: "vip_new_back"))
        return imageView
    }()
    
    // 角标
    lazy var cornView: UIImageView = {
        let imageView = UIImageView()
        return imageView
    }()
    
    // 特惠
    lazy var discountsView: UIImageView = {
        let imageView = UIImageView()
        return imageView
    }()
    
    
    lazy var memberTitle: UILabel = {
        let label = UILabel()
        label.textAlignment = .center
        label.font = LCDevice.DIN_Font_PF_M(16)
        label.textColor = UIColor("#FFC260")
        return label
    }()
    
    lazy var memberPrice: GradientLabel = {
        let label = GradientLabel()
        label.font = UIFont.systemFont(ofSize: 24, weight: .medium)
        return label
    }()
    
    lazy var memberPrice1: GradientLabel = {
        let label = GradientLabel()
        label.font = UIFont.systemFont(ofSize: 12, weight: .medium)
        label.text = "¥"
        return label
    }()
    
    lazy var memberTime: UILabel = {
        let label = UILabel()
        label.textColor = UIColor("#FFC260")
        label.textAlignment = .center
        label.font = UIFont.systemFont(ofSize: 14, weight: .regular)
        return label
    }()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        
        makeUI()
        business()
    }
    
    convenience init() {
        self.init(frame: .zero)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func makeUI() {
        contentView.backgroundColor = .clear
        backgroundColor = .clear
        
        contentView.addSubviews([background])
        background.addSubviews([memberBackground, memberTitle, memberPrice, memberPrice1, memberTime, cornView, discountsView])
        
        background.snp.makeConstraints { make in
            make.bottom.leading.trailing.top.equalToSuperview()
        }
        
        memberBackground.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        memberTitle.snp.makeConstraints { make in
            make.leading.equalToSuperview().inset(16)
            make.top.equalToSuperview().inset(16)
            make.height.equalTo(26)
        }
       
        
        memberTime.snp.makeConstraints { make in
            make.leading.bottom.equalToSuperview().inset(24)
        }
        
        cornView.snp.makeConstraints { make in
            make.trailing.top.equalToSuperview()
            make.height.equalTo(45)
            make.width.equalTo(46)
        }
        
        discountsView.snp.makeConstraints { make in
            make.trailing.equalToSuperview().inset(82)
            make.bottom.equalToSuperview().inset(22)
            make.height.equalTo(19)
            make.width.equalTo(58)
        }
        
        memberPrice.snp.makeConstraints { make in
            make.leading.equalTo(discountsView.snp.trailing).offset(15)
            make.trailing.equalToSuperview().inset(16)
            make.height.equalTo(30)
            make.bottom.equalToSuperview().inset(16)
        }
        
        memberPrice1.snp.makeConstraints { make in
            make.trailing.equalTo(memberPrice.snp.leading).offset(-2)
            make.bottom.equalTo(memberPrice.snp.bottom).offset(-4)
        }
        
    }
    
    func business() {
        
    }
    
    override func prepareForReuse() {
        super.prepareForReuse()
    }
}

extension LimitMemberCard3 {
    func bind(to model: PurchasingModel) {
        guard let discountPrice = model.discountPrice, let name = model.name, let originalPrice = model.originalPrice else { return }
        let  price = Int(discountPrice)
        updateSelected(selected: model.isSelect ?? false)
        // 1年·零售价¥198
        memberTitle.text = name + "·零售价 ¥" + "\(Int(originalPrice))"
        
        memberPrice.text = "\(price)"
        
        memberTime.text = model.memberLevelStr
        //
        
        if model.pic.count == 1, let url1 = model.pic.first, let url = URL(string: url1) {
            discountsView.setImageWith(url, placeholder: nil)
        }  else if model.pic.count == 2,
                    let url1 = model.pic.first,
                    let url2 = model.pic.last,
                    let Url1 = URL(string: url1),
                    let Url2 = URL(string: url2)  {
            
            discountsView.setImageWith(Url1, placeholder: nil)
            
            cornView.setImageWith(Url2, placeholder: nil)
        }
        
    }
    
    func updateSelected(selected: Bool) {
        background.borderColor = selected ? UIColor("#FFC260") : UIColor.clear
    }
}



class GradientLabel: UILabel {
    var gradientColors: [UIColor] = [
        UIColor("#FFC260"),
        UIColor("#FFDE71"),
        UIColor("#FFC260")
    ]
    
    override func drawText(in rect: CGRect) {
        if let textString = text {
            let textSize = textString.size(withAttributes: [.font: font as Any])
            let textRect = CGRect(x: 0, y: 0, width: textSize.width, height: textSize.height)
            
            // 创建渐变层
            let gradientLayer = CAGradientLayer()
            gradientLayer.frame = textRect
            gradientLayer.colors = gradientColors.map { $0.cgColor }
            gradientLayer.startPoint = CGPoint(x: 0.5, y: 0)
            gradientLayer.endPoint = CGPoint(x: 0.5, y: 1)
            gradientLayer.locations = [0.0, 0.5, 1.0]
            
            // 创建文本mask
            UIGraphicsBeginImageContextWithOptions(textRect.size, false, 0)
            text?.draw(in: textRect, withAttributes: [.font: font as Any])
            let maskImage = UIGraphicsGetImageFromCurrentImageContext()
            UIGraphicsEndImageContext()
            
            let maskLayer = CALayer()
            maskLayer.frame = textRect
            maskLayer.contents = maskImage?.cgImage
            gradientLayer.mask = maskLayer
            
            // 渲染渐变文本
            if let context = UIGraphicsGetCurrentContext() {
                context.saveGState()
                context.translateBy(x: 0, y: (bounds.height - textSize.height)/2)
                gradientLayer.render(in: context)
                context.restoreGState()
            }
        }
    }

}
