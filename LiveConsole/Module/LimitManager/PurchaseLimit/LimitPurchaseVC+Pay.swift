//
//  LimitPurchaseVC+Pay.swift
//  LivePlus
//
//  Created by 郭炜 on 2022/7/6.
//

/**
会员购买逻辑，从OpeningMemberVC搬过来的，这块代码不敢动 直接搬过来用了
 */
import UIKit

extension LimitPurchaseVC2 {
    @objc func openBtnAction() {
        HapticFeedback.Impact.light()
        guard checkButton.isSelected else {
//            checkButton.shake()
            HUD.showFail("请先同意《快瓴中控台会员协议》")
            return
        }
        
        //网络判断
        if !LCDevice.checkNetIsOk() { return }
        
        if self.memberInterfaceType == .noLogin {
            LCTools.shared.valiationLogin(popToRoot: false) { [weak self] in
                // 只更新用户信息  不更新商品
                guard let self = self else { return }
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.4) {
                    if let nav =  self.navigationController {
                        nav.popViewController(animated: false)
                        LCLog.d("返回会员页")
                    }
                       
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.25) {
                        self.updateMemberInterface()
                        self.willGoPay()
                    }
                    
                }
                
            }
            return
        }
        
        willGoPay()
        
    }
    
    func willGoPay() {
        if !LCDevice.isIOS16 {
            LessiOS16BuyAlert.show { [weak self] in
                guard let self = self else { return}
                gotoPay()
            }
            return
        }
        
        guard let user = UserInfo.currentUser() else { return  }
        
        if let vip = user.memberInfoList?.first(where: {$0.level ==  user.vipLevelId}) {
            
            let exT = vip.expiredTime
            
            if LCTools.check90DaysExpire(time: exT) {
                HUD.showFail("您当前会员时长还超过3个月 暂不需要续费哦")
                return
            }
        }
        
        gotoPay()
    }
    
    func gotoPay() {
        MiddleRequestNet.orderLimit { [weak self] m in
            guard let self = self, m == true else {
                return
            }
            
            let message = "\n本产品为虚拟商品，不含人工服务，由于虚拟产品的性质和特征，购买后不支持退货、换货，是否确认购买?"
            // 再次提示用户
            let alert = LLAlertView(title: "温馨提示", message: message, leftShow: ("取消", .defaultColor), rightShow: ("确认", .mainColor)) {
              
            } rightAction: { [ weak self] in
                
                guard let self = self else {
                    return
                }
                
                HUD.showWait()
                HUD.showInfo("订单正在支付中，请等待...", autoClear: false, autoClearTime: 10000)
                var pid: String = ""
                for item in self.purchasingList where item.isSelect! {
                    pid = "\(item.sku)"
                }
                guard !pid.isBlank else {
                    HUD.hideAllHUD()
                    return
                }
                LCTools.savePayLogInfo(message: " 准备付款商品：\(pid)")
                //通过product id 购买商品
                LPPayTool.purchaseProduct(pid, quantity: 1, atomically: false) {[weak self] (result, purModel, phone) in
                    HUD.hideAllHUD()
                    self?.resultParsing(result: result, model: purModel, phone: phone)
                }
            }

            alert.show(with: self)
        }
    }
    
    func showErrorAlert(model: InPurchasingModel) {
        let alert = LLAlertView(title: "温馨提示", message: "\n购买信息同步失败，请保持网络通畅并点击重试。", leftShow: ("取消", .defaultColor), rightShow: ("重试", .mainColor)) {
            InPurchasingModel.saveModel(model: model)
        } rightAction: { [weak self] in
            LCLog.d("---showErrorAlert--重试-")
            HUD.showInfo("订单正在验证，请等待...", autoClear: false, autoClearTime: 10000)
            LPPayTool.shared.validationForServer(paramer: ["code": model.code, "type": model.type, "phone": model.phone], model: model, counts: LCKey.ValidationPayCerCount) { [weak self] result, model, phone in
                HUD.hideAllHUD()
                self?.resultParsing(result: result, model: model, phone: phone)
            }
        }
        alert.show(with: self)
    }
    
    func resultParsing(result: Result<String, InPurchasingError>, model: InPurchasingModel, phone: String) {
        DispatchQueue.main.async {
            if !phone.isEmpty {
                HUD.showSuccess("\(phone)")
            }
           
            switch result {
            case .failure(let error):
                switch error {
                case .getAppPayFail:
                    LCLog.d("-苹果支付失败--")
                case .getAppStoreReceiptURLFail:
                    HUD.showFail("获取凭证失败")
                case .getUserInfoFail:
                    HUD.showFail("获取用户信息失败")
                case .serverValidationFail:
                    self.showErrorAlert(model: model)
                case .alreadlyValidation:
                    LCLog.d("-已经验证过凭证--")
                case .getIllegalProof:
                    HUD.showFail("非法凭证，验证失败！")
                }
            case .success:
               
                MiddleRequestNet.getUserInfoApiData(callback: { [weak self] _ in
                    guard let self = self else { return }
                    if let userInfo = UserInfo.currentUser() {
                        print("购买会员成功：\(userInfo)")
                        self.updateMemberInterface()
                        NotificationCenter.default.post(name: LCKey.noti_PaiedMember, object: nil)
                        UserDefaults.standard.set(false, forKey: UserDefault_expired)
                        GCDServices.delay(0.5) { [weak self] in
                            guard let self = self else { return }
                            if let vipLevel = userInfo.vipLevelId, vipLevel == .svip {
                                self.showVipPopView()
                            }
                        }
                    }
                }, isShowTip: false)
            }
        }
    }
    
    func getData() {
        requesProducts()
    }
    
    func requesProducts() {
        HUD.showWait()
        LCLog.d("--11111------222222")
        MiddleRequestNet.getPurchasingList { [weak self] models in
            guard let self = self else { return }
            HUD.hideAllHUD()
            self.retryLocalOrder()

            if models.count >= 1 {
                var tempModels: [PurchasingModel] = []
                for item in models {
                    var model = item
                    model.isSelect = false
                    if let defaultPro = model.defaultPro, defaultPro {
                        model.isSelect = true
                    }
                    tempModels.append(model)
                }
                if !tempModels.contains(where: { $0.isSelect == true }) {
                    if let first = tempModels.first {
                        var new = first
                        new.isSelect = true
                        tempModels[0] = new
                    }
                }
                self.setPurchasingListData(models: tempModels)
            }
        }
    }
    
    func retryLocalOrder() {
        guard let userInfo = UserInfo.currentUser(), let mphone = userInfo.phone else {
            return
        }

        InPurchasingModel.queryInPurchasingModels(phone: mphone) { array in
            guard let models = array else { return }
            LCLog.d("---getData--本地获取支付凭证去服务端验证---")
            HUD.showInfo("有一笔订单正在验证，请等待...", autoClear: false, autoClearTime: 10000)
            
            let group = DispatchGroup()
            let tasksQueue = DispatchQueue(label: "PurchasingQueue", qos: .default, attributes: .concurrent, autoreleaseFrequency: .inherit, target: nil)
            
            for (index, mod) in models.enumerated() {
                group.enter()
                LCLog.d("---本地获取支付凭证去服务端验证---")
                let phone = mod.phone
                let code = mod.code
                let type = mod.type
                tasksQueue.async(group: group, qos: .default, flags: []) {
                    MiddleRequestNet.purchasingValidation(param: ["code": code, "type": type, "phone": phone]) { (code, phone) in
                        print("---code--:\(code)")
                        if code == 0 {
                            InPurchasingModel.deleteInPurchasingModelsPhone(phones: [phone])
                        }
                        group.leave()
                    }
                }
            }
            group.notify(queue: DispatchQueue.main, execute: {
                HUD.hideAllHUD()
            })
        }
    }
    
    func setPurchasingListData(models: [PurchasingModel] ) {
        self.purchasingList = models
           
        self.memberCollection.reloadData()
        
        if let m = models.first(where: {$0.isSelect == true}) {
            self.welfareLab.text = m.remark
        }
    }
}

extension LimitPurchaseVC2 {
    func showVipPopView() {
        guard let resource = ConfigModel.currentUserConfig()?.dynamicResource.first(where: { $0.key == "vip_buy_dialog" }) else { return }
        if let action = resource.action, action == "3" { // 购买成功后显示 3
//            showPop(view: vipView)
            HUD.showSuccess("已购买成功")
        }
    }
}

extension LimitPurchaseVC2: VipServiceViewDelegate {
    func dismiss() {
        dismissPop(view: vipView)
    }
    
    func save(image: UIImage) {
        saveImage(image: image)
    }
    
    func saveImage(image: UIImage) {
        LCDevice.isCanVisitPhotoLibrary { [weak self] result in
            guard let self = self, result else { HUD.showFail("请前往系统设置，打开快瓴中控台图片访问权限"); return }
            UIImageWriteToSavedPhotosAlbum(image, self, #selector(self.saveImage(image:didFinishSavingWithError:contextInfo:)), nil)
        }
    }
    /// 保存
    @objc private func saveImage(image: UIImage, didFinishSavingWithError error: NSError?, contextInfo: AnyObject) {
        guard error == nil else {
            HUD.showFail("保存失败")
            return
        }
        HUD.showSuccess("保存成功")
        dismiss()
    }
}
