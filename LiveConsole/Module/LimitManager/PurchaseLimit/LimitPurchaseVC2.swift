//
//  LimitPurchaseVC2.swift
//  LivePlus
//
//  Created by 郭炜 on 2022/8/4.
//

import UIKit
import YYKit
import SnapKit
import Kingfisher
import RxSwift
import RxCocoa

/// 进入会员购买的类型
enum MemberInterfaceType {
    case member     // 会员中心：立即开通 or 立即续费
    case limit      // 会员or免费：立即开通/使用免费功能
    case noLogin // 未登录
    case trialVip // 试用会员
}


class LimitPurchaseVC2: BaseVC, Routable {
    
    static func initRouteParams(params: [String: Any]?) -> UIViewController { LimitPurchaseVC2() }
    
    lazy var backButton: UIButton = {
        let button = UIButton()
        button.setImage(UIImage(named: "icon_nav_back_white"), for: .normal)
        return button
    }()
    
    lazy var contentView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        return view
    }()
    
    lazy var headView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        return view
    }()
    
    lazy var memberView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        return view
    }()
    
    /// 头像
    lazy var headImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.cornerRadius = 28
        imageView.image = UIImage(named: "icon_user_headerDefault")
        imageView.isUserInteractionEnabled = true
        imageView.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(loginAction(tap:))))
        return imageView
    }()
    
    /// 名字
    lazy var nameLabel: UILabel = {
        let label = UILabel()
        label.textColor = .white
        label.font = LCDevice.DIN_Font_PF_M(16)
        return label
    }()
    
    /// vip标识
    lazy var vipIcon: UIImageView = {
        let imageView = UIImageView()
        return imageView
    }()
    
    /// vip提示
    lazy var vipTitle1: UILabel = {
        let label = UILabel()
        label.textColor = UIColor("#999999")
        label.font = LCDevice.DIN_Font_PF_R(12)
        return label
    }()
    
    /// 未登录提示
    lazy var noLoginTitle: UILabel = {
        let label = UILabel()
        label.textColor = .white
        label.font = LCDevice.DIN_Font_PF_M(16)
        label.isHidden = true
        label.text = "点击头像登录"
        return label
    }()
    
    /// 未登录提示
    lazy var desTitle: UILabel = {
        let label = UILabel()
        label.textColor = .white
        label.font = LCDevice.DIN_Font_PF_M(15)
        label.text = "选择套餐"
        label.isHidden = true
        return label
    }()
    
    /// 详细介绍按钮
    lazy var detailButton: UIButton = {
        let button = UIButton { Router.willOpenMemberDetail() }
        button.setTitle("会员权益明细", for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 12, weight: .regular)
        button.setBackgroundImage(UIImage(named: "vip_background"), for: .normal)
        button.isHidden = true
        return button
    }()
    
    
    lazy var twoLayout: UICollectionViewLayout = {
        let layout = UICollectionViewFlowLayout()
        layout.itemSize = CGSize(width: LCDevice.screenW - 24 * 2, height: 94)
        layout.minimumLineSpacing = 22
        layout.minimumInteritemSpacing = 22
        layout.sectionInset = UIEdgeInsets(top: 20, left: 19, bottom: 20, right: 19)
        layout.scrollDirection = .vertical
        return layout
    }()
        
    lazy var memberCollection: UICollectionView = {
        let collectionView = UICollectionView(frame: CGRect.zero, collectionViewLayout: self.twoLayout)
        collectionView.backgroundColor = UIColor.clear
        collectionView.register(cellWithClass: LimitMemberCard3.self)
        collectionView.delegate = self
        collectionView.dataSource = self
        collectionView.delaysContentTouches = false // 防止选中状态很快消失
        collectionView.showsHorizontalScrollIndicator = false
        return collectionView
    }()

    lazy var welfareLab: UILabel = {
        let label = UILabel()
        label.textColor = UIColor.white
        label.font = LCDevice.DIN_Font_PF_R(12)
        label.numberOfLines = 0
        return label
    }()
    
    lazy var unlockButton: UIButton = {
        let button = UIButton()
        button.setTitle("立即续费", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.titleLabel?.font = LCDevice.DIN_Font_PF_M(16)
        button.cornerRadius = 23
        button.addTarget(self, action: #selector(openBtnAction), for: .touchUpInside)
        button.backgroundColor = UIColor("#6974F2")
        button.applyGradient()
        return button
    }()
    
    lazy var purchaseProtocol: YYLabel = {
        let label = YYLabel()
        label.text = "本品为虚拟产品，不含人工服务，一旦激活后不支持退款，点击同意《快瓴中控台会员协议》"
        label.numberOfLines = 0
        return label
    }()
  
    
    /// 选中协议
    lazy var checkButton: UIButton = {
        let button = UIButton { [weak self] in
            guard let self = self else { return }
            self.checkButton.isSelected = !self.checkButton.isSelected
        }
        button.setImage(UIImage(named: "登录流程_组件_cell_勾选_nor"), for: .normal)
        button.setImage(UIImage(named: "icon_勾选_sel"), for: .selected)
        
        button.zl_enlargeValidTouchArea(insets: UIEdgeInsets(top: 10, left: 30, bottom: 12, right: 20))
        return button
    }()
    
    /// 会员中心的背景图片
    lazy var memberBackImage: UIImageView = {
        let imageView = UIImageView(image: UIImage(named: "vip_back"))
        imageView.backgroundColor = .clear
        imageView.contentMode = .scaleAspectFill
        imageView.isUserInteractionEnabled = true
        imageView.layer.masksToBounds = true
        return imageView
    }()
    
    /// 会员的数据源
    var purchasingList: [PurchasingModel] = []
    
    /// 进入会员界面的类型
    var memberInterfaceType: MemberInterfaceType {
        guard let _ = UserInfo.currentUser() else {
            return .noLogin
        }
        
        if UserInfo.isMember {
            return .member
        }
        
        if UserInfo.isTrialVip {
            return .trialVip
        }
        
        return .limit
        
    }
        
    lazy var vipView: VipServiceView = {
        let view = VipServiceView()
        view.frame = CGRect(x: 0, y: 0, width: 310, height: 450)
        view.delegate = self
        return view
    }()
    
    
    override func viewDidLoad() {
        super.viewDidLoad()
        makeUI()
        business()

    }
    
    deinit {
       
        NotificationCenter.default.removeObserver(self)
    }
    
    
    func makeUI() {
        view.backgroundColor = UIColor("#191C20")
        view.addSubview(contentView)
        
        contentView.addSubviews([headView, memberBackImage, memberView, backButton])
        
        headView.addSubviews([headImageView, nameLabel, vipIcon, vipTitle1])
        
        memberView.addSubviews([desTitle, detailButton, memberCollection, welfareLab, unlockButton, purchaseProtocol, checkButton, noLoginTitle])
                
        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        memberBackImage.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.height.equalTo(330)
            make.leading.trailing.equalToSuperview()
        }
        
        headView.snp.makeConstraints { make in
            make.top.equalTo(memberBackImage.snp.bottom).offset(24)
            make.leading.trailing.equalToSuperview()
            make.height.equalTo(90)
        }
        
       
        if !LCDevice.deviceIsIPad() {
            memberView.snp.makeConstraints { make in
                make.bottom.leading.trailing.equalToSuperview()
                make.top.equalTo(headView.snp.bottom)
            }
        } else {
            memberView.snp.makeConstraints { make in
                make.bottom.leading.trailing.equalToSuperview()
                make.top.equalTo(headView.snp.bottom)
            }
        }
        
        headImageView.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.leading.equalToSuperview().inset(20)
            make.width.height.equalTo(58)
        }
        
        nameLabel.snp.makeConstraints { make in
            make.leading.equalTo(headImageView.snp.trailing).offset(15)
            make.top.equalToSuperview().inset(4)
            make.height.equalTo(24)
        }
        
        vipTitle1.snp.makeConstraints { make in
            make.leading.equalTo(headImageView.snp.trailing).offset(15)
            make.top.equalTo(nameLabel.snp.bottom).offset(8)
            make.height.equalTo(16)
        }
        
        noLoginTitle.snp.makeConstraints { make in
            make.leading.equalTo(headImageView.snp.trailing).offset(15)
            make.centerY.equalTo(headImageView.snp.centerY)
            make.height.equalTo(24)
        }
        
        vipIcon.snp.makeConstraints { make in
            make.leading.equalTo(nameLabel.snp.trailing).offset(10)
            make.centerY.equalTo(nameLabel.snp.centerY)
            make.width.equalTo(29)
            make.height.equalTo(15)
        }
        
        detailButton.snp.makeConstraints { make in
            make.trailing.equalToSuperview().inset(12)
            make.top.equalToSuperview().inset(20)
            make.width.equalTo(112)
            make.height.equalTo(28)
        }

        desTitle.snp.makeConstraints { make in
            make.leading.equalToSuperview().inset(18)
            make.top.equalToSuperview().inset(20)
            make.width.equalTo(112)
            make.height.equalTo(28)
        }
        
        purchaseProtocol.snp.makeConstraints { make in
            make.bottom.equalToSuperview().inset(LCDevice.X_BOTTOM_INSET + 8)
            make.leading.equalToSuperview().inset(80)
            make.trailing.equalToSuperview().inset(16)
            make.height.equalTo(40)
        }
        
        
        unlockButton.snp.makeConstraints { make in
            make.bottom.equalTo(purchaseProtocol.snp.top).offset(-30)
            make.leading.trailing.equalToSuperview().inset(25)
            make.height.equalTo(46)
        }
        
       
        checkButton.snp.makeConstraints { make in
            make.trailing.equalTo(purchaseProtocol.snp.leading).offset(-20)
            make.centerY.equalTo(purchaseProtocol.snp.centerY)
            make.width.height.equalTo(20)
        }
        
       
        backButton.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(LCDevice.Nav_H - 45)
            make.leading.equalToSuperview().inset(10)
            make.width.height.equalTo(40)
        }
        
        memberCollection.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(24)
            make.bottom.equalToSuperview().inset(200)
            make.leading.trailing.equalToSuperview()
        }
        
        welfareLab.snp.makeConstraints { make in
            make.top.equalTo(memberCollection.snp.bottom)
            make.leading.trailing.equalToSuperview().inset(25)
        }
        
        deployProtocol()
    }
    
    func business() {
        backButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self else { return }
                self.navigationController?.popViewController()
            }.disposed(by: disposeBag)
        
        getData()
        updateMemberInterface()
    }
        
    
    @objc func loginAction(tap: UITapGestureRecognizer) {
        if let user = UserInfo.currentUser() {
            return
        }
        
        LCTools.shared.valiationLogin(popToRoot: false) { [weak self] in
            // 只更新用户信息  不更新商品
            guard let self = self else { return }
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.4) {
                self.navigationController?.popViewController(animated: false)
                self.updateMemberInterface()
            }
        }
        
    }
}

extension LimitPurchaseVC2: UICollectionViewDelegate, UICollectionViewDataSource, UICollectionViewDelegateFlowLayout {
    
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return purchasingList.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        // 内购项目
        let cell = collectionView.dequeueReusableCell(withClass: LimitMemberCard3.self, for: indexPath)
        cell.bind(to: purchasingList[indexPath.row])
        return cell
       
    }
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        HapticFeedback.Impact.light()
        /// 抄的openingmembervc的代码 这里其实用class处理就好了 不敢改 就这样吧
        if collectionView.isEqual(memberCollection) {
            var mds: [PurchasingModel] = []
            for (idx, var item) in purchasingList.enumerated() {
                if idx == indexPath.row {
                    item.isSelect = true
                } else {
                    item.isSelect = false
                }
                mds.append(item)
            }
            self.purchasingList = mds
            collectionView.reloadData()
            if let m = mds.first(where: {$0.isSelect == true}) {
                self.welfareLab.text = m.remark
            }
            
        }
    }
}

/// 更新会员界面
extension LimitPurchaseVC2 {
    func updateMemberInterface() {
        switch memberInterfaceType {
        case .member: // 根据是否会员 刷新页面
            UserInfo.isMember ? updateInterfaceOldMember() : updateInterfaceMember()
        case .limit: updateInterfaceLimit()
        case .noLogin: updateNoLoginInterfaceMember()
        case .trialVip: updateTrialVip()
        }
        updateUser()
    }
    
    /// 更新用户信息
    func updateUser() {
        if let userInfo = UserInfo.currentUser(), let userName = userInfo.nickName{
            vipIcon.image = UserInfo.isMember ? UIImage(named: "vip_vip") : UIImage(named: "vip_novip")
            nameLabel.text = userName
            if let headImage = userInfo.headImgUrl, let headUrl = URL(string: headImage) {
                headImageView.setImageWith(headUrl, placeholder: UIImage(named: "icon_user_headerDefault"))
            }
        }
    }
    
    /// 开通会员页面
    func updateInterfaceMember() {
        noLoginTitle.isHidden = true
        vipTitle1.text = "您当前是免费用户"
        vipTitle1.isHidden = false

        unlockButton.setTitle("立即开通", for: .normal)
        onlyOneInterface()
        
    }
    
    /// 未登录
    func updateNoLoginInterfaceMember() {
        vipTitle1.isHidden = true

        noLoginTitle.isHidden = false
        unlockButton.setTitle("立即开通", for: .normal)
        onlyOneInterface()
        
        
    }
    
    /// 老会员页面
    func updateInterfaceOldMember() {
        
        if  let userInfo = UserInfo.currentUser(),let vip = userInfo.memberInfoList?.first(where: {$0.level ==  userInfo.vipLevelId}) {
            
            let exT = vip.expiredTime
            let name = vip.level.des
            vipTitle1.text = "\(name)\(LCTools.covertDateString(timeString: exT))到期"
        }
       
        unlockButton.setTitle("立即续费", for: .normal)
        onlyOneInterface()
        noLoginTitle.isHidden = true
        vipTitle1.isHidden = false
        
    }
    
    func updateTrialVip() {
        if UserInfo.isTrialVip, let userInfo = UserInfo.currentUser(), let expireTime = userInfo.trialExpireTime {
            vipTitle1.text = "智能场控免费会员权益\(LCTools.covertDateString(timeString: expireTime))到期"
        }
        unlockButton.setTitle("立即开通", for: .normal)
        onlyOneInterface()
        noLoginTitle.isHidden = true
        vipTitle1.isHidden = false
        
    }
    
    /// 免费版本页面
    func updateInterfaceLimit() {
        vipTitle1.text = "您当前是免费用户"
        vipTitle1.isHidden = false

        noLoginTitle.isHidden = true
        unlockButton.setTitle("立即开通", for: .normal)
       
    }
    
    func onlyOneInterface() {
       
    }
}

extension LimitPurchaseVC2 {
    func deployProtocol() {
        let infoString = "本品为虚拟产品，不含人工服务，一旦激活后不支持退款，点击同意《快瓴中控台会员协议》"
        let agreementFS = "《快瓴中控台会员协议》"
        let agreementRange = infoString.range(of: agreementFS)!
        let agreementLinkRange = NSRange(agreementRange, in: infoString)
                
        let attString = NSMutableAttributedString.init(string: infoString)
        attString.font = LCDevice.DIN_Font_PF_R(12)
        attString.alignment = .left
        attString.color = UIColor("#FFFFFF")
        attString.setTextHighlight(agreementLinkRange, color: UIColor("#FFFFFF"), backgroundColor: nil) { (_ containerView, _ attText, _ range, _ rect) in
            Router.openMembershipAgreement()
        }
        purchaseProtocol.attributedText = attString
    }
}
