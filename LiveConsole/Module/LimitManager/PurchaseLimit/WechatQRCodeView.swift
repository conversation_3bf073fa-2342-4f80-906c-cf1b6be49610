//
//  WechatQRCodeView.swift
//  LivePlus
//
//  Created by 郭炜 on 2022/7/7.
//

import UIKit
import RxSwift

protocol WechatQRCodeViewDelegate: NSObjectProtocol {
    func noticeAction()
}

class WechatQRCodeView: UIView {
    
    weak var delegate: WechatQRCodeViewDelegate?
    
    lazy var qrImageView: UIImageView = {
        let imageView = UIImageView(image: UIImage(named: "non_wechat"))
        return imageView
    }()
    
    lazy var noticeButton: UIButton = {
        let button = UIButton()
        button.backgroundColor = .white
        button.cornerRadius = 20
        button.setTitle("我已关注", for: .normal)
        button.setTitleColor(UIColor("#5044E8"), for: .normal)
        button.titleLabel?.font = LCDevice.DIN_Font_PF_R(17)
        return button
    }()
    
    let disposeBag = DisposeBag()

    override init(frame: CGRect) {
        super.init(frame: frame)
        
        makeUI()
        business()
    }

    convenience init() {
        self.init(frame: .zero)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    func makeUI() {
        addSubviews([qrImageView, noticeButton])
        qrImageView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        noticeButton.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview().inset(24)
            make.bottom.equalToSuperview().inset(25)
            make.height.equalTo(40)
        }
    }
    
    func business() {
        noticeButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self else { return }
                if let delegate = self.delegate { delegate.noticeAction() }
            }.disposed(by: disposeBag)
    }
}
