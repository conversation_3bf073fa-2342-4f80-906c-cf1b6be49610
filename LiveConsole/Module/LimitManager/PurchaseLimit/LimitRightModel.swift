//
//  LimitMemberCard.swift
//  LivePlus
//
//  Created by 郭炜 on 2022/7/6.
//

import Foundation

enum LimitRightModel: CaseIterable {
    case greenSeg
    case beauty
    case record1080
    case greenSource
    case layerSwitch
    case teach
    case crop
    case layerCount
    
    var imageName: String {
        switch self {
        case .greenSeg:
            return "pay_greenseg"
        case .beauty:
            return "pay_beauty"
        case .record1080:
            return "pay_1080"
        case .greenSource:
            return "pay_green"
        case .layerSwitch:
            return "pay_layer"
        case .teach:
            return "pay_teach"
        case .crop:
            return "pay_crop"
        case .layerCount:
            return "pay_layercount"
        }
    }
}
