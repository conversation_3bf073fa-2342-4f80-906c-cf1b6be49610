//
//  LimitModel.swift
//  LivePlus
//
//  Created by 郭炜 on 2022/6/15.
//

/**
 会员权益模型
 */
import Foundation

let KUserDefault_limit = "KUserDefault_limit"


class LimitManager: NSObject {
    
    /// 减少外接代码长度 业务上调这个即可 LimitManager.limits.xxxx
    static var limits: LimitModel {
        get {
            /// 1、如果接口拉取成功， 就使用接口获取的数据
            if let model = LimitManager.manager.limitModel {
                return model
            }
            /// 2、接口拉取失败，使用本地的
            if let local = LimitModel.localModel() {
                return local
            }
            /// 3、如果都获取不到，那就使用默认的
            return LimitModel(vipFunction: VipFunction(), functionNum: FunctionNum(), maxLimit: MaxLimit())
        }
        set {
            LimitManager.manager.limitModel = newValue
        }
    }

    private static let manager = LimitManager()
    
    var limitModel: LimitModel?
    
}

struct LimitModel: Codable {
    var vipFunction: VipFunction
    var functionNum: FunctionNum
    var maxLimit: MaxLimit
}

extension LimitModel {
    func saveLocal() {
        let json = JsonTool.model2String(self)
        if json.count > 0 {
            UserDefaults.standard.set(json, forKey: KUserDefault_limit)
            UserDefaults.standard.synchronize()
        }
    }
    
    static func localModel() -> LimitModel? {
        guard let json = UserDefaults.standard.string(forKey: KUserDefault_limit) else {
            return nil
        }
        let limit: LimitModel = JsonTool.string2Model(json)!
        return limit
    }
}

/**
 * 功能是否被限制，true表示被限制，false表示不限制
 * true 显示标识、false 不显示
 */
struct VipFunction : Codable {
    /****探索版特有会员************************************/
    var aiReplyControl: Bool = true// 是否控制智能场控
    var aiInteractive: Bool = true // 智能互动功能 例如智能感谢观众来访、关注、点赞、加粉丝团、送礼等
    var customTemplate: Bool = true // 自定义模板
    var aiLike: Bool = true // 智能点赞功能
    var remoteEditSettings: Bool = true // 遥控修改智能互动设置
    var viewScreenInfo: Bool = true // 遥控实时查看公屏信息
    var backupCopy: Bool = true // 备份和恢复
    var keywordReply: Bool = true // 关键词回复
    var shareTemplate: Bool = true // 分享
    var recoverTemplate: Bool = true // 导入
}

/// !!!!!!!!这里的默认值不要去掉，为了方便不用属性包装器和可选解包 直接用的强解包
/// 默认值含义为：如果接口返回为空（表示不限制此项，所以用最大数量去限制）
struct FunctionNum: Codable {

    
}


struct MaxLimit: Codable {
   
}
