//
//  PrivacySettingVC.swift
//  LivePlus
//
//  Created by simon on 17.2.22.
//

import Foundation
import RxSwift

fileprivate let kPrivacySettingCellID = "PrivacySettingVC"

//enum PrivacySettingItemsType: String {
//    case privacyPolicy = "隐私协议"
//    case userInfoManager = "个人信息查询与管理"
//}

/// 设置页
class PrivacySettingVC: BaseVC {
    
    //列表区域
    var itemsTitleArr: [ItemsType] = [ .privacyPolicy, .userInfoManager, .authorization]
    let myTable = UITableView(frame: CGRect(x: 0, y: LCDevice.Nav_H, width: LCDevice.screenW, height: LCDevice.screenH-LCDevice.Nav_H), style: .grouped)
    
    override func viewDidLoad() {
        super.viewDidLoad()
        // 如果是内测版本 移除绑定微信选项
        // Do any additional setup after loading the view.
        view.backgroundColor = UIColor("#111111")
        let nav = getNavViewWithItem(titleStr: "隐私管理", leftImageName: "icon_nav_back_white", rightImageName: nil, color: UIColor("#8E72F2"))

        nav.backgroundColor =  UIColor("#111111")
        view.addSubview(nav)
        
        if let _ = UserInfo.currentUser() {
            itemsTitleArr = [ .privacyPolicy, .userInfoManager, .authorization]
        } else {
            itemsTitleArr = [ .privacyPolicy, .authorization]
        }
        
        addTableView()
        
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        myTable.reloadData()
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }

    // MARK: - UI
    func addTableView() {
        myTable.backgroundColor = .clear//UIColor("#F0F1F7")
        myTable.dataSource = self
        myTable.delegate = self
        myTable.separatorStyle = .none
        myTable.register(SettingCell.self, forCellReuseIdentifier: kPrivacySettingCellID)
        view.addSubview(myTable)
    }
}

extension PrivacySettingVC: UITableViewDelegate {
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        let itemType = itemsTitleArr[indexPath.row]
        switch itemType {
            
        case .privacyPolicy:
            //隐私政策
            Router.openPrivacyAgreement()
            
        case .userInfoManager:
            Router.openInformationList()
            
        case .authorization:
            push(to: InformationListVC())
        default :
            break
        }
    }
}

extension PrivacySettingVC: UITableViewDataSource {
            
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return LCDevice.DIN_WIDTH(52)
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return itemsTitleArr.count
    }
    
    func tableView(_ tableView: UITableView, heightForHeaderInSection section: Int) -> CGFloat {
        return 1
    }
    
    func tableView(_ tableView: UITableView, viewForHeaderInSection section: Int) -> UIView? {
        return UIView(frame: CGRect(x: 0, y: 0, width: LCDevice.screenW, height: 1))
    }
    
    func tableView(_ tableView: UITableView, heightForFooterInSection section: Int) -> CGFloat {
        if let _ = UserInfo.currentUser() {
            return LCDevice.DIN_WIDTH(12+52)
        }
        return LCDevice.DIN_WIDTH(12)
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell: SettingCell = tableView.dequeueReusableCell(withIdentifier: kPrivacySettingCellID, for: indexPath) as! SettingCell
        let itemType = itemsTitleArr[indexPath.row]
       
        cell.config(with: itemType.rawValue, isShowArrow: true)
            
        if indexPath.row == 0 {
            cell.setRadiusForType(with: .UpRadius)
        } else if indexPath.row == itemsTitleArr.count - 1 {
            cell.setRadiusForType(with: .DownRadius)
        } else {
            cell.setRadiusForType(with: .Normal)
        }
        
        return cell
    }
}
