//
//  PhoneBindDoneVC.swift
//  LivePlus
//
//  Created by iclick on 2020/12/25.
//

import UIKit

/// 手机号绑定成功
class PhoneBindDoneVC: BaseVC {

    override func viewDidLoad() {
        super.viewDidLoad()

        // Do any additional setup after loading the view.
        view.addSubview(getNavViewWithItem(titleStr: "手机号修改", leftImageName: "icon_nav_back_gray", rightImageName: nil))
        //icon_setting_bindSure
        let bgView = UIImageView(frame: CGRect(x: 0, y: LCDevice.DIN_WIDTH(122), width: LCDevice.DIN_WIDTH(157), height: LCDevice.DIN_WIDTH(156)))
        bgView.centerX = view.centerX
        bgView.image = UIImage(named: "icon_setting_bindSure")
        view.addSubview(bgView)
        
        let infoLab = UILabel(frame: CGRect(x: 0, y: bgView.bottom+LCDevice.DIN_WIDTH(9), width: LCDevice.screenW, height: 30))
        infoLab.text = "恭喜您，绑定成功"
        infoLab.textAlignment = .center
        infoLab.textColor = UIColor("#1E1F20")
        infoLab.font = LCDevice.DIN_Font_PF_M(18)
        view.addSubview(infoLab)
        
        
        //返回按钮
        let backBtn = UIButton()
        backBtn.frame = CGRect(x: LCDevice.DIN_WIDTH(18), y: infoLab.bottom + LCDevice.DIN_WIDTH(20), width: LCDevice.DIN_WIDTH(339), height: LCDevice.DIN_WIDTH(86))
        backBtn.setBackgroundImage(UIImage(named: "icon_login_btnBg"), for: .normal)
        backBtn.setTitle("返回设置", for: .normal)
        backBtn.titleLabel?.font = LCDevice.DIN_Font_PF_S(16)
        backBtn.setTitleColor(UIColor("#FFFFFF"), for: .normal)
        backBtn.addTarget(self, action: #selector(backBtnBtnAction), for: .touchUpInside)
        view.addSubview(backBtn)
    }
    
    @objc func backBtnBtnAction() {
        if let navCtrl = navigationController {
            let ctrls = navCtrl.viewControllers
            if ctrls.count>1, ctrls[1].isKind(of: SettingVC.self){
                navCtrl.popToViewController(ctrls[1], animated: true)
            }
        }
    }

    /*
    // MARK: - Navigation

    // In a storyboard-based application, you will often want to do a little preparation before navigation
    override func prepare(for segue: UIStoryboardSegue, sender: Any?) {
        // Get the new view controller using segue.destination.
        // Pass the selected object to the new view controller.
    }
    */

}
