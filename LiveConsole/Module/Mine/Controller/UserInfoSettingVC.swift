//
//  UserInfoSettingVC.swift
//  LivePlus
//
//  Created by simon on 17.2.22.
//

import Foundation
import RxSwift


fileprivate let kUserInfoSettingVCCellID = "UserInfoSettingVC"

enum UserInfoSettingItemsType: String {
    case bindPhone = "绑定手机号"
    case bindWechat = "绑定微信"
    case userProtocol = "用户协议"
    case privacyPolicy = "隐私管理"
    case accoutSafe = "账号与安全"
    case versionUpdate = "版本更新"
}

/// 设置页
class UserInfoSettingVC: BaseVC, WXApiDelegate {

    //列表区域
    var itemsTitleArr: [ItemsType] = [.bindPhone, .bindWechat]
    let myTable = UITableView(frame: CGRect(x: 0, y: LCDevice.Nav_H, width: LCDevice.screenW, height: LCDevice.screenH-LCDevice.Nav_H), style: .grouped)
    
    override func viewDidLoad() {
        super.viewDidLoad()
        // Do any additional setup after loading the view.
        view.backgroundColor = UIColor("#111111")
       
        let nav = getNavViewWithItem(titleStr: "个人信息", leftImageName: "icon_nav_back_white", rightImageName: nil, color: UIColor("#8E72F2"))
        nav.backgroundColor = UIColor("#111111")
        view.addSubview(nav)
        
        
        addTableView()
        
        NotificationCenter.default.addObserver(self, selector: #selector(didGetWeiXinResp(_:)), name: LCKey.noti_didGetWeiXinResponse, object: nil)
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        myTable.reloadData()
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }

    // MARK: - UI
    func addTableView() {
        myTable.backgroundColor = .clear//UIColor("#F0F1F7")
        myTable.dataSource = self
        myTable.delegate = self
        myTable.separatorStyle = .none
        myTable.register(SettingCell.self, forCellReuseIdentifier: kUserInfoSettingVCCellID)
        view.addSubview(myTable)
    }
    
    // MARK: - API
    
    ///绑定微信
    func postWeiXinBind() {
        if WXApi.isWXAppInstalled() {
            let req = SendAuthReq()
            req.scope = "snsapi_userinfo"
            req.state = "App"
            WXApi.sendAuthReq(req, viewController: self, delegate: self) { (_) in }
            UserDefaults.standard.setValue(true, forKey: LCKey.UD_wxForBind)
            UserDefaults.standard.setValue(false, forKey: LCKey.UD_wxForLogin)
        } else {
            HUD.showInfo("微信未安装")
        }
    }
    // MARK: - WXApiDelegate
    func onReq(_ req: BaseReq) {
        print("xxx")
    }
    @objc func didGetWeiXinResp(_ sender: Notification) {
        if !UserDefaults.standard.bool(forKey: LCKey.UD_wxForBind) {
            return
        }
        UserDefaults.standard.setValue(false, forKey: LCKey.UD_wxForBind)
        let data = sender.object as! SendAuthResp
        if let code = data.code {
            self.realHandleWxResp(code)
        }
    }
    
    @objc func realHandleWxResp(_ code: String) {
        let param = ["code": code]
        HUD.showWait()
        var req: Observable<Status<BaseRespModel<LoginRespData>>>
        
        if let userInfo = UserInfo.currentUser(), let bindWechat = userInfo.bindWechat, bindWechat {
            // 如果绑定过微信 说明是换绑
            req = APISession.post(APIURL.POST_Weixin_REBind, parameters: param) .asObservable()
        } else {
            // 没有绑定微信 说明是绑定
            req = APISession.post(APIURL.POST_WeiXin_BIND, parameters: param) .asObservable()
        }
        req.subscribe(onNext: {[weak self] status in
            HUD.hideAllHUD()
            switch status {
            case .success(let res):
                if res.code == 0 {
                    //判断新返回的token，为空时不覆盖当前的
                    var curToken: String?
                    if let userInfo = UserInfo.currentUser() {
                        curToken = userInfo.token
                    }
                    if var user = res.data {
                        if let newToken = user.token, !newToken.isEmpty {
                        } else {
                            user.token = curToken
                        }
                        user.saveLoginUser()

                        DispatchQueue.main.async {
                            self?.myTable.reloadData()
                        }
                    }
                } else {
                    if let mesg = res.msg {
                        HUD.showFail(mesg)
                    } else {
                        HUD.showFail("操作失败")
                    }
                }
            case .failure(let error):
                HUD.showFail(error.localizedDescription)
            case .pending: break
            }
        }).disposed(by: disposeBag)
    }
    
    ///解绑微信
    func getUnbindWeiXin() {
        HUD.showWait()
        let req: Observable<Status<BaseRespModel<LoginRespData>>> = APISession.get(APIURL.GET_WeiXin_UNBIND).asObservable()
        req.subscribe(onNext: {[weak self] status in
            HUD.hideAllHUD()
            switch status {
            case .success(let res):
                //判断新返回的token，为空时不覆盖当前的
                if res.code == 0 {
                    var curToken: String?
                    if let userInfo = UserInfo.currentUser(){
                        curToken = userInfo.token
                    }
                    if var user = res.data {
                        if let newToken = user.token,!newToken.isEmpty {
                        } else {
                            user.token = curToken
                        }
                        user.saveLoginUser()

                        NotificationCenter.default.post(name: LCKey.noti_reLogin, object: nil)
                        HUD.showInfo("微信解绑成功")
                        DispatchQueue.main.async {
                            self?.myTable.reloadData()
                        }
                    }
                } else {
                    if let mesg = res.msg {
                        HUD.showFail(mesg)
                    } else {
                        HUD.showFail("微信解绑失败")
                    }
                }
            case .failure(let error):
                HUD.showFail(error.localizedDescription)
            case .pending: break
            }
        }).disposed(by: disposeBag)
    }
}

extension UserInfoSettingVC: UITableViewDelegate {
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        let itemType = itemsTitleArr[indexPath.row]
        switch itemType {
        case .bindPhone: //绑定手机
            if let user = UserInfo.currentUser() {
                if let _ = user.phone {
                    //有手机号，换手机号
                    let alert = UIAlertController.init(title: "温馨提示", message: "确定修改手机号?", preferredStyle: .alert)
                    alert.addAction(UIAlertAction(title: "取消", style: .cancel, handler: nil))
                    let ok = UIAlertAction(title: "确定", style: .default) { (_) in
                        let ctrl = PhoneBindVC()
                        ctrl.bindType = .change
                        ctrl.needBackAfterDone = true
                        ctrl.bindSuccessBlock = {
                            if ctrl.needBackAfterDone {
                                ctrl.leftButtonAction()
                            }
                        }
                        self.push(to: ctrl)
                    }
                    alert.addAction(ok)
                    present(alert, animated: true, completion: nil)
                } else {
                    //有用户信息，但无手机号 （微信登录了，但没绑定手机）,去绑定手机
                    let ctrl = PhoneBindVC()
                    ctrl.bindType = .bind
                    ctrl.needBackAfterDone = true
                    ctrl.bindSuccessBlock = {
                        if ctrl.needBackAfterDone {
                            ctrl.leftButtonAction()
                        }
                    }
                    push(to: ctrl)
                }
            } else {
                /* 无用户信息，就是未登录，直接跳登录*/
                let loginCtrl = MainLoginVC()
                loginCtrl.needAutoBackAfterDone = true
                loginCtrl.loginSuccessBlock = {
                    //
                }
                push(to: loginCtrl)
            }
            
        case .bindWechat://绑定微信
            if let user = UserInfo.currentUser() {
                if let _ = user.phone {//有手机号,至少手机号登录过
                    //微信登录的，手机号+微信
                    if let bindWx = user.bindWechat, bindWx {
//                    if let sex = user.sex, sex != 3 {
                        //提示解绑
                        let alert = UIAlertController.init(title: "温馨提示", message: "确定换绑微信账号？", preferredStyle: .alert)
                        alert.addAction(UIAlertAction(title: "取消", style: .cancel, handler: nil))
                        let ok = UIAlertAction(title: "确定", style: .default) { (_) in
                            //换绑微信
                            self.postWeiXinBind()
//                            self.getUnbindWeiXin()
                        }
                        alert.addAction(ok)
                        present(alert, animated: true, completion: nil)
                    } else {
                        //微信未登录，去绑定微信
                        postWeiXinBind()
                    }
                } else {
                    //有用户信息，但无手机号 （微信登录了，但没绑定手机）
                    alertSingleAction(title: "温馨提示", msg: "请先绑定手机号，直接解绑微信会失去现在已经创建的内容哦！")
                }
            } else {
                /* 无用户信息，就是未登录，直接跳登录*/
                let loginCtrl = MainLoginVC()
                loginCtrl.needAutoBackAfterDone = true
                loginCtrl.loginSuccessBlock = {
                    //
                }
                push(to: loginCtrl)

            }
            
        default :
            break
        }
        
        
    }
}

extension UserInfoSettingVC: UITableViewDataSource {
            
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return LCDevice.DIN_WIDTH(52)
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return itemsTitleArr.count
    }
    
    func tableView(_ tableView: UITableView, heightForHeaderInSection section: Int) -> CGFloat {
        return 1
    }
    
    func tableView(_ tableView: UITableView, viewForHeaderInSection section: Int) -> UIView? {
        return UIView(frame: CGRect(x: 0, y: 0, width: LCDevice.screenW, height: 1))
    }
    
    func tableView(_ tableView: UITableView, heightForFooterInSection section: Int) -> CGFloat {
        return 1
    }
    
    func tableView(_ tableView: UITableView, viewForFooterInSection section: Int) -> UIView? {
        let footer = UIView(frame: CGRect(x: 0, y: 0, width: LCDevice.screenW, height: 1))
        footer.backgroundColor = .clear
        return footer
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell: SettingCell = tableView.dequeueReusableCell(withIdentifier: kUserInfoSettingVCCellID, for: indexPath) as! SettingCell
        let itemType = itemsTitleArr[indexPath.row]
        switch itemType {
        case .bindPhone://绑定手机号
            if let user = UserInfo.currentUser(), let phone = user.phone {
                cell.config(with: itemType.rawValue, isShowArrow: true, phoneNumber: phone, wechatName: nil, hasNewVersion: false)
            } else {
                cell.config(with: itemType.rawValue)
            }
            
        case .bindWechat://绑定微信
            if let user = UserInfo.currentUser(), let bindWx = user.bindWechat, bindWx {
//            if let user = UserInfo.currentUser(), let sex = user.sex, sex != 3 {
                //微信登录的，此处显示微信昵称
                cell.config(with: itemType.rawValue, isShowArrow: true, phoneNumber: nil, wechatName: user.nickName, hasNewVersion: false, showVersionInfo: false)
            } else {
                //微信未登录
                cell.config(with: itemType.rawValue, isShowArrow: true, phoneNumber: nil, wechatName: "未绑定", hasNewVersion: false, showVersionInfo: false)
            }
            
        case .versionUpdate://版本更新
            var hasNew = false
            if let vInfo = VersionUpdateInfoModel.currentVersionUpdateInfo() {
                hasNew = vInfo.isUpdate ?? false
            }
            cell.config(with: itemType.rawValue, isShowArrow: true, phoneNumber: nil, wechatName: nil, hasNewVersion: hasNew, showVersionInfo: !hasNew)
            
        default:
            cell.config(with: itemType.rawValue)
            
        }
        
        if indexPath.row == 0 {
            cell.setRadiusForType(with: .UpRadius)
        } else if indexPath.row == itemsTitleArr.count - 1 {
            cell.setRadiusForType(with: .DownRadius)
        } else {
            cell.setRadiusForType(with: .Normal)
        }
        return cell
    }
    
    // MARK: - Btn action
    @objc func saveBtnAction(sender: UIButton) {
        
        Router.openUserInfo()
        
    }
}
