//
//  MineVC.swift
//  LivePlus
//
//  Created by iclick on 2020/11/30.
//

import UIKit
import swiftScan

fileprivate let kTableCellID = "tableCellId"

/// 个人中心
class MineVC: BaseVC {
    
    var isMember: Bool = false  //是否是会员
    
    //列表区域
    var itemsArr: [[ItemsType]] = [[.membership],[.membershipPoints, ItemsType.setting]]

    
    lazy var myTable: UITableView = {
        let tableView = UITableView(frame: CGRect(x: 0, y: 0, width: LCDevice.screenW, height: 400), style: .grouped)
        tableView.backgroundColor = UIColor("#111111")
        tableView.delegate = self
        tableView.dataSource = self
        tableView.separatorStyle = .none
        tableView.showsVerticalScrollIndicator = false
        tableView.register(MineCell.self, forCellReuseIdentifier: kTableCellID)
        return tableView
    }()
    
    lazy private var topHeadView: MineTopView2 = {
        let v = MineTopView2()
        v.delegate = self
        v.gotoMemberButton.addTarget(self, action: #selector(memberAction), for: .touchUpInside)
        return v
    }()
    
    
    // MARK: - view lifeCycle
    override func viewDidLoad() {
        super.viewDidLoad()
        view.backgroundColor = UIColor("#111111")
        
        addHeaderBgView()

        NotificationCenter.default.addObserver(self, selector: #selector(userLoginNoticeAction), name: LCKey.noti_reLogin, object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(userLogoutNoticeAction), name: LCKey.noti_logout, object: nil)
    }
    
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        updateUserinfo()
    }
    
    
    func updateUserinfo() {
        setHeadInfo()

        guard let _ = UserInfo.currentUser() else {
            return
        }
        
        MiddleRequestNet.getUserInfoApiData(callback: { [weak self] _ in
            self?.setHeadInfo()
        }, isShowTip: false)
        
        // vip配置
        MiddleRequestNet.requestLimit()
    }
    
    // MARK: - Notification
    @objc func userLoginNoticeAction() {
       setHeadInfo()
    }
    
    @objc func userLogoutNoticeAction() {
        setHeadInfo()
    }
    
    func updateData() {
        
    }
    
    func setHeadInfo() {
        guard let user = UserInfo.currentUser() else {
            topHeadView.setLoginStatus(status: .NotLoginST)
            topHeadView.snp.updateConstraints { make in
                make.height.equalTo(190)
            }
            myTable.reloadData()
            return
        }
        
        isMember = UserInfo.isMember
        var status:MineUserLoginStatus = .AlreadyLoginST
        if isMember {
            status = .VIPLoginST
        } else {
            if UserInfo.isTrialVip {
                status = .TrialVipLoginST
            }
        }
        
        topHeadView.snp.updateConstraints { make in
            make.height.equalTo(266)
        }
        
        topHeadView.setLoginStatus(status: status)
        myTable.reloadData()
        
    }
    
    
    // MARK: - UI
    /// 添加顶部背景区域
    func addHeaderBgView() {
        view.addSubview(topHeadView)
        topHeadView.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview()
            make.height.equalTo(226)
        }
        
        view.addSubview(self.myTable)
        
        myTable.snp.makeConstraints { make in
            make.bottom.leading.trailing.equalToSuperview()
            make.top.equalTo(topHeadView.snp.bottom)
        }
    }
    
    // MARK: - Btn action
    @objc func gotoLoginAction() {
        if let _ = UserInfo.currentUser() {
            return
        }
        //没有登录
        let loginVC = LoginVC()
        loginVC.needAutoBackAfterDone = true
        loginVC.popToRoot = true
        push(to: loginVC)
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
        
    
    @objc func memberAction() {
        goMember()
    }
    
    func goMember(){
        let ovc = LimitPurchaseVC2()
        push(to: ovc)
    }
    
    func gotoWechatApplets() {
        /// 1、判断是否安装微信
        if !WXApi.isWXAppInstalled() {
//            Router.openHelpCenter()
            return
        }
        
        /// 2、安装的话 跳转到小程序
        let launchMiniProgramReq = WXLaunchMiniProgramReq.object()
        launchMiniProgramReq.userName = LCKey.WX_APPLET_GH_ID
        launchMiniProgramReq.path = LCKey.WX_APPLET_PATH_RELEASE
        launchMiniProgramReq.miniProgramType = .release
        WXApi.send(launchMiniProgramReq) { result in
            print("发送小程序消息：\(result)")
        }
    }
    
    func membershipPointsAction() {
       
        guard let user = UserInfo.currentUser() else { 
            gotoLoginAction()
            return
        }
        
        if !UserInfo.isMember {
            FreePointAlert.show {[weak self] in
                guard let self = self else { return }
                self.goMember()
            } cancelAction: {
                
            }
            return
        }

        push(to: MemberPointsVC())
    }

}

extension MineVC: UITableViewDataSource, UITableViewDelegate {
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        
        /// vip客服单独接口下发的，以后如果要改需求 这里一定要改 有时间的话重构吧 没法维护了
        let name = itemsArr[indexPath.section][indexPath.row]
        
        switch name {
        case .membershipPoints: membershipPointsAction()
        case .setting: push(to: SettingVC())
        case .membership: goMember()
        default: break
        }
        
        
       
    }
    
    func numberOfSections(in tableView: UITableView) -> Int {
        return itemsArr.count
    }
    
    func tableView(_ tableView: UITableView, heightForHeaderInSection section: Int) -> CGFloat {
        return 0.01
    }
    func tableView(_ tableView: UITableView, viewForHeaderInSection section: Int) -> UIView? {
        return UIView(frame: CGRect(x: 0, y: 0, width: LCDevice.screenW, height: 0.01))
    }
    
    func tableView(_ tableView: UITableView, heightForFooterInSection section: Int) -> CGFloat {
        return LCDevice.DIN_WIDTH(6)
    }
    
    func tableView(_ tableView: UITableView, viewForFooterInSection section: Int) -> UIView? {
        return UIView(frame: CGRect(x: 0, y: 0, width: LCDevice.screenW, height: LCDevice.DIN_WIDTH(6)))
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 62
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return itemsArr[section].count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell: MineCell = tableView.dequeueReusableCell(withIdentifier: kTableCellID, for: indexPath) as! MineCell
        let imageName = itemsArr[indexPath.section][indexPath.row]
        let img = UIImage(named: imageName.image)
        let title = itemsArr[indexPath.section][indexPath.row]
        
        if itemsArr[indexPath.section].count == 1 {
            cell.setRadiusForType(with: .AllRadius)
        } else if indexPath.row == 0 {
            cell.setRadiusForType(with: .UpRadius)
        } else if indexPath.row == itemsArr[indexPath.section].count - 1 {
            cell.setRadiusForType(with: .DownRadius)
        } else {
            cell.setRadiusForType(with: .Normal)
        }
        
        var subtitle: String?
        
        if title == ItemsType.membershipPoints {
            subtitle = "剩余 \(UserInfo.points)"
        } else if title == ItemsType.membership {
            subtitle = "升级会员享权益"
        }
        
        let isShowSepLine = itemsArr[indexPath.section].count - 1 == indexPath.row
        
        cell.config(with: img, title: title.rawValue, subtitle: subtitle, isShowSepLine: !isShowSepLine, imageUrl: nil, color: nil)
        
        return cell
        
    }
    
}


extension MineVC: MineTopViewDelegate {
    func scanAction() {
        
    }
    
    func headIconAction() {
        gotoLoginAction()
    }
    
    func openingMember() {
        //网络判断
        if !LCDevice.checkNetIsOk() {
            return
        }
        LCTools.shared.valiationLogin { [weak self] in
            let ovc = LimitPurchaseVC2()
            self?.push(to: ovc)
        }
    }
    
}
