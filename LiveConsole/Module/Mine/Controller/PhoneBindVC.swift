//
//  PhoneBindVC.swift
//  LivePlus
//
//  Created by iclick on 2020/12/25.
//

import UIKit
import RxSwift

/// 手机号绑定、修改
public enum PhoneBindType {
    /// 绑定
    case bind
    /// 修改
    case change
}

/// 手机号绑定（修改）
class PhoneBindVC: BaseVC {
    
    ///绑定成功后自动返回
    var needBackAfterDone = false
    var bindSuccessBlock:(() -> Void)?
    
    ///获取验证码按钮
    let getCodeBtn = UIButton()
    let phoneLoginBtn = UIButton()
    let phoneTF = UITextField()
    let codeTF = UITextField()
    
    var curType = PhoneBindType.bind
    
    ///设置页面类型
    var bindType: PhoneBindType {
        get {
            return curType
        }
        set {
            curType = newValue
        }
    }

    
      /// 渐变层
      lazy var gradientLayer: CAGradientLayer = {
          let leftColor = UIColor("#6974F2").alpha(value: 0.3)
          let rightColor = UIColor("#6974F2")
          let gradientColors = [leftColor.cgColor, rightColor.cgColor]
          let gradientLocations: [NSNumber] = [0.0, 1.0]
          let gradientLayer = CAGradientLayer()
          gradientLayer.startPoint = CGPoint(x: 0, y: 0)
          gradientLayer.endPoint = CGPoint(x: 1, y: 0)
          gradientLayer.colors = gradientColors
          gradientLayer.locations = gradientLocations
          return gradientLayer
      }()

    override func viewDidLoad() {
        super.viewDidLoad()

        // Do any additional setup after loading the view.
        var titleStr = "修改"
        switch curType {
        case .bind:
            titleStr = "绑定"
            break
        default:
            break
        }
        let nav = getNavViewWithItem(titleStr: "手机号"+titleStr, leftImageName: "icon_nav_back_gray", rightImageName: nil)
        view.addSubview(nav)
//        gradientLayer.frame = nav.frame
//        nav.layer.insertSublayer(gradientLayer, at: 0)
        setupLoginUI(titleStr)
    }
    
    func setupLoginUI(_ title:String) {
        //手机号输入
        let phoneView = UIView(frame: CGRect(x: LCDevice.DIN_WIDTH(36), y: LCDevice.DIN_WIDTH(143), width: LCDevice.DIN_WIDTH(303), height: LCDevice.DIN_WIDTH(50)))
        phoneView.layer.cornerRadius = phoneView.height/2
        phoneView.layer.masksToBounds = true
        phoneView.layer.borderWidth = LCDevice.THIN_LINE_HEIGHT
        phoneView.layer.borderColor = UIColor("#B8BABF").cgColor
        view.addSubview(phoneView)
        
        let pIconView = UIImageView(frame: CGRect(x: LCDevice.DIN_WIDTH(20), y: LCDevice.DIN_WIDTH(11), width: LCDevice.DIN_WIDTH(28), height: LCDevice.DIN_WIDTH(28)))
        pIconView.image = UIImage(named: "icon_login_phone")
        phoneView.addSubview(pIconView)
        
        let pLineView = UIView(frame: CGRect(x: LCDevice.DIN_WIDTH(56), y: LCDevice.DIN_WIDTH(17), width: LCDevice.DIN_WIDTH(1), height: LCDevice.DIN_WIDTH(16)))
        pLineView.backgroundColor = UIColor("#919499")
        phoneView.addSubview(pLineView)
        
        phoneTF.frame = CGRect(x: LCDevice.DIN_WIDTH(65), y: 0, width: LCDevice.DIN_WIDTH(200), height: LCDevice.DIN_WIDTH(30))
        phoneTF.centerY = pIconView.centerY
        phoneTF.keyboardType = .numberPad
        phoneTF.font = LCDevice.DIN_Font_PF_M(16)
        phoneTF.textColor = UIColor("#4D4E52")
        let plAttStr = NSMutableAttributedString(string: "请输入手机号")
        plAttStr.color = UIColor("#B8BABF")
        plAttStr.font = LCDevice.DIN_Font_PF_M(14)
        phoneTF.attributedPlaceholder = plAttStr
        phoneTF.addTarget(self, action: #selector(phoneTFValueDidChanged(sender:)), for: .editingChanged)
        phoneView.addSubview(phoneTF)
        
        let deleteBtn = UIButton(frame: CGRect(x: LCDevice.DIN_WIDTH(261), y: LCDevice.DIN_WIDTH(14), width: LCDevice.DIN_WIDTH(22), height: LCDevice.DIN_WIDTH(22)))
        deleteBtn.setImage(UIImage(named: "icon_login_delete"), for: .normal)
        deleteBtn.addTarget(self, action: #selector(phoneInputDeleteBtnAction(sender:)), for: .touchUpInside)
        phoneView.addSubview(deleteBtn)
        phoneView.centerX = view.centerX
        
        //验证码输入
        let codeView = UIView(frame: CGRect(x: LCDevice.DIN_WIDTH(36), y: phoneView.bottom+LCDevice.DIN_WIDTH(16), width: LCDevice.DIN_WIDTH(303), height: LCDevice.DIN_WIDTH(50)))
        codeView.layer.cornerRadius = codeView.height/2
        codeView.layer.masksToBounds = true
        codeView.layer.borderWidth = LCDevice.THIN_LINE_HEIGHT
        codeView.layer.borderColor = UIColor("#B8BABF").cgColor
        view.addSubview(codeView)
        codeView.centerX = view.centerX
        
        let codeIconView = UIImageView(frame: CGRect(x: LCDevice.DIN_WIDTH(20), y: LCDevice.DIN_WIDTH(11), width: LCDevice.DIN_WIDTH(28), height: LCDevice.DIN_WIDTH(28)))
        codeIconView.image = UIImage(named: "icon_login_code")
        codeView.addSubview(codeIconView)
        
        let codeLineView = UIView(frame: CGRect(x: LCDevice.DIN_WIDTH(56), y: LCDevice.DIN_WIDTH(17), width: LCDevice.DIN_WIDTH(1), height: LCDevice.DIN_WIDTH(16)))
        codeLineView.backgroundColor = UIColor("#919499")
        codeView.addSubview(codeLineView)
        
        codeTF.frame = CGRect(x: LCDevice.DIN_WIDTH(65), y: 0, width: LCDevice.DIN_WIDTH(100), height: LCDevice.DIN_WIDTH(30))
        codeTF.centerY = codeIconView.centerY
        codeTF.keyboardType = .numberPad
        codeTF.font = LCDevice.DIN_Font_PF_M(16)
        codeTF.textColor = UIColor("#4D4E52")
        let codeAttStr = NSMutableAttributedString(string: "请输入验证码")
        codeAttStr.color = UIColor("#B8BABF")
        codeAttStr.font = LCDevice.DIN_Font_PF_M(14)
        codeTF.attributedPlaceholder = codeAttStr
        codeTF.addTarget(self, action: #selector(codeTFValueDidChanged(sender:)), for: .editingChanged)
        codeView.addSubview(codeTF)
        
        let codeBtnLineView = UIView(frame: CGRect(x: LCDevice.DIN_WIDTH(208), y: LCDevice.DIN_WIDTH(20), width: LCDevice.DIN_WIDTH(1), height: LCDevice.DIN_WIDTH(12)))
        codeBtnLineView.backgroundColor = UIColor("#6974F2")
        codeView.addSubview(codeBtnLineView)
        
        getCodeBtn.frame = CGRect(x: LCDevice.DIN_WIDTH(210), y: LCDevice.DIN_WIDTH(14), width: LCDevice.DIN_WIDTH(80), height: LCDevice.DIN_WIDTH(22))
        getCodeBtn.setTitle("获取验证码", for: .normal)
        getCodeBtn.titleLabel?.font = LCDevice.DIN_Font_PF_S(14)
        getCodeBtn.setTitleColor(UIColor("#6974F2"), for: .normal)
        getCodeBtn.addTarget(self, action: #selector(getCodeBtnAction(sender:)), for: .touchUpInside)
        codeView.addSubview(getCodeBtn)
        
        let label = UILabel(frame: CGRect(x: 40, y: codeView.bottom + LCDevice.DIN_WIDTH(10), width: LCDevice.screenW - 80, height: 40))
        label.text = "若您已经在其他平台购买了会员，建议您使用当时购买会员的手机号登录"
        label.textColor = UIColor("#B8BABF")
        label.font = LCDevice.DIN_Font_PF_R(12)
        label.numberOfLines = 0
        view.addSubview(label)
        
        //登录按钮
        phoneLoginBtn.frame = CGRect(x: LCDevice.DIN_WIDTH(18), y: codeView.bottom + LCDevice.DIN_WIDTH(55), width: LCDevice.DIN_WIDTH(339), height: 50)
        phoneLoginBtn.setTitle(title, for: .normal)
        phoneLoginBtn.titleLabel?.font = LCDevice.DIN_Font_PF_S(16)
        phoneLoginBtn.setTitleColor(UIColor("#FFFFFF"), for: .normal)
        phoneLoginBtn.addTarget(self, action: #selector(phoneLoginBtnAction(sender:)), for: .touchUpInside)
        phoneLoginBtn.isEnabled = false
        phoneLoginBtn.cornerRadius = 25
        phoneLoginBtn.centerX = view.centerX
        phoneLoginBtn.applyGradient()
        
        view.addSubview(phoneLoginBtn)
        
//        //隐私协议
//        let infoString = "\(title)即同意《用户服务协议》&《隐私政策》"
//        let agreementFS = "《用户服务协议》"
//        let agreementRange = infoString.range(of: agreementFS)!
//        let agreementLinkRange = NSRange(agreementRange,in:infoString)
//        
//        let privacyFS = "《隐私政策》"
//        let privacyRange = infoString.range(of: privacyFS)!
//        let privacyLinkRange = NSRange(privacyRange,in:infoString)
//        
//        let attString = NSMutableAttributedString.init(string: infoString)
//        attString.font = LCDevice.DIN_Font_PF_M(12)
//        attString.alignment = .center
//        attString.color = UIColor("#919499")
//        attString.setTextHighlight(agreementLinkRange, color: UIColor("#0091FF"), backgroundColor: nil) { (_ containerView, _ attText, _ range, _ rect) in
//            Router.openUserAgreement()
//        }
//        attString.setTextHighlight(privacyLinkRange, color: UIColor("#0091FF"), backgroundColor: nil) { (_ containerView, _ attText, _ range, _ rect) in
//            Router.openPrivacyAgreement()
//        }
//        let agreementLab = YYLabel()
//        agreementLab.frame = CGRect(x: 0, y: phoneLoginBtn.bottom - LCDevice.DIN_WIDTH(5), width: LCDevice.screenW, height: LCDevice.DIN_WIDTH(30))
//        agreementLab.attributedText = attString
//        view.addSubview(agreementLab)
        
    }
    
    
    // MARK: - UITextFieldDelegate
    
    @objc func phoneTFValueDidChanged(sender:UITextField) {
        if phoneTF.text!.count>0 , codeTF.text!.count>0 {
            phoneLoginBtn.isEnabled = true
        } else {
            phoneLoginBtn.isEnabled = false
        }
    }
    
    @objc func codeTFValueDidChanged(sender:UITextField) {
        if phoneTF.text!.count>0 , codeTF.text!.count>0 {
            phoneLoginBtn.isEnabled = true
        } else {
            phoneLoginBtn.isEnabled = false
        }
    }
    
    // MARK: - Btn action
    @objc func phoneInputDeleteBtnAction(sender:UIButton) {
        phoneTF.text = nil
        phoneLoginBtn.isEnabled = false
    }
    
    @objc func getCodeBtnAction(sender:UIButton) {
        guard let phoneNum = phoneTF.text else {
            return;
        }
        guard LoginVC.isLegalPhoneNumber(phoneNum) else { HUD.showFail("手机号码错误"); return }
                
        self.handleCode(phone: phoneNum)

    }

    private func handleCode(phone: String) {
        var type = VerifyCodeType.bindPhone
        if let user = UserInfo.currentUser() {
            if let _ = user.phone{//当前有手机号，更改手机号
                type = .bindPhone
            }else{//当前无手机号（微信登录的），绑定手机号
                type = .bindPhone
            }
        }
        HUD.showWait()
        LoginVC.getVerifeCode(phone: phone, type: type, succes: { [weak self] resObj in
            HUD.hideAllHUD()
            if resObj.code == 0{
                HUD.showSuccess("发送成功")
                // 启动倒计时
                self?.isCounting = true
                self?.codeTF.becomeFirstResponder()
            }else{
                if let meg = resObj.msg {
                    HUD.showFail(meg)
                }else{
                    HUD.showFail("获取失败")
                }
            }
        }, disposeBag: disposeBag)
    }
    
    @objc func phoneLoginBtnAction(sender:UIButton) {
        keyBoardDismss()
        
        if (phoneTF.text?.count)! < 11 || phoneTF.text == "" {
            HUD.showFail("手机号码错误")
            return
        }
        if codeTF.text == "" || (codeTF.text?.count)! < 4 {
            HUD.showFail("验证码错误")
            return
        }
        if !(LoginVC.isLegalPhoneNumber(phoneTF.text!)) {
            HUD.showFail("输入的手机号码格式有误！")
            return
        }
        
        //判断走手机号绑定或者手机号修改接口
        switch curType {
        case .bind:
            postPhoneBind()
            break
        case .change:
            postPhoneChange()
            break
        }
    }
    
    // MARK: - API
    
    // 手机号绑定接口
    func postPhoneBind() {
        HUD.showWait()
        let param:[String:Any] = ["phone": phoneTF.text!, "code": codeTF.text!]
        let req : Observable<Status<BaseRespModel<LoginRespData>>> = APISession.post(APIURL.POST_PHONE_BIND, parameters: param).asObservable()
        req.subscribe(onNext: {[weak self] status in
            HUD.hideAllHUD()
            switch status {
            case .success(let res):
                if res.code == 0{
                    //判断新返回的token，为空时不覆盖当前的
                    var curToken : String?
                    if let userInfo = UserInfo.currentUser(){
                        curToken = userInfo.token
                    }
                    if var user = res.data {
                        if let newToken = user.token,!newToken.isEmpty{
                        }else{
                            user.token = curToken
                        }
                        user.saveLoginUser()

                        NotificationCenter.default.post(name: LCKey.noti_reLogin, object: nil)
                        //成功后跳转
                        HUD.showInfo("恭喜您，绑定成功")
                        self?.checkNeedBack()
                    }
                }else{
                    if let mesg = res.msg {
                        HUD.showFail(mesg)
                    }else{
                        HUD.showFail("手机号绑定失败")
                    }
                }
            case .failure(let error):
                LCLog.d(error.localizedDescription)
                HUD.showFail(error.localizedDescription)
            case .pending: break
            }
        }).disposed(by: disposeBag)
    }
    
    override func leftButtonAction() {
        if curType == .bind,
            let userInfo = UserInfo.currentUser(),
            let bindPhone = userInfo.bindPhone,
            !bindPhone {
            UserInfo.logout()
            NotificationCenter.default.post(name: LCKey.noti_logout, object: nil)
            self.navigationController?.popToRootViewController(animated: true)
            return
        }
        super.leftButtonAction()
    }
    
    func checkNeedBack() {
        if let block = bindSuccessBlock {
            block()
        }
//        if needBackAfterDone{
//            DispatchQueue.main.async {
//                self.leftButtonAction()
////                self.navigationController?.popViewController(animated: false)
////                if  let _ = self.navigationController?.popViewController(animated: false) {
////                    print("000000")
////                }else{
////                    self.dismiss(animated: false, completion: nil)
////                }
//            }
//        }
    }
    
    ///手机号修改接口
    func postPhoneChange() {
        guard let phone =  phoneTF.text else {
            HUD.showFail("请输入手机号")
            return
        }
        HUD.showWait()
        let param:[String:Any] = ["phone": phone, "code": codeTF.text!]
        let req : Observable<Status<BaseRespModel<String?>>> = APISession.post(APIURL.POST_PHONE_REBIND, parameters: param).asObservable()
        req.subscribe(onNext: {[weak self] status in
            guard let self = self else { return }
            HUD.hideAllHUD()
            switch status {
            case .success(let res):
                if res.code == 0{
                    // 更换手机号
                    guard var user = UserInfo.currentUser() else { return }
                    user.phone = phone
                    user.saveUser()
                    
                    NotificationCenter.default.post(name: LCKey.noti_reLogin, object: nil)
                    HUD.showInfo("恭喜您，修改成功")
                    self.navigationController?.popToRootViewController(animated: true)
                }else{
                    if let mesg = res.msg {
                        HUD.showFail(mesg)
                    }else{
                        HUD.showFail("手机号修改失败")
                    }
                }
            case .failure(let error):
                LCLog.d(error.localizedDescription)
                HUD.showFail(error.localizedDescription)
            case .pending: break
            }
        }, onError: { error in
            LCLog.d(error.localizedDescription)
            HUD.showFail(error.localizedDescription)
        }).disposed(by: disposeBag)
    }

    
    // MARK: - Private methods
    /// 验证码倒计时
    var countdownTimer: Timer?
    var isCounting = false {
        willSet {
            if newValue {
                countdownTimer = Timer.scheduledTimer(timeInterval: 1, target: self, selector: #selector(updateTime(timer:)), userInfo: nil, repeats: true)
                remainingSeconds = 59
            } else {
                countdownTimer?.invalidate()
                countdownTimer = nil
            }
            getCodeBtn.isEnabled = !newValue
        }
    }
    var remainingSeconds: Int = 0 {
        willSet {
            getCodeBtn.setTitle("\(newValue)S", for: .normal)
            getCodeBtn.setTitleColor(UIColor("#919499"), for: .normal)
            if newValue <= 0 {
                getCodeBtn.setTitle("获取验证码", for: .normal)
                getCodeBtn.setTitleColor(UIColor("#6974F2"), for: .normal)
                isCounting = false
            }
        }
    }
    
    // 计时开始时，逐秒减少remainingSeconds的值
    @objc func updateTime(timer: Timer) {
        remainingSeconds -= 1
    }

}

