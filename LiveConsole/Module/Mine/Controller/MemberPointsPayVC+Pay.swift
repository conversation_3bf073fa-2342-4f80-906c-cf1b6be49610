//
//  MemberPointsPayVC+Pay.swift
//  LiveConsole
//
//  Created by simon on 22.5.25.
//

import Foundation
import UIKit

extension MemberPointsPayVC {
    @objc func openBtnAction() {
        HapticFeedback.Impact.light()
        guard checkButton.isSelected else {
//            checkButton.shake()
            HUD.showFail("请先同意《快瓴中控台积分规则》")
            return
        }
        
        //网络判断
        if !LCDevice.checkNetIsOk() { return }
        
        guard let user = UserInfo.currentUser() else { return }
        
        willGoPay()
        
    }
    
    func willGoPay() {
        if !LCDevice.isIOS16 {
            LessiOS16BuyAlert.show { [weak self] in
                guard let self = self else { return}
                gotoPay()
            }
            return
        }
        
        gotoPay()
    }
    
    func gotoPay() {
        MiddleRequestNet.orderLimit(isMember: false) { [weak self] m in
            guard let self = self, m == true else {
                return
            }
            
            let message = "\n本产品为虚拟商品，不含人工服务，由于虚拟产品的性质和特征，购买后不支持退货、换货，是否确认购买?"
            // 再次提示用户
            let alert = LLAlertView(title: "温馨提示", message: message, leftShow: ("取消", .defaultColor), rightShow: ("确认", .mainColor)) {
              
            } rightAction: { [ weak self] in
                
                guard let self = self else {
                    return
                }
               
                guard let pid = self.purchasingList.first(where: {$0.defaultPro})?.sku else {
                    HUD.hideAllHUD()
                    return
                }
                
                HUD.showWait()
                HUD.showInfo("订单正在支付中，请等待...", autoClear: false, autoClearTime: 10000)
               
                
                LCTools.savePayLogInfo(message: " 准备付款商品：\(pid)")
                //通过product id 购买商品
                LPPayTool.purchaseProduct(pid, quantity: 1, atomically: false) {[weak self] (result, purModel, phone) in
                    HUD.hideAllHUD()
                    self?.resultParsing(result: result, model: purModel, phone: phone)
                }
            }

            alert.show(with: self)
        }
    }
    
    func showErrorAlert(model: InPurchasingModel) {
        let alert = LLAlertView(title: "温馨提示", message: "\n购买信息同步失败，请保持网络通畅并点击重试。", leftShow: ("取消", .defaultColor), rightShow: ("重试", .mainColor)) {
            InPurchasingModel.saveModel(model: model)
        } rightAction: { [weak self] in
            LCLog.d("---showErrorAlert--重试-")
            HUD.showInfo("订单正在验证，请等待...", autoClear: false, autoClearTime: 10000)
            LPPayTool.shared.validationForServer(paramer: ["code": model.code, "type": model.type, "phone": model.phone], model: model, counts: LCKey.ValidationPayCerCount) { [weak self] result, model, phone in
                HUD.hideAllHUD()
                self?.resultParsing(result: result, model: model, phone: phone)
            }
        }
        alert.show(with: self)
    }
    
    func resultParsing(result: Result<String, InPurchasingError>, model: InPurchasingModel, phone: String) {
        DispatchQueue.main.async {
            if !phone.isEmpty {
                HUD.showSuccess("\(phone)")
            }
           
            switch result {
            case .failure(let error):
                switch error {
                case .getAppPayFail:
                    LCLog.d("-苹果支付失败--")
                case .getAppStoreReceiptURLFail:
                    HUD.showFail("获取凭证失败")
                case .getUserInfoFail:
                    HUD.showFail("获取用户信息失败")
                case .serverValidationFail:
                    self.showErrorAlert(model: model)
                case .alreadlyValidation:
                    LCLog.d("-已经验证过凭证--")
                case .getIllegalProof:
                    HUD.showFail("非法凭证，验证失败！")
                }
            case .success:
               
                MiddleRequestNet.getUserInfoApiData(callback: { [weak self] _ in
                    guard let self = self else { return }
                    if let userInfo = UserInfo.currentUser() {
                        print("购买会员成功：\(userInfo)")
                        HUD.showSuccess("充值成功")
                        NotificationCenter.default.post(name: LCKey.noti_PaiedMember, object: nil)
                        UserDefaults.standard.set(false, forKey: UserDefault_expired)
                        
                    }
                }, isShowTip: false)
            }
        }
    }
    
    func getData() {
        requesProducts()
    }
    
    func requesProducts() {
        HUD.showWait()
        MiddleRequestNet.getPointsProductsList { [weak self] models in
            guard let self = self else { return }
            HUD.hideAllHUD()
            self.purchasingList = models
            self.memberCollection.reloadData()
        }
    }
}
