//
//  MemberPointsVC.swift
//  LiveConsole
//
//  Created by simon on 7.5.25.
//

/// 会员积分页
import Foundation
import UIKit

class MemberPointsVC: BaseVC {

    lazy var headView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        return view
    }()
    
    /// 头像
    lazy var headImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.cornerRadius = 28
        imageView.image = UIImage(named: "icon_user_headerDefault")
        imageView.isUserInteractionEnabled = true
        return imageView
    }()
    
    /// 名字
    lazy var nameLabel: UILabel = {
        let label = UILabel()
        label.textColor = .white
        label.font = LCDevice.DIN_Font_PF_M(16)
        return label
    }()
    
    lazy var vipTitle1: UILabel = {
        let label = UILabel()
        label.textColor = UIColor.white.alpha(value: 0.6)
        label.font = UIFont.systemFont(ofSize: 12, weight: .regular)
        return label
    }()
    
    //列表区域
    var datas: [PointsInfo] = []
    
    lazy var myTable: UITableView = {
        let tableView = UITableView()
        tableView.backgroundColor = UIColor("#282828")
        tableView.delegate = self
        tableView.dataSource = self
        tableView.separatorStyle = .none
        tableView.showsVerticalScrollIndicator = false
        tableView.cornerRadius = 8
        tableView.register(PointsCell.self, forCellReuseIdentifier: PointsCell.zl_identifier())
        return tableView
    }()
    
    
    // 积分
    lazy var pointView: UIImageView = {
        let label = UIImageView()
        label.backgroundColor = .clear
        label.image = UIImage(named: "points_bg")
        label.isUserInteractionEnabled = true
        label.contentMode = .scaleAspectFill
        label.layer.masksToBounds = true
        label.cornerRadius = 8
        return label
    }()
    
    // 积分数
    lazy var pointLab: UILabel = {
        let label = UILabel()
        label.textColor = UIColor.white
        label.text = "0"
        label.font = UIFont.systemFont(ofSize: 40, weight: .medium)
        return label
    }()
    
    lazy var jifenLab: UILabel = {
        let label = UILabel()
        label.textColor = UIColor.white.alpha(value: 0.5)
        label.font = UIFont.systemFont(ofSize: 13, weight: .regular)
        label.text = "积分"
        return label
    }()
    
    lazy var wuLab: UILabel = {
        let label = UILabel()
        label.textColor = UIColor.white.alpha(value: 0.6)
        label.font = UIFont.systemFont(ofSize: 14, weight: .regular)
        label.text = "暂无记录"
        label.textAlignment = .center
        return label
    }()
    
    lazy var shengyuView: UIImageView = {
        let label = UIImageView()
        label.backgroundColor = .clear
        label.image = UIImage(named: "points_剩余")
        label.isUserInteractionEnabled = true
        return label
    }()
    
    lazy var chongzhiView: UIImageView = {
        let label = UIImageView()
        label.backgroundColor = .clear
        label.image = UIImage(named: "points_充值")
        label.isUserInteractionEnabled = true
        let tap = UITapGestureRecognizer(target: self, action: #selector(pay))
        label.addGestureRecognizer(tap)
        return label
    }()
    
    
    lazy var desView: UIView = {
        let label = UIView()
        label.backgroundColor = .white.alpha(value: 0.8)
        label.cornerRadius = 2
        return label
    }()
    
    lazy var desLab: UILabel = {
        let label = UILabel()
        label.textColor = UIColor.white.alpha(value: 0.8)
        label.font = UIFont.systemFont(ofSize: 12, weight: .regular)
        label.text = "AI合成语音互动"
        return label
    }()
    
    lazy var desView1: UIView = {
        let label = UIView()
        label.backgroundColor = .white.alpha(value: 0.8)
        label.cornerRadius = 2
        return label
    }()
    
    lazy var desLab1: UILabel = {
        let label = UILabel()
        label.textColor = UIColor.white.alpha(value: 0.8)
        label.font = UIFont.systemFont(ofSize: 12, weight: .regular)
        label.text = "时实时朗读用户昵称"
        return label
    }()
    
    private lazy var speView: UIView = {
        let stack = UIView()
        stack.backgroundColor = UIColor("#282828")
        return stack
    }()
    
    lazy var titleLab: UILabel = {
        let label = UILabel()
        label.textColor = UIColor.white
        label.font = UIFont.systemFont(ofSize: 16, weight: .regular)
        label.text = "积分消耗记录"
        return label
    }()
    
    lazy var desView3: UIView = {
        let label = UIView()
        label.backgroundColor = UIColor("#6974F2")
        label.cornerRadius = 4
        return label
    }()
    
    lazy var ruleButton: UIButton = {
        let button = UIButton()
        button.setTitle("积分消耗规则?", for: .normal)
        button.setTitleColor(UIColor("#F78751"), for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 13, weight: .regular)
        button.addTarget(self, action: #selector(ruleAction), for: .touchUpInside)
        return button
    }()
    
    
    override func viewDidLoad() {
        super.viewDidLoad()

        view.backgroundColor = UIColor("#111111")
        
        let nav = getNavViewWithItem(titleStr: "我的积分", leftImageName: "icon_nav_back_white", rightImageName: nil, color: UIColor("#8E72F2"))
        nav.backgroundColor = UIColor("#111111")
        view.addSubview(nav)
        
        nav.addSubview(speView)
        speView.snp.makeConstraints { make in
            make.leading.bottom.trailing.equalToSuperview()
            make.height.equalTo(1.5)
        }
        
        
        setupUI()
        
        loadData()

    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        update()
    }
    
    func setupUI() {
        view.addSubviews([headView, pointView,ruleButton, desView3,titleLab, myTable,wuLab])
        headView.addSubviews([headImageView, nameLabel, vipTitle1])
        
        pointView.addSubviews([pointLab, shengyuView, chongzhiView, pointLab, desLab,desView, desLab1, desView1, jifenLab])

        headView.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview()
            make.top.equalToSuperview().inset(LCDevice.Nav_H + 8)
            make.height.equalTo(60)
        }
        
        headImageView.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.leading.equalToSuperview().inset(26)
            make.width.height.equalTo(56)
        }
        
        nameLabel.snp.makeConstraints { make in
            make.leading.equalTo(headImageView.snp.trailing).offset(12)
            make.top.equalToSuperview().inset(9)
            make.height.equalTo(24)
        }
        
        vipTitle1.snp.makeConstraints { make in
            make.leading.equalTo(headImageView.snp.trailing).offset(12)
            make.bottom.equalToSuperview().inset(9)
            make.height.equalTo(16)
        }
        
        
        pointView.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview().inset(16)
            make.top.equalTo(headView.snp.bottom).offset(16)
            make.height.equalTo(166)
        }
        
        shengyuView.snp.makeConstraints { make in
            make.leading.equalToSuperview().inset(25)
            make.top.equalToSuperview().inset(17)
            make.height.equalTo(24)
            make.width.equalTo(34)
        }
        
        chongzhiView.snp.makeConstraints { make in
            make.trailing.equalToSuperview()
            make.bottom.equalToSuperview().inset(26)
            make.height.equalTo(32)
            make.width.equalTo(78)
        }
        
        pointLab.snp.makeConstraints { make in
            make.leading.equalToSuperview().inset(24)
            make.top.equalToSuperview().inset(42)
            make.height.equalTo(44)
        }
        
        jifenLab.snp.makeConstraints { make in
            make.leading.equalTo(pointLab.snp.trailing).offset(6)
            make.bottom.equalTo(pointLab.snp.bottom).offset(-3)
            make.height.equalTo(16)
        }
        
        desView.snp.makeConstraints { make in
            make.leading.equalToSuperview().offset(24)
            make.top.equalToSuperview().inset(110)
            make.height.width.equalTo(4)
        }
        
        
        desView1.snp.makeConstraints { make in
            make.leading.equalToSuperview().offset(24)
            make.bottom.equalToSuperview().inset(30)
            make.height.width.equalTo(4)
        }
        
        desLab.snp.makeConstraints { make in
            make.leading.equalToSuperview().offset(32)
            make.centerY.equalTo(desView.snp.centerY)
        }
        
        desLab1.snp.makeConstraints { make in
            make.leading.equalToSuperview().offset(32)
            make.centerY.equalTo(desView1.snp.centerY)
        }

        titleLab.snp.makeConstraints { make in
            make.leading.equalToSuperview().inset(32)
            make.top.equalTo(pointView.snp.bottom).offset(30)
            make.height.equalTo(19)
        }
        
        desView3.snp.makeConstraints { make in
            make.leading.equalToSuperview().inset(16)
            make.centerY.equalTo(titleLab.snp.centerY)
            make.height.width.equalTo(8)
        }
        
        ruleButton.snp.makeConstraints { make in
            make.trailing.equalToSuperview().inset(12)
            make.centerY.equalTo(titleLab.snp.centerY)
            make.height.equalTo(30)
            make.width.equalTo(92)
        }
        
        myTable.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview().inset(16)
            make.bottom.equalToSuperview().inset(LCDevice.X_BOTTOM_INSET + 20)
            make.top.equalTo(pointView.snp.bottom).offset(62)
        }
        
        
        wuLab.snp.makeConstraints { make in
            make.centerX.trailing.equalToSuperview()
            make.bottom.equalToSuperview().inset(250)
        }
        
        
    }
    
    func loadData() {
        
        guard let _ = UserInfo.currentUser() else { return  }
        
        MiddleRequestNet.getPiontsStatistics { [weak self] infos in
            guard let self = self else { return}
            if let infos = infos {
                self.datas = infos
            } else {
                self.datas.removeAll()
            }
            self.update()
        
        }
        
        
        MiddleRequestNet.getUserInfoApiData(callback: { [weak self] _ in
            self?.update()
        }, isShowTip: false)
    }
    
    
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
    
    @objc func ruleAction() {
        let alertView = PointsAlertView()
        alertView.show( in: self.view)
        
    }
    
    @objc func pay() {
        if UserInfo.points > 5000 {
            HUD.showFail("您当前积分还超过5000\n暂不需要充值哦")
            return
        }
        
        push(to: MemberPointsPayVC())
    }
    
    
    func update() {
        
        updateUser()
        
        self.wuLab.isHidden = self.datas.count != 0
        self.myTable.reloadData()
        let point: Int64 = UserInfo.currentUser()?.audioInfo?.points ?? 0
        self.pointLab.text = "\(point)"
    
    }
    
    /// 更新用户信息
    func updateUser() {
        if let userInfo = UserInfo.currentUser(), let userName = userInfo.nickName{
            nameLabel.text = userName
            if let headImage = userInfo.headImgUrl, let headUrl = URL(string: headImage) {
                headImageView.setImageWith(headUrl, placeholder: UIImage(named: "icon_user_headerDefault"))
            }
            
            if UserInfo.points > 0,  let ext =  userInfo.audioInfo?.pointsExpiredTime {
                vipTitle1.text =  "积分\(LCTools.covertDateString(timeString: ext))到期"
            } else {
                vipTitle1.text = "您还没有积分"
            }
           
        }
        
        
    }
}
    
    


extension MemberPointsVC: UITableViewDataSource, UITableViewDelegate{
        
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        
    }
    
    // 三种高度 70 90 113
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return self.datas[indexPath.row].height
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return datas.count
    }
    
    
    func tableView(_ tableView: UITableView, heightForFooterInSection section: Int) -> CGFloat {
        if datas.count > 0 {
            return LCDevice.DIN_WIDTH(52)
        }
        return LCDevice.DIN_WIDTH(12)
    }
    
    func tableView(_ tableView: UITableView, viewForFooterInSection section: Int) -> UIView? {
        if datas.count == 0 {
            return nil
        }
        
        let footer = UIView(frame: CGRect(x: 0, y: 0, width: LCDevice.screenW - 32, height: LCDevice.DIN_WIDTH(52)))
        footer.backgroundColor = UIColor("#282828")
        
        let label = UILabel()
        label.textColor = UIColor.white.alpha(value: 0.6)
        label.font = UIFont.systemFont(ofSize: 12, weight: .regular)
        label.text = " 仅展示1个月内记录"
        label.textAlignment = .center
        label.frame = footer.bounds
        footer.addSubview(label)
        
        return footer
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell: PointsCell = tableView.dequeueReusableCell(withIdentifier: PointsCell.zl_identifier(), for: indexPath) as! PointsCell
        cell.delegete = self
        cell.config(with: self.datas[indexPath.row])
        return cell
    }
    
}

extension MemberPointsVC: PointsCellDelegate {
    func actionStatusChanged() {
        update()
    }
}
