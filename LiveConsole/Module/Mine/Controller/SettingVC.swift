//
//  SettingVC.swift
//  LivePlus
//
//  Created by iclick on 2020/12/24.
//

import UIKit
import RxSwift

fileprivate let kSetTableCellID = "tableCellId"

enum ItemsType: String {
    case userInfo = "个人信息"
    case bindPhone = "绑定手机号"
    case bindWechat = "绑定微信"
    case userProtocol = "用户协议"
    case privacyManager = "隐私管理"
    case privacyPolicy = "隐私政策"
    case userInfoManager = "个人信息收集清单"
    case accoutSafe = "账号与安全"
    case versionUpdate = "版本更新"
    case sdkList = "第三方SDK列表"
    case authorization = "系统权限管理"
    case membershipPoints = "我的积分"
    case setting = "设置"
    case membership = "会员中心"
    
    var image: String {
        switch self {
        case .userInfo:
            return ""
        case .bindPhone:
            return ""
        case .bindWechat:
            return ""
        case .userProtocol:
            return ""
        case .privacyManager:
            return ""
        case .privacyPolicy:
            return ""
        case .userInfoManager:
            return ""
        case .accoutSafe:
            return ""
        case .versionUpdate:
            return ""
        case .sdkList:
            return ""
        case .authorization:
            return ""
        case .membershipPoints:
            return "icon_user_积分"
        case .setting:
            return "icon_user_setting"
        case .membership:
            return "icon_user_会员"
        }
    }
    

}

/// 设置页
class SettingVC: BaseVC {

    //列表区域
    var itemsTitleArr: [ItemsType] = [.userInfo, .userProtocol, .privacyManager, .accoutSafe, .versionUpdate, .sdkList]
    let myTable = UITableView(frame: CGRect(x: 0, y: LCDevice.Nav_H, width: LCDevice.screenW, height: LCDevice.screenH-LCDevice.Nav_H), style: .grouped)
    
    override func viewDidLoad() {
        super.viewDidLoad()
        // 如果是内测版本 移除绑定微信选项
        if !LCTools.iLCLogin() { itemsTitleArr.removeAll(where: { $0 == .accoutSafe })}
        // Do any additional setup after loading the view.
        if !LCTools.iLCLogin() { itemsTitleArr.removeAll(where: { $0 == .accoutSafe || $0 == .userInfo })}
        view.backgroundColor = UIColor("#111111")
        let nav = getNavViewWithItem(titleStr: "设置", leftImageName: "icon_nav_back_white", rightImageName: nil, color: UIColor("#8E72F2"))
        nav.isUserInteractionEnabled = true
        nav.backgroundColor = UIColor("#111111")
        view.addSubview(nav)
        addTableView()
        
        let longPressGes = UILongPressGestureRecognizer(target: self, action: #selector(longPressGestureAction(sender:)))
        longPressGes.minimumPressDuration = 3
        nav.addGestureRecognizer(longPressGes)
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        myTable.reloadData()
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }

    // MARK: - UI
    func addTableView() {
        myTable.backgroundColor = .clear//UIColor("#F0F1F7")
        myTable.dataSource = self
        myTable.delegate = self
        myTable.separatorStyle = .none
        myTable.register(SettingCell.self, forCellReuseIdentifier: kSetTableCellID)
        view.addSubview(myTable)
    }
    
    @objc private func longPressGestureAction(sender: UILongPressGestureRecognizer) {
        
        LCTools.repaortPayLogInfo()
    }
  
}

extension SettingVC: UITableViewDelegate {
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        let itemType = itemsTitleArr[indexPath.row]
        switch itemType {
            
        case .userInfo:
            //用户协议
            push(to: UserInfoSettingVC())
            
        case .privacyManager:
            //隐私政策
//            Router.openPrivacyAgreement()
            push(to: PrivacySettingVC())
        case .accoutSafe: push(to: AccountVC())
        case .versionUpdate:
            //版本更新
            Router.openAppStore()
        case .userProtocol:
            //用户协议
            Router.openUserAgreement()
        case .sdkList:
            Router.openSDKList()
        default :
            break
        }
        
    }
}

extension SettingVC: UITableViewDataSource {
            
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 62
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return itemsTitleArr.count
    }
    
    func tableView(_ tableView: UITableView, heightForHeaderInSection section: Int) -> CGFloat {
        return LCDevice.DIN_WIDTH(12)
    }
    
    func tableView(_ tableView: UITableView, viewForHeaderInSection section: Int) -> UIView? {
        return UIView(frame: CGRect(x: 0, y: 0, width: LCDevice.screenW, height: LCDevice.DIN_WIDTH(12)))
    }
    
    func tableView(_ tableView: UITableView, heightForFooterInSection section: Int) -> CGFloat {
        if let _ = UserInfo.currentUser() {
            return LCDevice.DIN_WIDTH(12+52)
        }
        return LCDevice.DIN_WIDTH(12)
    }
    
    func tableView(_ tableView: UITableView, viewForFooterInSection section: Int) -> UIView? {
        var footH = LCDevice.DIN_WIDTH(12)
        if let _ = UserInfo.currentUser() {
            footH = LCDevice.DIN_WIDTH(12+52)
        }
        let footer = UIView(frame: CGRect(x: 0, y: 0, width: LCDevice.screenW, height: footH))
        footer.backgroundColor = .clear
        if let _ = UserInfo.currentUser() {
            let logoutBtn = UIButton(frame: CGRect(x: 16, y: LCDevice.DIN_WIDTH(12), width: LCDevice.screenW - 32, height: LCDevice.DIN_WIDTH(52)))
            logoutBtn.setTitle("退出登录", for: .normal)
            logoutBtn.setTitleColor(UIColor.white, for: .normal)
            logoutBtn.titleLabel?.font = LCDevice.DIN_Font_PF_M(15)
            logoutBtn.backgroundColor = UIColor("#282828")
            logoutBtn.cornerRadius = 10
            logoutBtn.addTarget(self, action: #selector(logoutBtnAction(sender:)), for: .touchUpInside)
            footer.addSubview(logoutBtn)
        }
        return footer
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell: SettingCell = tableView.dequeueReusableCell(withIdentifier: kSetTableCellID, for: indexPath) as! SettingCell
        let itemType = itemsTitleArr[indexPath.row]
        switch itemType {
        case .bindPhone://绑定手机号
            if let user = UserInfo.currentUser(), let phone = user.phone {
                cell.config(with: itemType.rawValue, isShowArrow: true, phoneNumber: phone, wechatName: nil, hasNewVersion: false)
            } else {
                cell.config(with: itemType.rawValue)
            }
            
        case .bindWechat://绑定微信
            if let user = UserInfo.currentUser(), let bindWx = user.bindWechat, bindWx {
//            if let user = UserInfo.currentUser(), let sex = user.sex, sex != 3 {
                //微信登录的，此处显示微信昵称
                cell.config(with: itemType.rawValue, isShowArrow: true, phoneNumber: nil, wechatName: user.nickName, hasNewVersion: false, showVersionInfo: false)
            } else {
                //微信未登录
                cell.config(with: itemType.rawValue, isShowArrow: true, phoneNumber: nil, wechatName: "未绑定", hasNewVersion: false, showVersionInfo: false)
            }
            
        case .versionUpdate://版本更新
            var hasNew = false
            if let vInfo = VersionUpdateInfoModel.currentVersionUpdateInfo() {
                hasNew = vInfo.isUpdate ?? false
            }
            cell.config(with: itemType.rawValue, isShowArrow: true, phoneNumber: nil, wechatName: nil, hasNewVersion: hasNew, showVersionInfo: !hasNew)
            
        default:
            cell.config(with: itemType.rawValue, isShowArrow: true)
            
        }
        
        if indexPath.row == 0 {
            cell.setRadiusForType(with: .UpRadius)
        } else if indexPath.row == itemsTitleArr.count - 1 {
            cell.setRadiusForType(with: .DownRadius)
        } else {
            cell.setRadiusForType(with: .Normal)
        }
        return cell
    }
    
    // MARK: - Btn action
    @objc func logoutBtnAction(sender: UIButton) {
        
        let alert = UIAlertController.init(title: "温馨提示", message: "确定退出登录吗?", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "取消", style: .cancel, handler: nil))
        let ok = UIAlertAction(title: "确定", style: .default) { (_) in
            UserInfo.logout()
            NotificationCenter.default.post(name: LCKey.noti_logout, object: nil)
            self.leftButtonAction()
        }
        alert.addAction(ok)
        present(alert, animated: true, completion: nil)
    }
}
