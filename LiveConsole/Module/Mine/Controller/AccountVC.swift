//
//  AccountVC.swift
//  LivePlus
//
//  Created by 郭炜 on 2022/1/24.
//

import UIKit

enum AccountOption: CaseIterable {
    /// 注销
    case logout
    
    typealias optionAtion = () -> Void
    var action: optionAtion {
        switch self {
        case .logout:
            return {
                Router.open(RouterBag(cls: LogOutVC.self, params: nil))
            }
        }
    }
    
    var title: String {
        switch self {
        case .logout:
            return "账号注销"
        }
    }
}

class AccountVC: BaseVC {

    private lazy var tableView: UITableView = {
        let tableView = UITableView()
        tableView.backgroundColor = .clear
        tableView.delegate = self
        tableView.dataSource = self
        tableView.register(SettingCell.self, forCellReuseIdentifier: SettingCell.zl_identifier())
        tableView.tableFooterView = UIView()
        tableView.separatorStyle = .none
        return tableView
    }()
    
 
    
    private let dataSource: [AccountOption] = AccountOption.allCases
    
    override func viewDidLoad() {
        super.viewDidLoad()
        view.backgroundColor = UIColor("#111111")
       
        let nav = getNavViewWithItem(titleStr: "个人信息", leftImageName: "icon_nav_back_white", rightImageName: nil, color: UIColor("#8E72F2"))
        nav.backgroundColor = UIColor("#111111")
        view.addSubview(nav)
        view.addSubview(tableView)
        tableView.snp.makeConstraints { make in
            make.leading.trailing.bottom.equalToSuperview()
            make.top.equalToSuperview().inset(LCDevice.Nav_H)
        }
    }

}

extension AccountVC: UITableViewDelegate, UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return dataSource.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell: SettingCell = tableView.dequeueReusableCell(withIdentifier: SettingCell.zl_identifier(), for: indexPath) as! SettingCell
        let option = dataSource[indexPath.row]
        cell.config(with: option.title, showSepLine: false)        
        cell.setRadiusForType(with: .AllRadius)
        return cell
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 62
    }
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        let option = dataSource[indexPath.row]
        option.action()
    }
}
