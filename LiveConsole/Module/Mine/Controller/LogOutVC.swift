//
//  LogOutVC.swift
//  LivePlus
//
//  Created by 郭炜 on 2022/1/24.
//

import UIKit
import Foundation
import RxSwift
import YYKit
import RxCocoa

enum PageSubject {
    case success(String)
    case failed(String)
}

enum WindowSubject {
    case success(String)
    case failed(String)
}

enum LogOffSubject {
    case success
    case failed(String)
}

class LogOutViewModel: NSObject {
    
    public var pageSubject = PublishSubject<PageSubject>()
    public var windowSubject = PublishSubject<WindowSubject>()
    public var logoffSubject = PublishSubject<LogOffSubject>()
    
    private let disposeBag = DisposeBag()
    
    fileprivate func queryPage() {
        
        if let page = ConfigModel.getDynamicResources(key: "key_LOGOFF_PAGE") {
            self.pageSubject.onNext(.success(page.value ?? ""))
        } else {
            self.pageSubject.onNext(.failed(""))
        }
        
    }
    
    fileprivate func queryWindow() {
        if let page = ConfigModel.getDynamicResources(key: "key_LOGOFF_WINDOW") {
            self.windowSubject.onNext(.success(page.value ?? ""))
        } else {
            self.windowSubject.onNext(.failed(""))
        }
    }
    
    fileprivate func logoff() { //GET_LOG_OFF
        HUD.showWait()
        let req: Observable<Status<BaseRespModel<String?>>> = APISession.post(APIURL.GET_LOG_OFF).asObservable()
        req.subscribe(onNext: { [weak self] status in
            guard let self = self else { return }
            HUD.hideAllHUD()
            switch status {
            case .success(let res):
                if let code = res.code, code != 0, let msg = res.msg {
                    self.logoffSubject.onNext(.failed(msg))
                    return
                }
                self.logoffSubject.onNext(.success)
            case .failure(let error):
                LCLog.d(error.localizedDescription)
                self.logoffSubject.onNext(.failed(error.localizedDescription))
            case .pending: break
            }
        },onError: { error in
            HUD.hideAllHUD()
            LCLog.d(error.localizedDescription)
            self.logoffSubject.onNext(.failed(error.localizedDescription))
        }).disposed(by: disposeBag)
    }
}

class LogOutVC: BaseVC {

    private lazy var seperator: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor("#CCCCCC")
        return view
    }()
    
    private lazy var textView: UITextView = {
        let textView = UITextView()
        textView.backgroundColor = UIColor("#111111")
        textView.isUserInteractionEnabled = false
        textView.isHidden = true
        return textView
    }()
    
    private lazy var selectButton: UIButton = {
        let button = UIButton()
        button.setImage(UIImage(named: "登录流程_组件_cell_勾选_nor"), for: .normal)
        button.setImage(UIImage(named: "icon_勾选_sel"), for: .selected)
        button.zl_enlargeValidTouchArea(inset: 16)
        return button
    }()
    
    private lazy var selectTitle: UILabel = {
        let label = UILabel()
        label.text = "我已阅读并同意"
        label.textColor = UIColor("#797B7D")
        label.font = LCDevice.DIN_Font_PF_R(12)
        return label
    }()
    
    private lazy var sureButton: UIButton = {
        let button = UIButton()
        button.setTitle("同意注销", for: .normal)
        button.setBackgroundImage(UIImage(color: UIColor("#E1E2E6")), for: .disabled)
        button.setBackgroundImage(UIImage(color: UIColor("#6974F2")), for: .normal)
        button.titleLabel?.textColor = UIColor.white
        button.titleLabel?.font = LCDevice.DIN_Font_PF_M(14)
        button.cornerRadius = 22
        button.isEnabled = false
        return button
    }()
    
    private lazy var retryView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.clear
        return view
    }()
    
    private lazy var tipRetryLabel: UILabel = {
        let label = UILabel()
        label.text = "出错啦！点击空白处重试~"
        label.font = LCDevice.DIN_Font_PF_R(14)
        label.textColor = UIColor.gray
        label.textAlignment = .center
        return label
    }()
    
    private let viewModel = LogOutViewModel()
    
    lazy var gradientLayer: CAGradientLayer = {
        let leftColor = UIColor("#6974F2").alpha(value: 0.3)
        let rightColor = UIColor("#6974F2")
        let gradientColors = [leftColor.cgColor, rightColor.cgColor]
        let gradientLocations: [NSNumber] = [0.0, 1.0]
        let gradientLayer = CAGradientLayer()
        gradientLayer.startPoint = CGPoint(x: 0, y: 0)
        gradientLayer.endPoint = CGPoint(x: 1, y: 0)
        gradientLayer.colors = gradientColors
        gradientLayer.locations = gradientLocations
        return gradientLayer
    }()
    
    override func viewDidLoad() {
        super.viewDidLoad()
        view.backgroundColor = UIColor("#111111")
        let nav = getNavViewWithItem(titleStr: "申请注销账号", leftImageName: "icon_nav_back_white", rightImageName: nil, color: UIColor("#8E72F2"))
        nav.backgroundColor = UIColor("#111111")
        view.addSubview(nav)
        
        view.addSubview(retryView)
        retryView.addSubview(tipRetryLabel)
        view.addSubviews([seperator, textView, selectButton, selectTitle, sureButton])
//        gradientLayer.frame = nav.frame
//        nav.layer.insertSublayer(gradientLayer, at: 0)
        makeConstraints()
        makeBusiness()
    }

    func makeConstraints() {
        seperator.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(LCDevice.Nav_H)
            make.leading.trailing.equalToSuperview()
            make.height.equalTo(0.5)
        }
        sureButton.snp.makeConstraints { make in
            make.bottom.equalToSuperview().inset(LCDevice.X_BOTTOM_INSET + 20)
            make.leading.trailing.equalToSuperview().inset(28)
            make.height.equalTo(44)
        }
        selectButton.snp.makeConstraints { make in
            make.leading.equalToSuperview().inset(28)
            make.bottom.equalTo(sureButton.snp.top).offset(-20)
            make.width.height.equalTo(24)
        }
        selectTitle.snp.makeConstraints { make in
            make.leading.equalTo(selectButton.snp.trailing).offset(2)
            make.centerY.equalTo(selectButton.snp.centerY)
        }
        textView.snp.makeConstraints { make in
            make.top.equalTo(seperator.snp.bottom).offset(26)
            make.bottom.equalTo(selectTitle.snp.top).offset(-20)
            make.leading.trailing.equalToSuperview().inset(20)
        }
        retryView.snp.makeConstraints { make in
            make.top.equalTo(seperator.snp.bottom)
            make.bottom.equalTo(selectTitle.snp.top).offset(-20)
            make.leading.trailing.equalToSuperview()
        }
        tipRetryLabel.snp.makeConstraints { make in
            make.center.equalToSuperview()
        }
    }
    
    func makeBusiness() {
        selectButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self else { return }
                self.selectButton.isSelected = !self.selectButton.isSelected
                self.sureButton.isEnabled = self.selectButton.isSelected
            }.disposed(by: disposeBag)
        
        sureButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self else { return }
                HUD.showWait()
                self.viewModel.queryWindow()
            }.disposed(by: disposeBag)
        
        retryView.addGestureRecognizer(UITapGestureRecognizer(actionBlock: { [weak self] _ in
            guard let self = self else { return }
            self.viewModel.queryPage()
        }))
        
        viewModel
            .pageSubject
            .subscribe { [weak self] event in
                guard let self = self, let status = event.element else { return }
                switch status {
                case .success(let content):
                    guard let data = content.data(using: .utf8) else { return }
                    let mutabled = NSMutableAttributedString(attributedString: data.html2AttributedString)
                    mutabled.addAttributes([NSAttributedString.Key.font: LCDevice.DIN_Font_Bold(18), NSAttributedString.Key.foregroundColor: UIColor.white],
                                           range: NSRange(location: 0, length: 10),)
                    mutabled.addAttributes([NSAttributedString.Key.font: UIFont.systemFont(ofSize: 16), NSAttributedString.Key.foregroundColor: UIColor.white],
                                           range: NSRange(location: 10, length: mutabled.length - 10))
                    self.textView.attributedText = mutabled
                    self.textView.isHidden = false
                    self.retryView.isHidden = true
                case .failed(let errMsg):
                    HUD.showFail(errMsg)
                }
            }.disposed(by: disposeBag)
        
        viewModel
            .windowSubject
            .subscribe { [weak self] event in
                guard let self = self else { return }
                guard let status = event.element else { return }
                HUD.hideAllHUD()
                switch status {
                case .success(let msg):
                    self.handleWindow(msg: msg)
                case .failed(let errMsg):
                    HUD.showFail(errMsg)
                }
            }.disposed(by: disposeBag)
        
        viewModel
            .logoffSubject
            .subscribe { event in
                guard let status = event.element else { return }
                switch status {
                case .success:
                    UserInfo.logout()
                    let loginCtrl = LoginVC()
                    loginCtrl.needAutoBackAfterDone = true
                    loginCtrl.popToRoot = true
                    let vc = AppDelegate.curDisplayVC()
                    vc.navigationController?.pushViewController(loginCtrl, animated: true)
                    NotificationCenter.default.post(name: LCKey.noti_logout, object: nil)
                case .failed(let errMsg):
                    HUD.showFail(errMsg)
                }
            }.disposed(by: disposeBag)
        
        viewModel.queryPage()
    }
    
    private func handleWindow(msg: String) {
        let data = msg.data(using: .utf8)
        guard let hpple = TFHpple(htmlData: data) else { return }
        var title: String = ""
        var content: String = ""
        if let peekElement = hpple.peekAtSearch(withXPathQuery: "//html") {
            if peekElement.hasChildren(), let children = peekElement.children as? [TFHppleElement] {
                for child in children {
                    if child.tagName.contains("body"), child.hasChildren(),
                        let childs = child.children as? [TFHppleElement] {
                        for side in childs {
                            if let row = side.raw, row.contains("title") {
                                title = title + side.content.withoutSpacesAndNewLines
                            } else {
                                content = content + side.content.withoutSpacesAndNewLines
                            }
                        }
                    }
                }
            }
        }
        let leftOption = AlertOption.gray(title: "确认注销") { [weak self] in
            guard let self = self else { return }
            print("确认注销")
            self.viewModel.logoff()
        }
        let rightOption = AlertOption.main(title: "取消", action: nil)
        AlertView.show(leftOption: leftOption,
                       rightOption: rightOption,
                       title: title,
                       message: content)
    }
}

extension LogOutVC: Routable {
    static func initRouteParams(params: [String: Any]?) -> UIViewController {
        return LogOutVC()
    }
}
