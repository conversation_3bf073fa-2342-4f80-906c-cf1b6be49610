//
//  InformationListVC.swift
//  LivePlus
//
//  Created by simon on 17.2.22.
//

import Foundation
import RxSwift
import UIKit
import Photos


enum AuthorizationTitle: String {
    case video = "相机权限"
    case audio = "麦克风权限"
    case photo = "系统相册读写权限"
    case network = "本地网络"
    case location = "位置信息权限"
}

enum AuthorizationDes: String {
    case video = "使用“快瓴中控台”进行扫描二维码，需要开启相机权限以采集摄像头画面"
    case audio = "使用“快瓴中控台”进行录音时，需要开启麦克风权限以采集声音"
    case photo = "保存拍摄视频、读取本地图片、视频时，需要使用该权限"
    case network = "使用“快瓴中控台”进行遥控时，需要开启本地网络权限"
    case location = "使用“快瓴中控台”进行遥控时，需要开启定位权限"
  
}

let content = "为了向您提供更好的用户体验\n快瓴中控台会在特定场景下向您申请以下手机系统权限"

let title1 = "相机权限"

let title3 = "麦克风权限"
let title4 = "系统相册读写权限"
//let title2 = "本地网络权限"
//let title2 = "本地网络权限"

let content1 = "使用“快瓴中控台”进行扫描二维码，需要开启相机权限以采集摄像头画面"
//let content2 = "需要使用本地网络连接进行遥控功能"
let content3 = "使用“快瓴中控台”进行直播/拍摄，需要开启麦克风权限以采集声音"
let content4 = "保存拍摄视频、读取本地图片、视频时，需要使用该权限"

fileprivate let kInformationListVCId = "InformationListVCId"

fileprivate let contentH = CGFloat(70.0)

/// 设置页
class InformationListVC: BaseVC {

    let location: CLLocationManager = CLLocationManager()
    
    //列表区域
    var itemsTitleArr: [AuthorizationTitle] = [.audio]
    var itemsContArr: [AuthorizationDes] = [.audio]
    let myTable = UITableView(frame: CGRect(x: 0, y: LCDevice.Nav_H + contentH + 16, width: LCDevice.screenW, height: LCDevice.screenH-LCDevice.Nav_H - contentH), style: .grouped)
    
    override func viewDidLoad() {
        super.viewDidLoad()
      
        // Do any additional setup after loading the view.
        view.backgroundColor = .white
        view.addSubview(getNavViewWithItem(titleStr: "系统权限管理", leftImageName: "icon_nav_back_gray", rightImageName: nil, color: UIColor.white))
        addTableView()
        let lab = UILabel()
        lab.backgroundColor = .clear
        lab.numberOfLines = 0
        lab.text = content
        lab.textColor = UIColor("#4D4E52")
        lab.font = LCDevice.DIN_Font_PF_M(14)
        self.view.addSubview(lab)
        
        lab.mas_makeConstraints { maker in
            maker?.left.equalTo()(view)?.offset()(16)
            maker?.top.equalTo()(view)?.offset()(LCDevice.Nav_H + 16)
            maker?.width.equalTo()(LCDevice.screenW - 32)
            maker?.height.equalTo()(contentH)
        }
        
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        myTable.reloadData()
    }

    // MARK: - UI
    func addTableView() {
        myTable.backgroundColor = .clear//UIColor("#F0F1F7")
        myTable.dataSource = self
        myTable.delegate = self
        myTable.separatorStyle = .none
        myTable.register(InformationListCell.self, forCellReuseIdentifier: kInformationListVCId)
        view.addSubview(myTable)
    }
  
}

extension InformationListVC: UITableViewDelegate {
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
    }
}

extension InformationListVC: UITableViewDataSource {
            
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return LCDevice.DIN_WIDTH(90)
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return 1
    }
    
    func numberOfSections(in tableView: UITableView) -> Int {
        return itemsTitleArr.count
    }
    
    func tableView(_ tableView: UITableView, heightForHeaderInSection section: Int) -> CGFloat {
        return LCDevice.DIN_WIDTH(1)
    }
   
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell: InformationListCell = tableView.dequeueReusableCell(withIdentifier: kInformationListVCId, for: indexPath) as! InformationListCell
        cell.delegate = self
        var open = false
        let title = itemsTitleArr[indexPath.section]
        
        switch title {
        case .video:
            if AVCaptureDevice.authorizationStatus(for: .video) == .authorized {
                open = true
            }
        case .audio:
            if AVCaptureDevice.authorizationStatus(for: .audio) == .authorized {
                open = true
            }
        case .photo:
            if PHPhotoLibrary.authorizationStatus() == .authorized {
                open = true
            }
        case .network:
            break
        case .location:
            if CLLocationManager.authorizationStatus() == .authorizedWhenInUse {
                open = true
            }
            
        }
        
        cell.config(with: title.rawValue, content: itemsContArr[indexPath.section].rawValue, open: open, index: Int(indexPath.section))
        return cell
    }
    
}

extension InformationListVC: InformationListCellDelegate {
    func  gotoSystemSeting(index: Int ) {
        let title = itemsTitleArr[index]
        switch title {
        case .video:
            if AVCaptureDevice.authorizationStatus(for: .video) == .notDetermined {
                AVCaptureDevice.requestAccess(for: .video) {  [weak self]  _ in
                    guard let self = self else { return }
                    DispatchQueue.main.async {
                        self.myTable.reloadData()
                    }
                }
                return
            }
        case .audio:
            if AVCaptureDevice.authorizationStatus(for: .audio) == .notDetermined {
                AVCaptureDevice.requestAccess(for: .audio) { [weak self] _ in
                    guard let self = self else { return }
                    DispatchQueue.main.async {
                        self.myTable.reloadData()
                    }
                }
                return
            }
        case .photo:
            if PHPhotoLibrary.authorizationStatus() == .notDetermined {
                PHPhotoLibrary.requestAuthorization { [weak self] _ in
                    guard let self = self else { return }
                    DispatchQueue.main.async {
                        self.myTable.reloadData()
                    }
                    
                }
                return
            }
        case .location:
            if #available(iOS 13.0, *) {
                if ( CLLocationManager.authorizationStatus() == .notDetermined ) {
                    location.delegate = self
                    location.requestWhenInUseAuthorization()
                }
                
            }
        default:
            break
        }
        
        Router.openSystemSetter()
        
    }
    
}

extension InformationListVC: CLLocationManagerDelegate {
    func locationManagerDidChangeAuthorization(_ manager: CLLocationManager) {
        DispatchQueue.main.async {
            self.myTable.reloadData()
        }
    }
    
    func locationManager(_ manager: CLLocationManager, didChangeAuthorization status: CLAuthorizationStatus) {
        DispatchQueue.main.async {
            self.myTable.reloadData()
        }
    }
}
