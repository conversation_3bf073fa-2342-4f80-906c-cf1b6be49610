//
//  MemberPointsPayVC.swift
//  LiveConsole
//
//  Created by simon on 22.5.25.
//



/// 会员积分充值
import Foundation
import UIKit

class MemberPointsPayVC: BaseVC {
        
    lazy var backButton: UIButton = {
        let button = UIButton()
        button.setImage(UIImage(named: "gift_close"), for: .normal)
        button.addTarget(self, action: #selector(backAction), for: .touchUpInside)
        return button
    }()
    
    lazy var contentView: UIImageView = {
        let view = UIImageView()
        view.backgroundColor = .clear
        view.image = UIImage(named: "vip_new_bg")
        view.isUserInteractionEnabled = true
        return view
    }()
    
    
    lazy var tipView: UIView = {
        let view = UIImageView()
        view.backgroundColor = .clear
        view.image = UIImage(named: "vip_new_p_tip_bg")
        view.isUserInteractionEnabled = true
        return view
    }()
    
    
    lazy var titleLab: UILabel = {
        let label = UILabel()
        label.textColor = .white
        label.font = UIFont.systemFont(ofSize: 18, weight: .medium)
        label.text = "充值积分"
        return label
    }()
    
    lazy var tipLabel: UILabel = {
        let label = UILabel()
        label.textColor = .white
        label.font = UIFont.systemFont(ofSize: 14, weight: .regular)
        label.numberOfLines = 0
        label.text = "积分不可转赠或提现。每次成功购买积分后，您账户内所有积分的有效期都将从该次购买日起自动刷新并顺延3个月。若自最近一次购买后，连续3个月内无新的购买行为，则届时账户内全部积分将自动到期清零。"
        return label
    }()
    
    /// vip标识
    lazy var vipIcon: UIImageView = {
        let imageView = UIImageView()
        return imageView
    }()
    
    
    lazy var twoLayout: UICollectionViewLayout = {
        let layout = UICollectionViewFlowLayout()
        layout.itemSize = CGSize(width: (LCDevice.screenW - 16 * 2 - 14) / 2, height: 93)
        layout.minimumLineSpacing = 12
        layout.minimumInteritemSpacing = 12
        layout.sectionInset = UIEdgeInsets(top: 10, left: 16, bottom: 10, right: 16)
        return layout
    }()
        
    lazy var memberCollection: UICollectionView = {
        let collectionView = UICollectionView(frame: CGRect.zero, collectionViewLayout: self.twoLayout)
        collectionView.backgroundColor = UIColor.clear
        collectionView.register(cellWithClass: PointsPayCell.self)
        collectionView.delegate = self
        collectionView.dataSource = self
        collectionView.delaysContentTouches = false // 防止选中状态很快消失
        collectionView.showsHorizontalScrollIndicator = false
        return collectionView
    }()

    
    lazy var unlockButton: UIButton = {
        let button = UIButton()
        button.setTitle("立即充值", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.titleLabel?.font = LCDevice.DIN_Font_PF_M(16)
        button.cornerRadius = 23
        button.addTarget(self, action: #selector(openBtnAction), for: .touchUpInside)
        button.backgroundColor = UIColor("#6974F2")
        button.applyGradient()
        return button
    }()
    
    lazy var purchaseProtocol: YYLabel = {
        let label = YYLabel()
        label.text = "本品为虚拟产品,不含人工服务,一旦激活后不支持退款,点击同意《快瓴中控台积分规则》"
        label.numberOfLines = 0
        return label
    }()
  
    
    /// 选中协议
    lazy var checkButton: UIButton = {
        let button = UIButton { [weak self] in
            guard let self = self else { return }
            self.checkButton.isSelected = !self.checkButton.isSelected
        }
        button.setImage(UIImage(named: "登录流程_组件_cell_勾选_nor"), for: .normal)
        button.setImage(UIImage(named: "icon_勾选_sel"), for: .selected)
        
        button.zl_enlargeValidTouchArea(insets: UIEdgeInsets(top: 10, left: 30, bottom: 12, right: 20))
        return button
    }()
    
    /// 会员的数据源
    var purchasingList: [PointsProductsModel] = []
    
    override func viewDidLoad() {
        super.viewDidLoad()
        makeUI()
        business()

    }
    
    deinit {
       
        NotificationCenter.default.removeObserver(self)
    }
    
    func backAction() {
        self.navigationController?.popViewController(animated: true)
    }
    
    func makeUI() {
        view.backgroundColor = UIColor("#191C20")
        view.addSubview(contentView)
        
        contentView.addSubviews([ memberCollection, tipView, backButton, checkButton, purchaseProtocol, titleLab, unlockButton])
                
        tipView.addSubviews([tipLabel])
                
        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        backButton.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(LCDevice.Nav_H - 45)
            make.leading.equalToSuperview().inset(10)
            make.width.height.equalTo(40)
        }
        
        titleLab.snp.makeConstraints { make in
            make.centerY.equalTo(backButton.snp.centerY)
            make.leading.equalToSuperview().inset(56)
        }
        

        memberCollection.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(LCDevice.Nav_H + 120)
            make.height.equalToSuperview().inset(300)
            make.leading.trailing.equalToSuperview()
        }
        
       
        unlockButton.snp.makeConstraints { make in
            make.top.equalTo(memberCollection.snp.bottom).offset(24)
            make.leading.trailing.equalToSuperview().inset(16)
            make.height.equalTo(46)
        }
       
        purchaseProtocol.snp.makeConstraints { make in
            make.top.equalTo(unlockButton.snp.bottom).offset(24)
            make.leading.equalToSuperview().inset(50)
            make.trailing.equalToSuperview().inset(16)
            make.height.equalTo(46)
        }
        
        
        checkButton.snp.makeConstraints { make in
            make.trailing.equalTo(purchaseProtocol.snp.leading).offset(-15)
            make.centerY.equalTo(purchaseProtocol.snp.centerY)
            make.width.height.equalTo(20)
        }
        
        
        tipView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.bottom.equalToSuperview().inset(68)
            make.width.equalTo(347)
            make.height.equalTo(142)
        }
        
        tipLabel.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview().inset(12)
            make.bottom.equalToSuperview().inset(10)
            make.top.equalToSuperview().inset(13)
        }
        
        
        deployProtocol()
    }
    
    func business() {
        backButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self else { return }
                self.navigationController?.popViewController()
            }.disposed(by: disposeBag)
        
        getData()
    }
        
}

extension MemberPointsPayVC: UICollectionViewDelegate, UICollectionViewDataSource, UICollectionViewDelegateFlowLayout {
    
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return purchasingList.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        // 内购项目
        let cell = collectionView.dequeueReusableCell(withClass: PointsPayCell.self, for: indexPath)
        cell.bind(to: purchasingList[indexPath.row])
        return cell
       
    }
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        HapticFeedback.Impact.light()
        purchasingList.forEach({$0.defaultPro = false})
        let m = purchasingList[indexPath.row]
        m.defaultPro = true
        collectionView.reloadData()
            
    }
}



extension MemberPointsPayVC {
    func deployProtocol() {
        let infoString = "本品为虚拟产品,不含人工服务,一旦激活后不支持退款,点击同意《快瓴中控台积分规则》"
        let agreementFS = "《快瓴中控台积分规则》"
        let agreementRange = infoString.range(of: agreementFS)!
        let agreementLinkRange = NSRange(agreementRange, in: infoString)
                
        let attString = NSMutableAttributedString.init(string: infoString)
        attString.font = LCDevice.DIN_Font_PF_R(12)
        attString.alignment = .left
        attString.color = UIColor("#FFFFFF")
        attString.setTextHighlight(agreementLinkRange, color: UIColor("#FFFFFF"), backgroundColor: nil) { (_ containerView, _ attText, _ range, _ rect) in
            Router.openPointsRulesAgreement()
        }
        purchaseProtocol.attributedText = attString
    }
}

