//
//  LoginScanVC.swift
//  LivePlus
//
//  Created by simon on 11.6.24.
//

import Foundation
import swiftScan

class LoginScanVC: BaseVC {
    
    lazy var resultView: UIView = {
        let vi = UIView()
        vi.backgroundColor = .white
        vi.isHidden = true
        return vi
    }()
    
    lazy var iconView: UIImageView = {
        let vi = UIImageView()
        vi.image = UIImage(named: "手机 1")
        return vi
    }()
    
    lazy var titleLab: UILabel = {
        let vi = UILabel()
        vi.text = "多设备登录提醒"
        vi.textColor = UIColor("#1E1F20")
        vi.font = UIFont.systemFont(ofSize: 20, weight: .heavy)
        vi.textAlignment = .center
        return vi
    }()
    
    lazy var desLab: UILabel = {
        let vi = UILabel()
        vi.numberOfLines = 0
        let paragraphStyle = NSMutableParagraphStyle()
        paragraphStyle.lineSpacing = 6
        let attribute = NSMutableAttributedString(string: "若本次授权登录后，登录设备数超上限，则较早登录的设备会自动登出", attributes: [.foregroundColor: UIColor("#4D4E52"), .font: UIFont.systemFont(ofSize: 15), NSAttributedString.Key.paragraphStyle :paragraphStyle ])
        vi.attributedText = attribute
        vi.textAlignment = .center
        return vi
    }()
    
    /// 渐变层
//    lazy var gradientLayer: CAGradientLayer = {
//        let leftColor = UIColor("#6974F2")
//        let rightColor = UIColor("#AC63F9")
//        let gradientColors = [leftColor.cgColor, rightColor.cgColor]
//        let gradientLocations: [NSNumber] = [0.0, 1.0]
//        let gradientLayer = CAGradientLayer()
//        gradientLayer.startPoint = CGPoint(x: 0, y: 0)
//        gradientLayer.endPoint = CGPoint(x: 1, y: 0)
//        gradientLayer.colors = gradientColors
//        gradientLayer.locations = gradientLocations
//        return gradientLayer
//    }()
    
    lazy var okButton: UIButton = {
        let button = UIButton(frame: CGRect(x: 0, y: 0, width: 256, height: 46))
        button.setTitle("授权登录", for: .normal)
        button.backgroundColor = UIColor("#F2728F")
        button.setTitleColor(UIColor.white, for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .regular)
        button.addTarget(self, action: #selector(okAction), for: .touchUpInside)
        button.cornerRadius = 23
        
        return button
    }()
    
    lazy var cancelButton: UIButton = {
        let button = UIButton(frame: CGRect(x: 0, y: 0, width: 78, height: 46))
        button.setTitle("取消登录", for: .normal)
        button.backgroundColor = .clear
        button.setTitleColor(UIColor("#4D4E52"), for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .regular)
        button.addTarget(self, action: #selector(cancelAction), for: .touchUpInside)
        button.cornerRadius = 23
        return button
    }()
    
    lazy var scanVC: LiveScanVC = {
        let vc = LiveScanVC()
        vc.scanResultDelegate = self
        vc.backButton.isHidden = true
        vc.titleLabel.text = "请扫码需登录手机上显示的二维码"
        return vc
    }()
    
    lazy var nav: UINavigationController = {
        let nav = UINavigationController(rootViewController: scanVC)
        nav.view.backgroundColor = UIColor.clear
        nav.setNavigationBarHidden(true, animated: false)
        return nav
    }()
    
    var scanCode: String?
    
    override func viewDidLoad() {
        super.viewDidLoad()
        UIApplication.shared.isIdleTimerDisabled = true
        makeUI()
//        okButton.layer.insertSublayer(gradientLayer, at: 0)
        
    }

    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
//        gradientLayer.frame = okButton.bounds
    }
    
    func makeUI() {
        let navgation = getNavViewWithItem(titleStr: "扫一扫", leftImageName: "icon_nav_back_gray", rightImageName: nil, color: UIColor.white)
        navgation.isUserInteractionEnabled = true
        view.addSubview(navgation)
            
        self.addChild(nav)
        view.addSubview(nav.view)
        nav.view.snp.makeConstraints { make in
            make.leading.bottom.trailing.equalToSuperview()
            make.top.equalTo(navgation.snp.bottom)
        }
        
        view.addSubview(resultView)
        resultView.snp.makeConstraints { make in
            make.top.bottom.leading.trailing.equalToSuperview()
        }
        
        resultView.addSubviews([iconView, titleLab, desLab, okButton, cancelButton])
        
        iconView.snp.makeConstraints { make in
            make.height.width.equalTo(162)
            make.top.equalToSuperview().inset(126)
            make.centerX.equalToSuperview()
        }
        
        titleLab.snp.makeConstraints { make in
            make.height.equalTo(30)
            make.top.equalTo(iconView.snp.bottom).offset(60)
            make.leading.trailing.equalToSuperview()
        }
        
        desLab.snp.makeConstraints { make in
            make.top.equalTo(titleLab.snp.bottom).offset(20)
            make.leading.trailing.equalToSuperview().inset(60)
        }
        
        okButton.snp.makeConstraints { make in
            make.width.equalTo(256)
            make.height.equalTo(46)
            make.top.equalTo(desLab.snp.bottom).offset(80)
            make.centerX.equalToSuperview()
        }
        
        cancelButton.snp.makeConstraints { make in
            make.width.equalTo(256)
            make.height.equalTo(46)
            make.top.equalTo(okButton.snp.bottom).offset(10)
            make.centerX.equalToSuperview()
        }
        
    }
    
    
    @objc func okAction() {
        guard let user = UserInfo.currentUser(), let userId = user.userId, let uudi = self.scanCode  else { return  }
        HUD.showWait()
        MiddleRequestNet.qrConfirm(uuid: uudi, userid: "\(userId)", action: .confirm) {[weak self]  mode in
            guard let self = self else { return }
            DispatchQueue.main.async {
                HUD.hideAllHUD()
                if mode.code != 0 {
                    // 重新扫码
                    if let msg = mode.msg {
                        HUD.showSuccess(msg)
                    }
                } else {
//                    if let msg = mode.msg {
//                        HUD.showSuccess(msg)
//                    }
                    HUD.showFail("授权登录成功")
                    self.leftButtonAction()
                }
            }
        }
    }
    
    @objc func cancelAction() {
        leftButtonAction()
        guard let user = UserInfo.currentUser(), let userId = user.userId, let uudi = self.scanCode  else { return  }
        
        MiddleRequestNet.qrConfirm(uuid: uudi, userid: "\(userId)", action: .cancel) { [weak self] mode in
            
        }
    }
    
    func scanFinished(message: String){
        guard let user = UserInfo.currentUser(), let userId = user.userId else { return  }
        self.scanCode = message
        self.scanVC.scanResultDelegate = nil
        HUD.showWait()
        // 查询结果
        MiddleRequestNet.getQRScan(uuid: message, userid: "\(userId)") { [weak self] model in
            guard let self = self else { return }
            DispatchQueue.main.async {
                HUD.hideAllHUD()
               
                if model.code != 0 {
                    // 重新扫码
                    if let msg = model.msg {
                        self.showError(msg: msg)
                    }
                } else {
                    self.resultView.isHidden = false
//                    if let msg = model.msg {
//                        HUD.showSuccess(msg)?.isUserInteractionEnabled = false
//                    }
                }
            }
        }
    }
    
    func showError(msg: String) {
        let alert = LLAlertView(title: "温馨提示", message: "\n" + msg , leftShow: ("取消", .defaultColor), rightShow: ("重试", .mainColor)) {[ weak self] in
            guard let self = self else { return }
            self.leftButtonAction()
        } rightAction: { [ weak self] in
            guard let self = self else { return }
            self.startScan()
        }
        alert.show(with: self)
    }
    
    func startScan() {
        scanVC.scanResultDelegate = self
        self.scanVC.startScan()
        self.resultView.isHidden = true
    }
    
}

extension LoginScanVC: LBXScanViewControllerDelegate {
    func scanFinished(scanResult: LBXScanResult, error: String?) {
        guard error == nil else { return }
        guard LCDevice.checkNetIsOk() else { return }

        if let message = scanResult.strScanned, message.hasPrefix("ckzs") {
            // 扫描结果  调用接口 上报给服务端
            self.scanFinished(message: message)
        } else {
            self.showError(msg: "请扫描正确的二维码")
        }
    }
}
