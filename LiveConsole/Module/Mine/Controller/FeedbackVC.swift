//
//  FeedbackVC.swift
//  LivePlus
//
//  Created by iclick on 2020/12/25.
//

import UIKit

/// 反馈意见
class FeedbackVC: BaseVC {

    override func viewDidLoad() {
        super.viewDidLoad()

        // Do any additional setup after loading the view.
        view.backgroundColor = SColor.RGBA(r: 242, g: 242, b: 242)
        view.addSubview(getNavViewWithItem(titleStr: "反馈建议", leftImageName: "icon_nav_back_gray", rightImageName: nil))
        let bgView = UIImageView(frame: CGRect(x: 0, y: LCDevice.Nav_H, width: LCDevice.screenW, height: LCDevice.DIN_WIDTH(163)))
        bgView.image = UIImage(named: "icon_setting_fb_bg")
        view.addSubview(bgView)
        
        let qrBgView = UIImageView(frame: CGRect(x: LCDevice.DIN_WIDTH(18), y: LCDevice.DIN_WIDTH(155), width: LCDevice.DIN_WIDTH(339), height: LCDevice.DIN_WIDTH(446)))
        qrBgView.image = UIImage(named: "icon_setting_fb_qrBg")
        view.addSubview(qrBgView)
        
        let infoA = UILabel(frame: CGRect(x: 0, y: LCDevice.DIN_WIDTH(50), width:qrBgView.width , height: 30))
        infoA.text = "请添加客服微信"
        infoA.textColor = UIColor("#1E1F20")
        infoA.font = LCDevice.DIN_Font_Bold(18)
        infoA.textAlignment = .center
        qrBgView.addSubview(infoA)
        
        let infoB = UILabel(frame: CGRect(x: 0, y:infoA.bottom, width:qrBgView.width , height: 30))
        infoB.text = "留下您的宝贵建议"
        infoB.textColor = UIColor("#6974F2")
        infoB.font = LCDevice.DIN_Font_Bold(14)
        infoB.textAlignment = .center
        qrBgView.addSubview(infoB)
        
        let qrImgV = UIImageView(frame: CGRect(x: LCDevice.DIN_WIDTH(77), y: LCDevice.DIN_WIDTH(136), width:LCDevice.DIN_WIDTH(185) , height: LCDevice.DIN_WIDTH(185)))
        qrImgV.image = UIImage(named: "icon_feedback_qr")
        qrBgView.addSubview(qrImgV)
        
        let infoC = UILabel(frame: CGRect(x: 0, y:qrImgV.bottom + LCDevice.DIN_WIDTH(8), width:qrBgView.width , height: 30))
        infoC.text = "扫描上方二维码"
        infoC.textColor = UIColor("#919499")
        infoC.font = LCDevice.DIN_Font_PF_M(14)
        infoC.textAlignment = .center
        qrBgView.addSubview(infoC)
    }
    

    /*
    // MARK: - Navigation

    // In a storyboard-based application, you will often want to do a little preparation before navigation
    override func prepare(for segue: UIStoryboardSegue, sender: Any?) {
        // Get the new view controller using segue.destination.
        // Pass the selected object to the new view controller.
    }
    */

}
