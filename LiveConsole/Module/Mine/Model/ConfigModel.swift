//
//  ConfigModel.swift
//  LivePlus
//
//  Created by iclick on 2021/1/22.
//

import UIKit

fileprivate let kConfigSaveKey = "currentUserConfig"

fileprivate let kVIPConfigSaveKey = "VIPConfigSaveKey"

fileprivate let kVersionUpdateSaveKey = "VersionUpdateSaveKey"

fileprivate let kImportantAlertSaveKey = "ImportantAlertSaveKey"

/// 直播间引导图片
struct LiveRoomImagesModel: Codable {
    var img: String = ""      //图片url
    var url: String?          //跳转url
    var startx: String?
    var starty: String?
    var endx: String?
    var endy: String?
    public init(imgUrl: String = "",
                jumpUrl: String = "",
                startx: String = "",
                starty: String = "",
                endx: String = "",
                endy: String = "") {
        
        self.img = imgUrl
        self.url = jumpUrl
        self.startx = startx
        self.starty = starty
        self.endx = endx
        self.endy = endy
    }
}

/// 版本更新信息
struct VersionUpdateInfoModel: Codable {
    var newVersion: String? //"1.0.2"
    var isUpdate: Bool?
    var forceUpdate: Bool?
    var apkUrl: String?
    var updateDescription: String?
    var title: String?
    var apkMD5: String?
    
    /// 保存版本更新信息
    func saveVersionUpdateInfo() {
        let json = JsonTool.model2String(self)
        if json.count > 0 {
            UserDefaults.standard.set(json, forKey: kVersionUpdateSaveKey)
        }
    }
    /// 获取本地保存的版本更新信息
    static func currentVersionUpdateInfo() -> VersionUpdateInfoModel? {
        guard let json = UserDefaults.standard.string(forKey: kVersionUpdateSaveKey) else {
            return nil
        }
        let version: VersionUpdateInfoModel = JsonTool.string2Model(json) ?? VersionUpdateInfoModel()
        return version
    }    
}


///公共配置
struct ConfigModel: Codable {

    var staticResource: ConfigStaticResourceModel
    var dynamicResource: [ConfigDynamicResourcesItemModel]
    var config: SubConfigModel?
    var banners:[ConfigDynamicResourcesBanner]
    var template:ConfigTemplate?
    
    
    func saveUserConfig() {
        let json = JsonTool.model2String(self)
        if json.count > 0 {
            UserDefaults.standard.set(json, forKey: kConfigSaveKey)
        }
    }
    
    static func currentUserConfig() -> ConfigModel? {
        guard let json = UserDefaults.standard.string(forKey: kConfigSaveKey) else {
            return nil
        }
        return JsonTool.string2Model(json)
    }
    
    
    static func getDynamicResources(key: String)-> ConfigDynamicResourcesItemModel? {
        guard let resource = ConfigModel.currentUserConfig()?.dynamicResource.first(where: { $0.key == key }) else { return nil}
        return resource
        
    }
    
    static func geConfigTemplate(type: ReplyMsgType) -> String? {
        switch type {
        case .keyword:
            return ConfigModel.getKeywordConfigTemplate()
        case .good:
            return ConfigModel.getGiftConfigTemplate()
        case .attention:
            return ConfigModel.getFollowConfigTemplate()
        }
    }
    
    static func getKeywordConfigTemplate() -> String? {
        guard let resource = ConfigModel.currentUserConfig()?.template?.comment?.first else { return nil}
        return resource
        
    }
    
    static func getGiftConfigTemplate() -> String? {
        guard let resource = ConfigModel.currentUserConfig()?.template?.gift?.first else { return nil}
        return resource
        
    }
    
    static func getFollowConfigTemplate() -> String? {
        guard let resource = ConfigModel.currentUserConfig()?.template?.follow?.first else { return nil}
        return resource
        
    }
    
    //  是否需要使用缓存
    static func webCache(key: String) -> Bool {
        if let configModel = ConfigModel.currentUserConfig() {
            let arr = configModel.dynamicResource
            if let item = arr.first(where: {$0.key == key}) , let verison = item.action {
                // 查看本地缓存的版本
                if let currVersion =  UserDefaults.standard.value(forKey: key) as? String {
                    if currVersion == verison {
                        return true
                    }
                }
            }
        }
        return false
    }
   
        

    
}


struct ConfigStaticResourceModel: Codable {
    var userAgreement: String!//用户政策
    var privacyAgreement: String!//隐私协议
    var privacyAgreementPdf: String!
    
    var sdkList: String!
    var vipCenterDetail: String
 
    var userCollectionList: String?
    var userCollectionSendEmail: String?
    var vipcenter_detail_h5: String?
    var memberAgreement: String?// 会员协议
    var pointsRulesAgreement: String?
}

struct ConfigDynamicResourcesItemModel: Codable {
    var name: String!
    var key: String!
    var url: String?
    var image: String?
    var action: String?
    var value: String?
}

struct ConfigDynamicResourcesBanner: Codable {
    var url: String
    var image: String
    var name: String
    var active: Int
}

struct ConfigTemplate: Codable {
    var gift: [String]?
    var follow: [String]?
    var comment: [String]?
}


struct SubConfigModel: Codable {
    var creativeUrl: String?
    var cloudCreativeUrl: String?
    var cloudCreativeUPUrl: String?
    var liveroomUrl: String?
    var freeDays: UInt?
    var betaTime: String?
}
