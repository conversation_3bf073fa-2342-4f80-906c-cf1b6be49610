//
//  InPurchasingModel.swift
//  LivePlus
//
//  Created by Brian-1 on 2021/11/1.
//

import UIKit
import Realm
import RealmSwift

/// 购买列表
//struct PurchasingModels:Codable {
//
//    var slideshows: [PurchasingModel]
//
//}

///  购买
struct PurchasingModel: Codable {
    var productId: String? = ""
    var name: String? = ""
    var discountPrice: Float? = 0
    var originalPrice: Float? = 0
    var originalPriceStr: String?
    var mainPic: String? = ""
    var sku: String = ""
    var isSelect: Bool? = false
    // 第一个是价格左边的图标 第二个是角标
    var pic: [String]
    var defaultPro: Bool?
    var type: String?
    var productType: String?
    var subName: String? //子标题
    var remark: String? // 商品的标记信息
    var memberLevel: Int
    var memberLevelStr: String // 标准帐户会员
    var typeName: String?
}

///  积分购买
class PointsProductsModel: NSObject, Codable {
    var name: String = ""
    var pic: [String]?
    var defaultPro: Bool
    var originalPrice: Float?
    var originalPriceStr: String?
    
    var discountPrice: Float?
    var discountPriceStr: String?
    var typeName: String
    var desc: String?
    var type: String?
    var version: String?
    var sku: String
    var id: Int
    
    init(name: String, pic: [String]?, defaultPro: Bool, originalPrice: Float? = nil, originalPriceStr: String? = nil, discountPrice: Float? = nil, discountPriceStr: String? = nil, typeName: String, desc: String? = nil, type: String? = nil, version: String? = nil, sku: String, id: Int) {
        self.name = name
        self.pic = pic
        self.defaultPro = defaultPro
        self.originalPrice = originalPrice
        self.originalPriceStr = originalPriceStr
        self.discountPrice = discountPrice
        self.discountPriceStr = discountPriceStr
        self.typeName = typeName
        self.desc = desc
        self.type = type
        self.version = version
        self.sku = sku
        self.id = id
    }
   
   
}



// 订阅验证
struct SubscribeVerify: Codable {
    var msg: String? = ""
}

/// 特权
struct MemberPrivilegeModel: Codable {
    var code: String?
    var params: String?
    var content: String?
}

/// 特权
struct PrivilegeModel: Codable {
    var name: String?
    var pic: String?
    var pic2: String?
    var pic3: String?
}

/// 会员限制
struct OrderLimitModel:Codable {
    var msg: String?
    var code: Int?
}

class InPurchasingModel: Object {
    @objc dynamic var code: String = ""
    @objc dynamic var type: String = ""
    @objc dynamic var phone: String = ""
    @objc dynamic var timestamp: Int = 0
    /// 刷新本地用户信息
    
    convenience init(type: String, code: String, phone: String, timestamp: Int) {
        self.init()
        self.type = type
        self.code = code
        self.phone = phone
        self.timestamp = timestamp
    }
    
    /// 保存
    public static func saveModel(model: InPurchasingModel) {
        LCLog.d("---保存支付凭证到本地---")
        DataBaseManager.addObject(object: model)
    }
    
    /// 根据手机号删除
    public static func deleteInPurchasingModelsPhone(phones: [String]) {
        LCLog.d("---删除本地支付凭证---")
        for phone in phones {
            let predicate = NSPredicate(format: "phone == %@", phone)
            DataBaseManager.deleteObjectFilter(objectClass: InPurchasingModel(), filter: predicate)
        }
    }
    
    /// 删除
    public static func deleteInPurchasingModels(models: [InPurchasingModel]) {
        LCLog.d("---删除本地支付凭证---")
        for mode in models {
            let predicate = NSPredicate(format: "phone == %@", mode.phone)
            DataBaseManager.deleteObjectFilter(objectClass: InPurchasingModel(), filter: predicate)
        }
    }
    
    /// - Returns: 结果
    public static func queryInPurchasingModels(phone: String, completion: @escaping (([InPurchasingModel]?) -> Void )) {
        LCLog.d("---本地查询支付凭证---")
        let filter = "phone == '\(phone)'"
        // 取
        let curModels = DataBaseManager.queryObject(objectClass: InPurchasingModel(), filter: filter)
        // 过滤超过7天的凭证，并删除
        if !curModels.isEmpty {
            var deleMo: [InPurchasingModel] = []
            var remo: [InPurchasingModel] = []
            for mode in curModels {
                let formate = DateFormatter.init()
                formate.locale = Locale(identifier: "en_US_POSIX")
                formate.timeZone = TimeZone(abbreviation: "GMT")
                formate.dateFormat = "yyyy-MM-dd HH:mm:ss"
                let days = LCTools.getDaysForNowFromTime(fromTime: formate.string(from: NSDate.init(timeIntervalSince1970: TimeInterval(mode.timestamp)) as Date))
                if days < -7 {
                    deleMo.append(mode)
                } else {
                    remo.append(mode)
                }
                
            }
            InPurchasingModel.deleteInPurchasingModels(models: deleMo)
            completion(remo)
        } else {
            completion(nil)
        }
        
    }
    
}
