//
//  UserInfo.swift
//  LivePlus
//
//  Created by iclick on 2020/12/23.
//

import UIKit
import SwiftyStoreKit

let KUserDefault_userJson = "currentUserJson"

///  微信 token
struct WechatTokenData:Codable {
    
    var access_token:String?
    
}

///  轮播图
struct CycleImages:Codable {
    
    var slideshows: [CycleImageObj]?
    
}

///
struct CycleImageObj:Codable {
    
    var image: String?
    var action: String?
    var url: String?
    var name: String?
}

/// 兑换激活码
struct RightCodeData:Codable {
    var nickName: String?
    var headImage: String?
    var sex: UInt?
    var newUser: Bool?
    var userId: UInt?
    var bindPhone: Bool?
    var bindWechat: Bool?
    var userCode: String?
    var registerTime: String?
    var expireTime: String?
    var expireFlag: Bool?
    var betaFlag: Bool?
    var codeTime: String?
    var freeDays: UInt?
    var currentTime: String?
    var newCode: Bool?
    var result: String?
    
}

/// 会员限制
struct EmailUserModel:Codable {
    var content: String?
    var code: String?
    var params: String?
}

///   初始化直播间ID
struct LiveroomData: Codable {
    
    var roomId: String?
    
}

struct RoomShareData: Codable {
    var id: Int
    var roomId: String
    var roomKey: String
    var shareCode: String
    var userId: Int
    var activeDate: String
    var expireDate: String
    var createTime: String
    var modifyTime: String
    var modifyUser: Int
    var roomName: String
    var roomSize: String
}


///   初始用户code
struct UserCode: Codable {
    
    var userCode: String?
    
}


///  登录信息
struct LoginRespData: Codable {
    
    var token: String?
    var nickName: String?
    var headImgUrl: String?
    var userCode: String?
    var sex: String?
    var phone: String?
    var newUser: Bool?
    var userId: UInt?
    var bindPhone: Bool?//是否绑定手机
    var bindWechat: Bool?//是否绑定微信
    
    var needBindWechat: Bool? // 是否需要绑定微信
    
    var expireFlag: Bool?     //(boolean): true 超过有效期, false 为超过有效期
    var vipLevelId: VipLevel?
    
    var memberInfoList: [VipLevelInfo]?
    var audioInfo: AudioInfo?
    
    var trialVipExpireFlag: Bool? // 试用会员是否过期
    var trialVipLevelId : VipLevel?   // // 试用会员等级
    var trialVipLevel: String?   // 试用会员名称
    var trialVipLevels: [TrialVipLevel]?   // 试用会员详情]
    var trialExpireDays: Int? // 试用会员剩余天数
    var trialExpireTime: String? // 试用
    
    func saveLoginUser() {
        let json = JsonTool.model2String(self)
        if json.count > 0 {
            UserDefaults.standard.set(json, forKey: KUserDefault_userJson)
            UserDefaults.standard.synchronize()
        }
        
    }
}

public struct VipLevelInfo: Codable {
    var expiredDays: Int
    var expiredTime: String
    var beginTime: String // 会员的开始时间
    var expireFlag: Bool
    var level: VipLevel
    var levelName: String
}

public struct AudioInfo: Codable {
    var balanceWord: Int64
    var points:Int64
    var pointsExpiredTime: String?// 积分过期时间
}

enum VipLevel: Int, Codable {
    case normal = 1
    case vip = 2
    case svip = 3
    case explorenormal = 6 // 探索的免费
    case explorevip = 7 // 高级中控
    
    var name: String {
        switch self {
        case .normal:
            return "您当前还不是会员用户"
        case .vip:
            return "您当前是标准帐户会员"
        case .svip:
            return "您当前是高级帐户会员"
        case .explorenormal:
            return "您当前是标准帐户会员"
        case .explorevip:
            return "您当前是标准帐户会员"
        }
    }
    
    var des: String {
        switch self {
        case .normal:
            return ""
        case .vip:
            return "标准帐户会员权益"
        case .svip:
            return "高级帐户会员权益"
        case .explorenormal:
            return "标准帐户会员权益"
        case .explorevip:
            return "标准帐户会员权益"
        }
    }
    
}


enum PointsType: String, Codable {
    case purchase // 购买
    case refund // 退款积分
    case nick // 昵称合成
    case timbre // AI合成语音
    case reclaim // 回收
    case train // 克隆音色
    case langdu // 互动实时朗读用户昵称
    case other // 其它
    case hand // 手动扣除
}

// 积分的记录
public class PointsInfo: NSObject, Codable {
    var createTime: String = ""
    var endTime: String?
    var point: String = ""
    var type: PointsType
    var title: String = ""
    
    var preLen: Int64 = 0
    
    var expand: Bool? = false
    
    // 三种高度 70 90 113
    var height: CGFloat  {
        switch self.type {
        case .purchase:
            return  70
        case .refund:
            return  70
        case .nick:
            return  70
        case .timbre:
            if expand == true {
                return  113
            }
            return  90
        case .reclaim:
            return  70
        case .train:
            return  70
        case .langdu:
            if expand == true {
                return  113
            }
            return  90
        case .other:
            return  70
        case .hand:
            return  70
        }
        
    }
    
    var createTimeStr: String {
        switch self.type {
        case .purchase:
            return  "购买时间：\(self.createTime)"
        case .refund:
            return  "退回时间：\(self.createTime)"
        case .nick:
            return  "合成时间：\(self.createTime)"
        case .timbre:
            return  "合成时间：\(self.createTime)"
        case .reclaim:
            return  "退回时间：\(self.createTime)"
        case .train:
            return  "创建时间：\(self.createTime)"
        case .langdu:
            return  "开启时间：\(self.createTime)"
        case .other:
            return  "创建时间：\(self.createTime)"
        case .hand:
            return  "扣除时间：\(self.createTime)"
        }
        
    }
    
    var endTimeStr: String {
        let end: String = self.endTime ?? ""
        
        switch self.type {
        case .purchase:
            return  ""
        case .refund:
            return  ""
        case .nick:
            return   ""
        case .timbre:
            return   ""
        case .reclaim:
            return   ""
        case .train:
            return  ""
        case .langdu:
            return  "结束时间：\(end)"
        case .other:
            return   ""
        case .hand:
            return  ""
        }
        
    }
    
    
    var durStr: String? {
        switch self.type {
        case .purchase:
            return  nil
        case .refund:
            return  nil
        case .nick:
            return   nil
        case .timbre:
            return   "总计字数：\(self.preLen)"
        case .reclaim:
            return   nil
        case .train:
            return  nil
        case .langdu:
            if let endTime = self.endTime {
                let d1 = LCTools.getDateFromTime(time: endTime)
                let d2 = LCTools.getDateFromTime(time: self.createTime)
                
                let now1: TimeInterval = d1.timeIntervalSince1970
                let now2: TimeInterval = d2.timeIntervalSince1970
                
                var now: Int = Int(ceil((now1 - now2) / 60))
                
                return "持续时长：\(now) 分钟"
            }
            
            return  "持续时长：0 分钟"
        case .other:
            return   nil
        case .hand:
            return  nil
        }
        
    }
    
    init(createTime: String, endTime: String? = nil, point: String, type: PointsType, title: String, preLen: Int64 = 0, expand: Bool? = nil) {
        self.createTime = createTime
        self.endTime = endTime
        self.point = point
        self.type = type
        self.title = title
        self.preLen = preLen
        self.expand = expand
    }
    
    
}

/*
 - 积分扣除规则：
 1. 实时朗读：每1分钟消耗2个积分，小于1分钟按照1分钟算
 2. AI合成：每20字消耗1个积分，少于20字按照1积分算
 3. 克隆音色每次消耗5积分，合成文字不可超过100字
 4. 购买档位：5元（500）、10元（1000）、50元（5000）、100元（10000）
 5. 积分3个月到期没用完清零（按照最近购买日期算三个月）
 */

// 积分使用方式
enum PointsConsumption {
    case realtimeReading // 实时朗读
    case aisynthesis // AI合成
    case clone // 克隆音色
    
}


struct PointsDeduct: Codable {
    var points: Int64
    var neededPoints: Int64
}


public struct TrialVipLevel: Codable{
    var expireDays : Int    // 试用会员剩余天数
    var vipLevelId: VipLevel      // 试用会员等级
    var userVipLevel : String  // 试用会员名称
    var expireTime: String  // 试用会员过期时间
    var expireFlag: Bool   // 试用会员是否过期
}


public struct UserInfo: Codable {
    
    var token: String?
    var nickName: String?
    var headImgUrl: String?
    var sex: String?
    var phone: String?
    var newUser: Bool?
    var userId: UInt?
    var userCode: String? //用户唯一码
    
    var bindPhone: Bool?//是否绑定手机
    var bindWechat: Bool?//是否绑定微信
    
    var needBindWechat: Bool? // 是否需要绑定微信
    
    var expireFlag: Bool?     //(boolean): true 超过有效期, false 未超过有效期
    
    
    // 微信token
    var access_token: String?
    var offiSubscribe: String?
    
    var vipLevelId: VipLevel?
    
    var memberInfoList: [VipLevelInfo]?
    var audioInfo: AudioInfo?
    
    // 企业微信专属的客服
    var staffService: Bool? = false //是否有人工服务
    var staffServiceDays: Int? = 0 //剩余天数
    //....   新增试用会员信息
    var trialVipExpireFlag: Bool? // 试用会员是否过期
    var trialVipLevelId : VipLevel?   // // 试用会员等级
    var trialVipLevel: String?   // 试用会员名称
    var trialVipLevels: [TrialVipLevel]?   // 试用会员详情]
    var trialExpireDays: Int? // 试用会员剩余天数
    var trialExpireTime: String? // 试用
    
    
    static func currentUser() -> UserInfo? {
        guard let json = UserDefaults.standard.string(forKey: KUserDefault_userJson) else {
            return nil
        }
        let user: UserInfo? = JsonTool.string2Model(json)
        return user
    }
    func saveUser() {
        let json = JsonTool.model2String(self)
        if json.count > 0 {
            UserDefaults.standard.set(json, forKey: KUserDefault_userJson)
            UserDefaults.standard.synchronize()
        }
    }
    
    /// 判断是否是会员
    public static var isMember: Bool {
        guard let userModel = UserInfo.currentUser() else {
            return false
        }
        if let expireFlag = userModel.expireFlag {
            return !expireFlag
        }
        return false
    }
    
    /// 判断是否是试用期会员
    public static var isTrialVip: Bool {
        guard let userModel = UserInfo.currentUser() else {
            return false
        }
        if let expireFlag = userModel.trialVipExpireFlag {
            return !expireFlag
        }
        return false
    }
    
    // 是探索版或者SVIP会员
    public static var isExploreOrSVipMember: Bool {
        return UserInfo.isExploreMember || UserInfo.isMember
    }
    
    
    /// 判断是否是会员
    public static var isExploreMember: Bool {
        guard let userModel = UserInfo.currentUser() else {
            return false
        }
        return false
    }
    
    /// 判断是否是高级会员
    public static var isSVip: Bool {
        guard let userModel = UserInfo.currentUser() else {
            return false
        }
        if let vipLevelId = userModel.vipLevelId {
            return vipLevelId == .svip
        }
        return false
    }
    
    /// 判断用户是否过期
    public static var isExpired: Bool {
        guard let userModel = UserInfo.currentUser() else {
            return false
        }
        if let expireFlag = userModel.expireFlag {
            return expireFlag
        }
        return false
        
    }
    
    /// 判断用户是否过期
    public static var isExploreExpired: Bool {
        guard let userModel = UserInfo.currentUser() else {
            return false
        }
        
        return false
        
    }
    
    /// 用户是否订阅过公众号
    public static var isSubscribe: Bool {
        return true
        guard let userModel = UserInfo.currentUser() else {
            return false
        }
        /// 这里是针对老版本用户 老版本用户可能没有这个字段
        guard let subscribe = userModel.offiSubscribe else {
            return false
        }
        /// =1: 关注 =0: 没关注
        return subscribe == "1"
    }
    
    // 是否绑定了手机号
    static func isBindPhone() -> Bool {
        if let us = UserInfo.currentUser(), let phone = us.phone,  !phone.isEmpty {
            return true
        }
        return false
    }
    
    static var points: Int64 {
        if let us = UserInfo.currentUser(), let p = us.audioInfo?.points  {
            return p
        }
        return 0
    }
    
    // 计算消耗的积分
    static func pointsConsumed_aisynthesis(text: String) -> Int64 {
        if text.isBlank {
            return 0
        }
        let wordCount = text.count
        let credits = (wordCount / 20) + (wordCount % 20 > 0 ? 1 : 0)
        return Int64(max(1, credits)) // 最少1积分
    }
    
    static func pointsConsumed_aisynthesis(count: Int) -> Int64 {
        if count == 0 {
            return 0
        }
        let credits = (count / 20) + (count % 20 > 0 ? 1 : 0)
        return Int64(max(1, credits)) // 最少1积分
    }
    
    // 计算消耗的积分
    static func pointsClone() -> Int64 {
        return 5
    }
    
    
    
    //    func updateUserInfo(withNewUserInfo newInfo: UserInfo)  {
    //        guard  let cumodel = UserInfo.currentUser() else {
    //            return
    //        }
    //
    //        var result = [String]()
    //        let count = UnsafeMutablePointer<UInt32>.alloc(0)
    //        let buff = class_copyPropertyList(object_getClass(self), count)
    //        let countInt = Int(count[0])
    //
    //        for(var i=0;i<countInt;i++){
    //            let temp = buff[i]
    //            let tempPro = property_getName(temp)
    //            let proper = String.init(UTF8String: tempPro)
    //            result.append(proper!)
    //
    //        }
    //    }
    
    //本地 检查会员是否过期
    static func checkExpireFlag() -> Bool {
        guard let userInfo = UserInfo.currentUser() else {
            HUD.showInfo("您还没有登录，请先登录")
            return true
        }
        
        if let expireFlag = userInfo.expireFlag, expireFlag {
            //            let tipView = ActivationCodeTip.shared
            //            tipView.present()
            // TODO:非会员、会员过期 弹窗逻辑 ???
            
            return true
        }
        return false
    }
    
    ///退出登录，清除userInfo缓存
    static func logout() {
        UserDefaults.standard.removeObject(forKey: KUserDefault_userJson)
        
    }
    
    /// 刷新本地用户信息
    static func update(withNewUserInfo newInfo: UserInfo){
        let json = JsonTool.model2String(newInfo)
        if json.count > 0 {
            UserDefaults.standard.removeObject(forKey: KUserDefault_userJson)
            UserDefaults.standard.set(json, forKey: KUserDefault_userJson)
            UserDefaults.standard.synchronize()
        }
    }
}

// MARK: - 扫码登录
struct QRScanModel:Codable {
    var code: Int?
    var msg: String?
}

struct QRInfoModel:Codable {
    var uuid: String?
}



struct QRCheckoutModel:Codable {
    var token: String?
}

struct QRCheckout_Result:Codable {
    var code: Int?
    var msg: String?
    var token: String?
}

// 行为 1.confirm 确认登录  2.cancel 取消登录
enum Qr_ConfirmAction: String {
    case confirm = "confirm"
    case cancel = "cancel"
}


struct Rebind_Result:Codable {
    var code: Int?
}
