//
//  MineTopView2.swift
//  LiveConsole
//
//  Created by simon on 29.4.25.
//

import Foundation
import UIKit
import SnapKit


class MineTopView2: UIView {

    var loginStatus: MineUserLoginStatus?
    weak var delegate: MineTopViewDelegate?
    
    lazy var vipInfoView: UIImageView = {
        let label = UIImageView()
        label.image = UIImage(named: "会员中心_bg_novip")
        label.isUserInteractionEnabled = true
        label.cornerRadius = 12
       return label
    }()
   
        
    lazy var headIcon: UIImageView = {
        let label = UIImageView()
        label.image = UIImage(named: "icon_user_headerDefault")
        label.isUserInteractionEnabled = true
        label.cornerRadius = 36
        label.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(gotoLoginAction)))
        label.isUserInteractionEnabled = true
       return label
    }()
    
    lazy var Icon: UIImageView = {
        let label = UIImageView()
        label.image = UIImage(named: "icon_user_arrow")
        label.isUserInteractionEnabled = true
        label.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(gotoLoginAction)))
        label.isUserInteractionEnabled = true
       return label
    }()
    
    lazy var gotoMemberButton: UIButton = {
        let button = UIButton()
        button.backgroundColor = .white
        button.cornerRadius = 19
        button.setTitle("立即开通", for: .normal)
        button.setImage( UIImage(named: "icon_user_arrow")?.byTintColor(UIColor("#4F3734").alpha(value: 0.6)), for: .normal)
        button.setTitleColor(UIColor("#6763F6"), for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        button.imagePosition(style: .right, spacing: 6)
        return button
    }()
    
    lazy var nameLbl: UILabel = {
        let label = UILabel()
        label.text = "注册/登录"
        label.textColor = .white
        label.font = LCDevice.DIN_Font_PF_M(20)
        return label
    }()
    
    lazy var vipInfoLab: UILabel = {
        let label = UILabel()
        label.text = "您当前是免费用户"
        label.textColor = UIColor.white
        label.font = UIFont.systemFont(ofSize: 16, weight: .bold)
        return label
    }()
    
    lazy var vipLab2: UILabel = {
        let label = UILabel()
        label.text = "您还不是会员"
        label.textColor = UIColor.white.alpha(value: 0.6)
        label.font = UIFont.systemFont(ofSize: 12)
        return label
    }()

    lazy var freeInfoLab: UILabel = {
        let label = UILabel()
        label.text = "您当前还不是会员用户"
        label.textColor = UIColor.white
        label.font = UIFont.systemFont(ofSize: 16, weight: .bold)
        return label
    }()
    
   
    override init(frame: CGRect) {
        super.init(frame: frame)
        createBaseUI()

        nameLbl.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(gotoLoginAction)))
        nameLbl.isUserInteractionEnabled = true
       
        guard let model = UserInfo.currentUser() else {
            return
        }
        loginStatus = .NotLoginST
        setMemberUseInfo(userModel: model)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    
    // MARK: - UI
    fileprivate func createBaseUI() {
        self.backgroundColor = .clear
        
        self.addSubviews([headIcon, nameLbl, vipInfoView, Icon])
        
        vipInfoView.addSubviews([vipInfoLab, vipLab2, gotoMemberButton, freeInfoLab])
        
        headIcon.snp.makeConstraints { make in
            make.leading.equalToSuperview().inset(16)
            make.top.equalToSuperview().inset(73)
            make.height.width.equalTo(72)
        }
        
        nameLbl.snp.makeConstraints { make in
            make.leading.equalTo(headIcon.snp.trailing).offset(16)
            make.centerY.equalTo(headIcon.snp.centerY)
            make.height.equalTo(32)
        }
        
        Icon.snp.makeConstraints { make in
            make.leading.equalTo(nameLbl.snp.trailing).offset(8)
            make.centerY.equalTo(nameLbl.snp.centerY)
            make.height.width.equalTo(24)
        }
        
        
        
        vipInfoView.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview().inset(16)
            make.top.equalTo(headIcon.snp.bottom).offset(24)
            make.height.equalTo(76)
        }
        
        vipInfoLab.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview().inset(16)
            make.top.equalToSuperview().inset(16)
            make.height.equalTo(22)
        }
        
        freeInfoLab.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview().inset(16)
            make.centerY.equalToSuperview()
            make.height.equalTo(22)
        }
        
        vipLab2.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview().inset(16)
            make.bottom.equalToSuperview().inset(16)
            make.height.equalTo(20)
        }
        
        gotoMemberButton.snp.makeConstraints { make in
            make.trailing.equalToSuperview().inset(16)
            make.centerY.equalToSuperview()
            make.height.equalTo(42)
            make.width.equalTo(112)
        }
        
    }
    
    func setMemberUseInfo(userModel: UserInfo ) {
        self.vipInfoView.isHidden = false
        self.Icon.isHidden = true
        if let imgStr = userModel.headImgUrl, let urlImg = URL(string: imgStr) {
            headIcon.layer.borderWidth = 1
            headIcon.layer.borderColor = UIColor.white.cgColor
            headIcon.setImageWith(urlImg, placeholder: UIImage(named: "icon_user_headerDefault"))
        }
        self.nameLbl.text = userModel.nickName
        
        
        if UserInfo.isMember {
            
            if let vip = userModel.memberInfoList?.first(where: {$0.level ==  userModel.vipLevelId}) {
                
                let exT = vip.expiredTime
                let name = vip.level.des
                vipLab2.text = "\(name)\(LCTools.covertDateString(timeString: exT))到期"
                vipInfoLab.text = vip.level.name
                
                gotoMemberButton.isHidden = LCTools.check90DaysExpire(time: exT)

            }
            vipLab2.isHidden =  false
            vipInfoLab.isHidden = false
            vipInfoView.image = UIImage(named: "会员中心_card_bg_vip")
            freeInfoLab.isHidden = true
            vipLab2.textColor = UIColor("#4F3734").alpha(value: 0.6)
            vipInfoLab.textColor = UIColor("#4F3734")
            gotoMemberButton.setTitleColor(UIColor("#4F3734"), for: .normal)
            gotoMemberButton.setTitle("续费会员", for: .normal)
            gotoMemberButton.setImage(UIImage(named: "icon_user_arrow")?.byTintColor(UIColor("#4F3734").alpha(value: 0.6)), for: .normal)
            gotoMemberButton.imagePosition(style: .right, spacing: 6)
            
           
        }else {
            vipInfoView.image = UIImage(named: "会员中心_bg_novip")
            freeInfoLab.isHidden = false
            vipLab2.isHidden =  true
            vipInfoLab.isHidden = true
            gotoMemberButton.setTitleColor(UIColor("#4F3734"), for: .normal)
            gotoMemberButton.setTitle("立即开通", for: .normal)
            gotoMemberButton.setImage( UIImage(named: "icon_user_arrow")?.byTintColor(UIColor("#6763F6").alpha(value: 0.6)), for: .normal)
            gotoMemberButton.imagePosition(style: .right, spacing: 6)
        }
         
    }
    

    public func setLoginStatus(status: MineUserLoginStatus) {
        loginStatus = status
        switch status {
        case .NotLoginST:
            self.nameLbl.text = "注册/登录"
            self.Icon.isHidden = false
            self.vipInfoView.isHidden = true

        case .AlreadyLoginST:
            
            guard let model = UserInfo.currentUser() else {
                return
            }
            setMemberUseInfo(userModel: model)
        case .VIPLoginST:
           
            guard let model = UserInfo.currentUser() else {
                return
            }
            setMemberUseInfo(userModel: model)
        case .TrialVipLoginST:
           
            guard let model = UserInfo.currentUser() else {
                return
            }
            setMemberUseInfo(userModel: model)
        }
        
    }
    
    
     func headIconAction(_ sender: Any) {
        delegate?.headIconAction()
    }
    
     func buttonActionByGotoMember(_ sender: Any) {
        delegate?.openingMember()
    }
    
    @objc private func openBtnAction( send: UIButton) {
        delegate?.openingMember()
    }
    
    @objc func gotoLoginAction() {
        if  loginStatus == .NotLoginST {
            delegate?.headIconAction()
        }
    }

}
