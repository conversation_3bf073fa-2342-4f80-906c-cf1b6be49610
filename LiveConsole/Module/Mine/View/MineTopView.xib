<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="32700.99.1234" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22684"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" id="fyY-RO-CeN" customClass="MineTopView" customModule="liveplusexplore" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="375" height="246"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <subviews>
                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="bg_个人中心_会员_1" translatesAutoresizingMaskIntoConstraints="NO" id="xbw-wJ-d3k">
                    <rect key="frame" x="0.0" y="0.0" width="375" height="246"/>
                </imageView>
                <imageView hidden="YES" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" fixedFrame="YES" image="个人中心_icon_个人信息_vip" translatesAutoresizingMaskIntoConstraints="NO" id="HiA-op-9pa">
                    <rect key="frame" x="90" y="97" width="81" height="21"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                            <real key="value" value="5"/>
                        </userDefinedRuntimeAttribute>
                    </userDefinedRuntimeAttributes>
                </imageView>
                <view contentMode="scaleAspectFit" translatesAutoresizingMaskIntoConstraints="NO" id="QgQ-ab-UhY">
                    <rect key="frame" x="20" y="136" width="335" height="110"/>
                    <subviews>
                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="mine_member_card_vip" translatesAutoresizingMaskIntoConstraints="NO" id="c1L-4C-d9y">
                            <rect key="frame" x="0.0" y="0.0" width="335" height="110"/>
                            <userDefinedRuntimeAttributes>
                                <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                    <real key="value" value="16"/>
                                </userDefinedRuntimeAttribute>
                            </userDefinedRuntimeAttributes>
                        </imageView>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="开通享会员高级功能" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="zLc-wL-bmP">
                            <rect key="frame" x="39" y="16" width="144" height="22.5"/>
                            <fontDescription key="fontDescription" name="PingFangSC-Semibold" family="PingFang SC" pointSize="16"/>
                            <color key="textColor" red="1" green="0.90196078431372551" blue="0.76078431372549016" alpha="1" colorSpace="calibratedRGB"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="mint_icon_vip" translatesAutoresizingMaskIntoConstraints="NO" id="GtS-MQ-Oqf">
                            <rect key="frame" x="19" y="19" width="16" height="16"/>
                            <constraints>
                                <constraint firstAttribute="width" constant="16" id="C2L-Fy-8AV"/>
                                <constraint firstAttribute="height" constant="16" id="FHR-bm-Wyf"/>
                            </constraints>
                        </imageView>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="会员权益" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="5vP-Fb-dyf">
                            <rect key="frame" x="256" y="18" width="52" height="18.5"/>
                            <fontDescription key="fontDescription" name="PingFangSC-Regular" family="PingFang SC" pointSize="13"/>
                            <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <label hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" fixedFrame="YES" text="2022-05-12到期" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="nBC-m4-agH">
                            <rect key="frame" x="19" y="50" width="290" height="15"/>
                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                            <fontDescription key="fontDescription" name="PingFangSC-Regular" family="PingFang SC" pointSize="12"/>
                            <color key="textColor" white="1" alpha="0.76707120706106868" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <label hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" fixedFrame="YES" text="2022-05-12到期" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="gUS-cg-5mM">
                            <rect key="frame" x="19" y="72" width="290" height="15"/>
                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                            <fontDescription key="fontDescription" name="PingFangSC-Regular" family="PingFang SC" pointSize="12"/>
                            <color key="textColor" white="1" alpha="0.77321385973282442" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="个人中心_icon_更多_跳转" translatesAutoresizingMaskIntoConstraints="NO" id="hoR-1D-2LV">
                            <rect key="frame" x="310" y="23" width="8" height="8"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="8" id="36B-Tf-EYe"/>
                                <constraint firstAttribute="width" constant="8" id="NbQ-Uh-UgV"/>
                            </constraints>
                        </imageView>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="1fA-t1-p9C">
                            <rect key="frame" x="256" y="0.0" width="79" height="79"/>
                            <state key="normal" title="Button"/>
                            <buttonConfiguration key="configuration" style="plain" title="   "/>
                            <connections>
                                <action selector="buttonActionByGotoMember:" destination="fyY-RO-CeN" eventType="touchUpInside" id="LB0-qw-EvG"/>
                            </connections>
                        </button>
                    </subviews>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstItem="zLc-wL-bmP" firstAttribute="centerY" secondItem="hoR-1D-2LV" secondAttribute="centerY" constant="0.25" id="4OG-uL-Ryr"/>
                        <constraint firstItem="hoR-1D-2LV" firstAttribute="leading" secondItem="5vP-Fb-dyf" secondAttribute="trailing" constant="2" id="5Lf-bm-lOs"/>
                        <constraint firstAttribute="bottom" secondItem="c1L-4C-d9y" secondAttribute="bottom" id="6wt-Mw-3jz"/>
                        <constraint firstItem="c1L-4C-d9y" firstAttribute="top" secondItem="QgQ-ab-UhY" secondAttribute="top" id="CU5-Lr-KnB"/>
                        <constraint firstItem="c1L-4C-d9y" firstAttribute="leading" secondItem="QgQ-ab-UhY" secondAttribute="leading" id="DHh-pc-Z3o"/>
                        <constraint firstAttribute="bottom" secondItem="1fA-t1-p9C" secondAttribute="bottom" constant="31" id="II4-ae-58T"/>
                        <constraint firstAttribute="trailing" secondItem="c1L-4C-d9y" secondAttribute="trailing" id="LKi-B7-rf9"/>
                        <constraint firstItem="1fA-t1-p9C" firstAttribute="top" secondItem="QgQ-ab-UhY" secondAttribute="top" id="Pt3-dx-hOz"/>
                        <constraint firstAttribute="trailing" secondItem="1fA-t1-p9C" secondAttribute="trailing" id="bct-QN-S8f"/>
                        <constraint firstItem="zLc-wL-bmP" firstAttribute="leading" secondItem="GtS-MQ-Oqf" secondAttribute="trailing" constant="4" id="cCH-Sf-xlV"/>
                        <constraint firstItem="GtS-MQ-Oqf" firstAttribute="top" secondItem="QgQ-ab-UhY" secondAttribute="top" constant="19" id="dQB-ZF-wcX"/>
                        <constraint firstItem="5vP-Fb-dyf" firstAttribute="centerY" secondItem="zLc-wL-bmP" secondAttribute="centerY" id="hA2-Hg-GpQ"/>
                        <constraint firstItem="5vP-Fb-dyf" firstAttribute="leading" secondItem="1fA-t1-p9C" secondAttribute="leading" id="if6-9W-7Ce"/>
                        <constraint firstAttribute="height" constant="110" id="pDq-kD-UFZ"/>
                        <constraint firstItem="GtS-MQ-Oqf" firstAttribute="leading" secondItem="QgQ-ab-UhY" secondAttribute="leading" constant="19" id="wYm-JQ-MkK"/>
                        <constraint firstItem="zLc-wL-bmP" firstAttribute="centerY" secondItem="GtS-MQ-Oqf" secondAttribute="centerY" constant="0.25" id="x29-74-zHv"/>
                        <constraint firstAttribute="trailing" secondItem="hoR-1D-2LV" secondAttribute="trailing" constant="17" id="xne-ec-Ppa"/>
                    </constraints>
                </view>
                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" insetsLayoutMarginsFromSafeArea="NO" image="mine_bg_bending" translatesAutoresizingMaskIntoConstraints="NO" id="TJu-8g-AQ2">
                    <rect key="frame" x="0.0" y="234" width="375" height="12"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="12" id="muP-Ya-G2e"/>
                    </constraints>
                </imageView>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" fixedFrame="YES" text="未开通会员" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="9Gl-rd-qdo">
                    <rect key="frame" x="90" y="70" width="205" height="20"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <fontDescription key="fontDescription" name="PingFangSC-Medium" family="PingFang SC" pointSize="16"/>
                    <color key="textColor" red="0.16470588235294117" green="0.16470588235294117" blue="0.16470588235294117" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                    <nil key="highlightedColor"/>
                </label>
                <button opaque="NO" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="HCm-yT-MZU">
                    <rect key="frame" x="26" y="64" width="56" height="56"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <state key="normal" title="Button"/>
                    <buttonConfiguration key="configuration" style="plain">
                        <backgroundConfiguration key="background" image="icon_user_headerDefault"/>
                    </buttonConfiguration>
                    <connections>
                        <action selector="headIconAction:" destination="fyY-RO-CeN" eventType="touchUpInside" id="1nR-Oa-uoH"/>
                    </connections>
                </button>
                <label hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" fixedFrame="YES" text="开通会员，解锁高级功能" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="5i1-Jj-ALE" userLabel="noVipLbl">
                    <rect key="frame" x="90" y="98" width="205" height="18"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <fontDescription key="fontDescription" name="PingFangSC-Regular" family="PingFang SC" pointSize="12"/>
                    <color key="textColor" red="0.30196078431372547" green="0.30588235294117649" blue="0.32156862745098036" alpha="1" colorSpace="calibratedRGB"/>
                    <nil key="highlightedColor"/>
                </label>
                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="IN6-zs-RXu">
                    <rect key="frame" x="319" y="75" width="40" height="40"/>
                    <constraints>
                        <constraint firstAttribute="height" relation="greaterThanOrEqual" constant="40" id="6pp-QZ-kev"/>
                        <constraint firstAttribute="width" relation="greaterThanOrEqual" constant="40" id="Egi-qB-oS8"/>
                        <constraint firstAttribute="width" constant="40" id="K3w-z8-G7T"/>
                        <constraint firstAttribute="height" constant="40" id="y8F-pt-fPo"/>
                    </constraints>
                    <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                    <state key="normal" image="扫一扫"/>
                    <connections>
                        <action selector="scanAction:" destination="fyY-RO-CeN" eventType="touchUpInside" id="1Mp-sw-BTQ"/>
                    </connections>
                </button>
            </subviews>
            <viewLayoutGuide key="safeArea" id="Xiv-oG-8Hz"/>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstItem="TJu-8g-AQ2" firstAttribute="leading" secondItem="fyY-RO-CeN" secondAttribute="leading" id="3lc-Zx-PS2"/>
                <constraint firstItem="IN6-zs-RXu" firstAttribute="top" secondItem="Xiv-oG-8Hz" secondAttribute="top" constant="27" id="4cu-yI-doY"/>
                <constraint firstItem="Xiv-oG-8Hz" firstAttribute="trailing" secondItem="IN6-zs-RXu" secondAttribute="trailing" constant="16" id="6Rb-Td-ah8"/>
                <constraint firstItem="IN6-zs-RXu" firstAttribute="top" secondItem="Xiv-oG-8Hz" secondAttribute="top" constant="27" id="Ahf-wY-QGg"/>
                <constraint firstAttribute="trailing" secondItem="TJu-8g-AQ2" secondAttribute="trailing" id="LyH-4K-FzM"/>
                <constraint firstItem="xbw-wJ-d3k" firstAttribute="trailing" secondItem="fyY-RO-CeN" secondAttribute="trailing" id="Mpl-Bb-hO4"/>
                <constraint firstItem="QgQ-ab-UhY" firstAttribute="leading" secondItem="fyY-RO-CeN" secondAttribute="leading" constant="20" id="MqR-gu-IMH"/>
                <constraint firstItem="xbw-wJ-d3k" firstAttribute="leading" secondItem="fyY-RO-CeN" secondAttribute="leading" id="QiM-kk-cdC"/>
                <constraint firstItem="Xiv-oG-8Hz" firstAttribute="trailing" secondItem="IN6-zs-RXu" secondAttribute="trailing" constant="16" id="cxX-P5-e2V"/>
                <constraint firstAttribute="bottom" secondItem="TJu-8g-AQ2" secondAttribute="bottom" id="ecn-be-Hc4"/>
                <constraint firstItem="QgQ-ab-UhY" firstAttribute="bottom" secondItem="fyY-RO-CeN" secondAttribute="bottom" id="gUE-6p-fub"/>
                <constraint firstItem="xbw-wJ-d3k" firstAttribute="bottom" secondItem="fyY-RO-CeN" secondAttribute="bottom" id="lTP-wW-ICB"/>
                <constraint firstAttribute="trailing" secondItem="QgQ-ab-UhY" secondAttribute="trailing" constant="20" id="mnE-Ez-1TG"/>
                <constraint firstItem="xbw-wJ-d3k" firstAttribute="top" secondItem="fyY-RO-CeN" secondAttribute="top" id="uq5-l7-IaP"/>
            </constraints>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <connections>
                <outlet property="bgBedingImg" destination="TJu-8g-AQ2" id="0zo-8Z-nAD"/>
                <outlet property="bgImg" destination="xbw-wJ-d3k" id="Z73-iW-efZ"/>
                <outlet property="expirationDateLbl" destination="5vP-Fb-dyf" id="NEv-c0-y1c"/>
                <outlet property="gotoMemberButton" destination="1fA-t1-p9C" id="49A-cq-weJ"/>
                <outlet property="headIconBtn" destination="HCm-yT-MZU" id="o7C-jE-wlE"/>
                <outlet property="iconJump" destination="hoR-1D-2LV" id="iKZ-9i-RGg"/>
                <outlet property="nameLbl" destination="9Gl-rd-qdo" id="nYb-FN-B4d"/>
                <outlet property="noVipLbl" destination="5i1-Jj-ALE" id="iif-5B-VSS"/>
                <outlet property="scanBtn" destination="IN6-zs-RXu" id="UCw-c2-q8T"/>
                <outlet property="vipInfoIcon" destination="HiA-op-9pa" id="tAl-SZ-kFP"/>
                <outlet property="vipInfoLbl" destination="zLc-wL-bmP" id="Puq-Hh-1KL"/>
                <outlet property="vipInfoView" destination="QgQ-ab-UhY" id="esK-sd-Kuh"/>
                <outlet property="vipLab1" destination="nBC-m4-agH" id="IPz-QW-gXt"/>
                <outlet property="vipLab2" destination="gUS-cg-5mM" id="ccv-he-4ue"/>
            </connections>
            <point key="canvasLocation" x="109.**************" y="-213.61607142857142"/>
        </view>
    </objects>
    <resources>
        <image name="bg_个人中心_会员_1" width="375" height="246"/>
        <image name="icon_user_headerDefault" width="76" height="76"/>
        <image name="mine_bg_bending" width="375" height="12"/>
        <image name="mine_member_card_vip" width="343" height="110"/>
        <image name="mint_icon_vip" width="20" height="20"/>
        <image name="个人中心_icon_个人信息_vip" width="81" height="21"/>
        <image name="个人中心_icon_更多_跳转" width="8" height="8"/>
        <image name="扫一扫" width="20" height="20"/>
    </resources>
</document>
