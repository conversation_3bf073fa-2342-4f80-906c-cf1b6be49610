//
//  OpeningMemberCell.swift
//  LivePlus
//
//  Created by Brian<PERSON><PERSON> on 2021/11/1.
//

import UIKit


public protocol OpeningMemberCellDelegate: AnyObject {
    func openingMember()    //开通会员
    
}

class OpeningMemberCell: UITableViewCell {
    let baseView = UIView()
    let openBtn = UIButton(type: .custom)
    let bgImagV = UIImageView()
    weak var delegate: OpeningMemberCellDelegate?
    var _isMember = false
    let titleLab = UILabel()
    let subtitleLab = UILabel()

    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        //cell点击无选中色
        let selectedView = UIView(frame: self.frame)
        selectedView.backgroundColor = .clear
        self.selectedBackgroundView = selectedView
        createBaseUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - UI
    fileprivate func createBaseUI() {
        self.backgroundColor = .clear
        let vH: CGFloat = 72.0
        let vW: CGFloat = LCDevice.screenW - 16 * 2
        
        baseView.frame = CGRect(x: 16, y: 0, width: vW, height: vH )
        contentView.addSubview(baseView)
        
        bgImagV.frame = CGRect(x: 0, y: 0, width: vW, height: vH)
        bgImagV.image = UIImage(named: "mine_open_member_btn")
        bgImagV.roundCorners([.topLeft, .bottomLeft], radius: 10.0)
        baseView.addSubview(bgImagV)
        
        openBtn.frame = CGRect(x: vW - LCDevice.DIN_WIDTH(84) - LCDevice.DIN_WIDTH(20), y: (vH - LCDevice.DIN_WIDTH(28))/2.0, width: LCDevice.DIN_WIDTH(84), height: LCDevice.DIN_WIDTH(28))
        openBtn.addTarget(self, action: #selector(openBtnAction(send:)), for: .touchUpInside)
        openBtn.setTitleColor(.white, for: .normal)
        openBtn.titleLabel?.font = LCDevice.DIN_Font_PF_S(14)
        openBtn.setTitle("立即开通", for: .normal)
        openBtn.cornerRadius = openBtn.height / 2.0
        openBtn.setBackgroundImage(UIImage(color: UIColor("#ffffff")), for: .normal)
        baseView.addSubview(openBtn)
        
        let lblW: CGFloat = 200.0
        let lblSpace: CGFloat = LCDevice.DIN_WIDTH(20)
        titleLab.frame = CGRect(x: lblSpace, y: LCDevice.DIN_WIDTH(16), width: lblW, height: LCDevice.DIN_WIDTH(18))
        titleLab.textColor = .white
        titleLab.font = LCDevice.DIN_Font_PF_S(15)
        baseView.addSubview(titleLab)

        subtitleLab.frame = CGRect(x: lblSpace, y: titleLab.bottom + 6, width: lblW, height: LCDevice.DIN_WIDTH(12))
        subtitleLab.textColor = .white
        subtitleLab.font = LCDevice.DIN_Font_PF_R(12)
        subtitleLab.textAlignment = .left
        baseView.addSubview(subtitleLab)
        
        titleLab.text = "开通快瓴中控台会员"
        titleLab.textColor = .white
        subtitleLab.text = "立即专享会员高级功能"
        subtitleLab.textColor = .white
        openBtn.setTitle("立即开通", for: .normal)
        openBtn.setTitleColor(UIColor("#6974F2"), for: .normal)
        openBtn.setBackgroundImage(UIImage(color: UIColor("#ffffff")), for: .normal)
        bgImagV.image = UIImage(named: "mine_open_member_btn")?.resizableImage(withCapInsets: UIEdgeInsets(top: 20, left: 5, bottom: 20, right: 170), resizingMode: .stretch)
    
    }
    
    
    @objc private func openBtnAction( send: UIButton) {
        self.delegate?.openingMember()
    }
    
    public func setIsMember(_ isMember: Bool) {
        if _isMember == isMember {return}
        _isMember = isMember
        if isMember {
            titleLab.text = "尊敬的快瓴中控台会员"
            titleLab.textColor = UIColor("#FAE1C3")
            subtitleLab.text = ""
            subtitleLab.textColor = .white
            openBtn.setTitle("立即续费", for: .normal)
            openBtn.setTitleColor(UIColor("#35364E"), for: .normal)
            openBtn.setBackgroundImage(UIImage(named: "mine_open_bg_btn"), for: .normal)
            bgImagV.image = UIImage(named: "mine_is_member_btn")?.resizableImage(withCapInsets: UIEdgeInsets(top: 20, left: 1, bottom: 20, right: 170), resizingMode: .stretch)
        } else {
            titleLab.text = "开通快瓴中控台会员"
            titleLab.textColor = .white
            subtitleLab.text = "立即专享会员高级功能"
            subtitleLab.textColor = .white
            openBtn.setTitle("立即开通", for: .normal)
            openBtn.setTitleColor(UIColor("#6974F2"), for: .normal)
            openBtn.setBackgroundImage(UIImage(color: UIColor("#ffffff")), for: .normal)
            bgImagV.image = UIImage(named: "mine_open_member_btn")?.resizableImage(withCapInsets: UIEdgeInsets(top: 20, left: 5, bottom: 20, right: 170), resizingMode: .stretch)
        }
        if (subtitleLab.text != nil), subtitleLab.text!.isEmpty {
            titleLab.y = LCDevice.DIN_WIDTH(27)
        } else {
            titleLab.y = LCDevice.DIN_WIDTH(16)
        }
    }

}
