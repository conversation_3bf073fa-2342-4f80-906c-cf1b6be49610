//
//  MineCell.swift
//  LivePlus
//
//  Created by iclick on 2020/12/23.
//

import UIKit

enum cellCornerRadiusType: Int {
    case Normal = 0
    case UpRadius = 1
    case DownRadius = 2
    case AllRadius = 3
}

class MineCell: UITableViewCell {
    
    let baseView = UIView()
    let iconView = UIImageView()
    let titleLab = UILabel()
    let subtitleLab = UILabel()
    let arrowView = UIImageView()
    let sepLine = UIView()
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        //cell点击无选中色
        let selectedView = UIView(frame: self.frame)
        selectedView.backgroundColor = .clear
        self.selectedBackgroundView = selectedView
        self.backgroundColor = .clear
        createBaseUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - UI
    fileprivate func createBaseUI() {
        let inset: CGFloat = 16 * 2
        baseView.frame = CGRect(x: 16, y: 0, width: contentView.width - inset, height: contentView.height)
        baseView.backgroundColor = UIColor("#282828")
        baseView.autoresizingMask = [.flexibleWidth, .flexibleHeight]
        contentView.addSubview(baseView)
        
        baseView.addSubview(iconView)
        iconView.snp.makeConstraints { make in
            make.leading.equalToSuperview().inset(18)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(24)
        }
        
        
        
        titleLab.textColor = UIColor.white
        titleLab.font = LCDevice.DIN_Font_PF_M(15)
        baseView.addSubview(titleLab)
        
        titleLab.snp.makeConstraints { make in
            make.leading.equalToSuperview().inset(56)
            make.centerY.equalToSuperview()
            make.trailing.equalToSuperview().inset(156)
            make.height.equalTo(30)
        }
        
        
        subtitleLab.textColor = UIColor("#797B7D")
        subtitleLab.font = LCDevice.DIN_Font_PF_R(14)
        subtitleLab.textAlignment = .right
        baseView.addSubview(subtitleLab)
        subtitleLab.snp.makeConstraints { make in
            make.trailing.equalToSuperview().inset(46)
            make.centerY.equalToSuperview()
            make.leading.equalToSuperview().inset(206)
            make.height.equalTo(30)
        }
        
        
        arrowView.image = UIImage(named: "icon_user_arrow")
        baseView.addSubview(arrowView)
        
        arrowView.snp.makeConstraints { make in
            make.trailing.equalToSuperview().inset(16)
            make.centerY.equalToSuperview()
            make.height.width.equalTo(24)
        }
        
        
        sepLine.backgroundColor = UIColor("#444444")
        baseView.addSubview(sepLine)
        sepLine.snp.makeConstraints { make in
            make.leading.trailing.bottom.equalToSuperview()
            make.height.equalTo(1)
        }
    }
    
    func config(with icon: UIImage?, title: String, subtitle: String? = nil, isShowSepLine: Bool = true, imageUrl: String? = nil, color: UIColor? = nil) {
        iconView.image = icon
        titleLab.text = title
        
        subtitleLab.text = subtitle
        if let imageUrl = imageUrl, let url = URL(string: imageUrl) {
            iconView.loadImageInDisk(with: url)
        }
        if let color = color {
            subtitleLab.textColor = color
        }
        sepLine.isHidden = !isShowSepLine
        sepLine.top = baseView.height - 1
    }
    
    func setRadiusForType(with type: cellCornerRadiusType) {
        switch type {
        case .AllRadius:
            baseView.roundCorners([.bottomLeft, .bottomRight, .topLeft, .topRight], radius: 10)
        case .UpRadius:
            baseView.roundCorners([.topLeft, .topRight], radius: 10)
        case .DownRadius:
            baseView.roundCorners([.bottomLeft, .bottomRight], radius: 10)
        default:
            baseView.roundCorners([.bottomLeft, .bottomRight, .topLeft, .topRight], radius: 0)
        }
    }
    
}
