//
//  InPurchasingBtnView.swift
//  LivePlus
//
//  Created by Brian<PERSON><PERSON> on 2021/11/2.
//

import UIKit

class InPurchasingBtnViewCell: UICollectionViewCell {
    
    
    var bigTitle: String = ""
    var currentPriceTxt: String = ""
    var originPriceTxt: String = ""
    var isSelect: Bool = false
    
  
    private lazy var centerImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(named: "member_purchasing_bg_nor")
        return imageView
    }()
    
    private lazy var titleLbal: UILabel = {
        let lbl = UILabel()
        lbl.textColor = UIColor("#1E1F20")
        lbl.textAlignment = .center
        lbl.font = LCDevice.DIN_Font_PF_M(16)
        return lbl
    }()
    
    private lazy var curPriceLbal: UILabel = {
        let lbl = UILabel()
        lbl.textColor = UIColor("#1E1F20")
        lbl.font = LCDevice.DIN_Font_PF_M(28)
        return lbl
    }()
    
    private lazy var iconLbal: UILabel = {
        let lbl = UILabel()
        lbl.textColor = UIColor("#1E1F20")
        lbl.font = LCDevice.DIN_Font_PF_M(18)
        lbl.text = "¥"
        return lbl
    }()
    
    private lazy var oriPriceLbal: UILabel = {
        let lbl = UILabel()
        lbl.textColor = UIColor("#8884FF")
        lbl.font = LCDevice.DIN_Font_PF_M(12)
        lbl.textAlignment = .center
        return lbl
    }()
    
//    convenience init(bigTitle: String,
//                     currentPriceTxt: String,
//                     originPriceTxt: String,isSelect:Bool) {
//        self.init(frame: CGRect(x: 0, y: 0, width: 120, height: 144))
//        self.bigTitle = bigTitle
//        self.currentPriceTxt = currentPriceTxt
//        self.originPriceTxt = originPriceTxt
//        self.isSelect = isSelect
//        setUpView()
//      
//    }
    
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        contentView.backgroundColor = UIColor.clear
        setUpView()
    }
    
    required init?(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
     
    private func setUpView() {
        
        //        let tap = UITapGestureRecognizer(target: self, action: #selector(tapGesture(gesture:)))
        //        self.addGestureRecognizer(tap)
        centerImageView.frame = contentView.frame
        contentView.addSubview(centerImageView)
        titleLbal.frame = CGRect(x: 7, y: 28, width: centerImageView.width - 14, height: 22)
        contentView.addSubview(titleLbal)
        
        iconLbal.frame = CGRect(x: 29, y: 70, width: 11, height: 23)
        contentView.addSubview(iconLbal)
        
        curPriceLbal.frame = CGRect(x: iconLbal.right + 4, y: 66, width: 70, height: 28)
        contentView.addSubview(curPriceLbal)
        
        oriPriceLbal.frame = CGRect(x: 29, y: curPriceLbal.bottom + 8, width: 70, height: 17)
        contentView.addSubview(oriPriceLbal)
        
    }
    
    public func setSelectStatus(status: Bool) {
        self.isSelect = status
        if status {
            centerImageView.image = UIImage(named: "member_purchasing_bg_sel")
        } else {
            centerImageView.image = UIImage(named: "member_purchasing_bg_nor")
        }
    }
    
    public func config(bigTitle: String, currentPriceTxt: String, originPriceTxt: String) {
        titleLbal.text = bigTitle
        curPriceLbal.text = currentPriceTxt
        if originPriceTxt != "0", originPriceTxt.count > 0 {
            let priceString = NSMutableAttributedString.init(string: "原价：\(originPriceTxt)")
            priceString.addAttribute(NSAttributedString.Key.strikethroughStyle, value: NSNumber.init(value: 1), range: NSRange(location: 0, length: priceString.length))
            oriPriceLbal.attributedText = priceString
            oriPriceLbal.isHidden = false
        } else {
            oriPriceLbal.isHidden = true
        }
        
    }

}
