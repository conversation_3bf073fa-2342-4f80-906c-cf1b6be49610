//
//  PointsCell.swift
//  LiveConsole
//
//  Created by simon on 7.5.25.
//

import Foundation
import UIKit

protocol PointsCellDelegate: NSObjectProtocol {
    func actionStatusChanged()
}

class PointsCell: UITableViewCell {
    
    weak var delegete: PointsCellDelegate?
    
    let baseView = UIView()

    lazy var arrowView: UIImageView = {
        let label = UIImageView()
        label.image = UIImage(named: "icon_user_arrow_下")
        label.isUserInteractionEnabled = true
        label.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(expAction)))
        label.isUserInteractionEnabled = true
       return label
    }()
    
    
    lazy var titleLab: UILabel = {
        let label = UILabel()
        label.textColor = .white
        label.font = UIFont.systemFont(ofSize: 14, weight: .regular)
       return label
    }()
    
    lazy var createTimeLab: UILabel = {
        let label = UILabel()
        label.textColor = .white.alpha(value: 0.6)
        label.font = UIFont.systemFont(ofSize: 12, weight: .regular)
       return label
    }()
    
    lazy var endTimeLab: UILabel = {
        let label = UILabel()
        label.textColor = .white.alpha(value: 0.6)
        label.font = UIFont.systemFont(ofSize: 12, weight: .regular)
       return label
    }()
    
    lazy var durTimeLab: UILabel = {
        let label = UILabel()
        label.textColor = .white.alpha(value: 0.6)
        label.font = UIFont.systemFont(ofSize: 12, weight: .regular)
       return label
    }()
    
    lazy var potLab: UILabel = {
        let label = UILabel()
        label.textColor = UIColor("#FA5F55")
        label.textAlignment = .right
        label.font = UIFont.systemFont(ofSize: 16, weight: .medium)
       return label
    }()
    
    lazy var sepLine: UIView = {
        let label = UIView()
        label.backgroundColor = UIColor("#444444")
       return label
    }()
    
    // 积分
    var p: PointsInfo?
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        //cell点击无选中色
        let selectedView = UIView(frame: self.frame)
        selectedView.backgroundColor = .clear
        self.selectedBackgroundView = selectedView
        self.backgroundColor = .clear
        createBaseUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    
    // MARK: - UI
    fileprivate func createBaseUI() {
        let inset: CGFloat = 16 * 2
        baseView.frame = CGRect(x: 16, y: 0, width: contentView.width - inset, height: contentView.height)
        baseView.backgroundColor = UIColor("#282828")
        baseView.autoresizingMask = [.flexibleWidth, .flexibleHeight]
        contentView.addSubview(baseView)
    
        
        baseView.addSubviews([titleLab, createTimeLab, endTimeLab, arrowView, durTimeLab, potLab, sepLine])
        
        titleLab.snp.makeConstraints { make in
            make.leading.equalToSuperview().inset(16)
            make.top.equalToSuperview().inset(16)
            make.height.equalTo(20)
        }
        
        createTimeLab.snp.makeConstraints { make in
            make.leading.equalToSuperview().inset(16)
            make.top.equalToSuperview().inset(40)
            make.height.equalTo(20)
        }
        
        durTimeLab.snp.makeConstraints { make in
            make.leading.equalToSuperview().inset(16)
            make.top.equalToSuperview().inset(40)
            make.height.equalTo(20)
        }
        
        endTimeLab.snp.makeConstraints { make in
            make.leading.equalToSuperview().inset(16)
            make.bottom.equalToSuperview().inset(16)
            make.height.equalTo(20)
        }
        
        
        potLab.snp.makeConstraints { make in
            make.trailing.equalToSuperview().inset(16)
            make.centerY.equalToSuperview()
        }
        
        arrowView.snp.makeConstraints { make in
            make.centerY.equalTo(createTimeLab.snp.centerY)
            make.leading.equalTo(createTimeLab.snp.trailing).offset(8)
            make.width.height.equalTo(30)
        }
        
        sepLine.snp.makeConstraints { make in
            make.leading.trailing.bottom.equalToSuperview()
            make.height.equalTo(1)
        }
    }
    
    @objc func expAction() {
        
        if let expand = self.p?.expand {
            self.p?.expand = !expand
        } else {
            self.p?.expand = true
        }
      
        self.delegete?.actionStatusChanged()
    }
    
    func config(with po: PointsInfo) {
        self.p = po
        
        self.titleLab.text = po.title
        
        self.createTimeLab.text = po.createTimeStr
        
        self.endTimeLab.text = po.endTimeStr
        
        self.potLab.text = po.point
        
        if  let durStr = po.durStr {
            self.durTimeLab.isHidden = false
            self.durTimeLab.text = durStr
            createTimeLab.snp.updateConstraints { make in
                make.top.equalToSuperview().inset(60)
            }
        } else {
            self.durTimeLab.isHidden = true
            createTimeLab.snp.updateConstraints { make in
                make.top.equalToSuperview().inset(40)
            }
        }
       
        
        self.arrowView.isHidden = po.type != .langdu
        
        if let expand = po.expand, expand {
            self.arrowView.image = UIImage(named: "icon_user_arrow_上")
            self.endTimeLab.isHidden = false
        } else {
            self.arrowView.image = UIImage(named: "icon_user_arrow_下")
            self.endTimeLab.isHidden = true
        }
        
    }
    
    
}
