//
//  PointsAlertView.swift
//  LiveConsole
//
//  Created by simon on 8.5.25.
//

import Foundation

class PointsAlertView: UIView {
    
    // MARK: - Properties
    private let containerView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        view.layer.cornerRadius = 16
        view.clipsToBounds = true
        return view
    }()
    
    private let titleLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 20, weight: .medium)
        label.textColor = UIColor("#1E1F20")
        label.text = "积分消耗规则"
        return label
    }()
    
    lazy var ruleButton: UIButton = {
        let button = UIButton()
        button.setTitle("规则详细 >", for: .normal)
        button.setTitleColor(UIColor("#6763F6"), for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 13, weight: .regular)
        button.addTarget(self, action: #selector(ruleAction), for: .touchUpInside)
        return button
    }()
    
    private let contentLabel1: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 14, weight: .bold)
        label.textColor = UIColor("#1E1F20")
        label.text = "互动时实时朗读用户昵称："
        return label
    }()
    
    private let text11: PointsAlertTextView = {
        let label = PointsAlertTextView()
        label.titleLabel.text = "2积分/分钟（开启后自动扣除）"
        return label
    }()
    
    private let text12: PointsAlertTextView = {
        let label = PointsAlertTextView()
        label.titleLabel.text = "计费方式：按分钟计费，不足1分钟按1分钟计算"
        return label
    }()
    
    //
    private let contentLabel2: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 14, weight: .bold)
        label.textColor = UIColor("#1E1F20")
        label.text = "AI合成语音："
        return label
    }()
    
    private let text21: PointsAlertTextView = {
        let label = PointsAlertTextView()
        label.titleLabel.text = "1积分/20字（合成后自动扣除）"
        return label
    }()
    
    private let text22: PointsAlertTextView = {
        let label = PointsAlertTextView()
        label.titleLabel.text = "计费方式：按字数计费，不足20字按20字计算"
        return label
    }()
    
    private let contentLabel3: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 14, weight: .bold)
        label.textColor = UIColor("#1E1F20")
        label.text = "克隆音色："
        return label
    }()
    
    
    private let text31: PointsAlertTextView = {
        let label = PointsAlertTextView()
        label.titleLabel.text = "5积分/次（音色生成后自动扣除）"
        return label
    }()
    
    
  
    
    
    private let confirmButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitle("我知道了", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.backgroundColor = UIColor("#AC63F9")
        button.titleLabel?.font = .systemFont(ofSize: 14, weight: .regular)
        button.applyGradient()
        button.cornerRadius = 20
        return button
    }()
    
    // MARK: - Init
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }
    
    // MARK: - Setup
    private func setupUI() {
        backgroundColor = UIColor.black.withAlphaComponent(0.4)
        
        addSubview(containerView)
        containerView.addSubviews([titleLabel, contentLabel1,contentLabel2,contentLabel3,text11,text12,text21,text22,text31, confirmButton, ruleButton])
        
        containerView.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview().inset(36)
            make.centerY.equalToSuperview()
            make.height.equalTo(276 + 76 + 56)
        }
        
        
        titleLabel.snp.makeConstraints { make in
            make.leading.top.equalToSuperview().inset(24)
            make.height.equalTo(27)
        }
        
        ruleButton.snp.makeConstraints { make in
            make.trailing.equalToSuperview().inset(24)
            make.top.equalToSuperview().inset(24)
            make.height.equalTo(27)
            make.width.equalTo(67)
        }
        
        contentLabel1.snp.makeConstraints { make in
            make.height.equalTo(21)
            make.top.equalToSuperview().inset(67)
            make.leading.trailing.equalToSuperview().inset(24)
        }
        
        
        text11.snp.makeConstraints { make in
            make.height.equalTo(21)
            make.top.equalTo(contentLabel1.snp.bottom).offset(3)
            make.leading.trailing.equalToSuperview().inset(24)
        }
        
        text12.snp.makeConstraints { make in
            make.top.equalTo(text11.snp.bottom)
            make.leading.trailing.equalToSuperview().inset(24)
            make.height.equalTo(45)
        }
        
        
        contentLabel2.snp.makeConstraints { make in
            make.height.equalTo(21)
            make.top.equalTo(text12.snp.bottom).offset(16)
            make.leading.trailing.equalToSuperview().inset(24)
        }
        
        text21.snp.makeConstraints { make in
            make.height.equalTo(21)
            make.top.equalTo(contentLabel2.snp.bottom).offset(3)
            make.leading.trailing.equalToSuperview().inset(24)
        }
        
        text22.snp.makeConstraints { make in
            make.top.equalTo(text21.snp.bottom)
            make.leading.trailing.equalToSuperview().inset(24)
            make.height.equalTo(45)
        }
        
        contentLabel3.snp.makeConstraints { make in
            make.height.equalTo(21)
            make.top.equalTo(text22.snp.bottom).offset(16)
            make.leading.trailing.equalToSuperview().inset(24)
        }
        
        text31.snp.makeConstraints { make in
            make.height.equalTo(21)
            make.top.equalTo(contentLabel3.snp.bottom).offset(3)
            make.leading.trailing.equalToSuperview().inset(24)
        }
    
        confirmButton.snp.makeConstraints { make in
            make.height.equalTo(40)
            make.bottom.equalToSuperview().inset(24)
            make.leading.trailing.equalToSuperview().inset(24)
        }
        
        confirmButton.addTarget(self, action: #selector(dismissAlert), for: .touchUpInside)
    }
    
    @objc private func ruleAction() {
        dismissAlert()
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {[weak self ] in
            Router.openPointsRulesAgreement()
        }
    }
    
    // MARK: - Public Methods
    func show( in view: UIView) {
        
        view.addSubview(self)
        self.frame = view.bounds
        
        // 添加显示动画
        containerView.transform = CGAffineTransform(scaleX: 0.1, y: 0.1)
        UIView.animate(withDuration: 0.3, delay: 0, options: .curveEaseOut) {
            self.containerView.transform = .identity
        }
    }
    
    @objc private func dismissAlert() {
        UIView.animate(withDuration: 0.2, animations: {
            self.alpha = 0
        }) { _ in
            self.removeFromSuperview()
        }
    }
}



class PointsAlertTextView: UIView {
    
    private let containerView: UIView = {
        let view = UIView()
        view.backgroundColor = .black
        view.layer.cornerRadius = 2
        return view
    }()
    
    
    public let titleLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 14, weight: .regular)
        label.textColor = UIColor("#1E1F20")
        label.numberOfLines = 0
        return label
    }()
    
    // MARK: - Init
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
    }
    
    func setupUI() {
        self.backgroundColor = .white
        self.addSubviews([containerView, titleLabel ])
        containerView.snp.makeConstraints { make in
            make.leading.equalToSuperview().inset(6)
            make.top.equalToSuperview().inset(8)
            make.height.width.equalTo(4)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.leading.equalToSuperview().inset(15)
            make.top.trailing.equalToSuperview()
        }
    }
    
}
