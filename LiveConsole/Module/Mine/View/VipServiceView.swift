//
//  VipServiceView.swift
//  LivePlus
//
//  Created by 郭炜 on 2022/8/9.
//

import UIKit
import RxSwift

protocol VipServiceViewDelegate: NSObjectProtocol {
    func dismiss()
    func save(image: UIImage)
}

class VipServiceView: UIView {

    weak var delegate: VipServiceViewDelegate?
    
    lazy var backView: UIView = {
        let view = UIView()
        return view
    }()
    
    lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.text = "您已成功开通VIP"
        label.textColor = .white
        label.font = LCDevice.DIN_Font_PF_M(20)
        return label
    }()
    
    lazy var descripLabel: UILabel = {
        let label = UILabel()
        label.text = "使用微信扫一扫下方二维码"
        label.textColor = UIColor("#797B7D")
        label.font = LCDevice.DIN_Font_PF_R(16)
        return label
    }()
    
    lazy var detailLabel: UILabel = {
        let label = UILabel()
        label.text = "小加老师"
        label.textColor = UIColor("#797B7D")
        label.font = LCDevice.DIN_Font_PF_R(14)
        return label
    }()
    
    lazy var backImageView: UIImageView = {
        let imageView = UIImageView(image: UIImage(named: "vip_service"))
        return imageView
    }()
    
    lazy var closeButton: UIButton = {
        let button = UIButton { [weak self] in
            guard let self = self, let delegate = self.delegate else { return }
            delegate.dismiss()
        }
        button.setImage(UIImage(named: "close_vip"), for: .normal)
        return button
    }()
    
    lazy var saveButton: UIButton = {
        let button = UIButton { [weak self] in
            guard let self = self,
                    let delegate = self.delegate,
                    let image = self.qrCodeImageView.image else { return }
            delegate.save(image: image)
        }
        button.backgroundColor = .white
        button.cornerRadius = 19
        button.setTitle("点击保存，添加助教老师", for: .normal)
        button.setTitleColor(UIColor("#6974F2"), for: .normal)
        button.titleLabel?.font = LCDevice.DIN_Font_PF_M(17)
        return button
    }()
    
    lazy var qrCodeBoardImage: UIImageView = {
        let imageView = UIImageView(image: UIImage(named: "qrcode_board"))
        return imageView
    }()
    
    lazy var qrCodeImageView: UIImageView = {
        let imageView = UIImageView()
        return imageView
    }()
    
    let disposeBag = DisposeBag()

    override init(frame: CGRect) {
        super.init(frame: frame)
        
        makeUI()
        business()
    }

    convenience init() {
        self.init(frame: .zero)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    func makeUI() {
        addSubviews([backView, closeButton])
        backView.addSubviews([backImageView, titleLabel, descripLabel, detailLabel, saveButton, qrCodeBoardImage, qrCodeImageView])
        
        backView.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview()
            make.height.equalTo(backView.snp.width).multipliedBy(394.0 / 310.0)
        }
        closeButton.snp.makeConstraints { make in
            make.top.equalTo(backView.snp.bottom).offset(20)
            make.centerX.equalToSuperview()
            make.width.height.equalTo(28)
        }
        backImageView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(18)
            make.centerX.equalToSuperview()
            make.height.equalTo(28)
        }
        descripLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(36)
            make.centerX.equalToSuperview()
            make.height.equalTo(22)
        }
        qrCodeBoardImage.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalTo(descripLabel.snp.bottom).offset(14)
            make.width.height.equalTo(132)
        }
        qrCodeImageView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalTo(descripLabel.snp.bottom).offset(15)
            make.width.height.equalTo(130)
        }
        detailLabel.snp.makeConstraints { make in
            make.top.equalTo(qrCodeImageView.snp.bottom).offset(12)
            make.centerX.equalToSuperview()
            make.height.equalTo(20)
        }
        saveButton.snp.makeConstraints { make in
            make.bottom.equalToSuperview().inset(25)
            make.leading.trailing.equalToSuperview().inset(24)
            make.height.equalTo(38)
        }
    }
    
    func business() {

    }
}

extension VipServiceView {
    func loadQrCode(urlString: String, title: String? = nil) {
        qrCodeImageView.loadImageInDisk(with: URL(string: urlString))
        if let title = title {
            titleLabel.text = title
        }
    }
}
