//
//  PointsPayCell.swift
//  LiveConsole
//
//  Created by simon on 22.5.25.
//

import Foundation
import UIKit

class PointsPayCell: UICollectionViewCell {
    
    lazy var background: UIImageView = {
        let view = UIImageView()
        view.backgroundColor = UIColor("#282828")
        view.image = UIImage(named: "vip_new_p_nor")
        view.cornerRadius = 12
        return view
    }()
    
    lazy var iconView: UIImageView = {
        let imageView = UIImageView(image: UIImage(named: "vip_new_p4"))
        imageView.contentMode = .scaleAspectFill
        return imageView
    }()
    
    lazy var memberTitle: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        label.textColor = .white
        return label
    }()
    
    lazy var memberPrice: UILabel = {
        let label = UILabel()
        label.textAlignment = .center
        label.textColor = .white
        label.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        return label
    }()
    
    lazy var memberTime: UILabel = {
        let label = UILabel()
        label.textColor = UIColor.white
        label.text = "积分"
        label.font = UIFont.systemFont(ofSize: 12)
        return label
    }()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        
        makeUI()
        business()
    }
    
    convenience init() {
        self.init(frame: .zero)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func makeUI() {
        contentView.backgroundColor = .clear
        backgroundColor = .clear
        
        contentView.addSubviews([background])
        background.addSubviews([iconView, memberTitle, memberPrice, memberTime])
        
        background.snp.makeConstraints { make in
            make.bottom.leading.trailing.top.equalToSuperview()
        }
        
        iconView.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(2)
            make.leading.equalToSuperview().inset(6)
            make.width.equalTo(74)
            make.height.equalTo(55)
        }
        
        memberTitle.snp.makeConstraints { make in
            make.leading.equalTo(iconView.snp.trailing).offset(1)
            make.centerY.equalTo(iconView.snp.centerY)
            make.height.equalTo(19)
        }
        
        memberTime.snp.makeConstraints { make in
            make.leading.equalTo(memberTitle.snp.trailing).offset(3)
            make.bottom.equalTo(memberTitle.snp.bottom)
        }
        
        memberPrice.snp.makeConstraints { make in
            make.trailing.leading.equalToSuperview().inset(8)
            make.bottom.equalToSuperview().inset(8)
            make.height.equalTo(20)
        }
        
    }
    
    func business() {
        
    }
    
}

extension PointsPayCell {
    func bind(to model: PointsProductsModel) {
        guard let discountPrice = model.discountPrice else { return }
        
        let  price = Int(discountPrice)
        updateSelected(selected: model.defaultPro ?? false)
        memberTitle.text = model.name
        
        let attributed = NSMutableAttributedString()
        let a1 = NSAttributedString(string: "￥", attributes: [.font: LCDevice.DIN_Font_PF_M(14), .foregroundColor: UIColor.white])
        attributed.append(a1)
        let a2 = NSAttributedString(string: "\(price)", attributes: [.font: LCDevice.DIN_Font_PF_M(16), .foregroundColor: UIColor("#FFC260")])
        attributed.append(a2)
        memberPrice.attributedText = attributed
        
        if let pic = model.pic?.first, let url = URL(string: pic) {
            iconView.setImageWith(url, placeholder: nil)
        }
       
    }
    
    func updateSelected(selected: Bool) {
        background.image = selected ? UIImage(named: "vip_new_p_sel") : UIImage(named: "vip_new_p_nor")
    }
}
