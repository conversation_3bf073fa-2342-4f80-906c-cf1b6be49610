//
//  MineTopView.swift
//  LivePlus
//
//  Created by Brian-<PERSON> on 2021/12/16.
//

import UIKit
import SnapKit

public enum MineUserLoginStatus {
    case NotLoginST
    case AlreadyLoginST
    case VIPLoginST
    case TrialVipLoginST
}

public protocol MineTopViewDelegate: AnyObject {
    func headIconAction()
    
    func openingMember()
    
    func scanAction()
    
}

class MineTopView: UIView {

    @IBOutlet weak var bgImg: UIImageView!
    @IBOutlet weak var vipInfoIcon: UIImageView!
    @IBOutlet weak var vipInfoView: UIView!
    @IBOutlet weak var bgBedingImg: UIImageView!
    @IBOutlet weak var iconJump: UIImageView!
    
    @IBOutlet weak var noVipLbl: UILabel!
    @IBOutlet weak var headIconBtn: UIButton!
    @IBOutlet weak var nameLbl: UILabel!
    
    @IBOutlet weak var expirationDateLbl: UILabel!
    @IBOutlet weak var vipInfoLbl: UILabel!
    
    @IBOutlet weak var vipLab1: UILabel!
    @IBOutlet weak var vipLab2: UILabel!
    @IBOutlet weak var gotoMemberButton: UIButton!
    
    @IBOutlet weak var scanBtn: UIButton!

    // 免费用户的
    let baseView = UIView()
    let openBtn = UIButton(type: .custom)
    let bgImagV = UIImageView()
//    weak var delegate: OpeningMemberCellDelegate?
//    var _isMember = false
    let titleLab = UILabel()
    let subtitleLab = UILabel()
    
    var loginStatus: MineUserLoginStatus?
    weak var delegate: MineTopViewDelegate?
    
    override func awakeFromNib() {
        createBaseUI()
        headIconBtn.setTitle("", for: .normal)
        nameLbl.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(gotoLoginAction)))
        nameLbl.isUserInteractionEnabled = true
        headIconBtn.layer.borderWidth = 1
        headIconBtn.layer.cornerRadius = headIconBtn.width/2
        headIconBtn.layer.masksToBounds = true
        headIconBtn.layer.borderColor = UIColor.white.cgColor
        guard let model = UserInfo.currentUser() else {
            return
        }
        loginStatus = .NotLoginST
        setMemberUseInfo(userModel: model)
    }
    
    // MARK: - UI
    fileprivate func createBaseUI() {
        self.backgroundColor = .clear
        let vH: CGFloat = 104.0
        let vW: CGFloat = LCDevice.screenW - 16 * 2
        
        baseView.frame = CGRect(x: 16, y: 143, width: vW, height: vH )
        self.addSubview(baseView)
        baseView.isHidden = true
        
        bgImagV.frame = CGRect(x: 0, y: 0, width: vW, height: vH)
        bgImagV.image = UIImage(named: "mine_free_bg")
        bgImagV.roundCorners([.topLeft, .bottomLeft], radius: 10.0)
        baseView.addSubview(bgImagV)
        
        openBtn.frame = CGRect(x: vW - LCDevice.DIN_WIDTH(90) - LCDevice.DIN_WIDTH(20), y: (vH - LCDevice.DIN_WIDTH(28))/2.0, width: LCDevice.DIN_WIDTH(84), height: LCDevice.DIN_WIDTH(28))
        openBtn.addTarget(self, action: #selector(openBtnAction(send:)), for: .touchUpInside)
        openBtn.setTitleColor(UIColor("#6974F2"), for: .normal)
        openBtn.titleLabel?.font = LCDevice.DIN_Font_PF_M(14)
        openBtn.setTitle("立即升级", for: .normal)
        openBtn.cornerRadius = openBtn.height / 2.0
        openBtn.setImage(UIImage(named: "icon_mine_open_tip"), for: .normal)
        openBtn.imagePosition(style: .right, spacing: 2)
        openBtn.setBackgroundImage(UIImage(color: UIColor("#ffffff")), for: .normal)
        baseView.addSubview(openBtn)
        
        let lblW: CGFloat = 150.0
        let lblSpace: CGFloat = LCDevice.DIN_WIDTH(20)
        titleLab.frame = CGRect(x: lblSpace, y: LCDevice.DIN_WIDTH(16), width: lblW, height: LCDevice.DIN_WIDTH(18))
        titleLab.textColor = .white
        titleLab.font = LCDevice.DIN_Font_PF_S(15)
        titleLab.text = "您当前是免费用户"
        titleLab.textColor = .white
        baseView.addSubview(titleLab)

        subtitleLab.frame = CGRect(x: lblSpace, y: titleLab.bottom + 6, width: lblW + 50, height: LCDevice.DIN_WIDTH(40))
        subtitleLab.textColor = .white
        subtitleLab.font = LCDevice.DIN_Font_PF_R(12)
        subtitleLab.numberOfLines = 0
        subtitleLab.text = "升级会员享更多功能（遥控、关键词回复、设置备份恢复）"
        baseView.addSubview(subtitleLab)
        
        
        bgImagV.image = UIImage(named: "mine_free_bg")?.resizableImage(withCapInsets: UIEdgeInsets(top: 20, left: 5, bottom: 20, right: 170), resizingMode: .stretch)
        
    }
    
    func setMemberUseInfo(userModel: UserInfo ) {
        scanBtn.isHidden = true
        if let imgStr = userModel.headImgUrl, let urlImg = URL(string: imgStr) {
            headIconBtn.layer.borderWidth = 1
            headIconBtn.layer.borderColor = UIColor.white.cgColor
            headIconBtn.setBackgroundImageWith(urlImg, for: .normal, placeholder: UIImage(named: "icon_user_headerDefault"))
        }
        self.nameLbl.text = userModel.nickName
        
         let exT = ""
        // 2023-06-13 23:59:59
        gotoMemberButton.isUserInteractionEnabled = false

        if UserInfo.isMember {
            switch userModel.vipLevelId {
            case .svip, .vip:
                vipLab1.text = "智能场控会员权益\(LCTools.covertDateString(timeString: exT))到期"
                vipInfoLbl.text = "您当前是智能场控会员"
                vipLab1.isHidden = false
                vipLab2.isHidden = true
            case .normal:
                vipLab1.isHidden = true
                vipLab2.isHidden = true
            default:
                break
            }
            gotoMemberButton.isUserInteractionEnabled = true

            scanBtn.isHidden = true
        }  else if UserInfo.isTrialVip {
            if let ex = userModel.trialExpireTime {
                vipLab1.text = "智能场控会员权益\(LCTools.covertDateString(timeString: ex))到期"
            }
           
            vipInfoLbl.text = "恭喜您成为免费体验会员"
            vipLab1.isHidden = false
            vipLab2.isHidden = true
            gotoMemberButton.isUserInteractionEnabled = true
        }else {
            vipLab1.isHidden = true
            vipLab2.isHidden = true

            scanBtn.isHidden = true
        }
         
        scanBtn.zl_enlargeValidTouchArea(inset: 8)
    }
    
    
    @IBAction func scanAction(_ sender: Any) {
        self.delegate?.scanAction()
    }
    
    public func setLoginStatus(status: MineUserLoginStatus) {
        scanBtn.isHidden = true
        loginStatus = status
        switch status {
        case .NotLoginST:
            self.bgImg.isHidden = true
            self.bgBedingImg.isHidden = true
            self.nameLbl.y = 80
            self.nameLbl.textColor = UIColor("#1E1F20")
            self.nameLbl.text = "注册/登录"
            self.nameLbl.font = LCDevice.DIN_Font_PF_S(20)
            self.vipInfoView.isHidden = true
            self.vipInfoIcon.isHidden = true
            self.noVipLbl.isHidden = true
            baseView.isHidden = true
            self.iconJump.isHidden = true
//            headIconBtn.setBackgroundImage(UIImage(named: "icon_user_headerDefault"), for: .normal)

        case .AlreadyLoginST:
            scanBtn.isHidden = true
            baseView.isHidden = false
            self.bgImg.isHidden = true
            self.bgBedingImg.isHidden = true
            self.nameLbl.y = 70
            self.nameLbl.textColor = UIColor("#2A2A2A")
            self.nameLbl.font = LCDevice.DIN_Font_PF_M(16)
            self.vipInfoView.isHidden = true
            self.vipInfoIcon.isHidden = true
            self.noVipLbl.isHidden = true
            self.iconJump.isHidden = true
            guard let model = UserInfo.currentUser() else {
                return
            }
            setMemberUseInfo(userModel: model)
        case .VIPLoginST:
            scanBtn.isHidden = true
            baseView.isHidden = true
            self.bgImg.isHidden = false
            self.bgBedingImg.isHidden = false
            self.nameLbl.y = 70
            self.nameLbl.textColor = UIColor("#2A2A2A")
            self.nameLbl.font = LCDevice.DIN_Font_PF_M(16)
            self.vipInfoView.isHidden = false
            self.vipInfoIcon.isHidden = false
            self.noVipLbl.isHidden = true
            self.iconJump.isHidden = false
            guard let model = UserInfo.currentUser() else {
                return
            }
            setMemberUseInfo(userModel: model)
        case .TrialVipLoginST:
            scanBtn.isHidden = true
            baseView.isHidden = true
            self.bgImg.isHidden = false
            self.bgBedingImg.isHidden = false
            self.nameLbl.y = 70
            self.nameLbl.textColor = UIColor("#2A2A2A")
            self.nameLbl.font = LCDevice.DIN_Font_PF_M(16)
            self.vipInfoView.isHidden = false
            self.vipInfoIcon.isHidden = false
            self.noVipLbl.isHidden = true
            self.iconJump.isHidden = false
            guard let model = UserInfo.currentUser() else {
                return
            }
            setMemberUseInfo(userModel: model)
        }
        
    }
    
    @objc func freeBtnAction(send: UIButton) {
       
    }
    
    @IBAction func headIconAction(_ sender: Any) {
        delegate?.headIconAction()
    }
    
    @IBAction func buttonActionByGotoMember(_ sender: Any) {
        delegate?.openingMember()
    }
    
    @objc private func openBtnAction( send: UIButton) {
        delegate?.openingMember()
    }
    
    @objc func gotoLoginAction() {
        if  loginStatus == .NotLoginST {
            delegate?.headIconAction()
        }
    }

}
