//
//  SettingCell.swift
//  LivePlus
//
//  Created by iclick on 2020/12/24.
//

import UIKit

class SettingCell: UITableViewCell {
   
    let baseView = UIView()
    let titleLab = UILabel()
    let nicknameLab = UILabel()//已经绑定的手机号或者微信号
    let arrowView = UIImageView()
    let sepLine = UIView()
    let newVersionLab = UILabel()//版本更新
    let curVersionLab = UILabel()// 当前版本

    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        //cell点击无选中色
        let selectedView = UIView(frame: self.frame)
        selectedView.backgroundColor = .clear
        self.selectedBackgroundView = selectedView
        
        createBaseUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - UI
    fileprivate func createBaseUI() {
        self.contentView.backgroundColor = UIColor("#111111")
        let inset: CGFloat = 16 * 2
        baseView.frame = CGRect(x: 16, y: 0, width: contentView.width - inset, height: contentView.height)
        
        baseView.backgroundColor = UIColor("#282828")
        baseView.autoresizingMask = [.flexibleWidth,.flexibleHeight]
        contentView.addSubview(baseView)
                
        titleLab.textColor = .white
        titleLab.font = LCDevice.DIN_Font_PF_M(14)
        baseView.addSubview(titleLab)
        titleLab.snp.makeConstraints { make in
            make.leading.equalToSuperview().inset(16)
            make.centerY.equalToSuperview()
            make.trailing.equalToSuperview().inset(200)
            make.height.equalTo(30)
        }
        

        arrowView.centerY = titleLab.centerY
        arrowView.image = UIImage(named: "icon_user_arrow")
        baseView.addSubview(arrowView)
        
        arrowView.snp.makeConstraints { make in
            make.trailing.equalToSuperview().inset(16)
            make.centerY.equalToSuperview()
            make.height.width.equalTo(24)
        }
        
        sepLine.backgroundColor = UIColor("#444444")
        baseView.addSubview(sepLine)
        sepLine.snp.makeConstraints { make in
            make.leading.trailing.bottom.equalToSuperview()
            make.height.equalTo(1)
        }
      
        
        nicknameLab.textAlignment = .right
        nicknameLab.font = LCDevice.DIN_Font_PF_R(14)
        nicknameLab.textColor = UIColor("#919499")
        nicknameLab.isHidden = true
        baseView.addSubview(nicknameLab)
        
        nicknameLab.snp.makeConstraints { make in
            make.trailing.equalToSuperview().inset(50)
            make.leading.equalToSuperview().inset(200)
            make.centerY.equalToSuperview()
            make.height.equalTo(24)
        }
        
        
        newVersionLab.layer.cornerRadius = newVersionLab.height/2
        newVersionLab.layer.borderWidth = LCDevice.DIN_WIDTH(0.5)
        newVersionLab.layer.borderColor = UIColor("#6974F2").cgColor
        newVersionLab.text = "更新"
        newVersionLab.textColor = UIColor("#6974F2")
        newVersionLab.font = LCDevice.DIN_Font_PF_M(14)
        newVersionLab.isHidden = true
        newVersionLab.textAlignment = .center
        baseView.addSubview(newVersionLab)
        newVersionLab.snp.makeConstraints { make in
            make.trailing.equalToSuperview().inset(50)
            make.leading.equalToSuperview().inset(200)
            make.centerY.equalToSuperview()
            make.height.equalTo(24)
        }
        
        
        curVersionLab.text = "v" + LCDevice.KBundleShortVersionString
        curVersionLab.textColor = UIColor("#919499")
        curVersionLab.font = LCDevice.DIN_Font_PF_R(14)
        curVersionLab.isHidden = true
        curVersionLab.textAlignment = .right
        baseView.addSubview(curVersionLab)
        curVersionLab.snp.makeConstraints { make in
            make.trailing.equalToSuperview().inset(50)
            make.leading.equalToSuperview().inset(200)
            make.centerY.equalToSuperview()
            make.height.equalTo(24)
        }
    }
    
    func config(with title:String , isShowArrow:Bool = true, phoneNumber:String? = nil, wechatName:String? = nil,hasNewVersion:Bool = false , showVersionInfo:Bool = false, showSepLine: Bool = true) {
        titleLab.text = title
        
        arrowView.isHidden = !isShowArrow
        sepLine.top = baseView.height - 1
        
        sepLine.isHidden = !showSepLine
        
        nicknameLab.isHidden = true
        if let pn = phoneNumber {
            nicknameLab.isHidden = false
            nicknameLab.text = pn
            nicknameLab.textColor = UIColor("#919499")
            nicknameLab.font = LCDevice.DIN_Font_PF_R(14)
        }
        if let wn = wechatName {
            nicknameLab.isHidden = false
            nicknameLab.text = wn
            nicknameLab.textColor = UIColor("#6974F2")
            nicknameLab.font = LCDevice.DIN_Font_PF_M(14)
        }
        
        newVersionLab.isHidden = true
        curVersionLab.isHidden = !showVersionInfo
        if hasNewVersion {
            curVersionLab.isHidden = true
            newVersionLab.isHidden = false
        }
    }

    func setRadiusForType(with type: cellCornerRadiusType) {
        switch type {
        case .AllRadius:
            baseView.roundCorners([.bottomLeft, .bottomRight, .topLeft, .topRight], radius: 10)
        case .UpRadius:
            baseView.roundCorners([.topLeft, .topRight], radius: 10)
        case .DownRadius:
            baseView.roundCorners([.bottomLeft, .bottomRight], radius: 10)
        default:
            baseView.roundCorners([.bottomLeft, .bottomRight, .topLeft, .topRight], radius: 0)
        }
    }
}
