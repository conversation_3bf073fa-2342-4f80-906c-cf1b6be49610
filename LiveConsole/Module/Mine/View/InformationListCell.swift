//
//  InformationListCell.swift
//  LivePlus
//
//  Created by simon on 17.2.22.
//

import Foundation
import UIKit


public protocol InformationListCellDelegate: AnyObject {
    func gotoSystemSeting(index: Int)    // 系统设置页面
    
}

class InformationListCell: UITableViewCell {
    weak var delegate: InformationListCellDelegate?
    let baseView = UIView()
    let titleLab = UILabel()
    let contentLab = UILabel()
    let arrowView = UIImageView()
    let setBtn = UIButton()
    var index = Int(0)
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        //cell点击无选中色
        let selectedView = UIView(frame: self.frame)
        selectedView.backgroundColor = .clear
        self.selectedBackgroundView = selectedView
        
        createBaseUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - UI
    fileprivate func createBaseUI() {
        self.contentView.backgroundColor = .clear
        self.backgroundColor = .clear
        baseView.frame = CGRect(x: 8, y: 0, width: contentView.width - 16, height: contentView.height)
        baseView.backgroundColor = .white
        baseView.autoresizingMask = [.flexibleWidth, .flexibleHeight]
        contentView.addSubview(baseView)
                
        titleLab.frame = CGRect(x: LCDevice.DIN_WIDTH(15), y: 10, width: 200, height: LCDevice.DIN_WIDTH(30))
        titleLab.textColor = UIColor("#161618")
        titleLab.font = LCDevice.DIN_Font_PF_M(14)
        baseView.addSubview(titleLab)
        contentLab.numberOfLines = 0
//        contentLab.frame = CGRect(x: LCDevice.DIN_WIDTH(15), y: 40, width: LCDevice.screenW - 40, height: LCDevice.DIN_WIDTH(90))
        contentLab.textColor = UIColor("#4D4E52")
        contentLab.font = LCDevice.DIN_Font_PF_R(12)
        baseView.addSubview(contentLab)
        contentLab.mas_makeConstraints { maker in
            maker?.left.equalTo()(titleLab)
            maker?.top.equalTo()(titleLab.mas_bottom)
            maker?.width.equalTo()(LCDevice.screenW - LCDevice.DIN_WIDTH(120))
        }
       
        arrowView.frame = CGRect(x: LCDevice.screenW - 16 - 16, y: 0, width: LCDevice.DIN_WIDTH(24), height: LCDevice.DIN_WIDTH(24))
        arrowView.image = UIImage(named: "icon_user_arrow")
        baseView.addSubview(arrowView)
        arrowView.mas_makeConstraints { maker in
            maker?.right.equalTo()(baseView.mas_right)?.offset()(-8)
            maker?.centerY.equalTo()(baseView)
            maker?.width.equalTo()(LCDevice.DIN_WIDTH(16))
            maker?.height.equalTo()( LCDevice.DIN_WIDTH(16))
        }
        
        baseView.addSubview(setBtn)
        setBtn.backgroundColor = .clear
        setBtn.setTitle("已开启", for: .normal)
        setBtn.setTitle("去设置", for: .selected)
        setBtn.setTitleColor(UIColor("#4D4E52"), for: .normal)
        setBtn.setTitleColor(UIColor("#6863F7"), for: .selected)
        setBtn.titleLabel?.font = UIFont.systemFont(ofSize: 13)
        setBtn.addTarget(self, action: #selector(openBtnAction(send:)), for: .touchUpInside)
        setBtn.mas_makeConstraints { maker in
            maker?.right.equalTo()(baseView.mas_right)?.offset()(-20)
            maker?.centerY.equalTo()(baseView)
            maker?.width.equalTo()(LCDevice.DIN_WIDTH(55))
            maker?.height.equalTo()(LCDevice.DIN_WIDTH(30))
        }
    }
    
    func config(with title: String, content: String, open: Bool, index: Int) {
        titleLab.text = title
        contentLab.text = content
        setBtn.isSelected = !open
        self.index = index
    }

    @objc private func openBtnAction( send: UIButton) {
        self.delegate?.gotoSystemSeting(index: self.index)
    }
    
}
