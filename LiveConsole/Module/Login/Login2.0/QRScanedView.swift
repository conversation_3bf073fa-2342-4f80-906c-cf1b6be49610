//
//  QRScanedView.swift
//  LivePlus
//
//  Created by simon on 12.6.24.
//

import Foundation

class QRScanedView: UIView {
    
    // 过期
    lazy var errorView: UIView = {
        let imageView = UIView()
        imageView.backgroundColor = UIColor.white.alpha(value: 0.8)
        return imageView
    }()
    
    // 重试
    lazy var retryButton: UIButton = {
        let button = UIButton(frame: CGRect(x: 0, y: 0, width: 30, height: 30))
        button.setImage(UIImage(named: "扫描成功"), for: .normal)
        button.backgroundColor = .clear
        return button
    }()
    
    lazy var titleLab: UILabel = {
        let vi = UILabel()
        vi.text = "扫码成功"
        vi.textColor = UIColor("#1E1F20")
        vi.font = UIFont.systemFont(ofSize: 20, weight: .heavy)
        vi.textAlignment = .center
        return vi
    }()
    
    lazy var desLab: UILabel = {
        let vi = UILabel()
        vi.numberOfLines = 0
        let paragraphStyle = NSMutableParagraphStyle()
        paragraphStyle.lineSpacing = 8
        let attribute = NSMutableAttributedString(string: "请在扫码手机上确认登录授权", attributes: [.foregroundColor: UIColor("#4D4E52"), .font: UIFont.systemFont(ofSize: 15), NSAttributedString.Key.paragraphStyle :paragraphStyle ])
        vi.attributedText = attribute
        vi.textAlignment = .center
        return vi
    }()
    
    lazy var iconImage: UIImageView = {
        let imageView = UIImageView(image: UIImage(named: ""))
        return imageView
    }()

    
    override init(frame: CGRect) {
        super.init(frame: frame)
        
        makeUI()
        business()
    }

    convenience init() {
        self.init(frame: .zero)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    func makeUI() {
        addSubviews([iconImage, titleLab, desLab, errorView])
        errorView.addSubview(retryButton)

        iconImage.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalToSuperview().inset(100)
            make.width.equalTo(180)
            make.height.equalTo(180)
        }
        
        
        titleLab.snp.makeConstraints { make in
            make.top.equalTo(iconImage.snp.bottom).offset(40)
            make.centerX.equalToSuperview()
            make.width.equalTo(180)
            make.height.equalTo(30)
        }
        
        desLab.snp.makeConstraints { make in
            make.top.equalTo(titleLab.snp.bottom).offset(20)
            make.leading.trailing.equalToSuperview().inset(89)
        }
        
        
        errorView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalToSuperview().inset(100)
            make.width.equalTo(180)
            make.height.equalTo(180)
        }
        
        retryButton.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview()
            make.width.equalTo(180)
            make.height.equalTo(100)
        }
       
      
    }
    
    func business() {

    }
}
