//
//  MainLoginVC.swift
//  LivePlus
//
//  Created by 郭炜 on 2022/7/7.
//

import UIKit
import YYKit
import SnapKit
import RxSwift

class MainLoginVC: BaseVC {
    
    ///登录成功后自动返回到起点
    var needAutoBackAfterDone = false
    var loginSuccessBlock:(() -> Void)?
    var popToRoot: Bool = false
    
    lazy var backButton: UIButton = {
        let button = UIButton()
        button.setImage(UIImage(named: "icon_nav_back_gray"), for: .normal)
        return button
    }()
    
    
    lazy var scanedView: QRScanedView = {
        let imageView = QRScanedView()
        imageView.backgroundColor = .white
        imageView.isHidden = true
        return imageView
    }()
    
    // 过期
    lazy var errorView: UIView = {
        let imageView = UIView()
        imageView.backgroundColor = UIColor.black.alpha(value: 0.7)
        imageView.isHidden = true
        return imageView
    }()
    
    // 重试
    lazy var retryButton: UIButton = {
        let button = UIButton(frame: CGRect(x: 0, y: 0, width: 180, height: 100))
        button.setImage(UIImage(named: "重试"), for: .normal)
        button.setTitle("刷新二维码", for: .normal)
        button.setTitleColor(UIColor.white, for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 16)
        button.imagePosition(style: .top, spacing: 12)
        button.backgroundColor = .clear
        return button
    }()
    
    lazy var titleLab: UILabel = {
        let vi = UILabel()
        vi.text = "扫码登录"
        vi.textColor = UIColor("#1E1F20")
        vi.font = UIFont.systemFont(ofSize: 20, weight: .heavy)
        vi.textAlignment = .center
        return vi
    }()
    
    lazy var desLab: UILabel = {
        let vi = UILabel()
        vi.numberOfLines = 0
        let paragraphStyle = NSMutableParagraphStyle()
        paragraphStyle.lineSpacing = 8
        let attribute = NSMutableAttributedString(string: "可使用已登录快瓴中控台的手机\n进入“我的”页面，点击扫一扫\n扫描下方二维码", attributes: [.foregroundColor: UIColor("#4D4E52"), .font: UIFont.systemFont(ofSize: 15), NSAttributedString.Key.paragraphStyle :paragraphStyle ])
        vi.attributedText = attribute
        vi.textAlignment = .center
        return vi
    }()
    
    lazy var iconImage: UIImageView = {
        let imageView = UIImageView(image: UIImage(named: ""))
        return imageView
    }()
    
    lazy var checkButton: UIButton = {
        let button = UIButton { [weak self] in
            guard let self = self else { return }
            self.checkButton.isSelected = !self.checkButton.isSelected
        }
        button.setImage(UIImage(named: "登录流程_组件_cell_勾选_nor"), for: .normal)
        button.setImage(UIImage(named: "icon_勾选_sel"), for: .selected)
        button.zl_enlargeValidTouchArea(inset: 22)
        return button
    }()
    
    lazy var loginProtocol: YYLabel = {
        let label = YYLabel()
        label.text = "登录即同意《用户服务协议》&《隐私政策》"
        return label
    }()
    
    lazy var wechatLoginButton: UIButton = {
        let button = UIButton()
        button.setImage(UIImage(named: "wechat"), for: .normal)
        button.setTitle("微信登录", for: .normal)
        button.setTitleColor(UIColor("#434447"), for: .normal)
        button.titleLabel?.font = LCDevice.DIN_Font_PF_R(16)
        button.cornerRadius = 26
        button.imagePosition(style: .left, spacing: 12)
        button.backgroundColor = .white
        button.borderWidth = 1
        button.borderColor = UIColor("#C1C1C1")
        return button
    }()
    
    lazy var phoneLoginButton: UIButton = {
        let button = UIButton()
        button.setImage(UIImage(named: "手机2 1"), for: .normal)
        button.setTitle("注册/登录", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.titleLabel?.font = LCDevice.DIN_Font_PF_R(16)
        button.imagePosition(style: .left, spacing: 12)
        button.cornerRadius = 26
        button.backgroundColor =  UIColor("#6974F2")
        return button
    }()
    
    /// 渐变层
    lazy var gradientLayer: CAGradientLayer = {
        let leftColor = UIColor("#6974F2")
        let rightColor = UIColor("#AC63F9")
        let gradientColors = [leftColor.cgColor, rightColor.cgColor]
        let gradientLocations: [NSNumber] = [0.0, 1.0]
        let gradientLayer = CAGradientLayer()
        gradientLayer.startPoint = CGPoint(x: 0, y: 0)
        gradientLayer.endPoint = CGPoint(x: 1, y: 0)
        gradientLayer.colors = gradientColors
        gradientLayer.locations = gradientLocations
        return gradientLayer
    }()

    var qrcode: String?
    
    var timer: Timer?
    
    var token: String?
    
    // 计数器 20次
    var count: Int = 0
    
    // 扫描成功的计时器
    var scanTimer: Timer?
    
    let max = 30
    
    let time = 3
    
    deinit {
        releaseTimer()
        NotificationCenter.default.removeObserver(self)
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        //禁掉侧滑手势 点击返回的时候 释放计时器
        fd_interactivePopDisabled = true
        makeUI()
        business()
        //
       
        retryButton.imagePosition(style: .top, spacing: 12)
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        requestQR()
    }
    
    
    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
//        gradientLayer.frame = wechatLoginButton.bounds
    }
    
    func makeUI() {
        view.backgroundColor = .white
        view.addSubviews([backButton, iconImage, checkButton, loginProtocol, wechatLoginButton, phoneLoginButton, titleLab, desLab, errorView])
//        phoneLoginButton.layer.insertSublayer(gradientLayer, at: 0)
        view.addSubview(scanedView)
        errorView.addSubview(retryButton)

        backButton.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(LCDevice.Nav_H - 35)
            make.leading.equalToSuperview().inset(10)
            make.width.height.equalTo(40)
        }
        
        titleLab.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(146)
            make.centerX.equalToSuperview()
            make.width.equalTo(180)
            make.height.equalTo(30)
        }
        
        desLab.snp.makeConstraints { make in
            make.top.equalTo(titleLab.snp.bottom).offset(20)
            make.leading.trailing.equalToSuperview().inset(50)
        }
        
        iconImage.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalTo(desLab.snp.bottom).offset(40)
            make.width.equalTo(180)
            make.height.equalTo(180)
        }
        
        
        errorView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalTo(desLab.snp.bottom).offset(40)
            make.width.equalTo(180)
            make.height.equalTo(180)
        }
        
        retryButton.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview()
            make.width.equalTo(180)
            make.height.equalTo(100)
        }
        
        
        checkButton.snp.makeConstraints { make in
            make.leading.equalToSuperview().inset(43)
            make.centerY.equalTo(loginProtocol.snp.centerY)
            make.width.height.equalTo(26)
        }
        
        loginProtocol.snp.makeConstraints { make in
            make.top.equalTo(wechatLoginButton.snp.bottom).offset(25)
            make.leading.equalTo(checkButton.snp.trailing).offset(8)
            make.height.equalTo(22)
        }
        
        phoneLoginButton.snp.makeConstraints { make in
            make.bottom.equalToSuperview().inset(177)
            make.centerX.equalToSuperview()
            make.leading.trailing.equalToSuperview().inset(43)
            make.height.equalTo(52)
        }
        
        wechatLoginButton.snp.makeConstraints { make in
            make.top.equalTo(phoneLoginButton.snp.bottom).offset(20)
            make.centerX.equalToSuperview()
            make.leading.trailing.equalToSuperview().inset(43)
            make.height.equalTo(52)
        }
        
        scanedView.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(88)
            make.leading.trailing.bottom.equalToSuperview()
        }
        
        if !WXApi.isWXAppInstalled() {
            wechatLoginButton.isHidden = true
        }
        
        deployProtocol()
    }
    
    func business() {
        backButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self else { return }
                self.leftButtonAction()
            }.disposed(by: disposeBag)
        
        wechatLoginButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self else { return }
                self.wechatLogin()
            }.disposed(by: disposeBag)
        
        phoneLoginButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self else { return }
                self.phoneLogin()
            }.disposed(by: disposeBag)
        
        retryButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self else { return }
                self.requestQR()
            }.disposed(by: disposeBag)
        
        NotificationCenter.default.addObserver(self, selector: #selector(didGetWeiXinResp(_:)), name: LCKey.noti_didGetWeiXinResponse, object: nil)
    }
    
    override func leftButtonAction() {
        if  !self.scanedView.isHidden {
            self.scanedView.isHidden = true
            releaseTimer()
            // 请求新的二维码
            requestQR()
            return
        }
        
        if popToRoot {
            self.navigationController?.popToRootViewController(animated: true)
        } else {
            super.leftButtonAction()
        }
        releaseTimer()
    }
}

extension MainLoginVC {
    /// 微信登录
    func wechatLogin() {
        /// 微信未安装 走手机号登录的逻辑 防止苹果审核失败
        if !WXApi.isWXAppInstalled() {
//            showUnInstallWechat()
            phoneLogin()
            return
        }
        
        if !self.checkButton.isSelected {
//            self.checkButton.shake()
            HUD.showFail("请先阅读并勾选相关协议政策")
            return
        }
        
        let req = SendAuthReq()
        req.scope = "snsapi_userinfo"
        req.state = "App"
        WXApi.sendAuthReq(req, viewController: self, delegate: self) { (_) in }
        UserDefaults.standard.setValue(true, forKey: LCKey.UD_wxForLogin)
        UserDefaults.standard.setValue(false, forKey: LCKey.UD_wxForBind)
    }
    
    func showUnInstallWechat() {
        let alert = UIAlertController(title: "微信提示", message: "您的手机上没有安装微信，需要您下载安装微信后，重新登录", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "我知道了", style: .default))
        self.present(alert, animated: true)
    }
    
    /// 手机号登录
    func phoneLogin() {
//        if !self.checkButton.isSelected {
//            self.checkButton.shake()
//            HUD.showFail("请先阅读并勾选相关协议政策")
//            return
//        }
        releaseTimer()
        let loginVC = LoginVC()
        loginVC.needAutoBackAfterDone = true
        loginVC.popToRoot = popToRoot
        loginVC.loginSuccessBlock = loginSuccessBlock
        push(to: loginVC)
    }
}

/// 老逻辑
extension MainLoginVC: WXApiDelegate {
    func onReq(_ req: BaseReq) {
        print("前往微信登录")
    }
    
    @objc func didGetWeiXinResp(_ sender: Notification) {
        if !UserDefaults.standard.bool(forKey: LCKey.UD_wxForLogin) {
            return
        }
        UserDefaults.standard.setValue(false, forKey: LCKey.UD_wxForLogin)
        let data = sender.object as! SendAuthResp
        if let code = data.code {
            let param = ["code": code]
            HUD.showWait()
            let req: Observable<Status<BaseRespModel<LoginRespData>>> = APISession.post(APIURL.POST_WeiXin_LOGIN, parameters: param) .asObservable()
            req.subscribe(onNext: {[weak self] status in
                HUD.hideAllHUD()
                switch status {
                case .success(let res):
                    if let user = res.data {
                        user.saveLoginUser()

                        if let userInfo = UserInfo.currentUser() {
    //                        if let phoneBind = userInfo.bindPhone,phoneBind {
                            if let _ = userInfo.phone {
    //                            print("当前手机号：\(phone)")
                                self?.handlePhoneLoginBlock()
                            } else {
                                self?.gotoPhoneBindAlert()
                            }
                        }
                    } else {
                        if let mesg = res.msg {
                            HUD.showFail(mesg)
                        }
                    }
                case .failure(let error):
                    LCLog.d(error.localizedDescription)
                    HUD.showFail(error.localizedDescription)
                case .pending: break
                }
                
            }).disposed(by: disposeBag)
        }
    }
        
    // 登录操作成功
    func handlePhoneLoginBlock() {
        MiddleRequestNet.requestLimit()
        DispatchQueue.main.async {
            if let block = self.loginSuccessBlock {
                block()
            }
            if self.needAutoBackAfterDone {
                self.leftButtonAction()
            }
        }
    }
    
    ///微信登录成功后前往手机号绑定页
    func gotoPhoneBindAlert() {
        let alert = UIAlertController.init(title: "温馨提示", message: "\n根据《网络安全法》等相关法律法规要求,网络用户需验证真实身份信息,请先绑定手机号以使用完整功能。", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "我再想想", style: .default, handler: { [weak self] _ in
            guard let self = self else { return }
            UserInfo.logout()
            NotificationCenter.default.post(name: LCKey.noti_logout, object: nil)
            self.leftButtonAction()
        }))
        alert.addAction(UIAlertAction(title: "去绑定", style: .destructive, handler: { [weak self] _ in
            guard let self = self else { return }
            self.goPhoneBind()
        }))
        present(alert, animated: true, completion: nil)
    }
    
    func goPhoneBind() {
        let bindVc = PhoneBindVC()
        bindVc.bindType = .bind
        bindVc.needBackAfterDone = self.needAutoBackAfterDone
        bindVc.bindSuccessBlock = {[weak self] in
            guard let self = self else { return }
            if !UserInfo.isBindPhone() {
                self.gotoPhoneBindAlert()
                return
            }
            if bindVc.needBackAfterDone {
                bindVc.leftButtonAction()
            }
            self.handlePhoneLoginBlock()
        }
        self.push(to: bindVc)
    }
}

extension MainLoginVC {
    func deployProtocol() {
        let infoString = "已阅读并同意《用户服务协议》&《隐私政策》"
        let agreementFS = "《用户服务协议》"
        let agreementRange = infoString.range(of: agreementFS)!
        let agreementLinkRange = NSRange(agreementRange, in: infoString)
                
        let protocolFS = "《隐私政策》"
        let protocolRange = infoString.range(of: protocolFS)!
        let progotolLinkRange = NSRange(protocolRange, in: infoString)
        
        let attString = NSMutableAttributedString.init(string: infoString)
        attString.font = LCDevice.DIN_Font_PF_M(12)
        attString.alignment = .left
        attString.color = UIColor("#797B7D")
        attString.setTextHighlight(agreementLinkRange, color: UIColor("#6974F2"), backgroundColor: nil) { (_ containerView, _ attText, _ range, _ rect) in
            Router.openUserAgreement()
        }
        attString.setTextHighlight(progotolLinkRange, color: UIColor("#6974F2"), backgroundColor: nil) { (_ containerView, _ attText, _ range, _ rect) in
            Router.openPrivacyAgreement()
        }
        
        loginProtocol.attributedText = attString
    }
}

// MARK: -  扫描登录
extension MainLoginVC {
    
    func requestQR() {
        releaseTimer()
        HUD.showWait()
        MiddleRequestNet.getQRInfo { [weak self] code in
            guard let self = self else { return }
            DispatchQueue.main.async {
                HUD.hideAllHUD()
                self.qrcode = code
                guard let img = LPSocketHelper.createQRimageString(code, sizeWidth: 180, fill: .black) else {
                    return
                }
                self.iconImage.image = img
                self.errorView.isHidden = true
                // 开始轮询
                self.startTimer()
            }
            
        }
    }
    // MARK: - 计时器
    func startTimer() {
        self.count = 0
        releaseTimer()
        self.timer = Timer.scheduledTimer(timeInterval: TimeInterval(time), target: self, selector: #selector(timeCount), userInfo: nil, repeats: true)
    }
    
    func releaseTimer() {
        self.timer?.invalidate()
        self.timer = nil
    }
    
    @objc func timeCount() {
//        if count > max  {
//           // 二维码失效
//            self.errorView.isHidden = false
//            releaseTimer()
//            return
//        }
        
        if let _ = self.token {
            releaseTimer()
            return
        }
        
        guard let  qrcode = self.qrcode else { return }
        self.count = count + 1
        //
        MiddleRequestNet.qrCheckout(uuid: qrcode, completion: { [weak self] result in
            guard let self = self else { return }
            DispatchQueue.main.async {
                self.handleResult(result: result)
            }
        })
    }
    
    func handleResult(result:QRCheckout_Result) {
        guard let code = result.code else { return }
        switch code {
        case 0:
            self.scanedView.isHidden = true
            self.releaseTimer()
            // 保存token  调用更新信息 登录
            if let token = result.token {
                self.token = token
                getUserInfo(token: token)
            }
        case 6001:
//            过期了
            self.errorView.isHidden = false
            releaseTimer()
            self.scanedView.isHidden = true
            HUD.showFail(result.msg ?? "二维码已失效，请重新扫码")
        case 6002:
            break
        case 6003:
            self.scanedView.iconImage.image = self.iconImage.image
            // 扫码成功，请在已登录手机确认登录
            self.scanedView.isHidden = false
        case 6004:
            // 取消
            self.scanedView.isHidden = true
            HUD.showFail(result.msg ?? "已取消")
            self.errorView.isHidden = false
            releaseTimer()
        default:
            break
        }
    }
    
    func getUserInfo(token: String) {
        MiddleRequestNet.getUserInfo(token: token) { [weak self] isPassValite in
            guard let self = self else { return }
            if !isPassValite {
                return
            }
            DispatchQueue.main.async {
                self.updateUserinfo()
            }
        }
    }
    
    func updateUserinfo() {
        NotificationCenter.default.post(name: LCKey.noti_reLogin, object: nil)
        MiddleRequestNet.requestLimit()
        self.leftButtonAction()
    }
    
}
