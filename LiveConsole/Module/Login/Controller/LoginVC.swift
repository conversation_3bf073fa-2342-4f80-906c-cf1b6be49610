//
//  LoginVC.swift
//  LivePlus
//
//  Created by iclick on 2020/12/24.
//

import UIKit
import RxSwift

/// 登录页
class LoginVC: BaseVC, UITextFieldDelegate {
    
    
    ///登录成功后自动返回到起点
    var needAutoBackAfterDone = true
    var loginSuccessBlock:(() -> Void)?
    var popToRoot: Bool = false
    
    ///获取验证码按钮
    let codeBtnLineView = UIView()
    let getCodeBtn = UIButton()
    let phoneLoginBtn = UIButton()
    let phoneTF = UITextField()
    let codeTF = UITextField()
    let selectBtn = UIButton()
    
    lazy var desLab: UILabel = {
        let vi = UILabel()
        vi.textColor = UIColor.white.alpha(value: 0.6)
        vi.font = UIFont.systemFont(ofSize: 13)
        vi.text = "手机号码未注册将自动创建新账号"
        return vi
    }()
    
    lazy var wechatLoginButton: UIButton = {
        let button = UIButton()
        button.setImage(UIImage(named: "wechat"), for: .normal)
        button.cornerRadius = 25
        button.backgroundColor = UIColor("#282828")
        button.borderWidth = 1
        button.borderColor = UIColor.white.alpha(value: 0.6)
        return button
    }()
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        fd_interactivePopDisabled = true
        
        let nvc = getNavViewWithItem(titleStr: nil, leftImageName: "24dp icon_关 闭 1", rightImageName: nil)
        nvc.backgroundColor = UIColor("#111111")
        view.addSubview(nvc)
        
        setupLoginUI()
        
        wechatLoginButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self else { return }
                self.wechatLogin()
            }.disposed(by: disposeBag)
        
        wechatLoginButton.isHidden = !WXApi.isWXAppInstalled()
        
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
    
    /// 渐变层
    private var gradientLayer: CAGradientLayer = {
        let leftColor = UIColor("#6974F2")
        let rightColor = UIColor("#AC63F9")
        let gradientColors = [leftColor.cgColor, rightColor.cgColor]
        let gradientLocations: [NSNumber] = [0.0, 1.0]
        let gradientLayer = CAGradientLayer()
        gradientLayer.startPoint = CGPoint(x: 0, y: 0)
        gradientLayer.endPoint = CGPoint(x: 1, y: 0)
        gradientLayer.colors = gradientColors
        gradientLayer.locations = gradientLocations
        return gradientLayer
    }()
    
    // MARK: - UI
    
    func setupLoginUI() {
        
        view.backgroundColor = UIColor("#111111")
        
        let topView = UIView(frame: CGRect(x: 0, y: LCDevice.Nav_H, width: LCDevice.screenW, height: LCDevice.DIN_WIDTH(210)))
        topView.isUserInteractionEnabled = true
        view.addSubview(topView)
        let tapGes = UITapGestureRecognizer(target: self, action: #selector(viewTapGesAction))
        topView.addGestureRecognizer(tapGes)
        
        let left = (LCDevice.screenW - 303) / 2
        
        let logoView = UIImageView(frame: CGRect(x: left, y: LCDevice.DIN_WIDTH(40), width: 120, height: 26))
        logoView.image = UIImage(named: "icon_logo")
        topView.addSubview(logoView)
        
        let helloLab = UILabel(frame: CGRect(x: logoView.left, y: logoView.bottom + LCDevice.DIN_WIDTH(13), width: 300, height: LCDevice.DIN_WIDTH(30)))
        helloLab.text = "欢迎使用" + LCDevice.KFBundleAppName
        helloLab.font = LCDevice.DIN_Font_Bold(22)//DIN_Font_PF_M(22)
        helloLab.textColor = UIColor.white
        topView.addSubview(helloLab)
        
        topView.addSubview(desLab)
        desLab.snp.makeConstraints { make in
            make.leading.equalTo(helloLab.snp.leading)
            make.top.equalTo(helloLab.snp.bottom).offset(12)
        }
        
        //手机号输入
        let phoneView = UIView(frame: CGRect(x: left, y: topView.bottom, width: LCDevice.DIN_WIDTH(303), height: LCDevice.DIN_WIDTH(50)))
        phoneView.layer.cornerRadius = phoneView.height/2
        phoneView.layer.masksToBounds = true
        phoneView.layer.borderWidth = LCDevice.THIN_LINE_HEIGHT
        phoneView.layer.borderColor = UIColor("#444444").cgColor
        view.addSubview(phoneView)
        
        let pIconView = UIImageView(frame: CGRect(x: LCDevice.DIN_WIDTH(20), y: LCDevice.DIN_WIDTH(11), width: LCDevice.DIN_WIDTH(28), height: LCDevice.DIN_WIDTH(28)))
        pIconView.image = UIImage(named: "icon_login_phone")
        phoneView.addSubview(pIconView)
        
        let pLineView = UIView(frame: CGRect(x: LCDevice.DIN_WIDTH(56), y: LCDevice.DIN_WIDTH(17), width: LCDevice.DIN_WIDTH(1), height: LCDevice.DIN_WIDTH(16)))
        pLineView.backgroundColor = UIColor("#444444")
        phoneView.addSubview(pLineView)
        
        phoneTF.frame = CGRect(x: LCDevice.DIN_WIDTH(65), y: 0, width: LCDevice.DIN_WIDTH(200), height: LCDevice.DIN_WIDTH(30))
        phoneTF.centerY = pIconView.centerY
        phoneTF.keyboardType = .numberPad
        phoneTF.font = LCDevice.DIN_Font_PF_M(16)
        phoneTF.textColor = UIColor.white
        let plAttStr = NSMutableAttributedString(string: "请输入手机号")
        plAttStr.color = UIColor("#B8BABF")
        plAttStr.font = LCDevice.DIN_Font_PF_M(14)
        phoneTF.attributedPlaceholder = plAttStr
        phoneTF.addTarget(self, action: #selector(phoneTFValueDidChanged(sender:)), for: .editingChanged)
        phoneView.addSubview(phoneTF)
        
        let deleteBtn = UIButton(frame: CGRect(x: LCDevice.DIN_WIDTH(261), y: LCDevice.DIN_WIDTH(14), width: LCDevice.DIN_WIDTH(22), height: LCDevice.DIN_WIDTH(22)))
        deleteBtn.setImage(UIImage(named: "icon_login_delete"), for: .normal)
        deleteBtn.addTarget(self, action: #selector(phoneInputDeleteBtnAction(sender:)), for: .touchUpInside)
        phoneView.addSubview(deleteBtn)
        
        //验证码输入
        let codeView = UIView(frame: CGRect(x: left, y: phoneView.bottom+LCDevice.DIN_WIDTH(16), width: LCDevice.DIN_WIDTH(303), height: LCDevice.DIN_WIDTH(50)))
        codeView.layer.cornerRadius = codeView.height/2
        codeView.layer.masksToBounds = true
        codeView.layer.borderWidth = LCDevice.THIN_LINE_HEIGHT
        codeView.layer.borderColor = UIColor("#444444").cgColor
        view.addSubview(codeView)
        
        let codeIconView = UIImageView(frame: CGRect(x: LCDevice.DIN_WIDTH(20), y: LCDevice.DIN_WIDTH(11), width: LCDevice.DIN_WIDTH(28), height: LCDevice.DIN_WIDTH(28)))
        codeIconView.image = UIImage(named: "icon_login_code")
        codeView.addSubview(codeIconView)
        
        let codeLineView = UIView(frame: CGRect(x: LCDevice.DIN_WIDTH(56), y: LCDevice.DIN_WIDTH(17), width: LCDevice.DIN_WIDTH(1), height: LCDevice.DIN_WIDTH(16)))
        codeLineView.backgroundColor = UIColor("#444444")
        codeView.addSubview(codeLineView)
        
        codeTF.frame = CGRect(x: LCDevice.DIN_WIDTH(65), y: 0, width: LCDevice.DIN_WIDTH(100), height: LCDevice.DIN_WIDTH(30))
        codeTF.centerY = codeIconView.centerY
        codeTF.keyboardType = .numberPad
        codeTF.font = LCDevice.DIN_Font_PF_M(16)
        codeTF.textColor = UIColor.white
        let codeAttStr = NSMutableAttributedString(string: "请输入验证码")
        codeAttStr.color = UIColor("#B8BABF")
        codeAttStr.font = LCDevice.DIN_Font_PF_M(14)
        codeTF.attributedPlaceholder = codeAttStr
        codeTF.delegate = self
        codeTF.addTarget(self, action: #selector(codeTFValueDidChanged(sender:)), for: .editingChanged)
        codeView.addSubview(codeTF)
        
        codeBtnLineView.frame = CGRect(x: LCDevice.DIN_WIDTH(208), y: LCDevice.DIN_WIDTH(20), width: LCDevice.DIN_WIDTH(1), height: LCDevice.DIN_WIDTH(12))
        codeView.addSubview(codeBtnLineView)
        
        getCodeBtn.frame = CGRect(x: LCDevice.DIN_WIDTH(210), y: LCDevice.DIN_WIDTH(14), width: LCDevice.DIN_WIDTH(80), height: LCDevice.DIN_WIDTH(22))
        getCodeBtn.setTitle("获取验证码", for: .normal)
        getCodeBtn.titleLabel?.font = LCDevice.DIN_Font_PF_S(14)
        setupGetCodeButtonStyle(false)
        getCodeBtn.addTarget(self, action: #selector(getCodeBtnAction(sender:)), for: .touchUpInside)
        codeView.addSubview(getCodeBtn)
        
        //登录按钮
        phoneLoginBtn.frame = CGRect(x: left - 18, y: codeView.bottom + LCDevice.DIN_WIDTH(74), width: LCDevice.DIN_WIDTH(339), height: LCDevice.DIN_WIDTH(46))
        phoneLoginBtn.backgroundColor = UIColor("#6974F2")
        phoneLoginBtn.setTitle("立即登录", for: .normal)
        phoneLoginBtn.titleLabel?.font = LCDevice.DIN_Font_PF_S(16)
        phoneLoginBtn.setTitleColor(UIColor("#FFFFFF"), for: .normal)
        phoneLoginBtn.addTarget(self, action: #selector(phoneLoginBtnAction(sender:)), for: .touchUpInside)
        phoneLoginBtn.isEnabled = false
        phoneLoginBtn.cornerRadius = 23
        view.addSubview(phoneLoginBtn)
        
        
        phoneLoginBtn.layer.insertSublayer(gradientLayer, at: 0)
        gradientLayer.frame = CGRect(x: 0, y: 0, width: LCDevice.DIN_WIDTH(339), height: LCDevice.DIN_WIDTH(46))
        
        
        // 勾选按钮
        selectBtn.frame = CGRect(x: left - 10, y: phoneLoginBtn.top - 46, width: 40, height: 40)
        selectBtn.setImage(UIImage(named: "登录流程_组件_cell_勾选_nor"), for: .normal)
        selectBtn.setImage(UIImage(named: "icon_勾选_sel"), for: .selected)
        selectBtn.addTarget(self, action: #selector(buttonActionBySelected(sender:)), for: .touchUpInside)
        view.addSubview(selectBtn)
        
        
        
        view.addSubview(wechatLoginButton)
        wechatLoginButton.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.width.height.equalTo(50)
            make.bottom.equalToSuperview().inset(LCDevice.BOTTOM_BAR + 20)
        }
        
        
        //隐私协议
        let infoString = "已阅读并同意《用户服务协议》&《隐私政策》"
        let agreementFS = "《用户服务协议》"
        let agreementRange = infoString.range(of: agreementFS)!
        let agreementLinkRange = NSRange(agreementRange, in: infoString)
        
        let privacyFS = "《隐私政策》"
        let privacyRange = infoString.range(of: privacyFS)!
        let privacyLinkRange = NSRange(privacyRange, in: infoString)
        
        let attString = NSMutableAttributedString.init(string: infoString)
        attString.font = LCDevice.DIN_Font_PF_R(12)
        attString.alignment = .left
        attString.color = .white
        attString.setTextHighlight(agreementLinkRange, color: UIColor("#ADAAFF"), backgroundColor: nil) { (_ containerView, _ attText, _ range, _ rect) in
            Router.openUserAgreement()
        }
        attString.setTextHighlight(privacyLinkRange, color: UIColor("#ADAAFF"), backgroundColor: nil) { (_ containerView, _ attText, _ range, _ rect) in
            Router.openPrivacyAgreement()
        }
        let agreementLab = YYLabel()
        agreementLab.frame = CGRect(x: selectBtn.right + 4, y: phoneLoginBtn.top - LCDevice.DIN_WIDTH(35), width: LCDevice.screenW - selectBtn.right - 4, height: LCDevice.DIN_WIDTH(30))
        agreementLab.centerY = selectBtn.centerY + 1
        agreementLab.attributedText = attString
        view.addSubview(agreementLab)
        
    }
    
    // MARK: - UITextFieldDelegate
    
    @objc func phoneTFValueDidChanged(sender: UITextField) {
        checkLoginBtnStatus()
        
        //需实时校验手机号是否合法，然后设置 获取验证码 按钮
        if (LoginVC.isLegalPhoneNumber(phoneTF.text!)) {
            setupGetCodeButtonStyle(true)
        } else {
            setupGetCodeButtonStyle(false)
        }
    }
    
    @objc func codeTFValueDidChanged(sender: UITextField) {
        checkLoginBtnStatus()
    }

    private func checkLoginBtnStatus() {
        if phoneTF.text!.count>0, codeTF.text!.count>0 {
            phoneLoginBtn.isEnabled = true
        } else {
            phoneLoginBtn.isEnabled = false
        }
    }
    
    func textField(_ textField: UITextField, shouldChangeCharactersIn range: NSRange, replacementString string: String) -> Bool {
        //验证码最多输6位
        if textField == codeTF, let text = textField.text {
            let str = (text as NSString).replacingCharacters(in: range, with: string)
            if str.count > 6 {
                return false
            }
        }
        return true
    }
    
    // MARK: - Btn action
    @objc func buttonActionBySelected(sender: UIButton) {
        sender.isSelected = !sender.isSelected
        checkLoginBtnStatus()
    }
    
    @objc func phoneInputDeleteBtnAction(sender: UIButton) {
        phoneTF.text = nil
        phoneLoginBtn.isEnabled = false
        setupGetCodeButtonStyle(false)
    }
    
    @objc func getCodeBtnAction(sender: UIButton) {
        guard let phoneNum = phoneTF.text else {
            HUD.showFail("请输入手机号")
            return
        }
        
        guard LoginVC.isLegalPhoneNumber(phoneNum) else {
            HUD.showFail("手机号码错误")
            return
        }
        
        // 非内测版本 || 内测用户验证成功
        self.handleCode(phoneNum: phoneNum)
    }
    
    private func handleCode(phoneNum: String) {
        LPStatistics.logEvent(.LOGIN_ACTION(.clickVerify), eventAction: nil)
        HUD.showWait()
        LoginVC.getVerifeCode(phone: phoneNum, type: .login, succes: { [weak self] resObj in
            if resObj.code == 0 {
                HUD.showSuccess("发送成功")
                // 启动倒计时
                self?.isCounting = true
                self?.codeTF.becomeFirstResponder()
            } else {
                HUD.hideAllHUD()
                if let meg = resObj.msg {
                    HUD.showFail(meg)
                }
            }
        }, disposeBag: disposeBag)
    }
    
    @objc func phoneLoginBtnAction(sender: UIButton) {
        keyBoardDismss()
        guard selectBtn.isSelected else {
            HUD.showFail("请先阅读并勾选相关协议政策")
            return
        }
        
        if (phoneTF.text?.count)! < 11 || phoneTF.text == "" {
            HUD.showFail("手机号码错误")
            return
        }
        if codeTF.text == "" || (codeTF.text?.count)! < 4 {
            HUD.showFail("验证码错误")
            return
        }
        if !(LoginVC.isLegalPhoneNumber(phoneTF.text!)) {
            HUD.showFail("输入的手机号码格式有误！")
            return
        }
        HUD.showWait()
        LPStatistics.logEvent(.LOGIN_ACTION(.clickLogin), eventAction: nil)
        // 登录接口
        let param = ["phone": phoneTF.text!, "code": codeTF.text!]
        let req: Observable<Status<BaseRespModel<LoginRespData>>> = APISession.post(APIURL.POST_LOGIN, parameters: param).asObservable()
        req.subscribe(onNext: {[weak self] status in
            HUD.hideAllHUD()
            switch status {
            case .success(let res):
                if let user = res.data {
                    user.saveLoginUser()
                    NotificationCenter.default.post(name: LCKey.noti_reLogin, object: nil)

                    self?.handlePhoneLoginBlock()
                } else {
                    if let mesg = res.msg {
                        HUD.showFail(mesg)
                    }
                }
            case .failure(let error):
                LCLog.d(error.localizedDescription)
                HUD.showFail(error.localizedDescription)
            case .pending: break
            }
            
        }).disposed(by: disposeBag)

    }
    
    /// 微信登录
    func wechatLogin() {
        /// 微信未安装 走手机号登录的逻辑 防止苹果审核失败
        if !WXApi.isWXAppInstalled() {
            return
        }
        
        if !self.selectBtn.isSelected {
            HUD.showFail("请先阅读并勾选相关协议政策")
            return
        }
        
        let req = SendAuthReq()
        req.scope = "snsapi_userinfo"
        req.state = "App"
        WXApi.sendAuthReq(req, viewController: self, delegate: self) { (_) in }
        UserDefaults.standard.setValue(true, forKey: LCKey.UD_wxForLogin)
        UserDefaults.standard.setValue(false, forKey: LCKey.UD_wxForBind)
    }
   
    @objc func viewTapGesAction() {
        keyBoardDismss()
    }
    
    func handlePhoneLoginBlock() {
        MiddleRequestNet.requestLimit()
        DispatchQueue.main.async {
            if self.needAutoBackAfterDone {
                self.leftButtonAction()
            }
            if let block = self.loginSuccessBlock {
                block()
            }
        }
    }
    

    override func leftButtonAction() {
        if popToRoot {
            self.navigationController?.popToRootViewController(animated: true)
        } else {
            /// 因为有上一个mainvc，所以如果不用poproot的话 就pop到上两个页面
//            if let index = self.navigationController?.viewControllers.firstIndex(of: self),
//                let oldVC = self.navigationController?.viewControllers[safe: index - 2] {
//                self.navigationController?.popToViewController(oldVC, animated: true)
//                return
//            }
            super.leftButtonAction()
        }
    }

    
    // MARK: - Private methods
    /// 验证码倒计时
    var countdownTimer: Timer?
    var isCounting = false {
        willSet {
            if newValue {
                countdownTimer = Timer.scheduledTimer(timeInterval: 1, target: self, selector: #selector(updateTime(timer:)), userInfo: nil, repeats: true)
                remainingSeconds = 59
            } else {
                countdownTimer?.invalidate()
                countdownTimer = nil
            }
            getCodeBtn.isEnabled = !newValue
        }
    }
    var remainingSeconds: Int = 0 {
        willSet {
            getCodeBtn.setTitle("\(newValue)S", for: .normal)
            setupGetCodeButtonStyle(false)
            if newValue <= 0 {
                getCodeBtn.setTitle("获取验证码", for: .normal)
                setupGetCodeButtonStyle(true)
                isCounting = false
            }
        }
    }
    
    ///设置获取验证码按钮是否可点击
    func setupGetCodeButtonStyle(_ isEnabled: Bool) {
        if isEnabled {
            getCodeBtn.setTitleColor(UIColor("#6974F2"), for: .normal)
            getCodeBtn.isEnabled = true
            codeBtnLineView.backgroundColor = UIColor("#6974F2")
        } else {
            getCodeBtn.setTitleColor(UIColor("#AEAEB2"), for: .normal)
            getCodeBtn.isEnabled = false
            codeBtnLineView.backgroundColor = UIColor("#AEAEB2")
        }
    }
    
    // 计时开始时，逐秒减少remainingSeconds的值
    @objc func updateTime(timer: Timer) {
        remainingSeconds -= 1
    }

}

extension LoginVC {
    // MARK: - 手机号验证
   static func isLegalPhoneNumber(_ phoneNum: String, countryCode codeNum: String = "86") -> Bool {
        ///审核测试账号
        if (phoneNum == "18000000000") {
            return true
        }
        //1、去除空格、换行等
        if phoneNum == "" || phoneNum.count < 11 {
//            HUD.showFail("输入的手机号有误！")
            return false
        }
        let phoneStr = phoneNum.removeSpaceAndNewline()
        let codeStr = codeNum.removeSpaceAndNewline()
        //2、判断是否是纯数字串
        if !phoneNum.isPureNumberCharacters() {
//            HUD.showFail("输入的号码有误！")
            return false
        }
    
        let phonePattern = "^1(0|1|2|3|4|5|6|7|8|9)\\d{9}$"
    
        do {
            let matcher = try RegexHelper(phonePattern)
            if matcher.match(phoneStr) {
              return true
            }
         } catch {
            
         }
    
    
//
//        //3、国家码+手机号校验
//        if let util = NBPhoneNumberUtil.sharedInstance(){
//            let numberTools = NBPhoneNumber()
//            numberTools.countryCode = NSNumber(string: codeStr)
//            numberTools.nationalNumber = NSNumber(string: phoneStr)
//            return util.isValidNumber(numberTools)
//        }
        return false
    }
    
    struct RegexHelper {
        let regex: NSRegularExpression
        
        init(_ pattern: String) throws {
            try regex = NSRegularExpression(pattern: pattern,
                                            options: .caseInsensitive)
        }
        
        func match(_ input: String) -> Bool {
            let matches = regex.matches(in: input,
                                        options: [],
                                        range: NSMakeRange(0, input.utf16.count))
            return matches.count > 0
        }
    }
    
    /// 获取验证码
    /// - Parameters:
    ///   - phone: 手机号 必须
    ///   - type: 1:手机登录, 2:手机绑定, 3: 手机解绑
    ///   - succes: 请求成功回调
    static func getVerifeCode(phone: String, type: VerifyCodeType, succes: @escaping ((_ responseObj: BaseRespModel<Int>) -> Void), disposeBag: DisposeBag) {
        let param = ["type": type.rawValue, "phone": phone]
        let req: Observable<Status<BaseRespModel<Int>>> = APISession.get(APIURL.GET_SMS_SEND, parameters: param).asObservable()
        req.subscribe(onNext: { status in
            switch status {
            case .success(let res): succes(res)
            case .failure(let error):
                LCLog.d(error.localizedDescription)
                HUD.showFail(error.localizedDescription)
            case .pending: break
            }
        }).disposed(by: disposeBag)
    }
    
}

/// 老逻辑
extension LoginVC: WXApiDelegate {
    func onReq(_ req: BaseReq) {
        print("前往微信登录")
    }
    
    @objc func didGetWeiXinResp(_ sender: Notification) {
        if !UserDefaults.standard.bool(forKey: LCKey.UD_wxForLogin) {
            return
        }
        UserDefaults.standard.setValue(false, forKey: LCKey.UD_wxForLogin)
        let data = sender.object as! SendAuthResp
        if let code = data.code {
            let param = ["code": code]
            HUD.showWait()
            let req: Observable<Status<BaseRespModel<LoginRespData>>> = APISession.post(APIURL.POST_WeiXin_LOGIN, parameters: param) .asObservable()
            req.subscribe(onNext: {[weak self] status in
                HUD.hideAllHUD()
                switch status {
                case .success(let res):
                    if let user = res.data {
                        user.saveLoginUser()

                        if let userInfo = UserInfo.currentUser() {
    //                        if let phoneBind = userInfo.bindPhone,phoneBind {
                            if let _ = userInfo.phone {
    //                            print("当前手机号：\(phone)")
                                self?.handlePhoneLoginBlock()
                            } else {
                                self?.gotoPhoneBindAlert()
                            }
                        }
                    } else {
                        if let mesg = res.msg {
                            HUD.showFail(mesg)
                        }
                    }
                case .failure(let error):
                    LCLog.d(error.localizedDescription)
                    HUD.showFail(error.localizedDescription)
                case .pending: break
                }
                
            }).disposed(by: disposeBag)
        }
    }
        
    
    ///微信登录成功后前往手机号绑定页
    func gotoPhoneBindAlert() {
        let alert = UIAlertController.init(title: "温馨提示", message: "\n根据《网络安全法》等相关法律法规要求,网络用户需验证真实身份信息,请先绑定手机号以使用完整功能。", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "我再想想", style: .default, handler: { [weak self] _ in
            guard let self = self else { return }
            UserInfo.logout()
            NotificationCenter.default.post(name: LCKey.noti_logout, object: nil)
            self.leftButtonAction()
        }))
        alert.addAction(UIAlertAction(title: "去绑定", style: .destructive, handler: { [weak self] _ in
            guard let self = self else { return }
            self.goPhoneBind()
        }))
        present(alert, animated: true, completion: nil)
    }
    
    func goPhoneBind() {
        let bindVc = PhoneBindVC()
        bindVc.bindType = .bind
        bindVc.needBackAfterDone = self.needAutoBackAfterDone
        bindVc.bindSuccessBlock = {[weak self] in
            guard let self = self else { return }
            if !UserInfo.isBindPhone() {
                self.gotoPhoneBindAlert()
                return
            }
            if bindVc.needBackAfterDone {
                bindVc.leftButtonAction()
            }
            self.handlePhoneLoginBlock()
        }
        self.push(to: bindVc)
    }
}
