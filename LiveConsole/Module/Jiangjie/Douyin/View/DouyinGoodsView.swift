//
//  DouyinGoodsView.swift
//  MockBuyin
//
//  Created by simon on 21.11.24.
//

import Foundation
import UIKit
import SwifterSwift

protocol DouyinGoodsViewDelegate: NSObjectProtocol {

   func goodsOperation(operation: DoyinGoodsOperation, data: [Promotions_data_promotions])
}

class DouyinGoodsView: UIView {
        
    weak var delegate:DouyinGoodsViewDelegate?

    lazy var topView: UIView = {
        let button = UIView()
        button.backgroundColor = .clear
        return button
    }()
    
    lazy var unbindButton: UIButton = {
        let button = UIButton(frame: CGRect(x: 0, y: 0, width: 64, height: 30))
        button.setTitle("下架", for: .normal)
        button.setTitleColor(UIColor.white, for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 14)
        button.addTarget(self, action: #selector(unbindAction), for: .touchUpInside)
        button.backgroundColor = UIColor("#444444")
        button.zl_enlargeValidTouchArea(inset: 8)
        button.cornerRadius = 15
        return button
    }()
    
    lazy var allButton: UIButton = {
        let button = UIButton()
        button.setTitle("全部选择", for: .normal)
        button.setImage(UIImage(named: "多选按钮_未选中"), for: .normal)
        button.setTitleColor(UIColor.white, for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 14)
        button.addTarget(self, action: #selector(allAction), for: .touchUpInside)
        button.backgroundColor = .clear
        button.imagePosition(style: .left, spacing: 4)
        button.zl_enlargeValidTouchArea(inset: 8)
        button.isHidden = true
        return button
    }()
    
    lazy var multiButton: UIButton = {
        let button = UIButton()
        button.setTitle("多选", for: .normal)
        button.setTitleColor(UIColor.white, for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 14)
        button.addTarget(self, action: #selector(multiAction), for: .touchUpInside)
        button.backgroundColor = UIColor("#444444")
        button.zl_enlargeValidTouchArea(inset: 8)
        button.cornerRadius = 15
        button.isHidden = true
        return button
    }()
    
    /// 渐变层
    private var gradientLayer: CAGradientLayer = {
        let leftColor = UIColor("#6974F2")
        let rightColor = UIColor("#AC63F9")
        let gradientColors = [leftColor.cgColor, rightColor.cgColor]
        let gradientLocations: [NSNumber] = [0.0, 1.0]
        let gradientLayer = CAGradientLayer()
        gradientLayer.startPoint = CGPoint(x: 0, y: 0)
        gradientLayer.endPoint = CGPoint(x: 1, y: 0)
        gradientLayer.colors = gradientColors
        gradientLayer.locations = gradientLocations
        return gradientLayer
    }()
    
    lazy var tableView: UITableView = {
        let tableView = UITableView()
        tableView.backgroundColor = UIColor.clear
        tableView.delegate = self
        tableView.dataSource = self
        tableView.separatorStyle = .none
        tableView.showsVerticalScrollIndicator = false
        tableView.register(cellWithClass: DoyinGoodsCell.self)
        tableView.register(cellWithClass: DoyinAllGoodsCell.self)
        
        return tableView
    }()
    
    var isMulti: Bool = false
    
    var dataSource: [Promotions_data_promotions] = []

    var data:Promotions_data?
    
    var selectedGoods: [Promotions_data_promotions] = []
    
    override init(frame: CGRect) {
        super.init(frame: frame)

        makeUI()
        business()
    }
    
    convenience init() {
        self.init(frame: .zero)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func makeUI() {
        self.backgroundColor = UIColor("#111111")
        addSubviews( [tableView,topView])
        topView.addSubviews( [ multiButton, unbindButton, allButton])
        
        topView.snp.makeConstraints { make in
            make.trailing.leading.equalToSuperview()
            make.top.equalToSuperview()
            make.height.equalTo(30)
        }
        
        multiButton.snp.makeConstraints { make in
            make.trailing.equalToSuperview().inset(12)
            make.top.equalToSuperview().inset(12)
            make.height.equalTo(30)
            make.width.equalTo(58)
        }
        
        unbindButton.snp.makeConstraints { make in
            make.trailing.equalToSuperview().inset(12)
            make.top.equalToSuperview().inset(12)
            make.height.equalTo(30)
            make.width.equalTo(58)
        }
        
        tableView.snp.makeConstraints { make in
            make.leading.bottom.trailing.equalToSuperview()
            make.top.equalToSuperview().inset(58)
        }
        
        allButton.snp.makeConstraints { make in
            make.leading.equalToSuperview().inset(8)
            make.top.equalToSuperview().inset(8)
            make.height.equalTo(30)
            make.width.equalTo(80)
        }
    }
    
    func business() {
        
    }
    
    // 全选
    @objc func allAction() {
        selectedGoods.removeAll()
        self.dataSource.forEach { m in
            selectedGoods.append(m)
        }
        reloadView()
    }
    
    @objc func unbindAction() {
        if selectedGoods.isEmpty {
            HUD.showFail("请先选中商品", autoClearTime: 1)?.isUserInteractionEnabled = false
            return
        }
        
        self.delegate?.goodsOperation(operation: .xiajia, data: selectedGoods)
    }
    
    @objc func multiAction() {
        self.isMulti = !self.isMulti
        
        if self.isMulti {
            multiButton.layer.insertSublayer(gradientLayer, at: 0)
            gradientLayer.frame = CGRect(x: 0, y: 0, width: 64, height: 30)
        } else {
            gradientLayer.removeFromSuperlayer()
        }
        selectedGoods.removeAll()
        reloadView()
    }
    
    var isLiving: Bool {
        if let data = data{
            return data.isLiving
        }
        return false
    }
    
    func isCurrent(promotion:Promotions_data_promotions) -> Bool {
        guard let data = data else { return false}
        return  data.isCurrent(promotion: promotion)
    }

    
    func reloadView() {
        if !dataSource.isEmpty, self.selectedGoods.count == dataSource.count {
            self.allButton.setImage(UIImage(named: "多选按钮_选中"), for: .normal)
        } else {
            self.allButton.setImage(UIImage(named: "多选按钮_未选中"), for: .normal)
        }
        unbindButton.isHidden = dataSource.isEmpty
        tableView.reloadData()
    }
    
    func hideToolButton() {
        self.topView.isHidden = true
    }
    
    func showToolButton() {
        self.topView.isHidden = false
    }
    
}

extension DouyinGoodsView {
    func bind(to data: Promotions_data?) {
        self.data = data
        self.dataSource = data?.promotions ?? []
        reloadView()
    }
    
    func isSelected(_ promotion_id: String) -> Bool {
        if let _ = self.selectedGoods.first(where: {$0.promotion_id == promotion_id}) {
            return true
        }
        return false
    }
    
}

extension DouyinGoodsView: UITableViewDelegate, UITableViewDataSource {
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 114
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        if let _ = self.data {
            return self.dataSource.count
        }
        
        return 0
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        
        if let _ = self.data {
            let model = dataSource[indexPath.row]
            let cell = tableView.dequeueReusableCell(withClass: DoyinGoodsCell.self)
            cell.delegate = self
            cell.bindBase(to: model, idx: indexPath.row, isLiving: self.isLiving, isCurrent: self.isCurrent(promotion: model), isMulti: isMulti, isSelected: isSelected(model.promotion_id))
            return cell
        }
        
        
        return UITableViewCell()
    }
    
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        let model = dataSource[indexPath.row]
        //
        
        if isMulti {
            if isSelected(model.promotion_id) {
                self.selectedGoods.removeAll(where: {$0.promotion_id == model.promotion_id})
            } else {
                self.selectedGoods.append(model)
            }
        } else {
            self.selectedGoods.removeAll()
            self.selectedGoods.append(model)
        }
        
        reloadView()
    }
}



extension DouyinGoodsView: DoyinGoodsCellDelegate {
    func goodsOperation(operation: DoyinGoodsOperation, data: Promotions_data_promotions) {
        self.delegate?.goodsOperation(operation: operation, data: [data])
    }
}



