//
//  DouyinAllGoodsView.swift
//  LiveConsole
//
//  Created by simon on 25.4.25.
//

import Foundation
import UIKit
import SwifterSwift

protocol DouyinAllGoodsViewDelegate: NSObjectProtocol {

   func goodsOperation(operation: DoyinGoodsOperation, data: [Promotions_all])
}

class DouyinAllGoodsView: UIView {
        
    weak var delegate:DouyinAllGoodsViewDelegate?
    lazy var topView: UIView = {
        let button = UIView()
        button.backgroundColor = .clear
        return button
    }()
    
    lazy var bindButton: UIButton = {
        let button = UIButton(frame: CGRect(x: 0, y: 0, width: 64, height: 30))
        button.setTitle("上架", for: .normal)
        button.setTitleColor(UIColor.white, for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 14, weight: .semibold)
        button.addTarget(self, action: #selector(bindAction), for: .touchUpInside)
        button.backgroundColor = UIColor("#444444")
        button.zl_enlargeValidTouchArea(inset: 8)
        button.cornerRadius = 15
        return button
    }()
    
    
    lazy var multiButton: UIButton = {
        let button = UIButton()
        button.setTitle("多选", for: .normal)
        button.setTitleColor(UIColor.white, for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 14, weight: .semibold)
        button.addTarget(self, action: #selector(multiAction), for: .touchUpInside)
        button.backgroundColor = UIColor("#444444")
        button.zl_enlargeValidTouchArea(inset: 8)
        button.cornerRadius = 15
        return button
    }()
    
    lazy var allButton: UIButton = {
        let button = UIButton()
        button.setTitle("全部选择", for: .normal)
        button.setImage(UIImage(named: "多选按钮_未选中"), for: .normal)
        button.setTitleColor(UIColor.white, for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 14)
        button.addTarget(self, action: #selector(allAction), for: .touchUpInside)
        button.backgroundColor = .clear
        button.imagePosition(style: .left, spacing: 4)
        button.zl_enlargeValidTouchArea(inset: 8)
        button.isHidden = true
        return button
    }()
    
    
    lazy var tableView: UITableView = {
        let tableView = UITableView()
        tableView.backgroundColor = UIColor.clear
        tableView.delegate = self
        tableView.dataSource = self
        tableView.separatorStyle = .none
        tableView.showsVerticalScrollIndicator = false
        tableView.register(cellWithClass: DoyinAllGoodsCell.self)
        return tableView
    }()
    
    /// 渐变层
    private var gradientLayer: CAGradientLayer = {
        let leftColor = UIColor("#6974F2")
        let rightColor = UIColor("#AC63F9")
        let gradientColors = [leftColor.cgColor, rightColor.cgColor]
        let gradientLocations: [NSNumber] = [0.0, 1.0]
        let gradientLayer = CAGradientLayer()
        gradientLayer.startPoint = CGPoint(x: 0, y: 0)
        gradientLayer.endPoint = CGPoint(x: 1, y: 0)
        gradientLayer.colors = gradientColors
        gradientLayer.locations = gradientLocations
        return gradientLayer
    }()
    
    var selectedGoods: [Promotions_all] = []
    
    var alldata:Promotions_all_data?
    
    var alldataSource: [Promotions_all] = []
    
    // 多选的状态
    var isMulti: Bool = false
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        
        makeUI()
        business()
    }
    
    convenience init() {
        self.init(frame: .zero)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func makeUI() {
        self.backgroundColor = UIColor("#111111")
        addSubviews( [tableView,topView])
        topView.addSubviews( [ multiButton, bindButton, allButton])
        
        topView.snp.makeConstraints { make in
            make.trailing.leading.equalToSuperview()
            make.top.equalToSuperview()
            make.height.equalTo(30)
        }
        
        multiButton.snp.makeConstraints { make in
            make.trailing.equalToSuperview().inset(8)
            make.top.equalToSuperview().inset(12)
            make.height.equalTo(30)
            make.width.equalTo(64)
        }
        
        bindButton.snp.makeConstraints { make in
            make.trailing.equalTo(multiButton.snp.leading).offset(-12)
            make.top.equalToSuperview().inset(12)
            make.height.equalTo(30)
            make.width.equalTo(64)
        }
        
        allButton.snp.makeConstraints { make in
            make.leading.equalToSuperview().inset(8)
            make.top.equalToSuperview().inset(12)
            make.height.equalTo(30)
            make.width.equalTo(80)
        }
        
        tableView.snp.makeConstraints { make in
            make.leading.bottom.trailing.equalToSuperview()
            make.top.equalToSuperview().inset(58)
        }
    }
    
    
    func hideToolButton() {
        self.topView.isHidden = true
    }
    
    func showToolButton() {
        self.topView.isHidden = false
    }
    
    func business() {
        
    }
    
    // 上架
    @objc func bindAction() {
        if selectedGoods.isEmpty {
            HUD.showFail("请先选中商品", autoClearTime: 1)?.isUserInteractionEnabled = false
            return
        }
        
        var goods: [Promotions_all] = []
        goods.append(contentsOf: selectedGoods)
        self.delegate?.goodsOperation(operation: .shangjia, data: goods)
        
        self.isMulti = false
      
        gradientLayer.removeFromSuperlayer()
        allButton.isHidden = true
        
        selectedGoods.removeAll()
        reloadView()

    }
    
    // 下架
    @objc func unbindAction() {
        if selectedGoods.isEmpty {
            HUD.showFail("请先选中商品", autoClearTime: 1)?.isUserInteractionEnabled = false
            return
        }
        
        self.delegate?.goodsOperation(operation: .xiajia, data: selectedGoods)
    }
    
    // 多选
    @objc func multiAction() {
        self.isMulti = !self.isMulti
        
        if self.isMulti {
            multiButton.layer.insertSublayer(gradientLayer, at: 0)
            gradientLayer.frame = CGRect(x: 0, y: 0, width: 64, height: 30)
            allButton.isHidden = false
        } else {
            gradientLayer.removeFromSuperlayer()
            allButton.isHidden = true
        }
        selectedGoods.removeAll()
        reloadView()
    }
    
    
    // 全选
    @objc func allAction() {
        selectedGoods.removeAll()
        self.alldataSource.forEach { m in
            selectedGoods.append(m)
        }
        reloadView()
    }
    

    func reloadView() {
        if !alldataSource.isEmpty, self.selectedGoods.count == alldataSource.count {
            self.allButton.setImage(UIImage(named: "多选按钮_选中"), for: .normal)
        } else {
            self.allButton.setImage(UIImage(named: "多选按钮_未选中"), for: .normal)
        }
       
        tableView.reloadData()
    }
    
    func isCurrent(promotion:Promotions_all) -> Bool {
        guard let data = alldata else { return false}
        return  data.isCurrent(promotion: promotion)
    }
}

extension DouyinAllGoodsView {
    
    
    func bind(to data: Promotions_all_data?, reset: Bool = false) {
        if reset {
            self.selectedGoods.removeAll()
        }
        self.alldata = data
        self.alldataSource = data?.products ?? []
        reloadView()
        
    }
    
    func isSelected(_ promotion_id: String) -> Bool {
        if let _ = self.selectedGoods.first(where: {$0.promotion_id == promotion_id}) {
            return true
        }
        return false
    }
}

extension DouyinAllGoodsView: UITableViewDelegate, UITableViewDataSource {
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 114
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        if let _ = self.alldata {
            return self.alldataSource.count
        }
        return 0
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        if let _ = self.alldata {
            let model = alldataSource[indexPath.row]
            let cell = tableView.dequeueReusableCell(withClass: DoyinAllGoodsCell.self)
            
            cell.bindAllBase(to: model, idx: indexPath.row, isSelected: isSelected(model.promotion_id), isCurrent: self.isCurrent(promotion: model), isMulti: self.isMulti)
            return cell
        }
        
        return UITableViewCell()
    }
    
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        let model = alldataSource[indexPath.row]
        //
        
        if isMulti {
            if isSelected(model.promotion_id) {
                self.selectedGoods.removeAll(where: {$0.promotion_id == model.promotion_id})
            } else {
                self.selectedGoods.append(model)
            }
        } else {
            self.selectedGoods.removeAll()
            self.selectedGoods.append(model)
        }
        
        reloadView()
    }
    
}


