//
//  BuyinEmptyView.swift
//  LiveConsole
//
//  Created by simon on 28.4.25.
//

import Foundation
class BuyinEmptyView: UIView {

    lazy var bgview: UIView = {
        let button = UIView()
        button.backgroundColor = .clear
        return button
    }()
    
    
    lazy var loginButton: UIButton = {
        let button = UIButton(frame: CGRect(x: 0, y: 0, width: 90, height: 40))
        button.setTitle("去登录", for: .normal)
        button.setTitleColor(UIColor.white, for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 15, weight: .medium)
        button.backgroundColor = UIColor("#444444")
        button.zl_enlargeValidTouchArea(inset: 8)
        button.cornerRadius = 20
        return button
    }()
    
   
    /// 渐变层
    private var gradientLayer: CAGradientLayer = {
        let leftColor = UIColor("#6974F2")
        let rightColor = UIColor("#AC63F9")
        let gradientColors = [leftColor.cgColor, rightColor.cgColor]
        let gradientLocations: [NSNumber] = [0.0, 1.0]
        let gradientLayer = CAGradientLayer()
        gradientLayer.startPoint = CGPoint(x: 0, y: 0)
        gradientLayer.endPoint = CGPoint(x: 1, y: 0)
        gradientLayer.colors = gradientColors
        gradientLayer.locations = gradientLocations
        return gradientLayer
    }()
    
    
    lazy var selectedView: UIImageView = {
        let button = UIImageView()
        button.image = UIImage(named: "缺省图")
        button.backgroundColor = .clear
        return button
    }()
    
    lazy var contentLabel: UILabel = {
        let label = UILabel()
        label.textColor = UIColor.white
        label.font = UIFont.systemFont(ofSize: 13)
        label.textAlignment = .center
        label.text = "还未登录账号，无法获取内容"
        label.numberOfLines = 0
        return label
    }()
   
    override init(frame: CGRect) {
        super.init(frame: frame)
        self.backgroundColor = .black.alpha(value: 0.5)
        makeUI()
        business()
        
        loginButton.layer.insertSublayer(gradientLayer, at: 0)
        gradientLayer.frame = CGRect(x: 0, y: 0, width: 90, height: 40)
    }
    
    convenience init() {
        self.init(frame: .zero)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func makeUI() {

        addSubview(bgview)
        
        bgview.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.leading.trailing.equalToSuperview()
            make.height.equalTo(212)
        }
        
        bgview.addSubviews([loginButton, selectedView, contentLabel])
        
        loginButton.snp.makeConstraints { make in
            make.bottom.equalToSuperview()
            make.centerX.equalToSuperview()
            make.height.equalTo(40)
            make.width.equalTo(90)
        }
        
        selectedView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalToSuperview()
            make.height.width.equalTo(136)
        }
        
        contentLabel.snp.makeConstraints { make in
            make.trailing.leading.equalToSuperview()
            make.top.equalTo(selectedView.snp.bottom)
            make.height.equalTo(20)
        }
    }
    
    
    
    func business() {
        
    }
    
    
    
}
