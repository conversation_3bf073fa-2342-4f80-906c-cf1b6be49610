//
//  DoyinGoodsCell.swift
//  MockBuyin
//
//  Created by simon on 21.11.24.
//

import Foundation
import UIKit
import UIColor_Hex_Swift

enum DoyinGoodsOperation: Int, Codable {
    case tui  = 0
    case jiang = 1
    case xiajia = 2 // 下架
    case shangjia = 3 // 上架
    
}

protocol DoyinGoodsCellDelegate: NSObjectProtocol {
    func goodsOperation(operation:DoyinGoodsOperation, data: Promotions_data_promotions)
}


class DoyinGoodsCell: UITableViewCell {
    
    weak var delegate:DoyinGoodsCellDelegate?
    
    lazy var headerImage: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFit
        imageView.cornerRadius = 6
        return imageView
    }()
    
    //
    lazy var bgview: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor("#282828")
        view.layer.cornerRadius = 12
        view.borderWidth = 1.5
        return view
    }()
    
    lazy var title: UILabel = {
        let label = UILabel()
        label.textColor = UIColor.white
        label.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        return label
    }()
    
    lazy var idxLab: UILabel = {
        let label = UILabel()
        label.textColor = UIColor.white
        label.font = UIFont.systemFont(ofSize: 12, weight: .semibold)
        label.textAlignment = .left
        return label
    }()
    
    lazy var contentLabel: UILabel = {
        let label = UILabel()
        label.textColor = UIColor("#53FF79")
        label.font = UIFont.systemFont(ofSize: 12)
        label.backgroundColor = UIColor.black.alpha(value: 0.6)
        label.textAlignment = .center
        label.text = "讲解中"
        return label
    }()
    // 售价
    lazy var priceLabel: UILabel = {
        let label = UILabel()
        label.textColor = UIColor("#FA5F55")
        label.font = UIFont.systemFont(ofSize: 16, weight: .semibold)
        return label
    }()
    
    // 库存
    lazy var kucunLabel: UILabel = {
        let label = UILabel()
        label.textColor = UIColor.white.alpha(value: 0.6)
        label.font = UIFont.systemFont(ofSize: 12)
        return label
    }()

    lazy var selectedView: UIImageView = {
        let button = UIImageView()
        button.image = UIImage(named: "商品未选中")
        button.backgroundColor = .clear
        return button
    }()
    
    lazy var jiangBtn: UIButton = {
        let label = UIButton()
        label.setTitle("讲解", for: .normal)
        label.titleLabel?.font = UIFont.systemFont(ofSize: 14)
        label.setTitleColor(UIColor("#ADAAFF"), for: .normal)
        label.addTarget(self, action: #selector(jiangjieAction), for: .touchUpInside)
        label.cornerRadius = 15
        label.borderColor = UIColor("#ADAAFF")
        label.borderWidth = 1.5
        label.backgroundColor = .clear
        return label
    }()
    
    /// 渐变层
    private var gradientLayer: CAGradientLayer = {
        let leftColor = UIColor("#6974F2")
        let rightColor = UIColor("#AC63F9")
        let gradientColors = [leftColor.cgColor, rightColor.cgColor]
        let gradientLocations: [NSNumber] = [0.0, 1.0]
        let gradientLayer = CAGradientLayer()
        gradientLayer.startPoint = CGPoint(x: 0, y: 0)
        gradientLayer.endPoint = CGPoint(x: 1, y: 0)
        gradientLayer.colors = gradientColors
        gradientLayer.locations = gradientLocations
        return gradientLayer
    }()
    
    var good: Promotions_data_promotions?
        
    override func awakeFromNib() {
        super.awakeFromNib()
    }
    
    override func setSelected(_ selected: Bool, animated: Bool) {
        super.setSelected(selected, animated: animated)
    }
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        makeUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    @objc func tuiAction() {
        guard let good = self.good else { return  }
        self.delegate?.goodsOperation(operation: .tui, data: good)
    }
    
    @objc func jiangjieAction() {
        guard let good = self.good else { return  }
        self.delegate?.goodsOperation(operation: .jiang, data: good)
    }
     
    @objc func jiaAction() {
        guard let good = self.good else { return  }
        self.delegate?.goodsOperation(operation: .xiajia, data: good)
    }
    
    func makeUI() {
        selectionStyle = .none
        contentView.addSubview(bgview)
        contentView.backgroundColor = .clear
        self.backgroundView?.backgroundColor = .clear
        self.backgroundColor = .clear
        
        bgview.addSubviews([ idxLab, headerImage, title, priceLabel, kucunLabel, jiangBtn, selectedView])
       
        headerImage.addSubviews([contentLabel])
        
        bgview.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview().inset(8)
            make.top.bottom.equalToSuperview().inset(5)
        }
        
        
        headerImage.snp.makeConstraints { make in
            make.leading.equalToSuperview().inset(34)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(80)
        }
        
        idxLab.snp.makeConstraints { make in
            make.leading.equalToSuperview().inset(8)
            make.centerY.equalToSuperview()
            make.width.equalTo(24)
        }
        
        selectedView.snp.makeConstraints { make in
            make.leading.equalToSuperview().inset(8)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(16)
        }
        
       
        title.snp.makeConstraints { make in
            make.leading.equalTo(headerImage.snp.trailing).offset(4)
            make.top.equalTo(headerImage.snp.top)
            make.trailing.equalToSuperview().inset(16)
        }
        
        contentLabel.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview()
            make.bottom.equalTo(headerImage.snp.bottom)
            make.height.equalTo(22)
        }
        
        priceLabel.snp.makeConstraints { make in
            make.leading.equalTo(headerImage.snp.trailing).offset(8)
            make.bottom.equalToSuperview().inset(8)
        }
        
        kucunLabel.snp.makeConstraints { make in
            make.leading.equalTo(headerImage.snp.trailing).offset(8)
            make.centerY.equalTo(headerImage.snp.centerY)
            make.trailing.equalToSuperview().inset(32)
            make.height.equalTo(20)
        }
        
        
        jiangBtn.snp.makeConstraints { make in
            make.trailing.equalToSuperview().inset(8)
            make.bottom.equalToSuperview().inset(14)
            make.height.equalTo(30)
            make.width.equalTo(62)
        }
        
        
    }

}

extension DoyinGoodsCell {
    func bindBase(to model: Promotions_data_promotions, idx: Int,  isLiving: Bool, isCurrent: Bool, isMulti: Bool,isSelected: Bool ) {
        self.good = model
        self.title.text = model.title

        let attributes = [NSAttributedString.Key.foregroundColor: UIColor("#FA5F55"), NSAttributedString.Key.font: UIFont.systemFont(ofSize: 14, weight: .regular)]
        
        let attributes1 = [NSAttributedString.Key.foregroundColor: UIColor("#FA5F55"), NSAttributedString.Key.font: UIFont.systemFont(ofSize: 16, weight: .semibold)]
        
        let attributedText = NSMutableAttributedString(string: "¥ ", attributes: attributes)
        
        var price = model.price_desc.min_price.integer
        if !model.price_desc.min_price.decimal.isEmpty {
            price = price + "." + model.price_desc.min_price.decimal
        }
        let attributedText1 = NSMutableAttributedString(string: price, attributes: attributes1)
        attributedText.append(attributedText1)
        
        self.priceLabel.attributedText = attributedText
        
        self.kucunLabel.text = "售出/库存:" + "\(model.online_room_operating_data.pay_combo_cnt)" + "/" + "\(model.stock_num)"
        if let url = URL(string: model.cover) {
            self.headerImage.setImageWith(url, placeholder: nil)
        }
        self.idxLab.text = "\(idx + 1)"
        
        self.jiangBtn.isHidden = false
        jiangBtn.isHidden = !isLiving
        if isCurrent {
            self.contentLabel.isHidden = false
            self.jiangBtn.setTitle("正在讲解", for: .normal)
            self.jiangBtn.setTitleColor(UIColor.white, for: .normal)
            self.jiangBtn.borderColor = UIColor.clear
            jiangBtn.snp.updateConstraints { make in
                make.width.equalTo(80)
            }
            jiangBtn.layer.insertSublayer(gradientLayer, at: 0)
            gradientLayer.frame = CGRect(x: 0, y: 0, width: 80, height: 30)
            
        } else {
            self.contentLabel.isHidden = true
            self.jiangBtn.setTitle("讲解", for: .normal)
            self.jiangBtn.setTitleColor(UIColor("#ADAAFF"), for: .normal)
            self.jiangBtn.borderColor = UIColor("#ADAAFF")
            jiangBtn.snp.updateConstraints { make in
                make.width.equalTo(62)
            }
            gradientLayer.removeFromSuperlayer()
        }
        
        //
        if isMulti {
            self.idxLab.isHidden  = true
            self.selectedView.isHidden = false
        } else {
            self.idxLab.isHidden  = false
            self.selectedView.isHidden = true
        }
        
        if isSelected {
            self.selectedView.image = UIImage(named: "商品选中")
        } else {
            self.selectedView.image = UIImage(named: "商品未选中")
        }
        
        bgview.borderColor = isSelected ? UIColor("#ADAAFF") : UIColor.clear
        
    }
    
   
}


// 待上架商品的cell
class DoyinAllGoodsCell: UITableViewCell {
    
    
    lazy var headerImage: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFit
        imageView.cornerRadius = 6
        return imageView
    }()
    
    //
    lazy var bgview: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor("#282828")
        view.layer.cornerRadius = 12
        view.borderWidth = 1.5
        return view
    }()
    
    lazy var title: UILabel = {
        let label = UILabel()
        label.textColor = UIColor.white
        label.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        return label
    }()
    
    lazy var idxLab: UILabel = {
        let label = UILabel()
        label.textColor = UIColor.white
        label.font = UIFont.systemFont(ofSize: 12, weight: .semibold)
        label.textAlignment = .left
        return label
    }()
    
    lazy var contentLabel: UILabel = {
        let label = UILabel()
        label.textColor = UIColor.white
        label.font = UIFont.systemFont(ofSize: 12)
        label.backgroundColor = UIColor.black.alpha(value: 0.6)
        label.textAlignment = .center
        label.text = "已上架"
        return label
    }()
    // 售价
    lazy var priceLabel: UILabel = {
        let label = UILabel()
        label.textColor = UIColor("#FA5F55")
        label.font = UIFont.systemFont(ofSize: 16, weight: .semibold)
        return label
    }()
    
    // 库存
    lazy var kucunLabel: UILabel = {
        let label = UILabel()
        label.textColor = UIColor.white.alpha(value: 0.6)
        label.font = UIFont.systemFont(ofSize: 12)
        return label
    }()
    
    lazy var selectedView: UIImageView = {
        let button = UIImageView()
        button.image = UIImage(named: "商品未选中")
        button.backgroundColor = .clear
        return button
    }()
            
    var good: Promotions_all?
    
    override func awakeFromNib() {
        super.awakeFromNib()
    }
    
    override func setSelected(_ selected: Bool, animated: Bool) {
        super.setSelected(selected, animated: animated)
    }
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        makeUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    
    func makeUI() {
        selectionStyle = .none
        contentView.addSubview(bgview)
        contentView.backgroundColor = .clear
        self.backgroundView?.backgroundColor = .clear
        self.backgroundColor = .clear
        
        bgview.addSubviews([idxLab, headerImage, title, priceLabel, kucunLabel, selectedView])
       
        headerImage.addSubviews([contentLabel])
        
        bgview.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview().inset(8)
            make.top.bottom.equalToSuperview().inset(5)
        }
        
        
        headerImage.snp.makeConstraints { make in
            make.leading.equalToSuperview().inset(34)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(80)
        }
        
        idxLab.snp.makeConstraints { make in
            make.leading.equalToSuperview().inset(8)
            make.centerY.equalToSuperview()
            make.width.equalTo(24)
        }
        
        selectedView.snp.makeConstraints { make in
            make.leading.equalToSuperview().inset(8)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(16)
        }
        
       
        title.snp.makeConstraints { make in
            make.leading.equalTo(headerImage.snp.trailing).offset(10)
            make.top.equalTo(headerImage.snp.top)
            make.trailing.equalToSuperview().inset(16)
        }
        
        contentLabel.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview()
            make.bottom.equalTo(headerImage.snp.bottom)
            make.height.equalTo(22)
        }
        
        priceLabel.snp.makeConstraints { make in
            make.leading.equalTo(headerImage.snp.trailing).offset(10)
            make.bottom.equalToSuperview().inset(8)
        }
        
        kucunLabel.snp.makeConstraints { make in
            make.leading.equalTo(headerImage.snp.trailing).offset(10)
            make.centerY.equalTo(headerImage.snp.centerY)
            make.trailing.equalToSuperview().inset(32)
            make.height.equalTo(20)
        }
        
    }
    
    @objc func selectedAction() {
        
    }
    

}

extension DoyinAllGoodsCell {
    
    func bindAllBase(to model: Promotions_all, idx: Int,  isSelected: Bool, isCurrent: Bool, isMulti: Bool) {
        self.good = model
        self.title.text = model.title
        
        let attributes = [NSAttributedString.Key.foregroundColor: UIColor("#FA5F55"), NSAttributedString.Key.font: UIFont.systemFont(ofSize: 14, weight: .regular)]
        
        let attributes1 = [NSAttributedString.Key.foregroundColor: UIColor("#FA5F55"), NSAttributedString.Key.font: UIFont.systemFont(ofSize: 16, weight: .semibold)]
        
        let attributedText = NSMutableAttributedString(string: "¥ ", attributes: attributes)
        
        var price = model.price.integer
        if !model.price.decimal.isEmpty {
            price = price + "." + model.price.decimal
        }
        let attributedText1 = NSMutableAttributedString(string: price, attributes: attributes1)
        attributedText.append(attributedText1)
        
        self.priceLabel.attributedText = attributedText
        
        self.kucunLabel.text = "库存:" + "\(model.stock_num)"
        if let url = URL(string: model.cover) {
            self.headerImage.setImageWith(url, placeholder: nil)
        }
        self.idxLab.text = "\(idx + 1)"
        
        if BuyinRequest.shared.isShelf(id: model.promotion_id) {
            self.contentLabel.isHidden = false
        } else {
            self.contentLabel.isHidden = true
        }
        
        bgview.borderColor = isSelected ? UIColor("#ADAAFF") : UIColor.clear
        
        //
        if isMulti {
            self.idxLab.isHidden  = true
            self.selectedView.isHidden = false
        } else {
            self.idxLab.isHidden  = false
            self.selectedView.isHidden = true
        }
        
        if isSelected {
            self.selectedView.image = UIImage(named: "商品选中")
        } else {
            self.selectedView.image = UIImage(named: "商品未选中")
        }
      
    }
}
