//
//  DouyinSite.swift
//  LivePlus
//
//  Created by simon on 28.10.24.
//
import Foundation


struct DouyinDanmakuArgs: Codable {
    let webRid: String?
    let roomId: String?
    let userId: String?
    let cookie: String?
}

// 定义LiveRoomDetail结构体
struct LiveRoomDetail: Codable {
    // 房间ID
    let roomId: String?
    // 房间标题
    let title: String?
    // 封面
    let cover: String?
    // 用户名
    let userName: String?
    // 头像
    let userAvatar: String?
    // 在线人数
    let online: Int?
    // 介绍
    let introduction: String?
    // 公告
    let notice: String?
    // 状态
    let status: Bool?
    // 附加信息（这里使用Any?，可能需要根据实际情况进一步明确类型）
    let data: String?
    // 弹幕附加信息
    let danmakuData: DouyinDanmakuArgs?
    // 是否录播
    let isRecord: Bool?
    // 链接
    let url: String?
    
    // 自定义的初始化方法（因为在Swift中默认的初始化方法可能不满足需求）
    init(roomId: String?, title: String?, cover: String?, userName: String?, userAvatar: String?, online: Int?, introduction: String?, notice: String?, status: Bool?, data: String?, danmakuData: DouyinDanmakuArgs?, isRecord: Bool?, url: String?) {
        self.roomId = roomId
        self.title = title
        self.cover = cover
        self.userName = userName
        self.userAvatar = userAvatar
        self.online = online
        self.introduction = introduction
        self.notice = notice
        self.status = status
        self.data = data
        self.danmakuData = danmakuData
        self.isRecord = isRecord
        self.url = url
    }
    
    
    
}
