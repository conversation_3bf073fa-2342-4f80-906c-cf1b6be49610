//
//  webjs.swift
//  LivePlus
//
//  Created by simon on 16.10.24.
//


let JavascriptBridgeJS = """
// JS调用——查找用户名
function findUserName() {
    console.log("**获取用户名**");
    var nodes = document.getElementsByClassName('btn-item-role-exchange-name__title');
    if (nodes.length > 0) {
        var node = nodes[0];
        var text = node.innerText;
         //传递的信息
         var jsonStr = {"key":"js_type_get_login","message":text};
        window.webkit.messageHandlers.getMessage.postMessage(jsonStr)
    } else {
        console.log("没有用户名**");
        var jsonStr = {"key":"js_type_get_login","message":""};
        window.webkit.messageHandlers.getMessage.postMessage(jsonStr);
    }
}


function findElementByTextFromNode(text, node) {
    var elements = node.querySelectorAll('*'); // 选择所有元素
    for (var i = 0; i < elements.length; i++) {
        var element = elements[i];
        if (element.textContent.trim() === text && isClickable(element)) {
            return element;
        }
    }
    return null;
}

//是否可以点击
function isClickable(element) {
    // 检查元素是否具有 onclick 属性
    if (element.onclick) {
        return true;
    }
    // 检查元素是否属于可点击的元素类型
    var clickableTypes = ['button', 'input', 'a'];
    return clickableTypes.includes(element.tagName.toLowerCase());
}


// 点击视图
function clickElement(element) {
    if (element) {
        element.click();
        console.log("触发点击---->" + element);
    }
}

// JS调用——查找元素并点击
function findElementAndClick(text) {
    var elements = document.querySelectorAll('*'); // 选择所有元素
    for (var i = 0; i < elements.length; i++) {
        var element = elements[i];
        if (element.textContent.trim() === text && isClickable(element)) {
            clickElement(element);
            var jsonStr = {"key":"js_type_find_click","message":element.outerHTML};
            window.webkit.messageHandlers.getMessage.postMessage(jsonStr);
            return element.outerHTML;
        }
    }
    var jsonStr = {"key":"js_type_find_click","message":""};
    window.webkit.messageHandlers.getMessage.postMessage(jsonStr);
}


//JS调用——查找元素
function findElement(text) {
    var elements = document.querySelectorAll('*'); // 选择所有元素
    for (var i = 0; i < elements.length; i++) {
        var element = elements[i];
        if (element.textContent.trim() === text && isClickable(element)) {
            var jsonStr = {"key":"js_type_find","message":element.outerHTML};
            window.webkit.messageHandlers.getMessage.postMessage(jsonStr);
        }
    }
    var jsonStr = {"key":"js_type_find","message":""};
    window.webkit.messageHandlers.getMessage.postMessage(jsonStr);
}


// js调用——查找商品并点击讲解与
function findAndClickGood(title, handlerTitle) {
    var goodsDiv = document.getElementById("live-control-goods-list-container");
    var selector = ':scope [class^="goodsItem-"]';
    var goods = goodsDiv.querySelectorAll(selector);
    goods.forEach(good => {
        var selectorTitle = ':scope [class^="title-"]';
        var goodTitle = good.querySelector(selectorTitle);
        var titleText = goodTitle ? goodTitle.innerText : '';

        if (titleText === title) {
            // 去点击
            var node = findElementByTextFromNode(handlerTitle, good);
            if (node) {
                clickElement(node);
                var jsonStr = {"key":"handle_jiangjie","message":"ok"};
                window.webkit.messageHandlers.getMessage.postMessage(jsonStr);
            }
        }
    });
    var jsonStr = {"key":"handle_jiangjie_no","message":"no"};
    window.webkit.messageHandlers.getMessage.postMessage(jsonStr);
}



//JS 调用——获取商品列表
function getGoodsData() {
    var goodsDiv = document.getElementById("live-control-goods-list-container");
    var selector = ':scope [class^="goodsItem-"]'; // 使用正常的单引号
    var goods = goodsDiv.querySelectorAll(selector);
    var result = [];
    if (goods.length > 0) {
        goods.forEach(item => {
            var selectorTitle = ':scope [class^="title-"]'; // 使用正常的单引号
            var title = item.querySelector(selectorTitle);
            var titleText = title ? title.innerText : '';

            var type=0
            var node = findElementByTextFromNode("取消主推", item)
            if (node) {
                type = 1
            } else {
                node = findElementByTextFromNode("设为主推", item)
                if (node) {
                    type = 2
                }
            }

            var sourceSrc = "";
            var source = item.querySelector("img");
            if (source) {
                sourceSrc = source.getAttribute("src");
              }

            // 补全 URL
            sourceSrc = completeUrl(sourceSrc);
            var resultEntry = {
                title: titleText,
                source: sourceSrc,
                type: type
            };
            result.push(resultEntry);
        });
    }
    var jsonStr = {"key":"js_type_get_goods","message":result};
    window.webkit.messageHandlers.getMessage.postMessage(jsonStr);
}

// 辅助函数：解析 srcset 属性
function parseSrcset(srcset) {
    return srcset.split(',').map(entry => {
        var parts = entry.trim().split(' ');
        return {
            url: parts[0],
            descriptor: parts[1]
        };
    });
}

// 辅助函数：补全 URL
function completeUrl(url) {
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
        // 假设当前页面的协议为 'https://'（可以根据实际情况调整）
        if (url.startsWith('//')) {
            return 'https:' + url;
        } else {
            return 'https://' + url;
        }
    }
    return url;
}

// js调用——点击经营和中控台
function toZhongkongtaiFromRoot(num) {
    if (num <= 0) {
        return
    }
    setTimeout(() => {
        var result = findElementAndClick("经营")
        if (result) {
            console.log("点击经营成功")

            setTimeout(() => {
                var result = findElementAndClick("直播中控台")
                if (result) {
                     console.log("点击直播中控台成功")
                     var jsonStr = {"key":"js_type_zhongkong","message":"ok"};
                     window.webkit.messageHandlers.getMessage.postMessage(jsonStr);
                     return "ok"
                } else {
                    toZhongkongtaiFromRoot(num - 1)
                }
            }, 3000);
        } else {
            toZhongkongtaiFromRoot(num - 1)
        }
    }, 5000)
}
// js调用——点击我知道了
function clickIKnow(num) {
    if (num <= 0) {
        return
    }
    setTimeout(() => {
        var result = findElementAndClick("我知道了")
        if (result) {
            console.log("点我知道了成功")
        } else {
            clickIKnow(num - 1)
        }
    }, 5000)
}

// js调用——点击开始体验
function clicktiyan(num) {
    if (num <= 0) {
        return
    }
    setTimeout(() => {
        var result = findElementAndClick("开始体验")
        if (result) {
            console.log("点开始体验成功")
        } else {
            clicktiyan(num - 1)
        }
    }, 5000)
}

// js调用——点击开始体验
function thanks() {
    setTimeout(() => {
        var result = findElementAndClick("感谢家人们")
        if (result) {
            console.log("点感谢家人们成功")
            var jsonStr = {"key":"js_type_zhongkong","message":"找到了家人"};
            window.webkit.messageHandlers.getMessage.postMessage(jsonStr);
        } else {
            var jsonStr = {"key":"js_type_zhongkong","message":"找不到家人"};
            window.webkit.messageHandlers.getMessage.postMessage(jsonStr);
        }

    }, 5000)
}

// js调用 选品车点击
function selectGoods(value) {
    // 方法1：通过完整的选择器路径查找
    let checkbox = document.querySelector(`#rc-tabs-4-panel-SELECT_GOODS > div.selectGoods-UeUpgg > div.goodList-iuDQoi > div.auxo-spin-nested-loading > div > div > div.auxo-checkbox-group > div:nth-child(1) > div > div.goodItemWrapper__checkbox-blLa3g input[value="${value}"]`);
    
    // 方法2：直接通过属性选择器查找（更简单可靠）
    if (!checkbox) {
        checkbox = document.querySelector(`input[value="${value}"]`);
    }
    
    if (checkbox) {
        // 如果元素未被选中，则点击它
        if (!checkbox.checked) {
            checkbox.click();
            // 或者设置checked属性并触发change事件
            checkbox.checked = true;
            checkbox.dispatchEvent(new Event('change', { bubbles: true }));
        }
        var jsonStr = {"key":"js_type_select_goods","message":"success"};
        window.webkit.messageHandlers.getMessage.postMessage(jsonStr);
        return 'success';
    }
    var jsonStr = {"key":"js_type_select_goods","message":"element not found"};
    window.webkit.messageHandlers.getMessage.postMessage(jsonStr);
    return 'element not found';
}




// 辅助函数：检查元素是否可点击
function isElementClickable(element) {
    if (!element) return false;
    
    const style = window.getComputedStyle(element);
    const rect = element.getBoundingClientRect();
    
    return (
        style.display !== 'none' &&
        style.visibility !== 'hidden' &&
        style.opacity !== '0' &&
        rect.width > 0 &&
        rect.height > 0 &&
        !element.disabled
    );
}

// 辅助函数：发送消息到原生端
function sendMessage(key, status, contentText, errorMessage = '') {
    const jsonStr = {
        key: key,
        message: status,
        contentText: contentText,
        errorMessage: errorMessage
    };
    
    if (window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.getMessage) {
        window.webkit.messageHandlers.getMessage.postMessage(jsonStr);
    }
}

// 搭配的取消功能
function clickCombinationDelete(contentText) {
    return new Promise((resolve) => {
        try {
            // 1. 首先找到包含指定内容的div
            let targetDiv = Array.from(document.querySelectorAll('.combinationCard_info_name-fAL9Mw')).find(div =>
                div.textContent.trim() === contentText
            );

            if (!targetDiv) {
                sendMessage('js_type_clickCombination', 'element not found', contentText);
                resolve('element not found');
                return;
            }

            // 2. 找到最外层的父级卡片div
            let parentCard = targetDiv.closest('.combinationCard-h0IN1z');

            if (!parentCard) {
                sendMessage('js_type_clickCombination', 'parent card not found', contentText);
                resolve('parent card not found');
                return;
            }

            // 3. 模拟完整的鼠标事件序列
            const events = ['mouseenter', 'mouseover', 'mousemove'];
            events.forEach(eventType => {
                const event = new MouseEvent(eventType, {
                    bubbles: true,
                    cancelable: true,
                    view: window,
                    clientX: parentCard.getBoundingClientRect().left + 10,
                    clientY: parentCard.getBoundingClientRect().top + 10
                });
                parentCard.dispatchEvent(event);
            });

            // 4. 使用递归函数尝试多次查找和点击删除按钮
            let attempts = 0;
            const maxAttempts = 10;
            const checkInterval = 100; // 每100ms检查一次

            function tryClickDelete() {
                attempts++;

                // 查找删除按钮
                let deleteButton = Array.from(parentCard.querySelectorAll('*')).find(element => {
                    const hasDelClass = Array.from(element.classList).some(className =>
                        className.toLowerCase().includes('del')
                    );
                    return hasDelClass;
                });

                if (deleteButton) {
                    // 先触发鼠标移入事件到父元素
                    const parentEvents = ['mouseenter', 'mouseover'];
                    parentEvents.forEach(eventType => {
                        const event = new MouseEvent(eventType, {
                            bubbles: true,
                            cancelable: true,
                            view: window,
                            clientX: parentCard.getBoundingClientRect().left + 5,
                            clientY: parentCard.getBoundingClientRect().top + 5
                        });
                        parentCard.dispatchEvent(event);
                    });

                    // 等待一小段时间后触发删除按钮的事件
                    setTimeout(() => {
                        try {
                            // 遍历并点击所有子元素
                            const clickAllChildren = (element) => {
                                // 获取所有子元素
                                const children = element.children;
                                if (children && children.length > 0) {
                                    Array.from(children).forEach(child => {
                                        child.click();
                                        console.log("子元素click")
                                    });
                                }
                            };

                            // 执行递归点击
                            clickAllChildren(deleteButton);
                            sendMessage('js_type_clickCombination', 'success', contentText);
                            resolve('success');

                        } catch (e) {
                            sendMessage('js_type_clickCombination', 'click failed', contentText);
                            resolve('click failed');
                        }
                    }, 50);

                } else if (attempts < maxAttempts) {
                    setTimeout(tryClickDelete, checkInterval);
                } else {
                    resolve('delete button not found or not clickable');
                    sendMessage('js_type_clickCombination', 'delete button not found or not clickable', contentText);
                }
            }

            // 开始尝试查找和点击
            setTimeout(tryClickDelete, 100);

        } catch (error) {
            resolve('error: ' + error.message);
            sendMessage('js_type_clickCombination', 'error: ' + error.message, contentText);
        }
    });
}

// 关闭搭配窗口
function clickCloseCombination() {
    // 找到具有类名 close-sZosI4 的元素
    const closeButton = document.querySelector('.auxo-drawer-close');

    // 检查元素是否存在
    if (closeButton) {
        // 执行点击操作
        closeButton.click();
        console.log('关闭按钮已点击');
    } else {
        console.error('未找到关闭按钮');
    }
}

// 关闭选品的弹窗
function closexuanpin() {
    // 找到具有类名 close-sZosI4 的元素
    const closeButton = document.querySelector('.close-sZosI4');

    // 检查元素是否存在
    if (closeButton) {
        // 执行点击操作
        closeButton.click();
        console.log('关闭按钮已点击');
    } else {
        console.error('未找到关闭按钮');
    }
}


// ====================== 核心模块 ======================
const iOSPromotionHelper = {
    // 模式配置：'auto'自动 | 'force'强制
    mode: 'force',
    debug: true, // 新增调试模式
    
    clickPromotion: function(num, draggableId, title, divname) {
        this.log(`开始执行: draggableId=${draggableId}, title=${title}`);
        this.cleanState();
        
        this.initializeScroll(divname, num)
            .then(() => {
                this.log('滚动初始化完成，开始查找元素...');
                return this.enhancedFindTarget(draggableId, title,divname);
            })
            .then(result => this.handleResult(result, draggableId))
            .catch(error => this.handleError(error, draggableId));
    },

    // 增强版元素查找（带重试机制）
    enhancedFindTarget: function(draggableId, title,divname, retry = 10) {
        return new Promise((resolve, reject) => {
            const search = () => {
                this.log(`开始第${4-retry}次查找...`);
                const result = this.findTargetElement(draggableId, title);
                
                if (result) {
                    this.log('元素找到:', result);
                    resolve(result);
                } else if (retry > 0) {
                    this.log('元素未找到，尝试滚动重试...');
                    scrollToItemNext(divname);
                    setTimeout(() => {
                        retry--;
                        search();
                    }, 500);
                } else {
                    reject(new Error('MAX_RETRY_REACHED'));
                }
            };
            
            search();
        });
    },

    // 改进的元素查找方法
    findTargetElement: function(draggableId, title) {
        try {
            const candidates = document.querySelectorAll(
                `[data-rbd-draggable-id="${draggableId}"]`
            );
            this.log(`找到${candidates.length}个候选元素`);

            for (const div of candidates) {
                const btn = this.findButton(div, title);
                if (!btn) {
                    this.log(`元素${draggableId}内未找到按钮: ${title}`);
                    continue;
                }

                const rect = btn.getBoundingClientRect();
                this.log(`按钮位置: top=${rect.top}, visible=${this.isVisible(btn)}`);
                
                // 强制模式直接返回
                if (this.mode === 'force') {
                    return { element: btn, virtual: true };
                }

                if (this.isVisible(btn)) {
                    return { element: btn, virtual: false };
                }

                this.log('元素不可见，尝试虚拟点击...');
                return { element: btn, virtual: true };
            }
        } catch (error) {
            this.log('查找过程中发生错误:', error);
        }
        return null;
    },

    // 增强的虚拟点击方法
    virtualClick: function(element) {
        try {
            ['mousedown', 'mouseup', 'click'].forEach(eventType => {
                const event = new MouseEvent(eventType, {
                    view: window,
                    bubbles: true,
                    cancelable: true,
                    composed: true  // 新增关键参数
                });
                element.dispatchEvent(event);
            });
            return true;
        } catch (error) {
            this.log('虚拟点击失败:', error);
            return false;
        }
    },

    // 新增调试日志
    log: function(...args) {
        if (this.debug) {
            console.log('[DEBUG]', ...args);
            window.webkit.messageHandlers.getMessage.postMessage({
                key: "js_type_debug",
                message: args.join(' ')
            });
        }
    },

    // 初始化滚动位置
    initializeScroll: async function(selector, times) {
        return new Promise(resolve => {
            let count = times;
            const scroll = () => {
                scrollToItemUp(selector);
                if (--count > 0) setTimeout(scroll, 250);
                else resolve();
            };
            scroll();
        });
    },


    // 处理点击结果
    handleResult: function(result, draggableId) {
        if (!result) {
            return this.sendMessage('element_not_found', draggableId);
        }

        const clickResult = result.virtual ?
            this.virtualClick(result.element) :
            result.element.click();

        this.sendMessage(clickResult ? 'success' : 'fail', draggableId, {
            method: result.virtual ? 'virtual' : 'direct',
            retry: this.retryCount
        });
    },

    // 工具方法
    findButton: (parent, text) =>
        Array.from(parent.querySelectorAll('button'))
            .find(btn => btn.textContent.trim() === text),
    
    isVisible: el => {
        const rect = el.getBoundingClientRect();
        return rect.top < window.innerHeight &&
               rect.bottom > 0 &&
               rect.width > 0 &&
               rect.height > 0;
    },
    
    
    sendMessage: (status, draggableId, extras) => {
        const message = {
            key: "js_type_clickPromotion",
            message: status,
            draggableId: draggableId,
            ...extras
        };
        window.webkit.messageHandlers.getMessage.postMessage(message);
    },
    
    cleanState: () => {
        sessionStorage.removeItem('goodsScrollIndex');
        iOSPromotionHelper.retryCount = 0;
    },
    
    handleError: (error, draggableId) => {
        console.error(`[iOS Promo] ${error}`);
        iOSPromotionHelper.sendMessage('error', draggableId, {
            error: error.message
        });
    },
    
    retryCount: 0
};

// ====================== 滚动模块（保持原有实现） ======================
function scrollToItemUp(divname) {
    const items = document.querySelectorAll(divname);
    if (items.length === 0) return;

    let currentIndex = parseInt(sessionStorage.getItem('goodsScrollIndex') || 0);
    const itemsPerPage = Math.floor(window.innerHeight / items[0].offsetHeight) || 1;
    
    currentIndex = Math.max(0, currentIndex - itemsPerPage);
    sessionStorage.setItem('goodsScrollIndex', currentIndex);
    
    items[currentIndex]?.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
    });
}

function scrollToItemNext(divname) {
    const items = document.querySelectorAll(divname);
    const lastIndex = items.length - 1;
    items[lastIndex]?.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
    });
}

// ====================== 对外接口（保持兼容） ======================
function clickPromotion(num, draggableId, title, divname) {
    iOSPromotionHelper.clickPromotion(num, draggableId, title, divname);
}

"""

// 接口请求监听
public let NetworkMonitor_injectedScript = """
    (function() {
        // 拦截 fetch 请求
        let originalFetch = window.fetch;
        window.fetch = async function() {
            let request = arguments[0];
            let init = arguments[1] || {};
            
            let requestId = Date.now().toString();
            
            // 获取完整的请求头信息
            let headers = {};
            
            // 1. 如果第一个参数是 Request 对象，获取其头信息
            if (request instanceof Request) {
                request.headers.forEach((value, key) => {
                    headers[key] = value;
                });
                
                // 克隆请求以获取更多信息
                request = request.clone();
            }
            
            // 2. 合并 init 中的头信息
            if (init.headers) {
                let initHeaders = new Headers(init.headers);
                initHeaders.forEach((value, key) => {
                    headers[key] = value;
                });
            }
            
            // 3. 添加默认和自动生成的头信息
            headers['User-Agent'] = navigator.userAgent;
            headers['Accept'] = headers['Accept'] || '*/*';
            headers['Accept-Language'] = navigator.language;
            headers['Origin'] = window.location.origin;
            headers['Referer'] = window.location.href;
            
            let requestInfo = {
                id: requestId,
                url: typeof request === 'string' ? request : request.url,
                method: (init.method || (request instanceof Request ? request.method : 'GET')),
                headers: headers,
                body: init.body || null
            };
            
            window.webkit.messageHandlers.requestHandler.postMessage(requestInfo);
            
            try {
                let response = await originalFetch.apply(this, arguments);
                let responseClone = response.clone();
                
                // 获取响应头
                let responseHeaders = {};
                response.headers.forEach((value, key) => {
                    responseHeaders[key] = value;
                });
                
                let responseInfo = {
                    id: requestId,
                    status: response.status,
                    headers: responseHeaders,
                    body: await responseClone.text(),
                    url: typeof request === 'string' ? request : request.url
                };
                
                window.webkit.messageHandlers.responseHandler.postMessage(responseInfo);
                return response;
            } catch (error) {
                let errorInfo = {
                    id: requestId,
                    error: error.toString()
                };
                window.webkit.messageHandlers.responseHandler.postMessage(errorInfo);
                throw error;
            }
        };
        
        // 拦截 XMLHttpRequest
        let XHR = XMLHttpRequest.prototype;
        let originalOpen = XHR.open;
        let originalSend = XHR.send;
        let originalSetRequestHeader = XHR.setRequestHeader;
        
        XHR.open = function() {
            this._requestId = Date.now().toString();
            this._method = arguments[0];
            this._url = arguments[1];
            this._headers = {
                // 添加默认头信息
                'User-Agent': navigator.userAgent,
                'Accept': '*/*',
                'Accept-Language': navigator.language,
                'Origin': window.location.origin,
                'Referer': window.location.href
            };
            return originalOpen.apply(this, arguments);
        };
        
        XHR.setRequestHeader = function(header, value) {
            this._headers[header] = value;
            return originalSetRequestHeader.apply(this, arguments);
        };
        
        XHR.send = function() {
            let xhr = this;
            let body = arguments[0];
            
            // 在发送前获取所有可用的请求头
            let allHeaders = {...xhr._headers};
            
            // 尝试获取额外的请求头
            try {
                let headerLines = xhr.getAllResponseHeaders().split('\\r\\n');
                headerLines.forEach(line => {
                    if (line) {
                        let [key, value] = line.split(': ');
                        if (key && value) {
                            allHeaders[key] = value;
                        }
                    }
                });
            } catch (e) {
                // 忽略错误
            }
            
            let requestInfo = {
                id: xhr._requestId,
                url: xhr._url,
                method: xhr._method,
                headers: allHeaders,
                body: body || null
            };
            
            window.webkit.messageHandlers.requestHandler.postMessage(requestInfo);
            
            xhr.addEventListener('load', function() {
                let responseHeaders = {};
                let headerString = xhr.getAllResponseHeaders();
                let headerLines = headerString.split('\\r\\n');
                
                headerLines.forEach(line => {
                    if (line) {
                        let [key, value] = line.split(': ');
                        if (key && value) {
                            responseHeaders[key] = value;
                        }
                    }
                });
                
                let responseInfo = {
                    id: xhr._requestId,
                    status: xhr.status,
                    headers: responseHeaders,
                    body: xhr.responseText,
                    url: xhr._url
                };
                
                window.webkit.messageHandlers.responseHandler.postMessage(responseInfo);
            });
            
            return originalSend.apply(this, arguments);
        };
    })();
    """


let getAllCookies_script = """
(function() {
    function getCookie(name) {
        const value = `; ${document.cookie}`;
        const parts = value.split(`; ${name}=`);
        if (parts.length === 2) return parts.pop().split(';').shift();
        return '';
    }
    
    let info = {
        userAgent: navigator.userAgent,
        verifyFp: getCookie('s_v_web_id') || '',
        msToken: '',  // 可以为空
        xBogus: '',   // 可以为空
        csrf_session_id: getCookie('csrf_session_id') || '',
        ttwid: getCookie('ttwid') || '',
        currentUrl: window.location.href,
        allCookies: document.cookie
    };
    
    return JSON.stringify(info);
})();
"""
