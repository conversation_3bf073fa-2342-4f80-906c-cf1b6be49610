//
//  BuyinVC.swift
//  LiveConsole
//
//  Created by simon on 23.4.25.
//

import Foundation
import UIKit
import WebKit
import SwifterSwift

// 上架商品
let all_key = ".productCardContainer-UsBAE1"
//直播商品：.goodsItem-KBGOY5
let living_key =  ".goodsItem-KBGOY5"

class BuyinVC: SmartBaseViewController {
    
    private lazy var buttonView: UIView = {
        let stack = UIView()
        return stack
    }()
    
    private lazy var speView: UIView = {
        let stack = UIView()
        stack.backgroundColor = UIColor("#282828")
        return stack
    }()
    
    
    private lazy var emptyView: BuyinEmptyView = {
        let button = BuyinEmptyView()
        button.loginButton.addTarget(self, action: #selector(goLoginAction), for: .touchUpInside)
        button.isHidden = true
        return button
    }()
   
    private lazy var livingGoodsBtn: UIButton = {
        let button = createButton(title: "直播商品")
        button.addTarget(self, action: #selector(livingGoodAction), for: .touchUpInside)
        return button
    }()
    
    private lazy var allGoodsBtn: UIButton = {
        let button = createButton(title: "待上架商品")
        button.addTarget(self, action: #selector(allGoodAction), for: .touchUpInside)
        return button
    }()
    
    // 会显示名称
    private lazy var statusButton: UIButton = {
        let button = UIButton()
        button.setTitle("未登录", for: .normal)
        button.setTitleColor(UIColor("#FFFFFF").alpha(value: 0.6), for: .normal)
        button.backgroundColor = .clear
        button.titleLabel?.font = .systemFont(ofSize: 12, weight: .regular)
        button.setImage(UIImage(named: "ic_smart_disconnect"), for: .normal)
        button.imagePosition(style: .left, spacing: 8)
        button.addTarget(self, action: #selector(goLoginAction), for: .touchUpInside)
        return button
    }()
    
    
    private lazy var debugButton: UIButton = {
        let button = createButton(title: "debug")
        button.addTarget(self, action: #selector(debugTapped), for: .touchUpInside)
        button.isHidden = true
        return button
    }()
    
    // 讲解
    var jjTimer: Timer?
    
    // 巨量授权
    var isLogin: Bool = false
    
    private lazy var lineView: UIImageView = {
        let stack = UIImageView()
        stack.image = UIImage(named: "main_post_line")
        return stack
    }()
    
    // 商品信息
    lazy var goodsView: DouyinGoodsView = {
        let v = DouyinGoodsView()
        v.delegate = self
        return v
    }()
    
    lazy var goodsAllView: DouyinAllGoodsView = {
        let v = DouyinAllGoodsView()
        v.delegate = self
        v.isHidden = true
        return v
    }()
    
    // 控制JS的操作的VC 需要在后台打开这个页面
    // 让用户扫的二维码
    private lazy var cookieVC: NetworkMonitorViewController = {
        let view = NetworkMonitorViewController(frame: CGRect(x: 0, y: 0, width: LCDevice.screenW, height: LCDevice.screenH))
        view.delegate = self
        view.isHidden = true
        return view
    }()
    
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        self.navigationController?.hidesBottomBarWhenPushed = true
        setupUI()
                
        // 启动APP的时候  如果登录了  绑定了抖音 就去登录
        guard let _ = UserInfo.currentUser() else {
            return
        }
        
        if !UserInfo.isMember {
            return
        }
        
        let cookies = BuyinCookieInfo.shared.getAllCookies()
        if cookies.count > 10 {
            cookieVC.qrbgView.isHidden = true
            cookieVC.webView.isHidden = true
            cookieVC.startWebView()
        }

        NotificationCenter.default.addObserver(self, selector: #selector(userLogoutNoticeAction), name: LCKey.noti_logout, object: nil)
        
        NotificationCenter.default.addObserver(self, selector: #selector(userLoginNoticeAction), name: LCKey.noti_reLogin, object: nil)

        start_jj_Timer()
    }
    
    
    @objc func userLoginNoticeAction() {
        updateStatus()
    }
    
    @objc func userLogoutNoticeAction() {
        updateStatus()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        updateStatus()
    }
    
    
    // MARK: - UI Setup
    private func setupUI() {
        fd_interactivePopDisabled = true
        UIApplication.shared.isIdleTimerDisabled = true
        
        // 添加按钮到堆栈视图
        buttonView.addSubviews([livingGoodsBtn, allGoodsBtn, statusButton,debugButton, lineView, speView])
        
        
        // 添加堆栈视图到主视图
        view.addSubview(buttonView)
        
        buttonView.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview().inset(4)
            make.top.equalToSuperview().inset(44)
            make.height.equalTo(44)
        }
        
        speView.snp.makeConstraints { make in
            make.bottom.leading.trailing.equalToSuperview()
            make.height.equalTo(1.5)
        }
        
        livingGoodsBtn.snp.makeConstraints { make in
            make.bottom.equalToSuperview()
            make.height.equalTo(44)
            make.width.equalTo(88)
        }
        
        allGoodsBtn.snp.makeConstraints { make in
            make.bottom.equalToSuperview()
            make.leading.equalTo(livingGoodsBtn.snp.trailing).offset(8)
            make.height.equalTo(44)
            make.width.equalTo(102)
        }
        
        
        statusButton.snp.makeConstraints { make in
            make.bottom.equalToSuperview()
            make.trailing.equalToSuperview().inset(12)
            make.height.equalTo(44)
            make.width.equalTo(80)
        }
        
        
        debugButton.snp.makeConstraints { make in
            make.bottom.equalToSuperview()
            make.trailing.equalTo(statusButton.snp.leading).offset(-8)
            make.height.equalTo(44)
            make.width.equalTo(70)
        }
        
        
        lineView.snp.makeConstraints { make in
            make.bottom.equalToSuperview()
            make.centerX.equalTo(livingGoodsBtn.snp.centerX)
            make.height.equalTo(8)
            make.width.equalTo(36)
        }
        
        
        view.addSubview(goodsView)
        goodsView.snp.makeConstraints { make in
            make.leading.trailing.bottom.equalToSuperview()
            make.top.equalTo(buttonView.snp.bottom).offset(8)
        }
        
        view.addSubview(goodsAllView)
        goodsAllView.snp.makeConstraints { make in
            make.leading.trailing.bottom.equalToSuperview()
            make.top.equalTo(buttonView.snp.bottom).offset(8)
        }
        
        view.addSubview(emptyView)
        emptyView.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(LCDevice.Nav_H)
            make.leading.trailing.bottom.equalToSuperview()
        }
        
        
        self.view.addSubview(self.cookieVC)
        
    }
    
    private func createButton(title: String) -> UIButton {
        let button = UIButton()
        button.setTitle(title, for: .normal)
        button.setTitleColor(UIColor("#FFFFFF").alpha(value: 0.6), for: .normal)
        button.backgroundColor = .clear
        button.titleLabel?.font = .systemFont(ofSize: 17, weight: .medium)
        return button
    }
    
   
    
    // MARK: - Actions
    
    @objc private func livingGoodAction() {
       
        if !LCTools.checkMember() {
            return
        }
        
        self.allGoodsBtn.setTitleColor(UIColor("#FFFFFF").alpha(value: 0.6), for: .normal)
        self.livingGoodsBtn.setTitleColor(UIColor.white, for: .normal)
        
        if checkLogin() {
            cookieVC.findAndClick(title: "直播商品")
            if let promotions_v2 = BuyinRequest.shared.promotions_v2  {
                requestLiveGoods(data: promotions_v2)
            }
        }
       
        self.goodsView.isHidden = false
        self.goodsAllView.isHidden = true
        
        
        self.lineView.snp.remakeConstraints { make in
            make.bottom.equalToSuperview()
            make.centerX.equalTo(livingGoodsBtn.snp.centerX)
            make.height.equalTo(8)
            make.width.equalTo(36)
        }
        
       
        
    }
    
    @objc private func allGoodAction() {

        if !LCTools.checkMember() {
            return
        }
        
        self.livingGoodsBtn.setTitleColor(UIColor("#FFFFFF").alpha(value: 0.6), for: .normal)
        self.allGoodsBtn.setTitleColor(UIColor.white, for: .normal)
        
        if checkLogin() {
            cookieVC.findAndClick(title: "待上架商品")
            if let promotions_v2 = BuyinRequest.shared.promotions_v2_all {
                // 刷新一下
                requestAllLiveGoods(data: promotions_v2)
            }
        }
        
        
        self.goodsView.isHidden = true
        self.goodsAllView.isHidden = false
        self.lineView.snp.remakeConstraints { make in
            make.bottom.equalToSuperview()
            make.centerX.equalTo(allGoodsBtn.snp.centerX)
            make.height.equalTo(8)
            make.width.equalTo(36)
        }
    }
    
    @objc func goLoginAction() {
        // 如果未登录APP 就先登录APP
        guard let _ = UserInfo.currentUser() else {
            loginApp()
            return
        }
        
        if !UserInfo.isMember {
            FreeAlert.show { [weak self] in
                guard let self = self else { return  }
                self.goMember()
            } cancelAction: {
                
            }

            return
        }
        startTapped()
    }
    
    func goMember(){
        let ovc = LimitPurchaseVC2()
        self.navigationController?.pushViewController(ovc)
    }
    
    func loginApp() {

        let loginCtrl = LoginVC()
        loginCtrl.needAutoBackAfterDone = true
        loginCtrl.popToRoot = true
        
        loginCtrl.loginSuccessBlock = {[weak self] in
            
        }
        
        self.navigationController?.pushViewController(loginCtrl)
        
    }
    
    // app 和抖音都要登录
    func checkLogin() -> Bool{
        if UserInfo.isMember, isLogin {
            return true
        }
        return false
    }
    
     func clearCacheTapped() {
        if #available(iOS 9.0, *) {
            let websiteDataTypes = WKWebsiteDataStore.allWebsiteDataTypes()
            let dateFrom = Date(timeIntervalSince1970: 0)
            
            WKWebsiteDataStore.default().removeData(
                ofTypes: websiteDataTypes,
                modifiedSince: dateFrom
            ) {
                HUD.showSuccess("已退出")
            }
            
            // 清除所有 cookies
            let cookieStore = WKWebsiteDataStore.default().httpCookieStore
            cookieStore.getAllCookies { cookies in
                cookies.forEach { cookie in
                    cookieStore.delete(cookie)
                }
            }
            
            // 清除 URLCache
            URLCache.shared.removeAllCachedResponses()
            
            // 清除 HTTPCookieStorage
            if let cookies = HTTPCookieStorage.shared.cookies {
                cookies.forEach { cookie in
                    HTTPCookieStorage.shared.deleteCookie(cookie)
                }
            }
            BuyinCookieInfo.shared.clearCookies()
            
        }
    }
    
    // 开始  加载web 页面
    @objc private func startTapped() {
        //
        if self.cookieVC.isCheckedLogin {
            unbindDouyin()
            return
        }
        cookieVC.qrbgView.isHidden = false
        cookieVC.webView.isHidden = true
        cookieVC.startWebView()
    }
    
    //
    func unbindDouyin() {
        AlertView.show(leftOption: .gray(title: "退出帐号", action: { [weak self] in
            guard let self = self else { return }
            // 清空
            self.clearCacheTapped()
            self.cookieVC.clear()
            self.isLogin = false
            self.updateStatus()
            
            self.goodsView.bind(to: nil)
            self.goodsAllView.bind(to: nil)
            
        }), rightOption: .main(title: "取消", action: nil), title: "确认退出当前帐号？", message: "退出后商品讲解、智能互动功能需重新授权")
    }
    
    @objc private func debugTapped() {
        cookieVC.isHidden = !cookieVC.isHidden
        cookieVC.webView.isHidden = false
//        let vc = WebVC(title: nil, url: targetURL, withProgressLine: true, needStartNewWeb: false)
//        self.navigationController?.pushViewController(vc)
    }
    
    
    func updateStatus() {
        // 导航条的UI显示只和 巨量登录有关系
        if isLogin {
            statusButton.setImage(UIImage(named: "ic_smart_connect"), for: .normal)
            let name: String = self.cookieVC.userName ?? "           "
            
            statusButton.setTitle(String(name.prefix(5)), for: .normal)
            statusButton.setTitleColor(UIColor.white, for: .normal)
            
            statusButton.snp.updateConstraints { make in
                make.width.equalTo(100)
            }
           
        } else {
            statusButton.setImage(UIImage(named: "ic_smart_disconnect"), for: .normal)
            statusButton.setTitle("未登录", for: .normal)
            statusButton.setTitleColor(UIColor("#FFFFFF").alpha(value: 0.6), for: .normal)
            statusButton.snp.updateConstraints { make in
                make.width.equalTo(80)
            }
        }
        updateViewData()
    }
    
    func updateViewData() {
        
        if !isLogin {
            self.emptyView.contentLabel.text = "还未登录账号，无法获取内容"
            self.emptyView.loginButton.isHidden = false
            self.emptyView.isHidden = false
            self.emptyView.selectedView.isHidden = false
            self.goodsView.hideToolButton()
            self.goodsAllView.hideToolButton()
            return
        }
        
        // 如果APP没登录，抖音登录了
        if UserInfo.currentUser() == nil, isLogin {
            self.emptyView.isHidden = false
            self.emptyView.selectedView.isHidden = true
            self.emptyView.contentLabel.text = "您的账号已登出，请重新登录"
            self.emptyView.loginButton.isHidden = false
            return
        }
        
        if !goodsView.isHidden, BuyinRequest.shared.livingGoodCount() == 0 {
            self.emptyView.contentLabel.text = "暂无内容"
            self.emptyView.loginButton.isHidden = true
            self.emptyView.selectedView.isHidden = false
            self.emptyView.isHidden = false
            
            return
        }
        
        if !goodsAllView.isHidden, BuyinRequest.shared.allGoodCount() == 0 {
            self.emptyView.contentLabel.text = "暂无内容"
            self.emptyView.loginButton.isHidden = true
            self.emptyView.selectedView.isHidden = false
            self.emptyView.isHidden = false
            
            return
        }
        self.goodsAllView.showToolButton()
        self.goodsView.showToolButton()
        self.emptyView.isHidden = true
        
    }
    
}

// MARK: - CookieRequestWebViewDelegate
extension BuyinVC: CookieRequestWebViewDelegate {
    
    // 这里回调的是JS处理结果
    func userContentController(key: String) {
        switch key {
        case js_type_get_login:
            break
        case js_type_zhongkong:
            //点击的是中控台
            HUD.hideAllHUD()
            //
            if self.goodsView.isHidden == false {
                livingGoodAction()
            } else if self.goodsAllView.isHidden == false {
                allGoodAction()
            }
        default:
            break
        }
        self.updateStatus()
    }
    
    func webViewMockUrls(request: DYRequestData) {
//        LCLog.d("promotions_v2:\(request)")
        if !checkLogin() {
            return
        }
        switch request.workType {
            
        case .promotion_v2:
            BuyinRequest.shared.promotions_v2 = request.data
            requestLiveGoods(data: request.data)
            LCLog.d("promotions_v2:\(request)")
        case .bind:
            requestBind(data: request.data)
        case .combination:
            break
        case .combination_on:
            break
        case .promotion_v2_all:
            BuyinRequest.shared.promotions_v2_all = request.data
            requestAllLiveGoods(data: request.data)
        case .promotion_v2_refresh:
            reloadLiveGoods()
        }
        
    }
    
    func webViewDidSuccess(cookies: [HTTPCookie], extraInfo: [String: Any]) {
        self.isLogin = true
        cookieVC.isHidden = true
        HUD.showWait()
        DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
            self.cookieVC.go_zhongkongtai()
            HUD.hideAllHUD()
        }
        self.isLogin = true
        self.updateStatus()
        
    }
    
    func webViewDidFail(error: Error) {
        print("获取信息失败：", error)
        self.isLogin = false
        self.updateStatus()
    }
}


// MARK: - 巨量接口请求
extension BuyinVC {
    // 刷新上架商品
    func reloadAllLiveGoods() {
        if !checkLogin() {
            return
        }
        
        if let promotions_v2 = BuyinRequest.shared.promotions_v2_all {
            // 刷新一下
            requestAllLiveGoods(data: promotions_v2)
        }
    }
    
    // 刷新直播商品
    func reloadLiveGoods() {
        if !checkLogin() {
            return
        }
        
        if let promotions_v2 = BuyinRequest.shared.promotions_v2 {
            // 刷新一下
            requestLiveGoods(data: promotions_v2)
        }
        
    }
    
    func requestAllLiveGoods(data:RequestData) {

        BuyinRequest.requestAllLiveGoods(requestData: data) { [weak self] responseData in
            guard let self = self else { return }
            BuyinRequest.shared.promotionsAll = responseData
            DispatchQueue.main.async {
                if let promotions = responseData, let data = promotions.data {
                    self.goodsAllView.bind(to: data)
                } else {
                    self.goodsAllView.bind(to: nil)
                }
                self.updateViewData()
            }
        }
        
    }
    
    func requestLiveGoods(data:RequestData) {

        BuyinRequest.requestLiveGoods(requestData: data) { [weak self] responseData in
            guard let self = self else { return }
            BuyinRequest.shared.promotions = responseData
            DispatchQueue.main.async {
                if let promotions = responseData, let data = promotions.data {
                    self.goodsView.bind(to: data)
                } else {
                    self.goodsView.bind(to: nil)
                }
                self.updateViewData()
            }
        }
        
    }
    
    
    func requestBind(data:RequestData) {
        
        BuyinRequest.requestBind(requestData: data) { [weak self] responseData in
            guard let self = self, let responseData = responseData else { return }
            
            DispatchQueue.main.async {
                if responseData.code == 0{
                    if let ta = responseData.data, let f = ta.failure_list?.first {
                        // 点击我知道了
                        self.failureAction()
                    } else {

                    }
                }
            }
        }
        
    }
    
    func failureAction() {
        cookieVC.findAndClick(title: "我知道了")
    }
    
}



// MARK: - 直播间商品的操作
extension BuyinVC: DouyinGoodsViewDelegate {
    func goodsOperation(operation: DoyinGoodsOperation, data: [Promotions_data_promotions]) {
        guard let d = BuyinRequest.shared.promotions?.data else {return }
        if !LCTools.checkMember() {
            return
        }
        var num: Int = 1
        if let p = d.promotions {
            // 一页可能显示6个
            num = max(Int(p.count / 3) + 1, num)
        }
                
        switch operation {
        case .xiajia:
            for (idx, prom) in data.enumerated() {
                xiajia(num:num, prom: prom)
            }
        case .jiang:
            jiangjie(num:num, data: data)
        case .tui:
            break
        case .shangjia:
            break
        }
    }
    
        
    func xiajia(num:Int, prom: Promotions_data_promotions) {
        if BuyinRequest.shared.isCurrent(promotion: prom) {
            
            AlertView.show(leftOption: .gray(title: "取消", action: nil), rightOption: .main(title: "确定", action: { [weak self] in
                guard let self = self else { return }
                HUD.showWait()
                self.cookieVC.clickPromotion(num:num, id: prom.promotion_id, title: "下架",key: living_key)
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                    self.cookieVC.findAndClick(title: "确认")
                }
                DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                    HUD.hideAllHUD()
                    HUD.showSuccess("已下架所选商品")
                }
            }), title: "该商品正在讲解，确定下架吗？", message: "下架后讲解商品将停止")
            
            
        } else {
            HUD.showWait()
            cookieVC.clickPromotion(num:num, id: prom.promotion_id, title: "下架",key: living_key)
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                self.cookieVC.findAndClick(title: "确认")
            }
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                HUD.hideAllHUD()
                HUD.showSuccess("已下架所选商品")
            }
        }
       
    }
    
    
    
    
    func jiangjie(num:Int, data: [Promotions_data_promotions]) {
        HUD.showWait()
        self.cookieVC.findAndClick(title: "直播商品")
        stop_jj_Timer()
        for (idx, prom) in data.enumerated() {
            
            if BuyinRequest.shared.isCurrent(promotion: prom) {
                cookieVC.clickPromotion(num:num, id: prom.promotion_id, title: "取消讲解",key: living_key)
            } else {
                cookieVC.clickPromotion(num:num, id: prom.promotion_id, title: "讲解",key: living_key)
            }
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 2){[weak self] in
            self?.start_jj_Timer()
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 2){[weak self] in
            guard let self = self else { return}
            HUD.hideAllHUD()
            self.cookieVC.findAndClick(title: "直播商品")
            if let promotions_v2 = BuyinRequest.shared.promotions_v2  {
                requestLiveGoods(data: promotions_v2)
            }
        }

    }
    
}


// MARK: - 直播间商品的操作
extension BuyinVC: DouyinAllGoodsViewDelegate {
    func goodsOperation(operation: DoyinGoodsOperation, data: [Promotions_all]) {
        guard let d = BuyinRequest.shared.promotionsAll?.data else {return }
        if !LCTools.checkMember() {
            return
        }
        var num: Int = 1
        let p = d.products
        // 一页可能显示6个
        num = max(Int(d.products.count / 3) + 1, num)
        
                
        switch operation {
        case .xiajia:
            break
        case .jiang:
            break
        case .tui:
            break
        case .shangjia:
            self.shangjia(num:num, data: data)
            
        }
//        debugTapped()
    }
    
    
    
    func shangjia(num:Int, data: [Promotions_all]) {
        HUD.showWait()
        for (idx, prom) in data.enumerated() {
            cookieVC.clickPromotion(num:num, id: prom.promotion_id, title: "上架",key: all_key)
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            HUD.hideAllHUD()
            HUD.showSuccess("已上架所选商品")
            self.reloadAllLiveGoods()
            
        }
    }
    
}

// MARK: - 讲解的计时器
extension BuyinVC {
    
    func start_jj_Timer() {
        stop_jj_Timer()
        jjTimer = Timer.scheduledTimer(timeInterval: TimeInterval(11), target: self, selector: #selector(timeAction), userInfo: nil, repeats: true)
    }
    
    
    func stop_jj_Timer() {
        jjTimer?.invalidate()
        jjTimer = nil
    }
    
    @objc func timeAction() {

        guard let data = BuyinRequest.shared.promotions?.data, let promotions =  data.promotions else { return }
        
        if !LCTools.checkMember() {
            return
        }
        
        // 如果有讲解的 那么要点击
        if let p = promotions.first(where: {BuyinRequest.shared.isCurrent(promotion: $0)}) {
            
            if checkLogin() {
                cookieVC.findAndClick(title: "直播商品")
            }
            
            let num: Int = Int(promotions.count / 3) + 1
            LCLog.d("计时器触发讲解弹窗")
           
            self.cookieVC.clickPromotion(num:num, id: p.promotion_id, title: "取消讲解",key: living_key)
            DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
                self.cookieVC.clickPromotion(num:num, id: p.promotion_id, title: "讲解",key: living_key)
            }
            
        }
        
    }
    
}
