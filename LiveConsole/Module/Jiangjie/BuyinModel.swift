//
//  BuyinModel.swift
//  LiveConsole
//
//  Created by simon on 23.4.25.
//

import Foundation
import UIKit



public class RequestRoomId: NSObject {
    // 评论信息数据模型
    struct CommentResponse: Codable {
        let code: Int
        let msg: String
        let extra: ExtraInfo
        let data: CommentData
        let st: Int
    }
    
    struct ExtraInfo: Codable {
        let now: Int64
        let logId: String
        
        enum CodingKeys: String, CodingKey {
            case now
            case logId = "log_id"
        }
    }
    
    struct InternalExt: Codable {
        let internalSrc: String
        let wssPushRoomId: String
        let wssPushDid: String
        let firstReqMs: Int64
        let fetchTime: Int64
        let seq: Int
        let wssInfo: String
        
        init(from decoder: Decoder) throws {
            let container = try decoder.singleValueContainer()
            let extString = try container.decode(String.self)
            
            // 解析字符串
            let components = extString.components(separatedBy: "|")
            var dict: [String: String] = [:]
            
            for component in components {
                let keyValue = component.components(separatedBy: ":")
                if keyValue.count == 2 {
                    dict[keyValue[0]] = keyValue[1]
                }
            }
            
            // 赋值
            self.internalSrc = dict["internal_src"] ?? ""
            self.wssPushRoomId = dict["wss_push_room_id"] ?? ""
            self.wssPushDid = dict["wss_push_did"] ?? ""
            self.firstReqMs = Int64(dict["first_req_ms"] ?? "0") ?? 0
            self.fetchTime = Int64(dict["fetch_time"] ?? "0") ?? 0
            self.seq = Int(dict["seq"] ?? "0") ?? 0
            self.wssInfo = dict["wss_info"] ?? ""
        }
        
        func encode(to encoder: Encoder) throws {
            var container = encoder.singleValueContainer()
            let extString = "internal_src:\(internalSrc)|wss_push_room_id:\(wssPushRoomId)|wss_push_did:\(wssPushDid)|first_req_ms:\(firstReqMs)|fetch_time:\(fetchTime)|seq:\(seq)|wss_info:\(wssInfo)"
            try container.encode(extString)
        }
    }
    
    struct CommentData: Codable {
        let commentInfos: [CommentInfo]
        let cursor: String
        let internalExt: InternalExt
        let anchorMessages: [String]
        let isLiving: Bool
        let nextFetchTime: Int64
        let similarCommentsInfo: SimilarCommentsInfo
        let nextFetchInterval: Int
        let summaryInfo: SummaryInfo
        
        enum CodingKeys: String, CodingKey {
            case commentInfos = "comment_infos"
            case cursor
            case internalExt = "internal_ext"
            case anchorMessages = "anchor_messages"
            case isLiving = "is_living"
            case nextFetchTime = "next_fetch_time"
            case similarCommentsInfo = "similar_comments_info"
            case nextFetchInterval = "next_fetch_interval"
            case summaryInfo = "summary_info"
        }
    }
    
    struct SimilarCommentsInfo: Codable {
        let items: [String]
        let needRefresh: Bool
        
        enum CodingKeys: String, CodingKey {
            case items
            case needRefresh = "need_refresh"
        }
    }
    
    struct SummaryInfo: Codable {
        let items: [String]?
    }
    
    struct CommentInfo: Codable {
        let nickName: String
        let uid: String
        let content: String
        let commentId: String
        let type: Int
        let tags: [Int]?
        
        enum CodingKeys: String, CodingKey {
            case nickName = "nick_name"
            case uid
            case content
            case commentId = "comment_id"
            case type
            case tags
        }
    }
    
    // 获取房间评论的方法
    static func fetchRoomComments(cursor: String = "") async throws -> CommentResponse {
        let urlString = commentRoomInfo
        var components = URLComponents(string: urlString)
        
        // 设置查询参数
        components?.queryItems = [
            URLQueryItem(name: "comment_query_type", value: "1"),
            URLQueryItem(name: "cursor", value: cursor),
            URLQueryItem(name: "internal_ext", value: ""),
            URLQueryItem(name: "similar_comment_enable", value: "true"),
            URLQueryItem(name: "request_source", value: "0"),
            URLQueryItem(name: "extra", value: "in_comment_opt_ab")
        ]
        
        guard let url = components?.url else {
            throw NSError(domain: "", code: -1, userInfo: [NSLocalizedDescriptionKey: "Invalid URL"])
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "GET"
        
        // 设置请求头
        request.setValue("application/json, text/plain, */*", forHTTPHeaderField: "Accept")
        request.setValue("https://buyin.jinritemai.com/mpa/account/login", forHTTPHeaderField: "Referer")
        request.setValue("Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36", forHTTPHeaderField: "User-Agent")
        
        // 发起请求
        let (data, _) = try await URLSession.shared.data(for: request)
        if let jsonResponse = try JSONSerialization.jsonObject(with: data, options: []) as? [String: Any] {
            
        }
        let response = try JSONDecoder().decode(CommentResponse.self, from: data)
        return response
    }
    
   
}


// MARK: - 商品信息数据模型
public struct Basic_listResponse: Codable {
    let code: Int
    let msg: String
    let extra: ExtraInfo
    let data: Basic_list_data
    let st: Int
    
}

public struct Basic_list_data: Codable {
    let basic_list: [Basic_list]
}


public struct Basic_list: Codable {
    let promotion_id: String
    let product_id: String
    let title: String
}

public struct ResponsePromotionsfeature : Codable {
    let code: Int
    let msg: String
    let extra: ExtraInfo
    let st: Int
}


// MARK: - 选品车商品
public struct XuanpincheModel : Codable {
    let code: Int
    let msg: String
    let log_id: String
    let extra: XuanpincheModel_extra?
    let data: [XuanpincheModel_data]?
    let st: Int
    let total: Int?
}

public struct XuanpincheModel_extra :Codable {
    let sku_same_price_entrance:  String
}

public struct XuanpincheModel_data :Codable {
    
    let filter_status: Int
    let promotion_id: String
    let product_id: String
    let shop_id: Int64
    let platform: Int
    let price: Int64
    let cos_fee: Int64
    let cos_ratio: Int64
    let title: String
    let new_cover: String
    let activity: XuanpincheModel_data_activity?
    let is_haitao: Bool?
    let pool_type : String
    let is_leader: Bool?
    let item_type : Int
    let pending_cos_ratio : Int?
    let is_marvel : Bool?
    let is_pk_competition : Bool?
    let pic_product_tag: [XuanpincheModel_data_pic_product_tag]?
    let text_product_tag: [XuanpincheModel_data_text_product_tag]?
    let price_text: String
    let stock_num_sum: Int64?
    let selection_reason: String?
}

public struct XuanpincheModel_data_activity: Codable {
    let activity_icon: String?
    let activity_type: Int?
    let width: Int?
    let height: Int?
}

public struct XuanpincheModel_data_pic_product_tag : Codable {
    let type: String?
    let pic: String?
    let height: Int?
    let width: Int?
}


public struct XuanpincheModel_data_text_product_tag : Codable {
    let type: String
    let border_color: String
    let text: XuanpincheModel_data_text_product_tag_text
}

public struct XuanpincheModel_data_text_product_tag_text : Codable {
    let text: String
}



  
// MARK: - 选品车上架
public struct PromotionBindModel : Codable {
    let code: Int
    let msg: String
    let log_id: String
    let data: PromotionBindModel_data?
    let st: Int
    let is_toast: Bool?
}

public struct PromotionBindModel_data : Codable {
    let success_count: Int
    let failure_count: Int
    let failure_list: [PromotionBindModel_data_failure_list]?
    let partial_failure_count: Int
}
    
public struct PromotionBindModel_data_failure_list : Codable {
    let bind_status: Int
    let bind_reason: String
    let product_id: String
    let title: String
    let rule_title: String
    let rule_url: String
    let product_pic: String
}
    


// MARK: - 搭配商品
public struct CombinationModel : Codable {
    let code: Int
    let msg: String
    let extra: ExtraInfo?
    let data: CombinationModel_data?
    let st: Int
    let is_toast: Bool?
}

public struct CombinationModel_data : Codable {
    let total: Int
    let has_more: Bool
    let combination_list: [CombinationModel_list]?
}
    
public struct CombinationModel_list : Codable {
    let combination_id: String
    let combination_name: String
    let combination_price_min: Int64
    let combination_price_max: Int64
    let origin_price_min: Int64
    let origin_price_max: Int64
    let status: Int
    let products: [CombinationModel_list_products]?
    
    // 是否包含未上架的商品
    var isIncludeUnlisted: Bool {
        if let products = self.products {
           return products.contains(where: {$0.status == 3})
        }
        return false
    }

}

public struct CombinationModel_list_products : Codable {
    let product_id : String
    let name: String
    let pic: String
    let price: Int64
    let status: Int
}

// MARK: - 搭配商品删除
public struct CombinationDeletModel : Codable {
    let code: Int
    let msg: String
    let extra: ExtraInfo?
    let data: CombinationDeletModel_data?
    let st: Int
    let is_toast: Bool?
}

public struct CombinationDeletModel_data : Codable {
    let total: Int?
    let has_more: Bool?
}



// MARK: - 直播商品
public struct Promotions : Codable {
    let code: Int
    let msg: String
    let extra: ExtraInfo
    let data: Promotions_data?
    let st: Int
}

public struct Promotions_data : Codable {
    let room_id: String?
    let promotions: [Promotions_data_promotions]?
    let room_info: Promotions_data_room?
    let fetch_interval_config: Promotions_data_fetch?
    
    var isLiving: Bool {
        if  let p = self.promotions {
            if let room_id = self.room_id, room_id.count > 2 {
                return true
            }
        }
        return false
    }
    
    func isCurrent(promotion:Promotions_data_promotions) -> Bool {
        guard  let room_info = self.room_info else { return false}
        return  room_info.current_promotion_id == promotion.promotion_id
    }
}


public struct Promotions_data_promotions : Codable {
    let campaign_management : Bool?
    let status : Int64
    let title : String?
    let channel_product_type : Int64?
    let has_size_chart : Bool?
    let promotion_id : String
//    let tag_list : 1 element
    let elastic_title : String?
    let exclusive_product_activity_type : Int64?
    let art_no : String?
    let platform_subsidy_price : Int64?
    let in_prompt : Bool?
    // 主推数据
    let feature_info : Promotions_data_promotions_feature_info?
    let kol_exclusive_info : Promotions_data_promotions_kol_exclusive_info
    let cover: String
    let stock_management : Bool
    let in_exclusive_channel_activity : Bool
    let product_id : String
    let is_kol_exclusive_channel_product : Bool
    let price_desc : Promotions_data_promotions_price_desc
    let has_prompt : Bool
    let stock_num : Int64
    let warm_up : Bool
    let platform_label : String?
    let item_type : Int64?
    let channel_stock_use_mode : Int64
    let lottery_info : Promotions_data_promotions_lottery_info
    let can_seckill : Bool
    let has_recom_prompt : Bool?
    let online_room_operating_data : Promotions_data_online_room_operating_data
    let combination : Promotions_data_room_combination?
    let shop_id : String?
}

public struct Promotions_data_room : Codable {
    let current_promotion_id : String?
    let on_shelf_combination_num : Int64
    let cart_category_list : [Promotions_data_room_cart_category_list]
}

public struct Promotions_data_room_cart_category_list : Codable {
    let id : String
    let name : String
    let type : Int64
}


public struct Promotions_data_room_combination : Codable {
    let combination_id : String
    let product : [Promotions_data_room_combination_product]
}

public struct Promotions_data_room_combination_product : Codable {
    let pic: String
    let status: Int64
    let product_id: String
}

public struct Promotions_data_promotions_feature_info: Codable {
    // 是否是主推商品
    let is_feature : Bool
    // 是否可以设置主推
    let can_set_feature : Bool
}

public struct Promotions_data_promotions_kol_exclusive_info : Codable{
    let exclusive_product: Bool
}

public struct Promotions_data_promotions_price_desc : Codable{
    let exclusive_product: Bool?
    let price_text : String
    let max_price : Promotions_data_promotions_price_desc_price
    let min_price : Promotions_data_promotions_price_desc_price
    
}

public struct Promotions_data_promotions_price_desc_price : Codable{
    let integer : String
    let decimal : String
    let origin : Int64
    let suffix : String
    
}


public struct Promotions_data_promotions_lottery_info: Codable {
    let is_lottery: Bool
}

public struct Promotions_data_online_room_operating_data: Codable {
    let  create_ucnt: Int64
    let  pay_ucnt: Int64
    let  pay_amt: Int64
    let  pay_combo_cnt: Int64
    let  show_ucnt: Int64
    let  click_ucnt: Int64
}

public struct Promotions_data_fetch : Codable {
    let promotion_info_fetch_interval : Promotions_data_fetch_interval
}

public struct Promotions_data_fetch_interval : Codable {
    let   id_10: [Int64]
    let   id_6: [Int64]
    let   id_4: [Int64]
    let   id_2: [Int64]
    let   id_1: [Int64]
    let   id_60: [Int64]
    
    enum CodingKeys: String, CodingKey {
        case id_10 = "10"
        case id_6 = "6"
        case id_4 = "4"
        case id_2 = "2"
        case id_1 = "-1"
        case id_60 = "60"
    }
}




// MARK: - 待上架商品
public struct PromotionsAll : Codable {
    let code: Int
    let msg: String
    let extra: ExtraInfo
    let data: Promotions_all_data?
    let st: Int
}


public struct Promotions_all_data: Codable {
    let products:[Promotions_all]
    
    let room_info: Promotions_data_room?
    func isCurrent(promotion:Promotions_all) -> Bool {
        guard  let room_info = self.room_info else { return false}
        return  room_info.current_promotion_id == promotion.promotion_id
    }
}

public struct Promotions_all: Codable {
    let  product_custom_type : String
    let  title : String
    let  live_offline_operating_data : Promotions_live_offline_operating_data
    let  tag_list : [Int64]
    let  promotion_id : String
    let  min_sku_price_trend : [Promotions_min_sku_price_trend]
    let  alliance_price : Promotions_alliance_price
    let  live_price : Promotions_min_sku_price_trend
    let  cos_ratio : Promotions_cos_ratio
    let  category : Promotions_category
    let  cover : String
    let  sell_point : String
    let  prompt : String
    let  price_level : Int64
    let  cos_ratio_trend : [Promotions_min_sku_price_trend]
    let  stock_info : Promotions_stock_info
    let  product_id : String
    let  price_text : String
    let  talent_price_level : Promotions_talent_price_level
    let  stock_num : String
    let  item_type : String
    let  on_shelf_info : Promotions_on_shelf_info
    let  price : Promotions_price
    let  shop_score_trend : [Promotions_min_sku_price_trend]
    let  shop_score_info : Promotions_shop_score_info
    let  three_price_level : Promotions_three_price_level
    let  shop_id : Int64
    
    
}

public struct Promotions_shop_score_info: Codable {
    let   shop_score: Promotions_shop_score_info_score
    let   has_data: Bool
}

public struct Promotions_shop_score_info_score: Codable {
    let  score : Int64
}

public struct Promotions_three_price_level: Codable {
    let price_level : Int64
    let change_price_available : Bool
    let price_level_desc : String
}

public struct Promotions_price:Codable {
    let integer : String
    let decimal : String
    let origin : Int64
    let suffix : String
}


public struct Promotions_live_offline_operating_data: Codable {
    
    let pay_ucnt : Int64
    let pay_order_cnt : Int64
    let pay_amt : Int64
    let exposure_sold_rate : Int64
}

public struct Promotions_min_sku_price_trend: Codable {
    let msg : String?
}


public struct Promotions_alliance_price:Codable {
    let  price_text : String
    let  price_num : Promotions_price_num
}

public struct Promotions_price_num:Codable {
    let integer : String
    let decimal : String
    let origin : Int64
    let suffix : String
}

public struct Promotions_cos_ratio:Codable {
    let integer : String
    let decimal : String
    let origin : Int64
    let suffix : String
}

public struct Promotions_category:Codable {
    let second_category : Promotions_category_second_category
    let first_category : Promotions_category_second_category
}

public struct Promotions_category_second_category:Codable {
    let category_name : String
    let category_id  : String
}


public struct Promotions_stock_info:Codable {
    let stock_num : Int64
    let can_show : Bool
}

public struct Promotions_talent_price_level:Codable {
    let price_level : Int64
    let same_low_product_brief : Promotions_same_low_product_brief
    let price_level_desc :String
}


public struct Promotions_same_low_product_brief:Codable {
    let cover : String
    let text : String
}

public struct Promotions_on_shelf_info:Codable {
    let can_show : Bool
}
