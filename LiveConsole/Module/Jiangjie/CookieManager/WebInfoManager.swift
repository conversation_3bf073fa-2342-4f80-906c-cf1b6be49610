//
//  CookieManager.swift
//  MockBuyin
//
//  Created by 郭炜 on 2024/11/18.
//

import Foundation

/// 登录授权的主链接
let targetURL = "https://buyin.jinritemai.com/mpa/account/login"
/// 发送评论的链接
let commentOperateURL = "https://buyin.jinritemai.com/api/anchor/comment/operate"
/// 获取评论信息-房间信息
let commentRoomInfo = "https://buyin.jinritemai.com/api/anchor/comment/info"

// 商品基础列表
let basic_list_URL = "https://buyin.jinritemai.com/api/anchor/livepc/basic_list"

// 直播商品
let promotions_v2_Host = "api/anchor/livepc/promotions_v2"

let promotions_v2_p1 = "list_type=1"

let promotions_v2_p2 = "source_type=force"

let promotions_v2_p3 = "source_type=poll"


let promotions_v2_all_URL = "https://buyin.jinritemai.com/api/anchor/livepc/get_pending_use_products"


// {"title":"","page_size":20,"page":1,"pool_type":"20","filter_type":1,"filter_bound":true,"request_from":0,"select_type":""}
// 选品车商品
let promotions_list_URL = "https://buyin.jinritemai.com/pc/selection_tool/promotion/list"

// 上架商品
let bind_promotions_URL = "https://buyin.jinritemai.com/pc/live/bind"

// 下架
let unbind_URL = "https://buyin.jinritemai.com/pc/live/unbind"

// 搭配管理
let combination_list_URL = "https://buyin.jinritemai.com/api/author/livepc/combination_list"

// 取消主推
let cancel_feature_product_URL = "https://buyin.jinritemai.com/api/anchor/livepc/cancel_feature_product"

// 设为主推
let set_feature_product_URL = "https://buyin.jinritemai.com/api/anchor/livepc/set_feature_product"

// 删除搭配
let combination_delet_URL =  "https://buyin.jinritemai.com/api/author/livepc/combination_delete"
// 搭配上架
let combination_ON_URL = "https://buyin.jinritemai.com/api/author/livepc/combination_on_shelf"

// MARK: - Web 信息模型
struct WebInfo: Codable {
    var cookies: [String: String]
    var userAgent: String?
    var verifyFp: String?
    var msToken: String?
    var xBogus: String?
    var csrf_session_id: String?
    var ttwid: String?
    
    // 生成请求头
    func getHeaders() -> [String: String] {
        var headers: [String: String] = [:]
        
        if let userAgent = userAgent {
            headers["User-Agent"] = userAgent
        }
        if let verifyFp = verifyFp {
            headers["verifyFp"] = verifyFp
        }
        if let msToken = msToken {
            headers["msToken"] = msToken
        }
        if let xBogus = xBogus {
            headers["X-Bogus"] = xBogus
        }
        
        return headers
    }
    
    // 生成完整的请求头
    func getFullHeaders(forURL url: String) -> [String: String] {
        var headers = getHeaders()
        
        // 添加标准请求头
        headers["Accept"] = "application/json, text/plain, */*"
        headers["Accept-Language"] = "en-US,en;q=0.5"
        headers["Accept-Encoding"] = "gzip, deflate, br"
        headers["Referer"] = url
        headers["Sec-Fetch-Dest"] = "empty"
        headers["Sec-Fetch-Mode"] = "cors"
        headers["Sec-Fetch-Site"] = "same-origin"
        headers["Connection"] = "keep-alive"
        
        // 添加 Cookie 头
        let cookieString = cookies.map { "\($0.key)=\($0.value)" }.joined(separator: "; ")
        headers["Cookie"] = cookieString
        
        return headers
    }
    
    /// 生成完整的 Cookie 字符串
    func getCookieString() -> String {
        var cookieParts: [String] = []
        
        // 必需的 Cookie 键值对
        let requiredCookies: [String] = [
            "gfkadpd",
            "passport_csrf_token",
            "passport_csrf_token_default",
            "s_v_web_id",
            "ttwid",
            "odin_tt",
            "sid_guard",
            "uid_tt",
            "uid_tt_ss",
            "sid_tt",
            "sessionid",
            "sessionid_ss",
            "is_staff_user",
            "sid_ucp_v1",
            "ssid_ucp_v1",
            "store-region",
            "store-region-src",
            "ucas_c0_buyin",
            "ucas_c0_ss_buyin",
            "x-web-secsdk-uid",
            "_tea_utm_cache_3813",
            "scmVer",
            "csrf_session_id",
            "SASID",
            "BUYIN_SASID",
            "buyin_shop_type",
            "buyin_account_child_type",
            "buyin_app_id",
            "buyin_shop_type_v2",
            "buyin_account_child_type_v2",
            "buyin_app_id_v2"
        ]
        
        // 添加所有存在的 cookie
        for key in requiredCookies {
            if let value = cookies[key] {
                cookieParts.append("\(key)=\(value)")
            }
        }
        
        // 添加其他未在必需列表中但存在的 cookie
        for (key, value) in cookies where !requiredCookies.contains(key) {
            cookieParts.append("\(key)=\(value)")
        }
        
        return cookieParts.joined(separator: "; ")
    }
    
    
}

// MARK: - 请求头信息
struct HeaderInfo: Codable {
    var userAgent: String?
    var referer: String?
    var xSecsdk: String?
    var xBogus: String?
    var authorization: String?
}

// MARK: - 存储信息
struct StorageInfo: Codable {
    var localStorage: [String: String]
    var sessionStorage: [String: String]
}

// MARK: - 额外信息
struct ExtraInfo: Codable {
    var msToken: String?
    var signature: String?
    var verifyFp: String?
}

// MARK: - Web 信息管理器
class WebInfoManager {
    static let shared = WebInfoManager()
    private let storageKey = "WebInfo"
    
    func save(_ cookies: [HTTPCookie], extraInfo: [String: Any] = [:]) {
        // 转换 cookies 为字典
        var cookieDict = [String: String]()
        for cookie in cookies {
            cookieDict[cookie.name] = cookie.value
        }
        
        let webInfo = WebInfo(
            cookies: cookieDict,
            userAgent: extraInfo["userAgent"] as? String,
            verifyFp: extraInfo["verifyFp"] as? String,
            msToken: extraInfo["msToken"] as? String,
            xBogus: extraInfo["xBogus"] as? String,
            csrf_session_id: extraInfo["csrf_session_id"] as? String,
            ttwid: extraInfo["ttwid"] as? String
        )
        
        if let encoded = try? JSONEncoder().encode(webInfo) {
            UserDefaults.standard.set(encoded, forKey: storageKey)
        }
    }
    
    func load() -> WebInfo? {
        guard let data = UserDefaults.standard.data(forKey: storageKey) else { return nil }
        return try? JSONDecoder().decode(WebInfo.self, from: data)
    }
    
    func clear() {
        UserDefaults.standard.removeObject(forKey: storageKey)
    }
    
    // 检查是否有有效的登录信息
    func isLoggedIn() -> Bool {
        guard let webInfo = load() else { return false }
        
        // 检查关键 cookie 是否存在
        let requiredCookies = ["BUYIN_SASID", "sessionid", "ttwid"]
        for cookieName in requiredCookies {
            if webInfo.cookies[cookieName] == nil {
                return false
            }
        }
        
        return true
    }
    
    // 构建网络请求
    func buildRequest(url: String, method: String = "GET") -> URLRequest? {
        guard let webInfo = load(),
              let url = URL(string: url) else { return nil }
        
        var request = URLRequest(url: url)
        request.httpMethod = method
        
        // 添加所有请求头
        let headers = webInfo.getFullHeaders(forURL: url.absoluteString)
        for (key, value) in headers {
            request.setValue(value, forHTTPHeaderField: key)
        }
        
        return request
    }
    
    /// 从完整的Cookie字符串创建并保存Web信息
    func saveFromFullCookieString(_ cookieString: String, extraInfo: [String: Any] = [:]) {
        // 解析cookie字符串
        var cookieDict = [String: String]()
        let cookiePairs = cookieString.components(separatedBy: "; ")
        
        for pair in cookiePairs {
            let components = pair.components(separatedBy: "=")
            if components.count == 2 {
                let key = components[0].trimmingCharacters(in: .whitespaces)
                let value = components[1].trimmingCharacters(in: .whitespaces)
                cookieDict[key] = value
            }
        }
        
        // 创建WebInfo对象
        let webInfo = WebInfo(
            cookies: cookieDict,
            userAgent: extraInfo["userAgent"] as? String ?? "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:131.0) Gecko/20100101 Firefox/131.0",
            verifyFp: cookieDict["s_v_web_id"],
            msToken: extraInfo["msToken"] as? String,
            xBogus: extraInfo["xBogus"] as? String,
            csrf_session_id: cookieDict["csrf_session_id"],
            ttwid: cookieDict["ttwid"]
        )
        
        // 保存到UserDefaults
        if let encoded = try? JSONEncoder().encode(webInfo) {
            UserDefaults.standard.set(encoded, forKey: storageKey)
        }
    }
}
