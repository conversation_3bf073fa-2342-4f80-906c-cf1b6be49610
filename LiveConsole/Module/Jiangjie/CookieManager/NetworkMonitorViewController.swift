//
//  NetworkMonitorViewController.swift
//  MockBuyin
//
//  Created by simon on 20.11.24.
//

import Foundation

import UIKit
import WebKit

class NetworkMonitorViewController: UIView {
    
    let User_Agent = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36"

    weak var delegate: CookieRequestWebViewDelegate?
    
    var webView: WKWebView!
            
    var isCheckedLogin = false
    // 用户名
    var userName: String?
        
    var loginTimer: Timer?
        
    // 二维码超时
    var qrtimerOut: Timer?
    
    // 循环获取二维码
    var qrtimerCheck: Timer?
    
    // 当前操作的URL
    var mLoginImgUrl: String?
    
    var qrUrl: String?
    
    // web 当前重定向之后加载的最新url
    var webLoadUrl: String?
    
    
    // 是否在中控台， 只有在中控的页面才可以抓到商品
    var selected_zhongkong = false
    
    lazy var closeButton: UIButton = {
        let button = UIButton()
        button.setImage(UIImage(named: "icon_close_black"), for: .normal)
        button.backgroundColor = .clear
        button.addTarget(self, action: #selector(closeAction), for: .touchUpInside)
        return button
    }()
    
    // 挡住web弹窗的右上角的关闭按钮
    lazy var closeButton1: UIButton  = {
        let button = UIButton()
        button.setImage(UIImage(named: "icon_close_black"), for: .normal)
        button.backgroundColor = .white
        button.addTarget(self, action: #selector(closeAction), for: .touchUpInside)
        button.cornerRadius = 6
        return button
    }()
        
    lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.text = "请使用抖音APP\n扫码登录"
        label.textColor = UIColor("#1E1F20")
        label.font = UIFont.systemFont(ofSize: 20, weight: .medium)
        label.numberOfLines = 0
        label.textAlignment = .center
        return label
    }()
    
    lazy var desLabel: UILabel = {
        let label = UILabel()
        
        let paraph = NSMutableParagraphStyle()
        paraph.lineSpacing = 6
        let attributes = [NSAttributedString.Key.paragraphStyle: paraph, NSAttributedString.Key.foregroundColor: UIColor("#868686"), NSAttributedString.Key.font: UIFont.systemFont(ofSize: 14, weight: .regular)]
        
        let attributedText = NSMutableAttributedString(string: "打开抖音App\n点击首页右上角 ", attributes: attributes)
        
        // 图片
        let imageAttachment = NSTextAttachment()
        imageAttachment.image = UIImage(named: "搜索")
        imageAttachment.bounds = CGRect(x: 0, y: -3, width: 12, height: 12)

        let imageString = NSAttributedString(attachment: imageAttachment)
        attributedText.append(imageString)
        
        let attributedText1 = NSMutableAttributedString(string: "，点击扫一扫 ", attributes: attributes)
        attributedText.append(attributedText1)
        
        // 图片
        let imageAttachment1 = NSTextAttachment()
        imageAttachment1.image = UIImage(named: "扫一扫 1")
        imageAttachment1.bounds = CGRect(x: 0, y: -3, width: 12, height: 12)

        let imageString1 = NSAttributedString(attachment: imageAttachment1)
        attributedText.append(imageString1)
        
       
        let attributedText2 = NSMutableAttributedString(string: "\n请确保授权的抖音号开通了巨量百应后台", attributes: attributes)
        attributedText.append(attributedText2)
        
        label.attributedText = attributedText
        label.textAlignment = .center
        label.numberOfLines = 0
        return label
    }()
    
    lazy var qrbgView: UIView  = {
        let v = UIView()
        v.backgroundColor = .white
        v.layer.cornerRadius = 12
        v.layer.masksToBounds = true
        return v
    }()
    
    lazy var retryView: UIView  = {
        let v = UIView()
        v.backgroundColor = .black.alpha(value: 0.7)
        v.isHidden = true
        return v
    }()
    
    lazy var retryButton: UIButton = {
        let button = UIButton()
        button.setImage(UIImage(named: "重试"), for: .normal)
        button.backgroundColor = .clear
        button.addTarget(self, action: #selector(retryAction), for: .touchUpInside)
        return button
    }()
    
    lazy var retryLabel: UILabel = {
        let label = UILabel()
        label.text = "刷新二维码"
        label.textColor = .white
        label.font = UIFont.systemFont(ofSize: 16, weight: .regular)
        return label
    }()
    
    lazy var qrImageView: UIImageView  = {
        let imageView = UIImageView()
        return imageView
    }()
    
    
    lazy var bgView: UIView  = {
        let v = UIView()
        v.backgroundColor = .clear
        v.layer.cornerRadius = 12
        v.layer.masksToBounds = true
        return v
    }()
    
    lazy var touchView: UIView  = {
        let v = UIView()
        v.backgroundColor = .clear
//        let tap = UITapGestureRecognizer(target: self, action: #selector(tapAction))
//        v.addGestureRecognizer(tap)
        return v
    }()
    
 
        
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    deinit {
        releaseQrtimerOut()
        releaseLoginTimer()
        releaseQrtimerCheck()
    }
  
    
    // MARK: - Initialization
    override init(frame: CGRect) {
        super.init(frame: frame)
        self.backgroundColor = UIColor.black.alpha(value: 0.5)
        setupWebView()
        setupNetworkMonitor()

        loadCookiesIntoWebView()
    }
    
   
    
    private func loadWebView(url: String) {
        releaseQrtimerOut()
        self.mLoginImgUrl = url
        // 加载登录页
        if let url = URL(string: url) {
            let request = NSMutableURLRequest.init(url: url)
            
            request.setValue(User_Agent, forHTTPHeaderField: "User-Agent")
            webView.load( request as URLRequest)
        } else {
            delegate?.webViewDidFail(error: CookieError.invalidURL)
        }
        
//        webView.isHidden = true
        self.retryView.isHidden = true
        self.qrImageView.image = nil
        self.isHidden = false
    }
    
    private func reloadWebView() {
        // 加载登录页
        self.webView.reload()
    }
    
    
    private func setupWebView() {
        self.addSubviews([touchView ,bgView, qrbgView])
        
        touchView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        bgView.snp.makeConstraints { make in
            make.leading.trailing.bottom.top.equalToSuperview()
        }
        
        let config = WKWebViewConfiguration()
        webView = WKWebView(frame: CGRect(x: -800, y: -100, width: 1000, height: LCDevice.screenH), configuration: config)
        webView.backgroundColor = .white
        webView.navigationDelegate = self
        webView.autoresizingMask = [.flexibleWidth, .flexibleHeight]
        bgView.addSubview(webView)

        webView.addSubview(closeButton1)
        
        closeButton1.snp.makeConstraints { make in
            make.trailing.equalToSuperview().inset(8)
            make.top.equalToSuperview().inset(10)
            make.width.height.equalTo(50)
        }
        
        qrbgView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview()
            make.leading.trailing.equalToSuperview().inset(30)
            make.height.equalTo(400)
        }
        
        qrbgView.addSubviews([titleLabel, desLabel, qrImageView, retryView, closeButton])
        
        qrImageView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview()
            make.height.width.equalTo(200)
        }
        
        closeButton.snp.makeConstraints { make in
            make.top.trailing.equalToSuperview().inset(6)
            make.width.height.equalTo(32)
        }
        
        
        titleLabel.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalToSuperview().inset(24)
            make.width.equalTo(200)
        }
        
        desLabel.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.bottom.equalToSuperview().inset(12)
            make.width.equalTo(300)
            make.height.equalTo(100)
        }
        
        retryView.addSubviews([retryButton, retryLabel])
        
        retryView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview()
            make.height.width.equalTo(200)
        }
        
        retryButton.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview()
            make.width.height.equalTo(44)
        }
        
        retryLabel.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.bottom.equalToSuperview().inset(28)
        }
    }
    
    @objc func tapAction() {
        self.isHidden = true
    }
    
    @objc func closeAction() {
        self.isHidden = true
    }
    
    //TODO: - Cookies 缓存和读取
    private func loadCookiesIntoWebView() {
        let cookies = BuyinCookieInfo.shared.getAllCookies() // 从持久化存储中加载 cookies
        let cookieStore = webView.configuration.websiteDataStore.httpCookieStore
        for cookie in cookies {
            cookieStore.setCookie(cookie)
        }
    }
    
   
    func getUserInfo() {
        DispatchQueue.main.asyncAfter(deadline: .now() + 1) { [weak self] in
            guard let self = self else  { return }
            // 如果还是获取不到用户信息 就不行
            self.startLoginTimer()
        }
        
    }
    
}

// MARK:  - public
extension NetworkMonitorViewController {
    func startWebView() {
       // 加载登录页
       self.loadWebView(url: targetURL)
   }
    
    func clear() {
        self.userName = nil
        self.isCheckedLogin = false
        BuyinCookieInfo.shared.clearCookies()
        WebInfoManager.shared.clear()
        
        releaseQrtimerOut()
        releaseLoginTimer()
        releaseQrtimerCheck()
        
    }
}


// MARK: - WKNavigationDelegate
extension NetworkMonitorViewController: WKNavigationDelegate {
    
    func webView(_ webView: WKWebView, didFinish navigation: WKNavigation!) {
        
        guard let url = webView.url?.absoluteString else {return}
        
        webView.configuration.websiteDataStore.httpCookieStore.getAllCookies { [weak self] cookies in
            
            guard let self = self else { return }
            
            // 检查是否存在 BUYIN_SASID cookie
            guard cookies.contains(where: { $0.name == "BUYIN_SASID" }) else {
                print("未找到 BUYIN_SASID cookie，不执行回调")
                return
            }
            // 保存并返回结果，不检查msToken和xBogus
            if let url = webView.url?.absoluteString, url.hasPrefix("https://buyin.jinritemai.com/dashboard") {
                // 保存 cookie 到 CookieInfo
                BuyinCookieInfo.shared.saveCookies(cookies)
                
                WebInfoManager.shared.save(cookies, extraInfo: [:])
                self.delegate?.webViewDidSuccess(cookies: cookies, extraInfo: [:])
                self.clickIkowon()
                self.releaseQrtimerOut()
                self.releaseQrtimerCheck()
                self.getUserInfo()
            }
        }
   
        injectJavaScript()
        
    }
    
    func webView(_ webView: WKWebView, didFail navigation: WKNavigation!, withError error: Error) {
        delegate?.webViewDidFail(error: error)
    }
    
    func webView(_ webView: WKWebView, didFailProvisionalNavigation navigation: WKNavigation!, withError error: Error) {
        delegate?.webViewDidFail(error: error)
    }
    
    func webView(_ webView: WKWebView, decidePolicyFor navigationAction: WKNavigationAction, decisionHandler: @escaping (WKNavigationActionPolicy) -> Void) {
        if let url =  navigationAction.request.url?.absoluteString {
            LCLog.d("web加载的url:\(url)")
            self.webLoadUrl  = url
            
            if url.contains("/qrconnect?container=") {
                self.qrUrl = url
                
                LCLog.d("用户未登录，开始授权")
             
                DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {[weak self ] in
                    guard let self = self else { return  }
                    // 在1.5秒之后， 如果还没重定向到https://buyin.jinritemai.com/dashboard， 说明是需要重新授权的
                    if let webLoadUrl = self.webLoadUrl, webLoadUrl.hasPrefix("https://buyin.jinritemai.com/dashboard") {
                        
                    } else {
                        if let mLoginImgUrl  = self.mLoginImgUrl, mLoginImgUrl.contains("/qrconnect?container=") {
                        
                        } else {
                            // 加载授权页
                            if let qrUrl = self.qrUrl {
                                self.loadWebView(url: qrUrl)
                                self.startQRCheck()
                            }
                        }
                    }
                }
                
            }
            
            
        }else {
            LCLog.d("没有地址********")
        }
        decisionHandler(.allow)
    }
}


extension NetworkMonitorViewController {
    
    /// 开启定时器切换
    public func startLoginTimer() {
        self.releaseLoginTimer()
        self.loginTimer = Timer.scheduledTimer(timeInterval: TimeInterval(1.0), target: self, selector: #selector(loginTimerAction(sender:)), userInfo: nil, repeats: true)
    }
    
    /// 释放定时器
    public func releaseLoginTimer() {
        self.loginTimer?.invalidate()
        self.loginTimer = nil
    }
    
    /// 触发定时切换
    @objc public func loginTimerAction(sender: Timer) {
        // 检查是否登录了 如果登录了就不查了
        if isCheckedLogin  {
            releaseLoginTimer()
        } else {
            // 查询用户
            goLogin()
        }
    }
}

// MARK: - JS API监控处理
extension NetworkMonitorViewController {
    // 处理所有的请求数据
    func handle(requestData: RequestData ) {
        //
        LCLog.d("requestData= \(requestData.url)")
        
        if requestData.url.contains(promotions_v2_Host), requestData.url.contains(promotions_v2_p1) ,requestData.url.contains(promotions_v2_p2) {
            self.delegate?.webViewMockUrls(request: DYRequestData(data: requestData, workType: .promotion_v2))
        }
        if requestData.url.contains(promotions_v2_Host), requestData.url.contains(promotions_v2_p1) ,requestData.url.contains(promotions_v2_p3) {
            self.delegate?.webViewMockUrls(request: DYRequestData(data: requestData, workType: .promotion_v2_refresh))
        }
        else if requestData.url.contains(promotions_list_URL) {
           
            
        } else if requestData.url.contains(bind_promotions_URL) {
            self.delegate?.webViewMockUrls(request: DYRequestData(data: requestData, workType: .bind))
        } else if requestData.url.contains(combination_list_URL) {
            self.delegate?.webViewMockUrls(request: DYRequestData(data: requestData, workType: .combination))
        } else if requestData.url.contains(promotions_v2_all_URL) {
            self.delegate?.webViewMockUrls(request: DYRequestData(data: requestData, workType: .promotion_v2_all))
        }
        
    }
    
    func handle(responseData: RequestData ) {
//        LCLog.d("requestData= \(responseData.url)")
        if responseData.url.contains(combination_ON_URL) {
            self.delegate?.webViewMockUrls(request: DYRequestData(data: responseData, workType: .combination_on))
        } else if responseData.url.contains(bind_promotions_URL)  {
            
        }
    }

    
     func setupNetworkMonitor() {
        // 配置网络监控
        NetworkMonitor.shared.configureWebView(webView)
        
        // 设置请求捕获回调
        NetworkMonitor.shared.onRequestCaptured = { [weak self] request in
            guard let self = self else { return }
            self.handle(requestData: request)
        }
         
         NetworkMonitor.shared.onResponseCaptured = { [weak self] request in
             guard let self = self else { return }
             self.handle(responseData: request)
         }

       
        NetworkMonitor.shared.onMessageReceived = { [weak self] keys in
            guard let self = self else { return  }
            guard let key = keys["key"] as? String else { return }
            
            switch key {
            case js_type_get_login:
                guard let name = keys["message"] as? String else { return }
                if !name.isEmpty {
                    LCLog.d("用户名称：\(name)")
                    self.userName = name
                    self.isCheckedLogin = true
                    self.clickIkowon()
                } else {
                    self.userName = nil
                }
            case js_type_get_goods:
                
                
                break
            case js_type_jiangjie:
                guard let ret = keys["message"] as? String else { return }
                if ret == "ok" {
                    LCLog.d("开始讲解：\(ret)")
                }
                
            case js_type_jiangjie_no:
                guard let ret = keys["message"] as? String else { return }
                if ret == "no" {
                    LCLog.d("取消讲解：\(ret)")
                }
            case js_type_find_click:
                guard let url = keys["message"] as? String else { return }
                
                LCLog.d("点击商品：\(url)")
                
            case js_type_find:
                guard let url = keys["message"] as? String else { return }
                LCLog.d("查找商品：\(url)")
            case js_type_zhongkong:
                //
                LCLog.d("已经进入到中控台的页面了")
                selected_zhongkong = true
           
            case js_type_clickPromotion:
                guard let url = keys["message"] as? String else { return }
                LCLog.d("商品操作结果：\(keys)")
                if url == "success" {
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.4) { [weak self] in
                        self?.findAndClick(title: "确认")
                    }
                }
            default:
                break
            }
            
            self.delegate?.userContentController(key: key)
            
        }
    }
}


// MARK: - JS 交互
extension NetworkMonitorViewController {
   
    
    func jiangjie(title: String, on: Bool) {
        // 讲解第一个商品
//        if let good = self.goods.first(where: {$0.title == title}) {
//            if on {
//                jiangjieElement(text: good.title)
//            } else {
//                jiangjieCancelElement(text: good.title)
//            }
//
//        }
    }
    
    // 注入所有JS代码
    func injectJavaScript() {
        if let js = JSSingleton.shared.jsCode {
            webView.evaluateJavaScript(js) { data, error in
                LCLog.d("js注入：\(error)")
            }
        }
        
        let jsCode = "navigator.userAgent = '\(User_Agent)';"
        webView.evaluateJavaScript(jsCode) { (result, error) in
            if let error = error {
                print("注入JavaScript代码出错: \(error.localizedDescription)")
            } else {
                print("成功注入JavaScript代码修改User - Agent")
            }
        }
        
    }
    
    func clickIkowon() {
        // 在5秒之后点击我知道了
        DispatchQueue.main.asyncAfter(deadline: .now() + 4) { [weak self] in
            guard let self = self else  { return }
            guard var name = JSSingleton.shared.iKnow else { return  }
            name = name + "(5)"
            self.webView.evaluateJavaScript(name) { data, error in
                LCLog.d("\(error)")
            }
            
            guard var name = JSSingleton.shared.tiyan else { return  }
            name = name + "(2)"
            self.webView.evaluateJavaScript(name) { data, error in
                LCLog.d("\(error)")
            }
            self.findAndClick(title: "开始体验")
            self.go_zhongkongtai()
        }
    }
    
    /****
     * 是否登录
     */
    func goLogin() {
        guard var name = JSSingleton.shared.findUserName else { return  }
        name = name + "()"
        webView.evaluateJavaScript(name) { data, error in
            LCLog.d("\(error)")
        }
    }
    
    func go_zhongkongtai() {
        if !isCheckedLogin {
            LCLog.d("暂未登录")
            return
        }
        guard var name = JSSingleton.shared.zhongkong else { return  }
        name = name + "(5)"
        webView.evaluateJavaScript(name) { data, error in
            LCLog.d("\(error)")
        }
    }
    
    
    // 发现商品然后点击
    func findAndClickGood(goodModel: DyGoodModel, type: String) {
        if !isCheckedLogin {
            LCLog.d("暂未登录")
            return
        }
        if !selected_zhongkong {
            HUD.showSuccess("正在等待直播中控台响应，请等待.....")
            return
        }
        guard var name = JSSingleton.shared.clickGood else { return  }
        
         name = name + "(\"" + goodModel.title + "\")"
        webView.evaluateJavaScript( name) { data, error in
            LCLog.d("\(error)")
        }
    }
    
    // 找元素然后点击
    func findAndClick(title: String) {
        if !isCheckedLogin {
            LCLog.d("暂未登录")
            return
        }
        if !selected_zhongkong {
            HUD.showWait()
            go_zhongkongtai()
            return
        }
        
        guard var name = JSSingleton.shared.clickGood else { return  }
         name = name + "(\"" + title + "\")"
        webView.evaluateJavaScript( name) { data, error in
            LCLog.d("\(error)")
        }
    }
    
    
    /***
     * 发现商品
     */
    func findElement(text: String) {
        if !isCheckedLogin {
            LCLog.d("暂未登录")
            return
        }
        if !selected_zhongkong {
            HUD.showSuccess("正在等待直播中控台响应，请等待.....")
            return
        }
        guard var name = JSSingleton.shared.findElement else { return  }
        name = name + "(\"" + text + "\")"
        webView.evaluateJavaScript( name) { data, error in
            LCLog.d("\(error)")
        }
    }
    
    
    func jiangjieElement(text: String) {
        if !isCheckedLogin {
            LCLog.d("暂未登录")
            return
        }
        
        if !selected_zhongkong {
            HUD.showSuccess("正在等待直播中控台响应，请等待.....")
            return
        }
        
        guard var name = JSSingleton.shared.jiangjie else { return  }
        
        name = name + "(\'" + text + "\',\'取消搭配\')"
        webView.evaluateJavaScript( name) { data, error in
            LCLog.d("\(error)")
        }
    }
    
    //    取消讲解
    func jiangjieCancelElement(text: String) {
        if !isCheckedLogin {
            LCLog.d("暂未登录")
            return
        }
        if !selected_zhongkong {
            HUD.showSuccess("正在等待直播中控台响应，请等待.....")
            return
        }
        
        guard var name = JSSingleton.shared.jiangjie else { return  }

        name = name + "(\'" + text + "\',\'取消主推\')"
        webView.evaluateJavaScript( name) { data, error in
            LCLog.d("\(error)")
        }
    }
    
    
    func clickPromotion(num: Int, id: String, title: String, key: String) {
        if !isCheckedLogin {
            LCLog.d("暂未登录")
            return
        }
        
        if !selected_zhongkong {
            HUD.showSuccess("正在等待直播中控台响应，请等待.....")
            return
        }
        
        guard var name = JSSingleton.shared.clickPromotion else { return  }
                
        name = name + "(" + "\(num)" + ",\'" + id + "\',\'\(title)\'" + ",\'\(key)\'" + ")"
        webView.evaluateJavaScript( name) { data, error in
            LCLog.d("\(error)")
        }
        
    }
    
}



// MARK: - 扫码登录的业务
extension NetworkMonitorViewController {

    func startQRCheck() {

        self.retryView.isHidden = true
        self.qrImageView.image = nil
        self.isHidden = false
        startQrTimerCheck()
        
    }
    
    // 获取二维码
    func getqrUrl(){
        
        webView.evaluateJavaScript(JSSingleton.shared.get_buyin_qr_img) { [weak self ](result, error) in
            if let error = error {
                print("Error getting image src: \(error.localizedDescription)")
            } else if let src = result as? String {
                print("Image src: \(src)")
                // 这里可以处理获取到的 src，例如保存或使用
                self?.creatQRImage(urlbase64: src)
            } else {
                print("No image found or src is nil.")
            }
        }
    }
    
    
    func creatQRImage(urlbase64: String) {

        // 去掉前缀
        let base64String = urlbase64.replacingOccurrences(of: "data:image/png;base64,", with: "")

        // 解码 Base64 字符串
        if let imageData = Data(base64Encoded: base64String, options: .ignoreUnknownCharacters) {
            // 创建 UIImage
            if let image = UIImage(data: imageData) {
                // 这里可以使用生成的 UIImage
                self.qrImageView.image = image
            } else {
                print("生成 UIImage 失败")
            }
        } else {
            print("解码 Base64 字符串失败")
        }
       
        // 开始计时二维码
        startQrTimerOut()
        
        releaseQrtimerCheck()
        
        self.retryView.isHidden = true
    }
    
    
    func startQrTimerOut() {
        releaseQrtimerOut()
        self.qrtimerOut = Timer.scheduledTimer(timeInterval: 40.0, target: self, selector: #selector(qrimageTimeout), userInfo: nil, repeats: false)
    }
    
    func releaseQrtimerOut() {
        self.qrtimerOut?.invalidate()
        self.qrtimerOut = nil
    }
    
    @objc func qrimageTimeout() {
        self.retryView.isHidden = false
    }
    
    // 获取二维码
    func startQrTimerCheck() {
        releaseQrtimerCheck()
        self.qrtimerCheck = Timer.scheduledTimer(timeInterval: 2.0, target: self, selector: #selector(qrimageCheck), userInfo: nil, repeats: true)
    }
    
    func releaseQrtimerCheck() {
        self.qrtimerCheck?.invalidate()
        self.qrtimerCheck = nil
    }
    
    @objc func qrimageCheck() {
        getqrUrl()
    }
    
    @objc func retryAction() {
        self.loadWebView(url: targetURL)
    }
    
}
