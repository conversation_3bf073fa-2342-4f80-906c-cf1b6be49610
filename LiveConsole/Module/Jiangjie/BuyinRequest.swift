//
//  BuyinRequest.swift
//  MockBuyin
//
//  Created by simon on 2.12.24.
//

import Foundation

// 改成单利 保存一下请求的所有数据

class BuyinRequest: NSObject {
    
    static let shared = BuyinRequest()
    
    // 上次的请求数据
    var promotions_v2: RequestData?
    
    // 上次的请求数据
    var promotions_v2_all: RequestData?
    
    // 直播商品
    var promotions: Promotions?
    
    // 待上架的商品
    var promotionsAll: PromotionsAll?
    
    
    var isLiving: Bool {
        if let p = self.promotions, let data = p.data{
            return data.isLiving
        }
        return false
    }
    
    func isCurrent(promotion:Promotions_data_promotions) -> Bool {
        guard let p = self.promotions, let data = p.data else { return false}
        return  data.isCurrent(promotion: promotion)
    }
    
    // 当前商品是否上架了
    func isShelf(id: String) -> Bool {
        guard let p = self.promotions, let data = p.data, let promotions = data.promotions else { return false}
        return promotions.contains(where: {$0.promotion_id == id})
    }
    
    func clear() {
        promotions = nil
        promotionsAll = nil
        promotions_v2 = nil
        promotions_v2_all = nil
    }
    
    func livingGoodCount() -> Int {
        guard let data = promotions?.data, let promotions = data.promotions else { return 0 }
        return promotions.count
    }
    
    func allGoodCount() -> Int {
        guard let data = promotionsAll?.data else { return 0 }
        return data.products.count
    }

}
    

// MARK: - 所有的请求数据
extension BuyinRequest {
    // 共同的请求方法封装
    static func sendRequest(requestData:RequestData, completion: @escaping ((_ response: [String: Any]?) -> Void))  {
        guard let Url = URL(string: requestData.url) else {
            completion(nil)
            return
        }
        
        // 构建请求
        var request = URLRequest(url: Url)
        request.httpMethod = requestData.method
        request.timeoutInterval = 30
        
        let cookies = WebInfoManager.shared.load()?.getCookieString()
        // 添加 cookies
        request.setValue(cookies, forHTTPHeaderField: "Cookie")
        
        // 设置 headers
        request.setValue("application/json, text/plain, */*", forHTTPHeaderField: "accept")
        
        if let Content = requestData.requestHeaders["Content-Type"]  {
            request.setValue(Content, forHTTPHeaderField: "Content-Type")
        }
        
        if let al = requestData.requestHeaders["Accept-Language"]  {
            request.setValue(al, forHTTPHeaderField: "Accept-Language")
        }
        
        if let origin = requestData.requestHeaders["Origin"]  {
            request.setValue(origin, forHTTPHeaderField: "Origin")
        }
        
        if let referer = requestData.requestHeaders["Referer"]  {
            request.setValue(referer, forHTTPHeaderField: "Referer")
        }
        
        if let useragent = requestData.requestHeaders["User-Agent"]  {
            request.setValue(useragent, forHTTPHeaderField: "User-Agent")
        }
        
        if let token = requestData.requestHeaders["x-secsdk-csrf-token"]  {
            request.setValue(token, forHTTPHeaderField: "x-secsdk-csrf-token")
        }
        
        if let requestBody = requestData.requestBody, let bodyData = requestBody.data(using: .utf8) {
            request.httpBody = bodyData
        }
        
        
        let task = URLSession.shared.dataTask(with: request) { (data, response, error) in
            if let error = error {
                print("请求错误: \(error)")
                DispatchQueue.main.async {
                    HUD.showFail("\(error.localizedDescription)")
                }
                completion(nil)
                return
            }
            
            guard let httpResponse = response as? HTTPURLResponse, httpResponse.statusCode == 200 else {
                DispatchQueue.main.async {
                    HUD.showFail("无效的响应")
                }
                completion(nil)
                return
            }
            
            guard let data = data else{
                DispatchQueue.main.async {
                    HUD.showFail("请求失败，状态码: \(httpResponse.statusCode)")
                }
                completion(nil)
                return
            }
            
            do {
                if let jsonResponse = try JSONSerialization.jsonObject(with: data, options: []) as? [String: Any] {
                    completion(jsonResponse)
                } else {
                    DispatchQueue.main.async {
                        HUD.showFail("无法解析JSON响应")
                    }
                    completion(nil)
                }
            } catch {
                DispatchQueue.main.async {
                    HUD.showFail("解析JSON响应时出错: \(error)")
                }
                completion(nil)
            }
        }
        
        task.resume()
    }
}

//
extension BuyinRequest {
    
    static func requestLiveGoods(requestData: RequestData, completion: @escaping ((_ responseData: Promotions?) -> Void)) {
        
        BuyinRequest.sendRequest( requestData: requestData) { response in
            guard let  response = response else {
                completion(nil)
                return
            }
            
            do {
                let jsonData = try JSONSerialization.data(withJSONObject: response, options: [])
                let decoder = JSONDecoder()
                let model = try decoder.decode(Promotions.self, from: jsonData)
                
                completion(model)
            } catch {
                LCLog.d("JS模型转换失败:\(error)")
                completion(nil)
            }
        }
    }
    
    
    static func requestAllLiveGoods(requestData: RequestData, completion: @escaping ((_ responseData: PromotionsAll?) -> Void)) {
        
        BuyinRequest.sendRequest( requestData: requestData) { response in
            guard let  response = response else {
                completion(nil)
                return
            }
            
            do {
                let jsonData = try JSONSerialization.data(withJSONObject: response, options: [])
                let decoder = JSONDecoder()
                let model = try decoder.decode(PromotionsAll.self, from: jsonData)
                
                completion(model)
            } catch {
                //
                LCLog.d("JS模型转换失败:\(error)")
                completion(nil)
            }
        }
    }
    
    
    static func  requestXuanpinche(requestData: RequestData, completion: @escaping ((_ responseData: XuanpincheModel?) -> Void)) {
        
        BuyinRequest.sendRequest( requestData: requestData) { response in
            guard let  response = response else {
                completion(nil)
                return
            }
            
            do {
                let jsonData = try JSONSerialization.data(withJSONObject: response, options: [])
                let decoder = JSONDecoder()
                let model = try decoder.decode(XuanpincheModel.self, from: jsonData)
                
                completion(model)
            } catch {
                LCLog.d("JS模型转换失败:\(error)")
                completion(nil)
            }
        }
    }
    
    
    static func  requestBind(requestData: RequestData, completion: @escaping ((_ responseData: PromotionBindModel?) -> Void)) {
        
        BuyinRequest.sendRequest( requestData: requestData) { response in
            guard let  response = response else {
                completion(nil)
                return
            }
            
            do {
                let jsonData = try JSONSerialization.data(withJSONObject: response, options: [])
                let decoder = JSONDecoder()
                let model = try decoder.decode(PromotionBindModel.self, from: jsonData)
                
                completion(model)
            } catch {
                LCLog.d("JS模型转换失败:\(error)")
                completion(nil)
            }
        }
    }
    
    
    
    
    static func  requestCombination(requestData: RequestData, completion: @escaping ((_ responseData: CombinationModel?) -> Void)) {
        
        BuyinRequest.sendRequest( requestData: requestData) { response in
            guard let  response = response else {
                completion(nil)
                return
            }
            
            do {
                let jsonData = try JSONSerialization.data(withJSONObject: response, options: [])
                let decoder = JSONDecoder()
                let model = try decoder.decode(CombinationModel.self, from: jsonData)
                
                completion(model)
            } catch {
                LCLog.d("JS模型转换失败:\(error)")
                completion(nil)
            }
        }
    }
    
    
    static func  requestDeletCombination(requestData: RequestData, completion: @escaping ((_ responseData: CombinationDeletModel?) -> Void)) {
        
        BuyinRequest.sendRequest( requestData: requestData) { response in
            guard let  response = response else {
                completion(nil)
                return
            }
            
            do {
                let jsonData = try JSONSerialization.data(withJSONObject: response, options: [])
                let decoder = JSONDecoder()
                let model = try decoder.decode(CombinationDeletModel.self, from: jsonData)
                
                completion(model)
            } catch {
                LCLog.d("JS模型转换失败:\(error)")
                completion(nil)
            }
        }
    }
    
    
    
}
