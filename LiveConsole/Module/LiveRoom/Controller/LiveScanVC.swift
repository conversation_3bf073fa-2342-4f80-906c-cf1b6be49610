//
//  LiveScanVC.swift
//  LivePlus
//
//  Created by 郭炜 on 2021/7/8.
//

import UIKit
import swiftScan

class LiveScanVC: LBXScanViewController {
        
    /// 返回按钮
    public lazy var backButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setImage(UIImage(named: "icon_nav_back_white"), for: .normal)
        return button
    }()
    
    /// 文本
    public lazy var titleLabel: UILabel = {
        let label = UILabel()
//        label.text = "扫描其他直播间的二维码\n可获取其直播间全部内容"
        label.textColor = UIColor.white
        label.numberOfLines = 0
//        label.font = LCDevice.DIN_Font_PF_M(14)
//        label.textAlignment = .center
        return label
    }()
    
    private var style: LBXScanViewStyle = LBXScanViewStyle()
    
    override func viewDidLoad() {
        super.viewDidLoad()
        fd_prefersNavigationBarHidden = true
        
        edgesForExtendedLayout = []
        automaticallyAdjustsScrollViewInsets = false
        
        requestRecursionPemission()
        
        style.colorAngle = UIColor("#6974F2")
        style.anmiationStyle = .LineMove
        style.isNeedShowRetangle = false
        style.animationImage = UIImage(named: "scan_icon")
        scanStyle = style
        
        let rect = self.getScanRectForAnimation()
        titleLabel.frame = CGRect(x: 60, y: rect.size.height + rect.origin.y - 30, width: LCDevice.screenW - 120, height: 60)
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        setupSubviews()
    }
    
    private func setupSubviews() {
        titleLabel.centerX = UIScreen.main.bounds.center.x
        backButton.frame = CGRect(x: 14, y: LCDevice.X_HAIR + 22, width: 33, height: 33)
        view.addSubview(titleLabel)
        view.addSubview(backButton)
        
        backButton.addTarget(self, action: #selector(buttonActionByBack), for: .touchUpInside)
    }
    
    @objc private func buttonActionByBack() {
        self.navigationController?.popViewController(animated: true)
    }
    
    private func getScanRectForAnimation() -> CGRect {
        let XRetangleLeft = style.xScanRetangleOffset
        let frame = UIScreen.main.bounds
        var sizeRetangle = CGSize(width: frame.size.width - XRetangleLeft * 2,
                                  height: frame.size.width - XRetangleLeft * 2)
        
        if style.whRatio != 1 {
            let w = sizeRetangle.width
            let h = w / style.whRatio
            sizeRetangle = CGSize(width: w, height: CGFloat(Int(h)))
        }
        
        // 扫码区域Y轴最小坐标
        let YMinRetangle = frame.size.height / 2.0 - sizeRetangle.height / 2.0 - style.centerUpOffset
        // 扫码区域坐标
        let cropRect = CGRect(x: XRetangleLeft, y: YMinRetangle, width: sizeRetangle.width, height: sizeRetangle.height)
        
        return cropRect
    }
}

extension LiveScanVC {
    private func requestRecursionPemission() {
        let videoAuth = AVCaptureDevice.authorizationStatus(for: .video)
        switch videoAuth {
        case .authorized: // 视频、录音权限同时存在
            print("已开启权限")
        case .notDetermined: // 视频权限未弹出过
            AVCaptureDevice.requestAccess(for: .video) { [weak self] isPermissed in
                guard let self = self else { return }
                if !isPermissed { self.restrictedAuth(); return }
                self.requestRecursionPemission() // 递归查询
            }
        default: // 权限未允许
            self.restrictedAuth()
        }
    }
    
    private func restrictedAuth() {
        let title = "权限未开启提示"
        let message = "您尚未开启以下权限：\n 1、相机（摄像头）权限"
        let alert = LLAlertView(title: title, message: message, leftShow: ("确认", .defaultColor), rightShow: ("去设置", .mainColor), leftAction: nil) {
            Router.openSystemSetter()
        }
        DispatchQueue.main.async {
            alert.show(with: AppDelegate.curDisplayVC())
        }
    }
}
