//
//  AIGiftReplyVc+AudioReply.swift
//  LivePlus
//
//  Created by simon on 11.2.25.
//

import Foundation

import UIKit

// MARK: - 语音回复
extension AIGiftReplyVc {
    
    /// 语音回复提示
    func audioReplyQuestion() {
        let alertView = SmartAlertView()
        alertView.show(
            title: "回复内容",
            content: "1、点击【+】添加回复内容，上限添加5个\n\n2、添加后，系统会根据触发礼物，随机播放1个回复内容",
            in: self.view
        )
    }
    
}

extension AIGiftReplyVc: ReplayAudioViewDelegate {
    func endEdit() {
        endEditing()
    }
}


extension AIGiftReplyVc: SmartRecordAudioVCDelegate, SmartTTSAudioVCDelegate {
    /// 添加语音回复
    func audioReplyPlusAction(addType: AudioAddType) {
        
        
        let count: Int = self.replyItemModel.replyItem?.resources?.count ?? 0
        
        // 只能添加5个
        if  self.replyItemModel.itemType == .musicLocal, self.replyItemModel.itemCount >= 5 {
            HUD.showFail("语音数量已达上限")
            return
        }
        
        switch addType {
        case .record:
            print("现在录音")
            let viewController = SmartRecordAudioVC()
            viewController.delegate = self
            self.navigationController?.pushViewController(viewController)
        case .aiGenerate:
            //先选礼物才能添加
            guard let gift = self.currentGift else {
                HUD.showFail("请先添加触发礼物")
                return
            }
            print("AI合成")
            AIReplyDataManager.shared.ttsDelegate = self
            let viewController = SmartTTSAudioVC()
            viewController.delegate = self
            viewController.gift = gift
            viewController.audioName = "语音0\(count + 1)"
            self.navigationController?.pushViewController(viewController)
            if isNew {
                AIReplyDataManager.shared.addNewCardItem(replyItem: replyItemModel)
            }
        case .importFile:
            print("导入语音")
            self.processExport()
        }
    }
    
    /// 导入音频
    func processExport() {
        let types = ["public.audio", "public.mp3", "public.mpeg-4-audio", "com.apple.m4a-audio", "public.wav"]
        let documentPicker = UIDocumentPickerViewController(documentTypes: types, in: .open)
        documentPicker.delegate = self
        documentPicker.modalPresentationStyle = .formSheet
        self.present(documentPicker, animated: true, completion: nil)
    }
    
    /// 录音保存成功
    func actionForRecordAudioCompletion(url: URL, duration: TimeInterval, isLocal: Bool) {
        let resourceModel = ResFileModel(path: nil, sourceType: .musicLocal)
        resourceModel.duration = Int64(duration * 1000) // 毫秒
        resourceModel.path = url.lastPathComponent // 后缀
        // 需求要求：名称要改成 录音
        resourceModel.title = isLocal ? "本地" : "录音" //"\(duration.toUIHourString)"
        self.replyItemModel.replyItem?.resources?.append(resourceModel)
        self.replyItemModel.modifyUpdateTime()
        // 刷新页面
        self.audioReplyView.updateTable()
        updateReplyView()
        if isNew {
            AIReplyDataManager.shared.addNewCardItem(replyItem: replyItemModel)
        }
    }
    
    /// 删除语音回复
    func audioReplyMinusAction() {
        
    }
    
    // 获取到了一个任务ID
    func didGetTTSCompletion(taskId: String, audioName: String) {
        // 增加一个模型
        let resourceModel = ResFileModel(path: nil, sourceType: .musicLocal)
        resourceModel.duration = 0 // 毫秒
        resourceModel.title = audioName
        resourceModel.taskId = taskId
        resourceModel.audioState = .waitting
        self.replyItemModel.replyItem?.resources?.append(resourceModel)
        self.replyItemModel.modifyUpdateTime()
        // 刷新页面
        self.audioReplyView.updateTable()
        updateReplyView()
        if isNew {
            AIReplyDataManager.shared.addNewCardItem(replyItem: replyItemModel)
        }
        
    }
    
}

extension AIGiftReplyVc: AIReplyDataManagerTTSDelegate {
    func didCombineAudio(taskId: String, finsh: Bool, filePath: String?) {
        // 刷新页面
        self.audioReplyView.updateTable()
        self.replyItemModel.modifyUpdateTime()
        if isNew {
            AIReplyDataManager.shared.addNewCardItem(replyItem: replyItemModel)
        }
        updateReplyView()
        HUD.showSuccess(finsh ? "AI语音合成成功" : "AI语音合成失败")
       
    }
    
}


extension AIGiftReplyVc: UIDocumentPickerDelegate {
    func documentPicker(_ controller: UIDocumentPickerViewController, didPickDocumentsAt urls: [URL]) {
        guard let sourceURL = urls.first, sourceURL.startAccessingSecurityScopedResource() else { return }

        let asset = AVAsset(url: sourceURL)
        let duration = Int(CMTimeGetSeconds(asset.duration))
        
        if duration > 10 {
            // 提示用户选择不符合要求
            HUD.showFail("当前音频\(duration)秒，最大支持10秒时长")
            return
        }
        
        let coordinator = NSFileCoordinator()
        var error: NSError?
        coordinator.coordinate(readingItemAt: sourceURL, error: &error) { [weak self] newURL in
            guard let self = self else { return }
            DispatchQueue.global().async {
                let fileName = "fileCopy_" + newURL.lastPathComponent
                let destinationPath = AudioPathStyle.audioRecord.path.appendingPathComponent(fileName)
                let destinationURL = URL(fileURLWithPath: destinationPath)
                do {
                    if FileManager.default.fileExists(atPath: destinationPath) {
                        try FileManager.default.removeItem(at: destinationURL)
                    }
                    try FileManager.default.copyItem(at: newURL, to: destinationURL)
                    AudioDurationHelper.getAudioDuration(from: destinationURL) { [weak self] result in
                        guard let self = self else { return }
                        switch result {
                        case .success(let duration):
                            self.actionForRecordAudioCompletion(url: destinationURL,
                                                                duration: duration,
                                                                isLocal: true)

                        case .failure(let failure):
                            self.actionForRecordAudioCompletion(url: destinationURL,
                                                                duration: 10,
                                                                isLocal: true)
                        }
                    }
                } catch {
                    print("文件保存失败: \(error)")
                    DispatchQueue.main.async {
                        HUD.showFail("无法使用该音频\n\(error.localizedDescription)")
                    }
                }
                DispatchQueue.main.async {
                    sourceURL.stopAccessingSecurityScopedResource()
                }
            }
        }
    }
}
