//
//  TextReplay.swift
//  LiveConsole
//
//  Created by simon on 28.4.25.
//

import Foundation
// MARK: - 文字部分的操作
extension AIGiftReplyVc {
    
    /// 添加文字回复内容
    func plusTextReplyAction() {
        
        if  self.replyItemModel.itemType == .text, self.replyItemModel.itemCount >= 5 {
            HUD.showFail("文字回复数量已达上限")
            return
        }
        
        let view = ReplyTextPlusView()
        view.confirmCallback = { [weak self] text in
            guard let self = self else { return }
            guard let text = text, !text.isEmpty else { return }
            let resFileModel = ResFileModel(path: nil, sourceType: .text, text: text)
            self.replyItemModel.replyItem?.resources?.append(resFileModel)
            self.replyItemModel.modifyUpdateTime()
            // 刷新页面
            self.audioReplyView.updateTable()
            updateReplyView()
            if isNew {
                AIReplyDataManager.shared.addNewCardItem(replyItem: replyItemModel)
            }
        }
        view.show(title: "添加新内容", in: self.view, isGift: true, replyType: .good)
    }
    
    /// 修改文字回复内容
    func editTextReplyAction() {
        guard let selectionModel = self.audioReplyView.selectionModel else {
            HUD.showFail("请选择要编辑的文本")
            return
        }
        let view = ReplyTextPlusView()

        view.confirmCallback = { [weak self] text in
            guard let self = self else { return }
            guard let text = text, !text.isEmpty else { return }
            selectionModel.text = text
            self.replyItemModel.modifyUpdateTime()
            // 刷新页面
            self.audioReplyView.updateTable()
        }
        view.show(title: "编辑内容", in: self.view, isGift: true, text: selectionModel.text ?? "", replyType: .good)
    }
}
