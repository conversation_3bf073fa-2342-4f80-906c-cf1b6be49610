//
//  AIGiftReplyVc.swift
//  LivePlus
//
//  Created by simon on 9.1.25.
//

import Foundation


class AIGiftReplyVc: BaseVC {
    
    lazy var giftView: AIGiftReplyView = {
        let v = AIGiftReplyView()
        v.delegate = self
        return v
    }()
    
    lazy var navigation: SmartNavigation = {
        let view = SmartNavigation()
        view.titleLabel.text = "添加送礼回复"
        view.backgroundColor = .clear
        return view
    }()
    
    lazy var giftListView: DouyinGiftListView = {
        let v = DouyinGiftListView(frame: CGRect(x: 0, y: LCDevice.screenH, width: LCDevice.screenW, height: 516))
        v.delegate = self
        return v
    }()
    
    /// 语音回复内容
    lazy var audioReplyView: ReplayAudioView = {
        let view = ReplayAudioView()
        view.delegate = self
        return view
    }()
    
    lazy var bgView: UIView = {
        let v = UIView(frame: CGRect(x: 0, y: 0, width: LCDevice.screenW, height: LCDevice.screenH))
        v.backgroundColor = UIColor("#111111")
        return v
    }()
    
    // 回复的一个卡片模型
    var replyItemModel: AiKeywordVoiceReplyItemModel
    
    // 是否是新增卡片
    var isNew: Bool = false
    
    // 通过接口获取的礼物列表
    var giftModel: DouyinGiftModel?
    
    // DouyinGiftModel_Row
    var gift_row: DouyinGiftModel_Row?
    
    var currentGift:DouyinGiftInfo? {
        guard let gifts = self.replyItemModel.gifts, let gift = gifts.first else {
            return nil
        }
        return gift
    }
    
    init(model: AiKeywordVoiceReplyItemModel, isNew: Bool = false) {
        self.isNew = isNew
        self.replyItemModel = model
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    
    override func viewDidLoad() {
        super.viewDidLoad()
        makeUI()
        business()
        bindViewModel()
        updateReplyView()
        updateGiftView()
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        /// 需要重置代理 因为录制页面也会抢代理
        AudioPlayManager.shared.delegate = self.audioReplyView
    }
    
    
    func makeUI() {
        
        view.addSubview(bgView)
        
        view.addSubview(navigation)
        navigation.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview()
            make.height.equalTo(LCDevice.Nav_H)
        }
        
        bgView.addSubviews([giftView, audioReplyView])
        
        giftView.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview()
            make.height.equalTo(140)
            make.top.equalToSuperview().inset(107)
        }
        
        audioReplyView.snp.makeConstraints { make in
            make.leading.trailing.bottom.equalToSuperview()
            make.top.equalTo(giftView.snp.bottom).offset(12)
        }
        
    }
    
    func loadData() {
        if let _ = self.giftModel {
            self.showGiftListView()
            return
        }
        
        
        HUD.showWait()
        MiddleRequestNet.getDouyinGifts { [weak self] data in
            guard let self = self else { return }
            guard let data = data else { return}
            HUD.hideAllHUD()
            data.records.forEach({ row in
                if let info: DouyinGiftInfo = JsonTool.string2Model(row.giftInfo) {
                    row.image = info
                }
            })
            
            data.sortByDiamondCountAscending()
            self.giftModel = data
            
            updateGiftEnable()
            
            self.showGiftListView()
            
        }
    }
    
    // 更新哪些礼物是可用的
    func updateGiftEnable() {
        // 礼物
        var allGiftids: [String] = []
        
        if let sys = AIReplyDataManager.shared.interactionModel {
            sys.cardList.forEach { item in
                if item.interactionType  == .systemInteraction, item.systemInteractionType == .gift, let gifts = item.gifts {
                    gifts.forEach { g in
                        allGiftids.append(g.id)
                    }
                }
            }
        }
        
        giftModel?.records.forEach({ row in
            //查询这个礼物是否可用， 如果列表中给这个礼物选过回复了， 不能再次选择该礼物
            row.enable = !allGiftids.contains(where: {$0 == row.image?.id})
        })
        
    }
    
    func business() {

        audioReplyView.questionButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self else { return }
                self.audioReplyQuestion()
            }.disposed(by: rx.disposeBag)
        
       
        audioReplyView.questionButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self else { return }
                self.audioReplyQuestion()
            }.disposed(by: rx.disposeBag)
        
        
        audioReplyView.recordButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self else { return }
                self.audioReplyPlusAction(addType: .record)
            }.disposed(by: rx.disposeBag)
        audioReplyView.combineButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self else { return }
                self.audioReplyPlusAction(addType: .aiGenerate)
            }.disposed(by: rx.disposeBag)
        audioReplyView.uploadButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self else { return }
                self.audioReplyPlusAction(addType: .importFile)
            }.disposed(by: rx.disposeBag)
        audioReplyView.deleteButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self else { return }
                self.audioReplyMinusAction()
            }.disposed(by: rx.disposeBag)
        
        /// 文字部分
        audioReplyView.textPlusButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self else { return }
                self.plusTextReplyAction()
            }.disposed(by: rx.disposeBag)
        audioReplyView.textEditButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self else { return }
                self.editTextReplyAction()
            }.disposed(by: rx.disposeBag)
        audioReplyView.textDeleteButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self else { return }
                self.endEditing()
            }.disposed(by: rx.disposeBag)
    }
    func bindViewModel() {
        self.audioReplyView.bind(to: replyItemModel)
    }
    
    override func touchesBegan(_ touches: Set<UITouch>, with event: UIEvent?) {
        super.touchesBegan(touches, with: event)
        self.view.endEditing(true)
    }
    
    func showGiftListView() {
        if let gift = self.giftModel {
            self.giftListView.updateData(data:gift)
            showPopBotRight(view: self.giftListView)
        }
    }
        
    func willDeletReplyContenr() {
        guard let resources =  replyItemModel.replyItem?.resources, let file = resources.first else {
            updateReplyView()
            return
        }
        
        AlertView.show(leftOption: .gray(title: "取消", action: nil), rightOption: .main(title: "确定", action: { [weak self] in
            guard let self = self else { return }
            self.deletReplyContenr()
        }), title: "温馨提示", message: "确定删除回复内容?")
        
    }
    
    func deletReplyContenr() {
        replyItemModel.replyItem?.resources?.removeAll()
        replyItemModel.modifyUpdateTime()
        updateReplyView()
    }
    
    func endEditing() {
        self.view.endEditing(true)
    }
}


// MARK: - 选择礼物
extension AIGiftReplyVc: AIGiftReplyViewDelegate {
    
    func didAction(gift: DouyinGiftModel_Row?, type: AIGiftReplyViewType) {
        switch type {
        case .add:
            loadData()
        case .edit:
            loadData()
        case .help:
            giftReplyQuestion()
        }
        
    }
    
}

// MARK: - 礼物列表
extension AIGiftReplyVc: DouyinGiftListViewDelegate {
    func didAction(gift: DouyinGiftModel_Row?) {
        guard let gi = gift else { dismissPop(view: self.giftListView) ; return  }
        
        if let currentGift = currentGift, currentGift.id == gi.image?.id {
            return
        } else {
            if gi.enable == false {
                showGiftSelectedAlert(gi)
                return
            }
        }
        willSelectedGift(gi)
    }
    
    // 这个礼物已经添加了别的回复了
    func showGiftSelectedAlert(_ gift: DouyinGiftModel_Row) {
        
        AlertView.show(leftOption: .gray(title: "取消", action: nil), rightOption: .main(title: "确定", action: { [weak self] in
            guard let self = self else { return }
            self.replaceReplyItemModel(gift)
        }), title: "该礼物已设置过回复，现在要去修改吗？", message: "")
        
    }
    
    // 替换掉回复模型的卡片
    func replaceReplyItemModel(_ gift: DouyinGiftModel_Row) {
        guard let id = gift.image?.id else { return }

        guard let card = AIReplyDataManager.shared.giftCard(giftid: id) else {
            return
        }
        
        
        self.replyItemModel = card
        dismissPop(view: self.giftListView)
        bindViewModel()
        updateReplyView()
        updateGiftView()
        replyItemModel.modifyUpdateTime()
        updateGiftEnable()
    }
    
    
    func willSelectedGift(_ gift: DouyinGiftModel_Row) {
        
        self.gift_row = gift
        self.giftView.config(model: gift)
        //
        if  let image = gift.image {
            replyItemModel.gifts = [image]
        }
        replyItemModel.modifyUpdateTime()
        dismissPop(view: self.giftListView)
        updateReplyView()
        
        if isNew {
            AIReplyDataManager.shared.addNewCardItem(replyItem: replyItemModel)
        }
    }
    
}

// MARK: - 回复内容的代理
extension AIGiftReplyVc {
    
    
    func giftReplyQuestion() {
        let alertView = SmartAlertView()
        alertView.show(
            title: "触发礼物",
            content: "您最多可以添加1个触发礼物",
            in: self.view
        )
    }
    
    func replyQuestion() {
        let alertView = SmartAlertView()
        alertView.show(
            title: "回复内容",
            content: "1、点击【+】添加回复内容，语音和文字回复上限添加5个，若想更换回复类型，请先清空当前回复内容\n2、添加后，系统会根据触发礼物，随机播放1个回复内容",
            in: self.view
        )
    }
    
}

// MARK: - 区域新增素材
extension AIGiftReplyVc {
    // 更新回复的内容
    func updateReplyView() {
        
    }
    
    func updateGiftView() {
        // 刷新回复的礼物View
        if let gifts = replyItemModel.gifts, let info = gifts.first {
            self.gift_row =  DouyinGiftModel_Row(id: 0, giftInfo: "", valid: false, image: info, enable: true)
            self.giftView.config(model: self.gift_row)
        } else {
            self.giftView.config(model: nil)
        }
    }
   
    
}
