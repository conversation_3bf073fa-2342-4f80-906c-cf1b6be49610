//
//  ReplyManager.swift
//  LivePlus
//
//  Created by 郭炜 on 2025/2/19.
//

import UIKit

enum EventType {
    case message, gift, joinRoom, follow
}

struct ReplyConfig {
    let eventType: EventType
    let maxReplies: Int?             // 最大回复次数
    let maxFrequencyInSeconds: TimeInterval? // 最小回复间隔（秒）
    let userSpecific: Bool           // 是否对每用户独立控制
    let globalReplyCooldown: TimeInterval?   // 用户全局冷却时间（秒）
    let resetIntervalInSeconds: TimeInterval? // 计数重置间隔（秒）
}

class ReplyState {
    var lastReplyTimestamp: TimeInterval = 0    // 每种事件类型的最后回复时间
    var lastUserReplyTimestamp: TimeInterval = 0 // 用户全局最后回复时间
    var replyCount: Int = 0                     // 回复次数计数
}

class ReplyConfigManager {
    private var configs: [EventType: ReplyConfig] = [:]
    
    func addConfig(_ config: ReplyConfig) {
        configs[config.eventType] = config
    }
    
    func getConfig(for eventType: EventType) -> ReplyConfig? {
        return configs[eventType]
    }
}

class ReplyManager {
    private let configManager: ReplyConfigManager
    private var replyStates: [String: ReplyState] = [:]
    
    init(configManager: ReplyConfigManager) {
        self.configManager = configManager
    }
    
    func shouldReply(userId: String?, eventType: EventType, content: String? = nil, giftName: String? = nil) -> Bool {
        guard let config = configManager.getConfig(for: eventType) else { return false }
        let currentTime = Date().timeIntervalSince1970
        
        // 获取或初始化状态
        let userKey = config.userSpecific ? "\(userId ?? "")-" : ""
        let key = "\(userKey)\(eventType)-\(content ?? "")-\(giftName ?? "")"
        let state = replyStates[key] ?? ReplyState()
        
        // 全局冷却时间检查
        if let cooldown = config.globalReplyCooldown {
            if currentTime - state.lastUserReplyTimestamp < cooldown { return false }
        }
        
        // 重置计数检查
        if let resetInterval = config.resetIntervalInSeconds {
            if currentTime - state.lastReplyTimestamp > resetInterval {
                state.replyCount = 0
            }
        }
        
        // 频率检查
        if let maxFrequency = config.maxFrequencyInSeconds {
            if currentTime - state.lastReplyTimestamp < maxFrequency { return false }
        }
        
        // 最大次数检查
        if let maxReplies = config.maxReplies {
            if state.replyCount >= maxReplies { return false }
        }
        
        // 更新状态
        state.lastReplyTimestamp = currentTime
        state.replyCount += 1
        state.lastUserReplyTimestamp = currentTime
        replyStates[key] = state
        
        return true
    }
    
    func getLastUserReplyTime(userId: String) -> TimeInterval? {
        return replyStates["\(userId)-ALL"]?.lastUserReplyTimestamp
    }
}

// 使用示例
/*
let configManager = ReplyConfigManager()

configManager.addConfig(ReplyConfig(
    eventType: .message,
    maxReplies: 5,
    maxFrequencyInSeconds: 60,
    userSpecific: true,
    globalReplyCooldown: 180,
    resetIntervalInSeconds: nil
))

configManager.addConfig(ReplyConfig(
    eventType: .gift,
    maxReplies: 3,
    maxFrequencyInSeconds: 120,
    userSpecific: true,
    globalReplyCooldown: nil,
    resetIntervalInSeconds: 86400
))

let replyManager = ReplyManager(configManager: configManager)

let userId = "user123"
let shouldReplyToMessage = replyManager.shouldReply(userId: userId, eventType: .message, content: "hello")
print("Should reply to message: \(shouldReplyToMessage)")

let shouldReplyToGift = replyManager.shouldReply(userId: userId, eventType: .gift, giftName: "flower")
print("Should reply to gift: \(shouldReplyToGift)")

if let lastReplyTime = replyManager.getLastUserReplyTime(userId: userId) {
    print("Last reply time for user \(userId): \(lastReplyTime)")
}
*/
