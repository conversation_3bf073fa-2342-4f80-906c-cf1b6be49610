//
//  DouyinMessageHanlerQueue.swift
//  LivePlus
//
//  Created by 郭炜 on 2025/2/19.
//

import UIKit
import Foundation

class DouyinMessageHandlerQueue {
    private var handlerRunnables: [(DouyinMessageData) -> Void] = []
    private var messageQueue: [DouyinMessageData] = []
    private var isProcessing = false
    private let queue = DispatchQueue(label: "com.douyin.message.queue", qos: .userInitiated)
    
    func addHandlerRunnable(_ handler: @escaping (DouyinMessageData) -> Void) {
        queue.async { [weak self] in
            self?.handlerRunnables.append(handler)
        }
    }
    
    func clearHandlerRunnable() {
        queue.async { [weak self] in
            self?.handlerRunnables.removeAll()
        }
    }
    
    func enqueue(_ messageData: DouyinMessageData) {
        queue.async { [weak self] in
            guard let self = self else { return }
            self.messageQueue.append(messageData)
            if !self.isProcessing {
                self.processQueue()
            }
        }
    }
    
    func cancelAll() {
        queue.async { [weak self] in
            self?.messageQueue.removeAll()
            self?.isProcessing = false
        }
    }
    
    private func processQueue() {
        queue.async { [weak self] in
            guard let self = self else { return }
            
            self.isProcessing = true
            
            while !self.messageQueue.isEmpty {
                let messageData = self.messageQueue.removeFirst()
                
                // 对每个处理器执行消息处理
                for handler in self.handlerRunnables {
                    handler(messageData)
                }
            }
            
            self.isProcessing = false
        }
    }
}
