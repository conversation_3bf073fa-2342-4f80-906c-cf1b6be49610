//
//  AutoReplyManager.swift
//  LivePlus
//
//  Created by 郭炜 on 2025/2/19.
//

import UIKit
import Foundation

typealias ReplayCallback = (_ messageData: DouyinMessageData) -> Void

class AutoReplyManager {
    
    static let shared = AutoReplyManager()
    
    private var douyinMessageHandlerQueue: DouyinMessageHandlerQueue?
    
    private var replyGiftRunnable: ReplayCallback?
    private var replyChatRunnable: ReplayCallback?
    private var replyWelcomeRunnable: ReplayCallback?
    private var replyFollowRunnable: ReplayCallback?
    
    private var configManager: ReplyConfigManager
    private let replyManager: ReplyManager
    
    init() {
        self.configManager = ReplyConfigManager()
        self.replyManager = ReplyManager(configManager: configManager)
        
        douyinMessageHandlerQueue = DouyinMessageHandlerQueue()
        douyinMessageHandlerQueue?.addHandlerRunnable { [weak self] messageData in
            guard let self = self else { return }
            
            let userId = messageData.user?.displayId
            
            switch messageData.msgType {
            case .CHAT:
//                不需要计算是否回复
//                let shouldReply = self.replyManager.shouldReply(
//                    userId: userId,
//                    eventType: .message,
//                    content: messageData.chatInfo?.content
//                )
//                if shouldReply {
                    self.replyChatRunnable?(messageData)
//                }
                
            case .GIFT:
                //                不需要计算是否回复
//                let shouldReply = self.replyManager.shouldReply(
//                    userId: userId,
//                    eventType: .gift,
//                    giftName: messageData.giftInfo?.gift?.name
//                )
//                if shouldReply {
                    self.replyGiftRunnable?(messageData)
//                }
                
            case .SOCIAL:
                //                不需要计算是否回复
//                let shouldReply = self.replyManager.shouldReply(
//                    userId: userId,
//                    eventType: .follow
//                )
//                if shouldReply {
                    self.replyFollowRunnable?(messageData)
//                }
                
            default:
                break
            }
        }
    }
    
    func setConfigManager(_ configManager: ReplyConfigManager) {
        self.configManager = configManager
    }
    
    func getConfigManager() -> ReplyConfigManager {
        return configManager
    }
    
    func setReplyGiftRunnable(_ runnable: @escaping ReplayCallback) {
        replyGiftRunnable = runnable
    }
    
    func setReplyChatRunnable(_ runnable: @escaping ReplayCallback) {
        replyChatRunnable = runnable
    }
    
    func setReplyWelcomeRunnable(_ runnable: @escaping ReplayCallback) {
        replyWelcomeRunnable = runnable
    }
    
    func setReplyFollowRunnable(_ runnable: @escaping ReplayCallback) {
        replyFollowRunnable = runnable
    }

    func enqueue(_ messageData: DouyinMessageData) {
        douyinMessageHandlerQueue?.enqueue(messageData)
    }
    
    func release() {
        douyinMessageHandlerQueue?.clearHandlerRunnable()
        douyinMessageHandlerQueue?.cancelAll()
    }
}
