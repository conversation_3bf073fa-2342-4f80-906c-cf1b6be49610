//
//  GiftFilterCell.swift
//  LivePlus
//
//  Created by simon on 10.1.25.
//

import Foundation

class GiftFilterCell: UITableViewCell {
    
   var _model: GiftFilterModel?
    
    public var backView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor("#3A3A3C")
        return view
    }()
    

    private lazy var titleLab: UILabel = {
        let label = UILabel()
        label.textColor = UIColor.white
        label.font = UIFont.systemFont(ofSize: 14, weight: .regular)
        label.backgroundColor = .clear
        return label
    }()
    

    override func awakeFromNib() {
        super.awakeFromNib()
    }

    override func setSelected(_ selected: <PERSON><PERSON>, animated: Bool) {
        super.setSelected(selected, animated: animated)
    }
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        //cell点击无选中色
        let selectedView = UIView(frame: self.frame)
        selectedView.backgroundColor = .clear
        self.selectedBackgroundView = selectedView
        self.backgroundColor = .clear
        
        makeUI()
        
    }
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    func makeUI() {
        contentView.addSubview(backView)
        backView.addSubviews([titleLab])

        backView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        titleLab.snp.makeConstraints { make in
            make.leading.equalToSuperview().inset(20)
            make.centerY.equalToSuperview()
        }
        
    }
    

}

extension GiftFilterCell {
    func bind(to model: GiftFilterModel) {
        _model = model
        titleLab.text = model.type.title
        titleLab.textColor = model.selected ? UIColor("#ADAAFF") : .white
    }
}
