//
//  DouyinGiftListView.swift
//  LivePlus
//
//  Created by simon on 9.1.25.
//

import Foundation


protocol DouyinGiftListViewDelegate: NSObjectProtocol {
    func didAction(gift: DouyinGiftModel_Row?)
}

class DouyinGiftListView: UIView {
    
    weak var delegate:DouyinGiftListViewDelegate?
    
    var sort: DouyinGiftSort = .none
    
    lazy var filterView: GiftFilterView = {
        let v = GiftFilterView()
        v.delegate = self
        v.isHidden = true
        return v
    }()
    
    lazy var nameLab: UILabel = {
        let v = UILabel()
        v.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        v.textColor = UIColor.white
        v.text = "请选择礼物"
        return v
    }()
    
    
    lazy var line2: UIView = {
        let v = UIView()
        v.backgroundColor = UIColor("#352E4D")
        return v
    }()
    
    lazy var priceButton: UIButton = {
        let button = UIButton()
        button.setTitle("价格", for: .normal)
        button.setImage(UIImage(named: "礼物排序"), for: .normal)
        button.setTitleColor(UIColor("#ACACAC"), for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 14)
        button.imagePosition(style: .left, spacing: 4)
        button.addTarget(self, action: #selector(priceAction), for: .touchUpInside)
        
        return button
    }()
    
    /// 空视图
    private lazy var emptyImage: UIImageView = {
        let view = UIImageView()
        view.image = UIImage(named: "占位图_上传素材_空空如也")
        view.isHidden = true
        return view
    }()
    
    
    lazy var closeButton: UIButton = {
        let button = UIButton()
        button.setImage(UIImage(named: "gift_close"), for: .normal)
        button.addTarget(self, action: #selector(closeAction), for: .touchUpInside)
        button.zl_enlargeValidTouchArea(inset: 10)
        return button
    }()
    
    lazy var filterButton: UIButton = {
        let button = UIButton(frame: CGRect(x: 0, y: 0, width: 100, height: 40))
        button.setTitle("推荐", for: .normal)
        button.setImage(UIImage(named: "gift_tuijian"), for: .normal)
        button.setTitleColor(UIColor("#ACACAC"), for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        button.imagePosition(style: .right, spacing: 8)
        button.addTarget(self, action: #selector(filterAction), for: .touchUpInside)
        
        return button
    }()
    
    lazy var keyText: UITextField = {
        let textField = UITextField()
        let plAttStr = NSMutableAttributedString(string: "请输入关键字")
        plAttStr.color = UIColor.white.alpha(value: 0.7)
        plAttStr.font = UIFont.systemFont(ofSize: 14, weight: .regular)
        textField.attributedPlaceholder = plAttStr
        textField.textAlignment = .left
        textField.borderStyle = .none
        textField.textColor = UIColor.white
        textField.returnKeyType = .done
        textField.delegate = self
        textField.backgroundColor = .clear
        return textField
    }()
    
    // 搜索主页面
    lazy var searchView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor("#444444")
        view.cornerRadius = 18
        return view
    }()
    
    // 搜查
    lazy var searchButton: UIButton = {
        let button = UIButton(frame: CGRect(x: 0, y: 0, width: 100, height: 40))
        button.setTitle("搜索", for: .normal)
        button.setTitleColor(UIColor("#ADAAFF"), for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 14)
        button.imagePosition(style: .right, spacing: 8)
        button.addTarget(self, action: #selector(searchAction), for: .touchUpInside)
        return button
    }()
    
    lazy var myCollectionView: UICollectionView = {
        //滚动列表
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .horizontal
        layout.minimumLineSpacing = 8 //行间距
        layout.minimumInteritemSpacing = 8 //列间距最小值
        layout.itemSize = CGSize(width: (LCDevice.screenW - 24 - 16) / 4, height: 110)
        layout.scrollDirection = .vertical
        let rect = CGRect(x: 0, y: 0, width: LCDevice.screenW, height: 300)
        let collectionView = UICollectionView(frame: rect, collectionViewLayout: layout)
        collectionView.backgroundColor = UIColor("#282828")
        collectionView.delegate = self
        collectionView.dataSource = self
        collectionView.register(cellWithClass: DouyinGiftCell.self)
        
        return collectionView
    }()
    
    lazy var bgView: UIView = {
        let v = UIView()
        v.backgroundColor = UIColor("#282828")
        v.cornerRadius = 30
        return v
    }()
    
    var filter: GiftFilterModel?
    
    var data: DouyinGiftModel?
    
    var sources: [DouyinGiftModel_Row] = []
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        makeUI()
        business()
    }
    
    convenience init() {
        self.init(frame: .zero)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    
    func sortByDiamondCountDescending() {
        sources.sort(by: {
            guard let first = $0.image?.diamondCount, let second = $1.image?.diamondCount  else {
                return false
            }
            let firstCount: Int64 = Int64(first) ?? 0
            let secondCount: Int64 = Int64(second) ?? 0
            
            return firstCount  > secondCount
        })
        
    }
    
    func sortByDiamondCountAscending() {
        sources.sort(by: {
            guard let first = $0.image?.diamondCount, let second = $1.image?.diamondCount  else {
                return false
            }
            let firstCount: Int64 = Int64(first) ?? 0
            let secondCount: Int64 = Int64(second) ?? 0
            
            return firstCount  < secondCount
        })
    }
    
    @objc func closeAction() {
        self.delegate?.didAction(gift: nil)
    }
    
    
    @objc func priceAction() {
        // 价格筛选是在当前的数据的基础上
        switch self.sort {
        case .none:
            self.sort = .ascending
            self.priceButton.setImage(UIImage(named: "礼物升序"), for: .normal)
            self.priceButton.setTitleColor(UIColor("#ADAAFF"), for: .normal)
        case .ascending:
            self.sort = .descending
            sortByDiamondCountDescending()
            self.priceButton.setImage(UIImage(named: "礼物降序"), for: .normal)
            self.priceButton.setTitleColor(UIColor("#ADAAFF"), for: .normal)
        case .descending:
            self.sort = .ascending
            sortByDiamondCountAscending()
            self.priceButton.setImage(UIImage(named: "礼物升序"), for: .normal)
            self.priceButton.setTitleColor(UIColor("#ADAAFF"), for: .normal)
        }
        collectionViewReloadData()
        
    }
    
    @objc func filterAction() {
       resetData()
       self.filterView.isHidden = false
    }
    
    
    @objc func searchAction() {
        // 搜索时把过滤条件去掉
        filterView.titles.forEach({$0.selected = false})
        self.filter = filterView.titles.first(where: {$0.type == .all})
        self.filter?.selected = true
        
        fliterData()
        
        
        self.keyText.resignFirstResponder()
        
        guard let text = self.keyText.text, !text.isEmpty else {
            resetData()
            collectionViewReloadData()
            return
        }
        
        filterButton.setTitleColor(UIColor("#ACACAC"), for: .normal)
        //
        let messages = sources.filter { $0.image!.name.contains(text) }
        
        sources.removeAll()
        sources.append(contentsOf: messages)
        
        collectionViewReloadData()
    }
 
    
    func makeUI() {
        
        addSubview(bgView)
        bgView.addSubviews([nameLab, priceButton, filterButton, searchView, self.myCollectionView, filterView, closeButton, emptyImage])
        
        searchView.addSubviews([keyText, line2, searchButton])
        
        bgView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        filterView.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview()
            make.bottom.top.equalToSuperview()
        }
        
        nameLab.snp.makeConstraints { make in
            make.leading.equalToSuperview().inset(20)
            make.top.equalToSuperview().inset(15)
            make.height.equalTo(22)
        }
       
        
        priceButton.snp.makeConstraints { make in
            make.trailing.equalToSuperview().inset(55)
            make.centerY.equalTo(nameLab.snp.centerY)
            make.height.equalTo(32)
            make.width.equalTo(50)
        }
        
        closeButton.snp.makeConstraints { make in
            make.trailing.equalToSuperview().inset(12)
            make.centerY.equalTo(nameLab.snp.centerY)
            make.height.equalTo(22)
            make.width.equalTo(22)
        }
        
        filterButton.snp.makeConstraints { make in
            make.leading.equalToSuperview().inset(12)
            make.top.equalToSuperview().inset(56)
            make.height.equalTo(36)
            make.width.equalTo(100)
        }
        
        searchView.snp.makeConstraints { make in
            make.trailing.equalToSuperview().inset(20)
            make.top.equalToSuperview().inset(56)
            make.height.equalTo(36)
            make.width.equalTo(210)
        }
        
        
        keyText.snp.makeConstraints { make in
            make.leading.equalToSuperview().inset(10)
            make.centerY.equalToSuperview()
            make.height.equalTo(36)
            make.width.equalTo(120)
        }
        
        line2.snp.makeConstraints { make in
            make.leading.equalToSuperview().inset(148)
            make.centerY.equalToSuperview()
            make.height.equalTo(16)
            make.width.equalTo(1)
        }
        
        searchButton.snp.makeConstraints { make in
            make.trailing.equalToSuperview().inset(12)
            make.centerY.equalToSuperview()
            make.height.equalTo(32)
            make.width.equalTo(40)
        }
        
        myCollectionView.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview()
            make.bottom.equalToSuperview().inset(32)
            make.top.equalToSuperview().inset(110)
        }
        
        emptyImage.snp.makeConstraints { make in
            make.centerX.centerY.equalToSuperview()
            make.width.height.equalTo(120)
        }
        
    }
    
    
    func business() {
        
    }
    
    
    // 重置所有的数据
    func resetData() {
        self.sort = .none
        self.priceButton.setImage(UIImage(named: "礼物排序"), for: .normal)
        self.priceButton.setTitleColor(UIColor("#ACACAC"), for: .normal)
        self.sources.removeAll()
        if let data = self.data {
            self.sources.append(contentsOf: data.records)
        }
    }
    
    
    func fliterData() {
                
        resetData()
        
        guard let filter = self.filter else {
            collectionViewReloadData()
            return
        }
        
        var w = filter.type.title.boundingRect(font: UIFont.systemFont(ofSize: 14, weight: .medium), limitSize: CGSize(width: CGFloat.greatestFiniteMagnitude, height: 22)).width + 16
        if w < 60 {
            w = 60
        }
        
        filterButton.snp.updateConstraints { make in
            make.width.equalTo(w)
        }
        
        filterButton.setTitleColor(UIColor("#ADAAFF"), for: .normal)
        filterButton.setTitle(filter.type.title, for: .normal)
        
        filterButton.imagePosition(style: .right, spacing: 8)
        
        if filter.type == .all || filter.type == .recommend {
            collectionViewReloadData()
            return
        }
        
        var datas:[DouyinGiftModel_Row] = []
        
        sources.forEach { row in
            if let image = row.image {
                let count: Int64 = Int64(image.diamondCount) ?? 0
                
                switch filter.type {
                case .all:
                    break
                case .less100:
                    if count <= 100 {
                        datas.append(row)
                    }
                case .less2000:
                    if count <= 2000, count > 100 {
                        datas.append(row)
                    }
                case .less9999:
                    if count <= 9999, count > 2000 {
                        datas.append(row)
                    }
                case .more10000:
                    if  count >= 10000 {
                        datas.append(row)
                    }
                case .recommend:
                    break
                }
            }
            
            sources.removeAll()
            sources.append(contentsOf: datas)
            
            collectionViewReloadData()
            
        }
        
    }
    
    func collectionViewReloadData() {
        self.emptyImage.isHidden =  self.sources.count > 0
        myCollectionView.reloadData()
    }
    
}

extension DouyinGiftListView: UICollectionViewDelegate, UICollectionViewDataSource, UICollectionViewDelegateFlowLayout {
    
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, insetForSectionAt section: Int) -> UIEdgeInsets {
        return UIEdgeInsets.init(top: 8, left: 8, bottom: 0, right: 8)
    }
    
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return self.sources.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withClass: DouyinGiftCell.self, for: indexPath)
        guard indexPath.row < self.sources.count else { return cell }
        let curModel = self.sources[indexPath.row]
        cell.config(model: curModel)
        
        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        self.keyText.resignFirstResponder()
        let curModel = self.sources[indexPath.row]
        
        self.delegate?.didAction(gift: curModel)
    }
    
}

//
extension DouyinGiftListView: GiftFilterViewProtocol {
    func didDismiss(model: GiftFilterModel?) {
        self.filterView.isHidden = true
        self.filter = model
        fliterData()
        self.keyText.text = nil
        self.keyText.resignFirstResponder()
    }
    
}

// public
extension DouyinGiftListView {
    func updateData(data: DouyinGiftModel) {
        self.data = data
        self.sources.removeAll()
        self.keyText.text = nil
        self.keyText.resignFirstResponder()
        self.sort = .none
        self.priceButton.setImage(UIImage(named: "礼物排序"), for: .normal)
        self.priceButton.setTitleColor(UIColor("#ACACAC"), for: .normal)
        
        filterView.titles.forEach({$0.selected = false})
        self.filter = filterView.titles.first(where: {$0.type == .recommend})
        self.filter?.selected = true
        
        self.filterButton.setTitle(DouyinGiftFilter.recommend.title, for: .normal)
        self.filterButton.setTitleColor(UIColor("#ACACAC"), for: .normal)
        
        
        
        var w = DouyinGiftFilter.recommend.title.boundingRect(font: UIFont.systemFont(ofSize: 14, weight: .medium), limitSize: CGSize(width: CGFloat.greatestFiniteMagnitude, height: 22)).width + 16
        if w < 60 {
            w = 60
        }
        
        filterButton.snp.updateConstraints { make in
            make.width.equalTo(w)
        }
        filterButton.imagePosition(style: .right, spacing: 8)
        self.sources.append(contentsOf: data.records)
        
        collectionViewReloadData()
        
    }
}

extension DouyinGiftListView: UITextFieldDelegate {
    func textField(_ textField: UITextField, shouldChangeCharactersIn range: NSRange, replacementString string: String) -> Bool {
        if string == "\n" {
            searchAction()
            return false
        }
        return true
    }
}
