//
//  AIGiftReplyContentView.swift
//  LivePlus
//
//  Created by simon on 13.1.25.
//

// 礼物的恢复内容
import Foundation


enum AIGiftReplyContentViewType {
    case add
    case delet
    case help
}

protocol AIGiftReplyContentViewDelegate: NSObjectProtocol {
    func didAction(item: AreaItemModel?, type: AIGiftReplyContentViewType)
    /// 预览当前素材
    func didPreview(model: AreaItemModel)
}


class AIGiftReplyContentView: UIView {
    
    weak var delegate: AIGiftReplyContentViewDelegate?
    
    ///封面图
    lazy var itemView: GiftReplyItemView = {
        let v = GiftReplyItemView()
        v.isHidden = true
        return v
    }()
    
    
    lazy var audioView: GiftReplyAudioView = {
        let v = GiftReplyAudioView()
        return v
    }()
    
    
    lazy var line: UIView = {
        let v = UIView()
        v.backgroundColor = UIColor("#ADAAFF")
        return v
    }()
    
    lazy var titleLab: UILabel = {
        let v = UILabel()
        v.font = UIFont.systemFont(ofSize: 14, weight: .regular)
        v.textColor = .white
        v.text = "回复内容"
        return v
    }()
    
    lazy var nameLab: UILabel = {
        let v = UILabel()
        v.font = UIFont.systemFont(ofSize: 14, weight: .regular)
        v.textColor = UIColor("#FCFCFC")
        v.textAlignment = .center
        return v
    }()
    
    lazy var priceLab: UILabel = {
        let v = UILabel()
        v.font = UIFont.systemFont(ofSize: 12, weight: .regular)
        v.textColor = UIColor("#ACACAC")
        return v
    }()
    
    lazy var helpButton: UIButton = {
        let button = UIButton()
        button.setImage(UIImage(named: "Animation_帮助"), for: .normal)
        button.addTarget(self, action: #selector(helpAction), for: .touchUpInside)
        return button
    }()
    
    
    lazy var addButton: UIButton = {
        let button = UIButton()
        button.setImage(UIImage(named: "green_add"), for: .normal)
        button.addTarget(self, action: #selector(addAction), for: .touchUpInside)
        return button
    }()
    
    lazy var minButton: UIButton = {
        let button = UIButton()
        button.setImage(UIImage(named: "直播间_弹窗_组件_icon_删除"), for: .normal)
        button.addTarget(self, action: #selector(deletAction), for: .touchUpInside)
        return button
    }()
    
    
    var gift: DouyinGiftModel_Row?
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        makeUI()
        business()
    }
    
    deinit {
        
    }
    
    convenience init() {
        self.init(frame: .zero)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func makeUI() {
        
        self.backgroundColor = UIColor.clear
        addSubviews([line, helpButton, nameLab, itemView, audioView, titleLab,  addButton, minButton])
        
        itemView.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(33)
            make.height.width.equalTo(104)
            make.leading.equalToSuperview().inset(16)
        }
        
        audioView.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(33)
            make.leading.trailing.bottom.equalToSuperview()
        }
        
        line.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(7)
            make.height.equalTo(14)
            make.width.equalTo(4)
            make.leading.equalToSuperview().inset(18)
        }
        
        titleLab.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(4)
            make.height.equalTo(20)
            make.width.equalTo(60)
            make.leading.equalToSuperview().inset(32)
        }
        
        
        
        helpButton.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.height.equalTo(28)
            make.width.equalTo(28)
            make.leading.equalTo(titleLab.snp.trailing)
        }
        
        
        minButton.snp.makeConstraints { make in
            make.trailing.equalToSuperview().inset(8)
            make.height.equalTo(28)
            make.width.equalTo(40)
            make.top.equalToSuperview()
        }
        
      
        addButton.snp.makeConstraints { make in
            make.trailing.equalTo(minButton.snp.leading).offset(-12)
            make.height.equalTo(28)
            make.width.equalTo(40)
            make.top.equalToSuperview()
        }

    }
    
    @objc func addAction() {
        self.delegate?.didAction(item: nil, type: .add)
    }
    
    @objc func helpAction() {
        self.delegate?.didAction(item: nil, type: .help)
    }
   
    
    @objc func deletAction() {
        self.delegate?.didAction(item: nil, type: .delet)
    }
    
    func business() {
        
        itemView.didPreview = {  [weak self] item in
            guard let self = self else { return }
            
            if let delegate = self.delegate {
                delegate.didPreview(model: item)
            }
        }
    }
    
    public func config(item: AreaItemModel?) {
        guard let item = item else { 
            self.itemView.isHidden = true
            return
        }
        self.itemView.config(with: item)
        self.itemView.isHidden = false
        self.audioView.isHidden = true
       
    }
    
    
    
}

