//
//  GiftReplyItemView.swift
//  LivePlus
//
//  Created by simon on 13.1.25.
//

import Foundation


class GiftReplyItemView: UIView {
    
    public var didPreview: ((AreaItemModel) -> Void)?
    
    ///封面图
    lazy var coverV: UIImageView = {
        let v = UIImageView()
        v.backgroundColor = .clear
        v.contentMode = .scaleAspectFill
        v.cornerRadius = 6
        return v
    }()
    
    lazy var typeInfoLab: UILabel = {
        let v = UILabel()
        v.font = UIFont.systemFont(ofSize: 12, weight: .medium)
        v.textColor = UIColor("#E6E6E6")
        v.backgroundColor = UIColor.black.alpha(value: 0.5)
        v.cornerRadius = 2
        v.textAlignment = .center
        return v
    }()
    
    
    private lazy var previewButton: UIButton = {
        let button = UIButton()
        button.setImage(UIImage(named: "选择素材_icon_预览"), for: .normal)
        return button
    }()
    
    lazy var vipLogo: UIButton = {
        let button = UIButton()
        button.setImage(UIImage(named: "vip_source"), for: .normal)
        button.imageView?.contentMode = .scaleAspectFit
        button.isHidden = true
        button.isUserInteractionEnabled = false
        return button
    }()
    

    private var model:AreaItemModel?
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
        previewButton.addTarget(self, action: #selector(previewAction), for: .touchUpInside)
        previewButton.zl_enlargeValidTouchArea(inset: 16)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    @objc func previewAction() {
        if let didPreview = didPreview, let m = self.model {
            didPreview(m)
        }
    }
    
    private func setupUI() {
        backgroundColor = UIColor.clear
        cornerRadius = 6
        addSubviews([coverV, typeInfoLab, vipLogo, previewButton])

        coverV.snp.makeConstraints { make in
            make.leading.top.bottom.trailing.equalToSuperview()
        }

        typeInfoLab.snp.makeConstraints { make in
            make.leading.equalToSuperview().inset(4)
            make.height.equalTo(16)
            make.bottom.equalToSuperview().inset(4)
        }
        
        vipLogo.snp.makeConstraints { make in
            make.trailing.top.equalToSuperview()
            make.height.equalTo(16)
            make.width.equalTo(30)
        }
        
        previewButton.snp.makeConstraints { make in
            make.trailing.bottom.equalToSuperview().inset(2)
            make.height.equalTo(24)
            make.width.equalTo(24)
        }
    }
    
    // MARK: - Public methods
  
   
    // MARK: - private method
    /// cell配置
    public func config(with model: AreaItemModel) {

        self.model = model
        
        self.coverV.isHidden = true
        self.vipLogo.isHidden = true
        
    
    }
}



class VideoReplyItemView: UIView {
        
    ///封面图
    lazy var coverV: UIImageView = {
        let v = UIImageView()
        v.backgroundColor = .clear
        v.contentMode = .scaleAspectFill
        v.cornerRadius = 6
        return v
    }()

    private var model:AreaItemModel?
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        backgroundColor = UIColor.clear
        cornerRadius = 6
        addSubviews([coverV])

        coverV.snp.makeConstraints { make in
            make.leading.top.bottom.trailing.equalToSuperview()
        }

    }
    
    // MARK: - Public methods
  
   
    // MARK: - private method
    /// cell配置
    public func config(with model: AreaItemModel) {

        self.model = model
        
        self.coverV.isHidden = true
        
    }
}
