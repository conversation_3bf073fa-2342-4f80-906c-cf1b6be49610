//
//  SmartActionCardView.swift
//  LivePlus
//
//  Created by simon on 14.2.25.
//

import Foundation

enum SmartActionCardViewAction {
    case edit
    case delet
    case on_off
}


protocol SmartActionCardViewDelegate: NSObjectProtocol {
    func didAction(to action: SmartActionCardViewAction, model: AiKeywordVoiceReplyItemModel)
}
   
    
class SmartActionCardView: UITableViewCell {
    
    weak var delegate: SmartActionCardViewDelegate?
    
    lazy var background: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor("#282828")
        view.cornerRadius = 12
        view.borderColor = UIColor("#FFFFFF").alpha(value: 0.07)
        view.borderWidth = 1
        return view
    }()
    
    lazy var replView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor("#111111")
        view.cornerRadius = 6
        return view
    }()
    
    lazy var shouImage: UILabel = {
        let imageView = UILabel()
        imageView.text = "评"
        imageView.textColor = .white
        imageView.font = UIFont.systemFont(ofSize: 12, weight: .medium)
        return imageView
    }()
    
    lazy var huiImage: UILabel = {
        let imageView = UILabel()
        imageView.text = "回"
        imageView.textColor = .white
        imageView.font = UIFont.systemFont(ofSize: 12, weight: .medium)
        return imageView
    }()
    
    lazy var itemImage: UIImageView = {
        let imageView = UIImageView(image: UIImage(named: "smart_card_ci"))
        return imageView
    }()
    
    // 音频 下
    lazy var audioView: SmartCardAudioView = {
        let label = SmartCardAudioView()
        return label
    }()
    
   // 礼物回复的上
    lazy var giftView: SmartCardGiftView = {
        let label = SmartCardGiftView()
        return label
    }()
    
    // 关注的回复上
    lazy var followView: SmartCardFollowView = {
        let label = SmartCardFollowView()
        return label
    }()
    
    // 关键词回复的评论部分 上
    lazy var keyView: SmartCardKeyWordView = {
        let label = SmartCardKeyWordView()
        return label
    }()
    
    // 回复的是文本内容
    lazy var textView: SmartCardTextView = {
        let label = SmartCardTextView()
        return label
    }()
    
    
    private var gradientLayer: CAGradientLayer = {
        let leftColor = UIColor("#111111").alpha(value: 0.0)
        let rightColor = UIColor("#111111")
        let gradientColors = [leftColor.cgColor, rightColor.cgColor]
        let gradientLocations: [NSNumber] = [0.0, 1.0]
        let gradientLayer = CAGradientLayer()
        gradientLayer.startPoint = CGPoint(x: 0, y: 0.5)
        gradientLayer.endPoint = CGPoint(x: 1, y: 0.5)
        gradientLayer.colors = gradientColors
        gradientLayer.locations = gradientLocations
        return gradientLayer
    }()
    
    lazy var shadeView: UIView = {
        let layerView = UIView(frame: CGRect(x: 0, y: 0, width: 26, height: 106))
        
        return layerView
    }()
    
    
    // 编辑
    lazy var editButton: UIButton = {
        let button = UIButton()
        button.setImage(UIImage(named: "smart_card_edit"), for: .normal)
        button.zl_enlargeValidTouchArea(inset: 14)
        return button
    }()
    
    // shanc
    lazy var deletButton: UIButton = {
        let button = UIButton()
        button.setImage(UIImage(named: "smart_card_det"), for: .normal)
        button.zl_enlargeValidTouchArea(inset: 14)
        return button
    }()
    
    // open
    lazy var openButton: UIButton = {
        let button = UIButton()
        button.setImage(UIImage(named: "smart_card_open"), for: .normal)
        button.zl_enlargeValidTouchArea(inset: 14)
        return button
    }()
    

    weak var model: AiKeywordVoiceReplyItemModel?
    
    override func awakeFromNib() {
        super.awakeFromNib()
    }
    
    override func setSelected(_ selected: Bool, animated: Bool) {
        super.setSelected(selected, animated: animated)
    }
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        makeUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func layoutSubviews() {
        super.layoutSubviews()
        
        gradientLayer.frame = shadeView.bounds
        shadeView.layer.insertSublayer(gradientLayer, at: 0)
    }
    
    func makeUI() {
        selectionStyle = .none
        backgroundColor = .clear
        contentView.addSubview(background)
        
        gradientLayer.frame = shadeView.bounds
        shadeView.layer.insertSublayer(gradientLayer, at: 0)
        
        background.addSubviews([itemImage, replView, editButton, deletButton, openButton])
        replView.addSubviews([shouImage, huiImage, giftView, followView, keyView, audioView, textView, shadeView])
        
        background.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.leading.trailing.equalToSuperview().inset(16)
            make.bottom.equalToSuperview().inset(10)
        }
        
        itemImage.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(16)
            make.leading.equalToSuperview().inset(20)
            make.size.equalTo(24)
        }
        
        openButton.snp.makeConstraints { make in
            make.trailing.equalToSuperview().inset(16)
            make.top.equalToSuperview().inset(12)
            make.height.width.equalTo(32)
        }
        
        
        deletButton.snp.makeConstraints { make in
            make.trailing.equalTo(openButton.snp.leading).offset(-12)
            make.top.equalToSuperview().inset(12)
            make.height.width.equalTo(32)
        }
        
        
        editButton.snp.makeConstraints { make in
            make.trailing.equalTo(deletButton.snp.leading).offset(-12)
            make.top.equalToSuperview().inset(12)
            make.height.width.equalTo(32)
        }
        
        replView.snp.makeConstraints { make in
            make.leading.bottom.trailing.equalToSuperview().inset(12)
            make.top.equalToSuperview().inset(52)
        }
        
        shouImage.snp.makeConstraints { make in
            make.leading.equalToSuperview().inset(12)
            make.top.equalToSuperview().inset(12)
            make.width.equalTo(20)
            make.height.equalTo(37)
        }
        
        huiImage.snp.makeConstraints { make in
            make.leading.equalToSuperview().inset(12)
            make.bottom.equalToSuperview().inset(12)
            make.width.equalTo(20)
            make.height.equalTo(37)
        }
        
        giftView.snp.makeConstraints { make in
            make.centerY.equalTo(shouImage.snp.centerY)
            make.leading.equalToSuperview().inset(35)
            make.height.equalTo(37)
            make.trailing.equalToSuperview()
        }
        
        keyView.snp.makeConstraints { make in
            make.centerY.equalTo(shouImage.snp.centerY)
            make.leading.equalToSuperview().inset(35)
            make.height.equalTo(37)
            make.trailing.equalToSuperview()
        }
        
        followView.snp.makeConstraints { make in
            make.centerY.equalTo(shouImage.snp.centerY)
            make.leading.equalToSuperview().inset(35)
            make.height.equalTo(37)
            make.trailing.equalToSuperview()
        }
        
        audioView.snp.makeConstraints { make in
            make.centerY.equalTo(huiImage.snp.centerY)
            make.leading.equalToSuperview().inset(35)
            make.height.equalTo(37)
            make.trailing.equalToSuperview()
        }
        
        textView.snp.makeConstraints { make in
            make.centerY.equalTo(huiImage.snp.centerY)
            make.leading.equalToSuperview().inset(35)
            make.height.equalTo(37)
            make.trailing.equalToSuperview()
        }
        
        shadeView.snp.makeConstraints { make in
            make.width.equalTo(26)
            make.height.equalTo(106)
            make.trailing.equalToSuperview()
            make.centerY.equalToSuperview()
        }
        
        editButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self, let m = self.model else { return }

                self.delegate?.didAction(to: .edit, model: m)
            }.disposed(by: rx.disposeBag)
        
        deletButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self, let m = self.model else { return }
                self.delegate?.didAction(to: .delet, model: m)
            }.disposed(by: rx.disposeBag)
        
        openButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self, let m = self.model else { return }
                self.delegate?.didAction(to: .on_off, model: m)
            }.disposed(by: rx.disposeBag)
        
    }

   
}

extension SmartActionCardView {
    
    func bind(to model: AiKeywordVoiceReplyItemModel) {
        self.model = model
        
        if model.enable {
            self.openButton.setImage(UIImage(named: "smart_card_open"), for: .normal)
        } else {
            self.openButton.setImage(UIImage(named: "smart_card_close"), for: .normal)
        }
        
        switch model.interactionType {
        case .userChat:
            updateForUserChat(model: model)
            shouImage.text = "评"
        case .systemInteraction:
            updateForSystem(model: model)
            shouImage.text = "收"
        }
        
    }
    
    // 用户评论的回复
    func updateForUserChat(model: AiKeywordVoiceReplyItemModel) {
        self.itemImage.image = UIImage(named: "smart_card_ci")
    
        self.giftView.isHidden = true
        self.followView.isHidden = true
        
        self.keyView.isHidden = false
        
        self.keyView.config(model: model)
        
        switch model.itemType {
        case .musicLocal:
            self.textView.isHidden = true
            self.audioView.isHidden = false
            self.audioView.bind(to: model)
        case .text:
            self.textView.isHidden = false
            self.audioView.isHidden = true
            self.textView.bind(to: model)
        
        default :
            self.audioView.isHidden = true
            self.textView.isHidden = true
        }
        
    }
    
    // 系统回复
    func updateForSystem(model: AiKeywordVoiceReplyItemModel) {
        self.followView.isHidden = true
        self.keyView.isHidden = true
        
        switch model.systemInteractionType {
        case .gift:
            self.itemImage.image = UIImage(named: "smart_card_gift")
            if let gift = model.gifts?.first {
                self.giftView.isHidden = false
                self.giftView.config(model: gift)
            } else {
                self.giftView.isHidden = true
            }
            
            // 回复的资源有3种
            switch model.itemType {
            case .musicLocal:
                self.textView.isHidden = true
                self.audioView.isHidden = false
                self.audioView.bind(to: model)
            case .text:
                self.textView.isHidden = false
                self.audioView.isHidden = true
                self.textView.bind(to: model)
            
            default :
                self.audioView.isHidden = true
                self.textView.isHidden = true
            }
        case .follow:
            // 关注回复只有语音

            self.giftView.isHidden = true
            
            self.followView.isHidden = false
            self.itemImage.image = UIImage(named: "smart_card_guan")
            
            switch model.itemType {
            case .musicLocal:
                self.textView.isHidden = true
                self.audioView.isHidden = false
                self.audioView.bind(to: model)
            case .text:
                self.textView.isHidden = false
                self.audioView.isHidden = true
                self.textView.bind(to: model)
            
            default :
                self.audioView.isHidden = true
                self.textView.isHidden = true
            }
        }
    }
}

