//
//  SmartActionToolBar.swift
//  LivePlus
//
//  Created by 郭炜 on 2025/1/13.
//

import UIKit
import QMUIKit
//import QMUIKit

// MARK: - 顶部的toolbar视图
protocol SmartActionToolBarDelegate: NSObjectProtocol {
    func actionForToolBarStatusChanged()
    func actionForToolBarCreate()
    func actionForToolBarSetting()
}
class SmartActionToolBar: SmartBaseView {
    
    weak var delegate: SmartActionToolBarDelegate?
    
    /// 用户评论、系统互动
    lazy var filterButton: UIButton = {
        let button = UIButton()
        button.setImage(UIImage(named: "ic_smart_filter"), for: .normal)
        button.zl_enlargeValidTouchArea(inset: 10)
        return button
    }()
    
    /// 创建时间、修改时间
    lazy var sortButton: UIButton = {
        let button = UIButton()
        button.setImage(UIImage(named: "ic_smart_sort"), for: .normal)
        button.zl_enlargeValidTouchArea(inset: 10)
        return button
    }()
    
    /// 创建时间、修改时间
    lazy var sysTypeButton: UIButton = {
        let button = UIButton()
        button.setImage(UIImage(named: "ic_smart_sys_nor"), for: .normal)
        button.zl_enlargeValidTouchArea(inset: 10)
        button.isHidden = true
        return button
    }()
    
    /// 全部、仅看生效的、仅看停用的
    lazy var statusButton: UIButton = {
        let button = UIButton()
        button.setImage(UIImage(named: "ic_smart_status"), for: .normal)
        button.setImage(UIImage(named: "ic_smart_status_sel"), for: .selected)
        button.zl_enlargeValidTouchArea(inset: 10)
        return button
    }()
    
    lazy var createButton: UIButton = {
        let button = UIButton()
        button.setImage(UIImage(named: "ic_smart_create"), for: .normal)
        button.zl_enlargeValidTouchArea(inset: 10)
        return button
    }()
    
    lazy var settingButton: UIButton = {
        let button = UIButton()
        button.setImage(UIImage(named: "ic_smart_set"), for: .normal)
        button.zl_enlargeValidTouchArea(inset: 10)
        return button
    }()
    
    weak var model: InteractionModel?

    override init(frame: CGRect) {
        super.init(frame: frame)
    }
    
    convenience init() {
        self.init(frame: .zero)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func makeUI() {
        super.makeUI()
        
        addSubviews([filterButton, sysTypeButton, sortButton, statusButton, createButton, settingButton])
        
        filterButton.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.leading.equalToSuperview().inset(16)
            make.size.equalTo(36)
        }
        sysTypeButton.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.leading.equalTo(filterButton.snp.trailing).offset(20)
            make.size.equalTo(36)
        }
        sortButton.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.leading.equalTo(sysTypeButton.snp.trailing).offset(20)
            make.size.equalTo(36)
        }
        statusButton.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.leading.equalTo(sortButton.snp.trailing).offset(20)
            make.size.equalTo(36)
        }
        settingButton.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.trailing.equalToSuperview().inset(16)
            make.size.equalTo(36)
        }
        createButton.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.trailing.equalTo(settingButton.snp.leading).offset(-20)
            make.size.equalTo(36)
        }
    }
    
    override func business() {
        super.business()
        
        filterButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self else { return }
                self.filterAction()
            }.disposed(by: rx.disposeBag)
        
        sortButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self else { return }
                self.sortAction()
            }.disposed(by: rx.disposeBag)
        
        statusButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self else { return }
                self.statusAction()
            }.disposed(by: rx.disposeBag)
        
        createButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self else { return }
                self.createAction()
            }.disposed(by: rx.disposeBag)
        
        settingButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self else { return }
                self.SettingAction()
            }.disposed(by: rx.disposeBag)
        
        
        sysTypeButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self else { return }
                self.sysTypeAction()
            }.disposed(by: rx.disposeBag)
        
    }

}

extension SmartActionToolBar {
    func bind(to model: InteractionModel) {
        self.model = model
        updateView()
    }
    
    func updateView() {
        guard let model = self.model else { return }
        if model.interactionType == .systemInteraction {
            filterButton.setImage(UIImage(named: "ic_smart_filter1"), for: .normal)
            sysTypeButton.isHidden = false
            sortButton.snp.remakeConstraints { make in
                make.centerY.equalToSuperview()
                make.leading.equalTo(sysTypeButton.snp.trailing).offset(20)
                make.size.equalTo(36)
            }
            
            if  model.systemInteractionTypeList.count == 1 {
                sysTypeButton.setImage(UIImage(named: "ic_smart_sys"), for: .normal)
            } else {
                sysTypeButton.setImage(UIImage(named: "ic_smart_sys_nor"), for: .normal)
            }
                
        } else {
            filterButton.setImage(UIImage(named: "ic_smart_filter"), for: .normal)
            sysTypeButton.isHidden = true
            sortButton.snp.remakeConstraints { make in
                make.centerY.equalToSuperview()
                make.leading.equalTo(filterButton.snp.trailing).offset(20)
                make.size.equalTo(36)
            }
            
            
        }
        
        statusButton.isSelected = model.previewType != .all
        
    }
}

extension SmartActionToolBar {
    
    func filterAction() {
        if !LCTools.checkMember() {
            return
        }
        
        guard let model = self.model else { return }

        let popupView = QMUIPopupMenuView()
        popupView.minimumWidth = 160
        popupView.maximumWidth = 160
        popupView.itemHeight = 44
        popupView.itemTitleColor = .white
        popupView.itemTitleFont = LCDevice.DIN_Font_PF_R(14)
        popupView.backgroundColor = UIColor("#444444")
        popupView.sourceRect = CGRect(x: self.filterButton.frame.origin.x + 40, y: 137, width: 0, height: 0)
        popupView.automaticallyHidesWhenUserTap = true
        popupView.arrowSize = .zero
        popupView.highlightedBackgroundColor = .clear

        /// 用户评论
        let userCommentItem = QMUIPopupMenuButtonItem(image: UIImage(named: "ic_smart_filter_custom")?.byTintColor(UIColor.white),
                                                      title: "用户评论") { [weak self] item in
            guard let self = self else { return }
            guard let model = self.model else { return }
            model.interactionType = .userChat
            self.delegate?.actionForToolBarStatusChanged()
            self.updateView()
            popupView.hideWith(animated: true)
        }
        
        /// 系统互动
        let systemItem = QMUIPopupMenuButtonItem(image: UIImage(named: "ic_smart_filter_system"),
                                                 title: "系统互动") { [weak self] item in
            guard let self = self else { return }
            guard let model = self.model else { return }
            model.interactionType = .systemInteraction
            self.delegate?.actionForToolBarStatusChanged()
            self.updateView()
            popupView.hideWith(animated: true)
        }
        
        if #available(iOS 13.0, *) {
            userCommentItem.button.setImage(UIImage(named: "ic_smart_filter_custom")?.withTintColor( UIColor("#ADAAFF")), for: .selected)
            systemItem.button.setImage(UIImage(named: "ic_smart_filter_system")?.withTintColor(UIColor("#ADAAFF")), for: .selected)
        }
        userCommentItem.button.setTitleColor(UIColor.white, for: .normal)
        userCommentItem.button.setTitleColor(UIColor("#ADAAFF"), for: .selected)
        systemItem.button.setTitleColor(UIColor.white, for: .normal)
        systemItem.button.setTitleColor(UIColor("#ADAAFF"), for: .selected)

        userCommentItem.button.spacingBetweenImageAndTitle = 8
        systemItem.button.spacingBetweenImageAndTitle = 8
        userCommentItem.button.highlightedBackgroundColor = .clear
        systemItem.button.highlightedBackgroundColor = .clear
        
        userCommentItem.button.isSelected = model.interactionType == .userChat
        systemItem.button.isSelected = model.interactionType == .systemInteraction
        popupView.items = [userCommentItem, systemItem]
        popupView.showWith(animated: true)
    }
    
    func sortAction() {
        if !LCTools.checkMember()  {
            return
        }
        
        guard let model = self.model else { return }

        let popupView = QMUIPopupMenuView()
        popupView.minimumWidth = 160
        popupView.maximumWidth = 160
        popupView.itemHeight = 44
        popupView.itemTitleColor = .white
        popupView.itemTitleFont = LCDevice.DIN_Font_PF_R(14)
        popupView.backgroundColor = UIColor("#444444")
        popupView.sourceRect = CGRect(x: self.sortButton.frame.origin.x + 40, y: 137, width: 0, height: 0)
        popupView.automaticallyHidesWhenUserTap = true
        popupView.arrowSize = .zero
        popupView.highlightedBackgroundColor = .clear

        let createButton = QMUIPopupMenuButtonItem(image: nil, title: "按创建时间") { [weak self] item in
            guard let self = self else { return }
            guard let model = self.model else { return }
            switch model.sortType {
            case .createTime:
                self.model?.orderType = model.orderType.reversed
            case .updateTime:
                self.model?.sortType = .createTime
            }
            self.delegate?.actionForToolBarStatusChanged()
            popupView.hideWith(animated: true)
        }
        let editButton = QMUIPopupMenuButtonItem(image: nil, title: "按修改时间") { [weak self] item in
            guard let self = self else { return }
            guard let model = self.model else { return }
            switch model.sortType {
            case .createTime:
                self.model?.sortType = .updateTime
            case .updateTime:
                self.model?.orderType = model.orderType.reversed
            }
            self.delegate?.actionForToolBarStatusChanged()
            popupView.hideWith(animated: true)
        }
        createButton.button.imagePosition = .right
        createButton.button.spacingBetweenImageAndTitle = 34
        editButton.button.imagePosition = .right
        editButton.button.spacingBetweenImageAndTitle = 34
        createButton.button.highlightedBackgroundColor = .clear
        editButton.button.highlightedBackgroundColor = .clear

        createButton.button.setTitleColor(UIColor.white, for: .normal)
        createButton.button.setTitleColor(UIColor("#ADAAFF"), for: .selected)
        editButton.button.setTitleColor(UIColor.white, for: .normal)
        editButton.button.setTitleColor(UIColor("#ADAAFF"), for: .selected)

        switch model.sortType {
        case .createTime:
            switch model.orderType {
            case .aes:
                createButton.button.isSelected = true
                createButton.button.setImage(UIImage(named: "ic_smart_sort_up"), for: .normal)
            case .des:
                createButton.button.isSelected = true
                createButton.button.setImage(UIImage(named: "ic_smart_sort_down"), for: .normal)
            }
        case .updateTime:
            switch model.orderType {
            case .aes:
                editButton.button.isSelected = true
                editButton.button.setImage(UIImage(named: "ic_smart_sort_up"), for: .normal)
            case .des:
                editButton.button.isSelected = true
                editButton.button.setImage(UIImage(named: "ic_smart_sort_down"), for: .normal)
            }
        }
        popupView.items = [createButton, editButton]
        popupView.showWith(animated: true)
    }
    
    func statusAction() {
        if !LCTools.checkMember()  {
            return
        }
        
        guard let model = self.model else { return }

        let popupView = QMUIPopupMenuView()
        popupView.minimumWidth = 160
        popupView.maximumWidth = 160
        popupView.itemHeight = 44
        popupView.itemTitleColor = .white
        popupView.itemTitleFont = LCDevice.DIN_Font_PF_R(14)
        popupView.backgroundColor = UIColor("#444444")
        popupView.sourceRect = CGRect(x: self.statusButton.frame.origin.x + 40, y: 137, width: 0, height: 0)
        popupView.automaticallyHidesWhenUserTap = true
        popupView.arrowSize = .zero
        popupView.highlightedBackgroundColor = .clear
        
        let allButton = QMUIPopupMenuButtonItem(image: nil, title: "全部") { [weak self] _ in
            guard let self = self else { return }
            guard let model = self.model else { return }
            model.previewType = .all
            self.delegate?.actionForToolBarStatusChanged()
            popupView.hideWith(animated: true)
        }
        let usedButton = QMUIPopupMenuButtonItem(image: nil, title: "仅看生效的") { [weak self] _ in
            guard let self = self else { return }
            guard let model = self.model else { return }
            model.previewType = .onlyEffective
            self.delegate?.actionForToolBarStatusChanged()
            popupView.hideWith(animated: true)
        }
        let stopedButton = QMUIPopupMenuButtonItem(image: nil, title: "仅看停用的") { [weak self] _ in
            guard let self = self else { return }
            guard let model = self.model else { return }
            model.previewType = .onlyInvalid
            self.delegate?.actionForToolBarStatusChanged()
            popupView.hideWith(animated: true)
        }
        allButton.button.highlightedBackgroundColor = .clear
        usedButton.button.highlightedBackgroundColor = .clear
        stopedButton.button.highlightedBackgroundColor = .clear

        allButton.button.setTitleColor(UIColor.white, for: .normal)
        allButton.button.setTitleColor(UIColor("#ADAAFF"), for: .selected)
        usedButton.button.setTitleColor(UIColor.white, for: .normal)
        usedButton.button.setTitleColor(UIColor("#ADAAFF"), for: .selected)
        stopedButton.button.setTitleColor(UIColor.white, for: .normal)
        stopedButton.button.setTitleColor(UIColor("#ADAAFF"), for: .selected)

        switch model.previewType {
        case .all:
            allButton.button.isSelected = true
        case .onlyEffective:
            usedButton.button.isSelected = true
        case .onlyInvalid:
            stopedButton.button.isSelected = true
        }
        popupView.items = [allButton, usedButton, stopedButton]
        popupView.showWith(animated: true)
    }
    
    func sysTypeAction() {
        if !LCTools.checkMember() {
            return
        }
        guard let model = self.model else { return }

        let popupView = QMUIPopupMenuView()
        popupView.minimumWidth = 160
        popupView.maximumWidth = 160
        popupView.itemHeight = 44
        popupView.itemTitleColor = .white
        popupView.itemTitleFont = LCDevice.DIN_Font_PF_R(14)
        popupView.backgroundColor = UIColor("#444444")
        popupView.sourceRect = CGRect(x: self.sysTypeButton.frame.origin.x + 40, y: 137, width: 0, height: 0)
        popupView.automaticallyHidesWhenUserTap = true
        popupView.arrowSize = .zero
        popupView.highlightedBackgroundColor = .clear
        // 多选按钮_未选中
        let allButton = QMUIPopupMenuButtonItem(image: UIImage(named: "多选按钮_选中"), title: "全部分类") { [weak self] itm in
            guard let self = self else { return }
            guard let model = self.model else { return }
            model.systemInteractionTypeList.removeAll()
            self.delegate?.actionForToolBarStatusChanged()
            popupView.hideWith(animated: true)
        }
        let usedButton = QMUIPopupMenuButtonItem(image: UIImage(named: "多选按钮_未选中"), title: "送礼回复") { [weak self] itm in
            guard let self = self else { return }
            guard let model = self.model else { return }

            if model.systemInteractionTypeList.isEmpty {
                model.systemInteractionTypeList.append(.follow)
            } else {
                if model.systemInteractionTypeList.contains(where: {$0 == .gift}) {
                    model.systemInteractionTypeList.removeAll(.gift)
                } else {
                    model.systemInteractionTypeList.append(.gift)
                }
            }
            
            self.delegate?.actionForToolBarStatusChanged()
            popupView.hideWith(animated: true)
        }
        
        let stopedButton = QMUIPopupMenuButtonItem(image: UIImage(named: "多选按钮_未选中"), title: "关注回复") { [weak self] itm in
            guard let self = self else { return }
            guard let model = self.model else { return }
            if model.systemInteractionTypeList.isEmpty {
                model.systemInteractionTypeList.append(.gift)
            } else {
                if model.systemInteractionTypeList.contains(where: {$0 == .follow})  {
                    model.systemInteractionTypeList.removeAll(.follow)
                } else {
                    model.systemInteractionTypeList.append(.follow)
                }
            }
            
            self.delegate?.actionForToolBarStatusChanged()
            popupView.hideWith(animated: true)
        }
        
        
        
        allButton.button.highlightedBackgroundColor = .clear
        usedButton.button.highlightedBackgroundColor = .clear
        stopedButton.button.highlightedBackgroundColor = .clear

        allButton.button.setTitleColor(UIColor.white, for: .normal)
        allButton.button.setTitleColor(UIColor("#ADAAFF"), for: .selected)
    
        allButton.button.setImage(UIImage(named: "多选按钮_未选中"), for: .normal)
        allButton.button.setImage(UIImage(named: "多选按钮_选中"), for: .selected)
        
        usedButton.button.setTitleColor(UIColor.white, for: .normal)
        usedButton.button.setTitleColor(UIColor("#ADAAFF"), for: .selected)
        usedButton.button.setImage(UIImage(named: "多选按钮_未选中"), for: .normal)
        usedButton.button.setImage(UIImage(named: "多选按钮_选中"), for: .selected)
        
        stopedButton.button.setTitleColor(UIColor.white, for: .normal)
        stopedButton.button.setTitleColor(UIColor("#ADAAFF"), for: .selected)
        stopedButton.button.setImage(UIImage(named: "多选按钮_未选中"), for: .normal)
        stopedButton.button.setImage(UIImage(named: "多选按钮_选中"), for: .selected)

        if model.systemInteractionTypeList.isEmpty  {
            allButton.button.isSelected = true
            usedButton.button.isSelected = true
            stopedButton.button.isSelected = true
        } else {
            if model.systemInteractionTypeList.contains(where: {$0 == .follow}) && model.systemInteractionTypeList.contains(where: {$0 == .gift}) {
                allButton.button.isSelected = true
                usedButton.button.isSelected = true
                stopedButton.button.isSelected = true
            } else if model.systemInteractionTypeList.contains(where: {$0 == .gift}) {
                allButton.button.isSelected = false
                usedButton.button.isSelected = true
                stopedButton.button.isSelected = false
            } else if model.systemInteractionTypeList.contains(where: {$0 == .follow}) {
                allButton.button.isSelected = false
                usedButton.button.isSelected = false
                stopedButton.button.isSelected = true
            }
        }

        popupView.items = [allButton, usedButton, stopedButton]
        popupView.showWith(animated: true)
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.15) {
            usedButton.frame = CGRect(x: 30, y: 44, width: 130, height: 44)
            stopedButton.frame = CGRect(x: 30, y: 88, width: 130, height: 44)
        }
      
    }
    
    func createAction() {
        if !LCTools.checkMember()  {
            return
        }
        self.delegate?.actionForToolBarCreate()
    }
    
    func SettingAction() {

        if !LCTools.checkMember() {
            return
        }
        self.delegate?.actionForToolBarSetting()
    }
    
 
}
