//
//  SmartCardFollowView.swift
//  LivePlus
//
//  Created by simon on 14.2.25.
//

// MARK: - 回复的关注view

import Foundation

class SmartCardFollowView: UIView {
    
    ///封面图
    lazy var itemView: UIImageView = {
        let v = UIImageView()
        v.image = UIImage(named: "follow_icon")
        return v
    }()
    
    lazy var bgV: UIImageView = {
        let v = UIImageView()
        v.backgroundColor = .clear
        v.image = UIImage(named: "smart_card_kuang")?.byTintColor(UIColor("#444444"))
        return v
    }()
    
    lazy var titleLab: UILabel = {
        let v = UILabel()
        v.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        v.textColor = UIColor("#FCFCFC")
        v.text = "关注"
        return v
    }()
    
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        makeUI()
    }
    
    deinit {
        
    }
    
    convenience init() {
        self.init(frame: .zero)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func makeUI() {
        
        self.backgroundColor = UIColor.clear
        addSubviews([bgV])
        
        bgV.addSubviews([itemView, titleLab])
        
        bgV.snp.makeConstraints { make in
            make.top.leading.bottom.equalToSuperview()
            make.width.equalTo(80)
        }
        
        
        itemView.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.height.width.equalTo(18)
            make.leading.equalToSuperview().inset(16)
        }
        
        titleLab.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.trailing.equalToSuperview()
            make.leading.equalTo(itemView.snp.trailing).offset(6)
        }
    }
}
