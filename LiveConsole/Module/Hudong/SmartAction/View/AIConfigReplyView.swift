//
//  AIConfigReplyView.swift
//  LivePlus
//
//  Created by simon on 11.4.25.
//

import Foundation


protocol AIConfigReplyViewDelegate: NSObjectProtocol {
    func updateModel(vi: AIConfigReplyView)
}
    
    
class AIConfigReplyView: UIView {
        
    weak var delegate: AIConfigReplyViewDelegate?
    
    let maxCount = 50
 
    var model: AiAutoTtsModel?
    
    var type: AIAudioReplyType = .keyword
    
    var inView: KeyWordInputView?
    
    lazy var resetButton: UIButton = {
        let button = UIButton()
        button.setTitle("还原默认", for: .normal)
        button.setTitleColor(UIColor("#ADAAFF"), for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 14)
        button.addTarget(self, action: #selector(resetAction), for: .touchUpInside)
        button.backgroundColor = .clear
        button.zl_enlargeValidTouchArea(inset: 8)
        return button
    }()
    
    lazy var egButton: UIButton = {
        let button = UIButton()
        button.setTitle("示例", for: .normal)
        button.setTitleColor(UIColor("#F78751"), for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 14)
        button.addTarget(self, action: #selector(egAction), for: .touchUpInside)
        button.backgroundColor = .clear
        button.zl_enlargeValidTouchArea(inset: 8)
        return button
    }()
    
    lazy var editButton: UIButton = {
        let button = UIButton()
        button.setImage(UIImage(named: "ic_hud_edit"), for: .normal)
        button.addTarget(self, action: #selector(eidtAction), for: .touchUpInside)
        button.backgroundColor = .clear
        button.zl_enlargeValidTouchArea(inset: 8)
        return button
    }()
    
    lazy var titleLab: UILabel = {
        let v = UILabel()
        v.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        v.textColor = .white.alpha(value: 0.6)
        v.text = "送礼回复最长等待时间"
        return v
    }()
    
    private lazy var textView: UITextView = {
        let textView = UITextView()
        textView.isScrollEnabled = false
        textView.isEditable = false
        textView.borderColor = UIColor.clear
        textView.borderWidth = 1
        textView.backgroundColor = UIColor("#111111")
        textView.font = UIFont.systemFont(ofSize: 14, weight: .regular)
        textView.textColor = .white
        textView.cornerRadius = 6
        textView.returnKeyType = .done
        textView.textContainerInset = UIEdgeInsets(top: 8, left: 12, bottom: 4, right: 12)
        return textView
    }()

    var isAudio: Bool = true
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        makeUI()
    }
    
    deinit {
        
    }
    
    convenience init(type: AIAudioReplyType, isAudio: Bool = true) {
        self.init(frame: .zero)
        self.isAudio = isAudio
        self.type = type
        
        self.titleLab.text =  isAudio ? type.synthesisTitle : type.title
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    @objc func resetAction() {
        guard let model = model else { return  }
        if isAudio {
            model.text = self.type.synthesisContent
        } else {
            model.text = self.type.content
        }
        
        bind(mode: model)
    }
    
    @objc func eidtAction() {
        guard let model = model else { return  }
        let vi = KeyWordInputView()
        vi.delegate = self
        vi.show(model: model)
        guard let appDel = UIApplication.shared.delegate as? AppDelegate else { return }
        appDel.wd.addSubview(vi)
        vi.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        self.inView = vi
    }
    
    
    @objc func egAction() {
        guard let model = model else { return  }
        
        let att: NSAttributedString  = isAudio ? type.synthesisSample : type.sample
        
        HudongSampleTextSAlert.show(sureAction: {[weak self] in
            
        }, attStr: att)
        
    }
    
    func makeUI() {
        
        self.backgroundColor = UIColor.clear
        addSubviews([titleLab, textView, editButton, resetButton,egButton])
        
        titleLab.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.height.equalTo(18)
            make.leading.equalToSuperview().inset(12)
        }
        
        resetButton.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.height.equalTo(20)
            make.width.equalTo(80)
            make.trailing.equalToSuperview().inset(12)
        }
        
        
        editButton.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.height.equalTo(20)
            make.width.equalTo(30)
            make.trailing.equalTo(resetButton.snp.leading).offset(-12)
        }
        
        egButton.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.height.equalTo(20)
            make.width.equalTo(32)
            make.leading.equalTo(titleLab.snp.trailing).offset(8)
        }
        
        
        textView.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(30)
            make.bottom.equalToSuperview()
            make.leading.trailing.equalToSuperview().inset(12)
        }
    
    }
    
    func bind(mode: AiAutoTtsModel ) {
        self.model = mode
        self.textView.text = mode.text
        self.textView.attributedText = LCTools.checkNickConten(text: mode.text, color: .white)
    }

    override func layoutSubviews() {
        super.layoutSubviews()
        guard let model = model else { return }
        self.textView.attributedText = LCTools.checkNickConten(text: model.text, color: .white)
    }
}


extension AIConfigReplyView: KeyWordInputViewDelegate {
    
    func doneKeyWord(model: AiAutoTtsModel) {
        // 页面刷新
        bind(mode: model)
        self.delegate?.updateModel(vi: self)
    }
    
}
