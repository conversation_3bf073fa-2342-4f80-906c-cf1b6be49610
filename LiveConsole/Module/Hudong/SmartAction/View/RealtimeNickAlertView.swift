//
//  RealtimeNickAlertView.swift
//  LiveConsole
//
//  Created by simon on 12.5.25.
//

import Foundation

// MARK: - 免费用户的积分弹窗
class RealtimeNickAlertView: UIView {
    
    private var sureAction: VIPAction?
    
    private var cancelAction: VIPAction?
    
    lazy var contentView: UIView = {
        let view = UIView()
        view.cornerRadius = 8
        view.backgroundColor = UIColor.white
        return view
    }()
    
    lazy var iconView: UIImageView = {
        let view = UIImageView()
        view.image = UIImage(named: "roomalert")
        view.backgroundColor = UIColor.clear
        return view
    }()
    
    lazy var bgView: UIImageView = {
        let view = UIImageView()
        view.cornerRadius = 8
        view.contentMode = .scaleAspectFill
        view.image = UIImage(named: "roomalertbg")
        view.backgroundColor = UIColor.clear
        view.isUserInteractionEnabled = true
        return view
    }()
    
    lazy var title: UILabel = {
        let label = UILabel()
        label.text = "计费规则"
        label.font = UIFont.systemFont(ofSize: 20, weight: .medium)
        label.textColor = UIColor("#333333")
        return label
    }()
    
    lazy var vipLab: UILabel = {
        let label = UILabel()
        let text = "开启后，系统将实时播报互动用户的昵称，让交流更生动有趣~"
        
        let paraph = NSMutableParagraphStyle()
        paraph.lineSpacing = 6
        let attributes = [NSAttributedString.Key.paragraphStyle: paraph, NSAttributedString.Key.foregroundColor: UIColor("#1E1F20"), NSAttributedString.Key.font: UIFont.systemFont(ofSize: 16, weight: .regular)]
        
        let attributedText = NSMutableAttributedString(string: text, attributes: attributes)
        
        attributedText.setAttributes([NSAttributedString.Key.foregroundColor: UIColor("#6763F6"), NSAttributedString.Key.font: UIFont.systemFont(ofSize: 16, weight: .medium)], range: NSRange(location: 7, length: 11))
        label.attributedText = attributedText
        label.numberOfLines = 0
        return label
    }()
    
    private let contentLabel1: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 16, weight: .bold)
        label.textColor = UIColor("#1E1F20")
        label.text = "积分消耗规则："
        return label
    }()
    
    private let text11: PointsAlertTextView = {
        let label = PointsAlertTextView()
        label.titleLabel.text = "2积分/分钟（开启后自动扣除）"
        label.titleLabel.font = UIFont.systemFont(ofSize: 15)
        return label
    }()
    
    private let text12: PointsAlertTextView = {
        let label = PointsAlertTextView()
        label.titleLabel.text = "计费方式：按分钟计费，不足1分钟按1分钟计算"
        label.titleLabel.font = UIFont.systemFont(ofSize: 15)
        return label
    }()
    
    private lazy var cancelBtn: UIButton = {
        let btn = UIButton(frame: CGRect(x: 0.0, y: 0.0, width: 170, height: 40))
        btn.setImage(UIImage(named: "登录流程_组件_cell_勾选_nor"), for: .normal)
        btn.setImage(UIImage(named: "icon_勾选_sel"), for: .selected)
        btn.setTitle(" 每次开启前都提示", for: .normal)
        btn.setTitleColor(UIColor("#4D4E52"), for: .normal)
        btn.titleLabel?.font = UIFont.systemFont(ofSize: 14, weight: .regular)
        btn.addTarget(self, action: #selector(cancelBtnAction(sender:)), for: .touchUpInside)
        btn.imagePosition(style: .left, spacing: 6)
        return btn
    }()
    
    
    private lazy var okBtn: UIButton = {
        let btn = UIButton(frame: CGRect(x: 0.0, y: 0.0, width: 170, height: 40))
        btn.backgroundColor = UIColor("#6974F2")
        btn.setTitle("我知道了", for: .normal)
        btn.setTitleColor(UIColor.white, for: .normal)
        btn.titleLabel?.font = UIFont.systemFont(ofSize: 15, weight: .medium)
        btn.addTarget(self, action: #selector(okBtnAction(sender:)), for: .touchUpInside)
        btn.cornerRadius = 20
        btn.applyGradient()
        return btn
    }()
    
    
    override init(frame: CGRect) {
        super.init(frame: frame)
    }

    convenience init( sureAction: @escaping VIPAction, cancelAction: @escaping VIPAction) {
        self.init(frame: CGRect(x: 0, y: 0, width: LCDevice.screenW, height: LCDevice.screenH))
        self.sureAction = sureAction
        self.cancelAction = cancelAction
        makeUI()
        business()
        
        // 规则的
        if let rule = UserDefaults.standard.value(forKey: LCKey.UD_Realtime_Rlue) as? String {
            self.cancelBtn.isSelected = rule == "1"
        } else {
            self.cancelBtn.isSelected  = true
        }
    }

    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func makeUI() {
        addSubviews([contentView])
        contentView.addSubviews([bgView, iconView, title, okBtn, vipLab, cancelBtn, contentLabel1, text11, text12])
        
        contentView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview()
            make.width.equalTo(323)
            make.height.equalTo(400)
        }
       
        bgView.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview()
            make.height.equalTo(241)
        }
        
        title.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalToSuperview().inset(24)
        }
        
        iconView.snp.makeConstraints { make in
            make.trailing.equalTo(title.snp.leading).offset(-8)
            make.centerY.equalTo(title.snp.centerY)
            make.width.height.equalTo(30)
        }
        
        vipLab.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(76)
            make.leading.trailing.equalToSuperview().inset(24)
            make.height.equalTo(60)
        }
        
        contentLabel1.snp.makeConstraints { make in
            make.top.equalTo(vipLab.snp.bottom).offset(12)
            make.leading.trailing.equalToSuperview().inset(24)
            make.height.equalTo(20)
        }
        
        text11.snp.makeConstraints { make in
            make.height.equalTo(21)
            make.top.equalTo(contentLabel1.snp.bottom).offset(4)
            make.leading.trailing.equalToSuperview().inset(24)
        }
        
        text12.snp.makeConstraints { make in
            make.top.equalTo(text11.snp.bottom).offset(4)
            make.leading.trailing.equalToSuperview().inset(24)
            make.height.equalTo(45)
        }
      
        okBtn.snp.makeConstraints { make in
            make.bottom.equalToSuperview().inset(64)
            make.trailing.leading.equalToSuperview().inset(24)
            make.height.equalTo(40)
        }
        
        cancelBtn.snp.makeConstraints { make in
            make.bottom.equalToSuperview().inset(20)
            make.centerX.equalToSuperview()
            make.height.equalTo(30)
            make.width.equalTo(150)
        }
       
    }
    
    func business() {
        
    }
    
    @objc func okBtnAction(sender: UIButton) {
        let rule = cancelBtn.isSelected ? "1" : "0"
        UserDefaults.standard.setValue(rule, forKey: LCKey.UD_Realtime_Rlue)
        
        UserDefaults.standard.setValue(!cancelBtn.isSelected, forKey: LCKey.UD_Realtime_Open)
            
        if let action = self.sureAction {
            action()
        }
        self.dismiss()
    }
    
    @objc func cancelBtnAction(sender: UIButton) {
        // 勾选
        sender.isSelected = !sender.isSelected
    }
    
    func dismiss() {
        UIView.animate(withDuration: 0.2) {
            self.alpha = 0
        } completion: { _ in
            self.removeFromSuperview()
        }
    }
}


extension RealtimeNickAlertView {
    public static func show(sureAction: @escaping VIPAction, cancelAction: @escaping VIPAction) {
        guard let appDel = UIApplication.shared.delegate as? AppDelegate else { return }
        let showingView = RealtimeNickAlertView(sureAction: sureAction, cancelAction: cancelAction)
        showingView.alpha = 0.0
        appDel.wd.addSubview(showingView)
        UIView.animate(withDuration: 0.2) {
            showingView.alpha = 1.0
        }
    }
}



// MARK: - 免费用户的积分弹窗1
class RealtimeNickAlertOpenView: UIView {
    
    private var sureAction: VIPAction?
    
    private var cancelAction: VIPAction?
    
    lazy var contentView: UIView = {
        let view = UIView()
        view.cornerRadius = 8
        view.backgroundColor = UIColor.white
        return view
    }()
    
    lazy var iconView: UIImageView = {
        let view = UIImageView()
        view.image = UIImage(named: "roomalert")
        view.backgroundColor = UIColor.clear
        return view
    }()
    
    lazy var bgView: UIImageView = {
        let view = UIImageView()
        view.cornerRadius = 8
        view.contentMode = .scaleAspectFill
        view.image = UIImage(named: "roomalertbg")
        view.backgroundColor = UIColor.clear
        view.isUserInteractionEnabled = true
        return view
    }()
    
    lazy var title: UILabel = {
        let label = UILabel()
        label.text = "温馨提示"
        label.font = UIFont.systemFont(ofSize: 20, weight: .medium)
        label.textColor = UIColor("#333333")
        return label
    }()
    
    lazy var vipLab: UILabel = {
        let label = UILabel()
        let text = "开启后，系统将实时播报互动用户的昵称，让交流更生动有趣~"
        
        let paraph = NSMutableParagraphStyle()
        paraph.lineSpacing = 6
        let attributes = [NSAttributedString.Key.paragraphStyle: paraph, NSAttributedString.Key.foregroundColor: UIColor("#1E1F20"), NSAttributedString.Key.font: UIFont.systemFont(ofSize: 16, weight: .regular)]
        
        let attributedText = NSMutableAttributedString(string: text, attributes: attributes)
        
        attributedText.setAttributes([NSAttributedString.Key.foregroundColor: UIColor("#6763F6"), NSAttributedString.Key.font: UIFont.systemFont(ofSize: 16, weight: .medium)], range: NSRange(location: 7, length: 11))
        label.attributedText = attributedText
        label.numberOfLines = 0
        return label
    }()
    
    private let contentLabel1: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 16, weight: .bold)
        label.textColor = UIColor("#1E1F20")
        label.text = "积分消耗规则："
        return label
    }()
    
    private let text11: PointsAlertTextView = {
        let label = PointsAlertTextView()
        label.titleLabel.text = "2积分/分钟（开启后自动扣除）"
        label.titleLabel.font = UIFont.systemFont(ofSize: 15)
        return label
    }()
    
    private let text12: PointsAlertTextView = {
        let label = PointsAlertTextView()
        label.titleLabel.text = "计费方式：按分钟计费，不足1分钟按1分钟计算"
        label.titleLabel.font = UIFont.systemFont(ofSize: 15)
        return label
    }()
    
    private lazy var checkBtn: UIButton = {
        let btn = UIButton(frame: CGRect(x: 0.0, y: 0.0, width: 170, height: 40))
        btn.setImage(UIImage(named: "登录流程_组件_cell_勾选_nor"), for: .normal)
        btn.setImage(UIImage(named: "icon_勾选_sel"), for: .selected)
        btn.setTitle(" 不再提示", for: .normal)
        btn.setTitleColor(UIColor("#4D4E52"), for: .normal)
        btn.titleLabel?.font = UIFont.systemFont(ofSize: 14, weight: .regular)
        btn.addTarget(self, action: #selector(checkAction(sender:)), for: .touchUpInside)
        btn.imagePosition(style: .left, spacing: 6)
        return btn
    }()
    
    
    private lazy var cancelBtn: UIButton = {
        let btn = UIButton(frame: CGRect(x: 0.0, y: 0.0, width: 170, height: 40))
        btn.backgroundColor = .white
        btn.setTitle("取消", for: .normal)
        btn.borderColor = UIColor("#E5E3EB")
        btn.borderWidth = 1.5
        btn.setTitleColor(UIColor("#4D4E52"), for: .normal)
        btn.titleLabel?.font = UIFont.systemFont(ofSize: 15, weight: .medium)
        btn.addTarget(self, action: #selector(cancelBtnAction(sender:)), for: .touchUpInside)
        btn.cornerRadius = 20
        return btn
    }()
    
    private lazy var okBtn: UIButton = {
        let btn = UIButton(frame: CGRect(x: 0.0, y: 0.0, width: 170, height: 40))
        btn.backgroundColor = UIColor("#6974F2")
        btn.setTitle("确定开启", for: .normal)
        btn.setTitleColor(UIColor.white, for: .normal)
        btn.titleLabel?.font = UIFont.systemFont(ofSize: 15, weight: .medium)
        btn.addTarget(self, action: #selector(okBtnAction(sender:)), for: .touchUpInside)
        btn.cornerRadius = 20
        btn.applyGradient()
        return btn
    }()
    
    
    override init(frame: CGRect) {
        super.init(frame: frame)
    }

    convenience init( sureAction: @escaping VIPAction, cancelAction: @escaping VIPAction) {
        self.init(frame: CGRect(x: 0, y: 0, width: LCDevice.screenW, height: LCDevice.screenH))
        self.sureAction = sureAction
        self.cancelAction = cancelAction
        makeUI()
        business()
        
        self.checkBtn.isSelected = UserDefaults.standard.bool(forKey: LCKey.UD_Realtime_Open)
        
    }

    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func makeUI() {
        addSubviews([contentView])
        contentView.addSubviews([bgView, iconView, title, okBtn, vipLab, checkBtn,cancelBtn, contentLabel1, text11, text12])
        
        contentView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview()
            make.width.equalTo(323)
            make.height.equalTo(400)
        }
       
        bgView.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview()
            make.height.equalTo(241)
        }
        
        title.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalToSuperview().inset(24)
        }
        
        iconView.snp.makeConstraints { make in
            make.trailing.equalTo(title.snp.leading).offset(-8)
            make.centerY.equalTo(title.snp.centerY)
            make.width.height.equalTo(30)
        }
        
        vipLab.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(76)
            make.leading.trailing.equalToSuperview().inset(24)
            make.height.equalTo(60)
        }
        
        contentLabel1.snp.makeConstraints { make in
            make.top.equalTo(vipLab.snp.bottom).offset(12)
            make.leading.trailing.equalToSuperview().inset(24)
            make.height.equalTo(20)
        }
        
        text11.snp.makeConstraints { make in
            make.height.equalTo(21)
            make.top.equalTo(contentLabel1.snp.bottom).offset(4)
            make.leading.trailing.equalToSuperview().inset(24)
        }
        
        text12.snp.makeConstraints { make in
            make.top.equalTo(text11.snp.bottom).offset(4)
            make.leading.trailing.equalToSuperview().inset(24)
            make.height.equalTo(45)
        }
      
        okBtn.snp.makeConstraints { make in
            make.bottom.equalToSuperview().inset(64)
            make.trailing.equalToSuperview().inset(24)
            make.height.equalTo(40)
            make.width.equalTo(123)
        }
        
        cancelBtn.snp.makeConstraints { make in
            make.bottom.equalToSuperview().inset(64)
            make.leading.equalToSuperview().inset(24)
            make.height.equalTo(40)
            make.width.equalTo(123)
        }
        
        checkBtn.snp.makeConstraints { make in
            make.bottom.equalToSuperview().inset(20)
            make.centerX.equalToSuperview()
            make.height.equalTo(30)
            make.width.equalTo(150)
        }
       
    }
    
    func business() {
        
    }
    
    @objc func okBtnAction(sender: UIButton) {
        
        let rule = checkBtn.isSelected ? "0" : "1"
        
        UserDefaults.standard.setValue(rule, forKey: LCKey.UD_Realtime_Rlue)
        
        UserDefaults.standard.setValue(sender.isSelected, forKey: LCKey.UD_Realtime_Open)
        
        if let action = self.sureAction {
            action()
        }
        self.dismiss()
    }
    
    @objc func checkAction(sender: UIButton) {
        sender.isSelected = !sender.isSelected
       
    }
    
    @objc func cancelBtnAction(sender: UIButton) {
       dismiss()
    }
    
    func dismiss() {
        UIView.animate(withDuration: 0.2) {
            self.alpha = 0
        } completion: { _ in
            self.removeFromSuperview()
        }
    }
}


extension RealtimeNickAlertOpenView {
    public static func show(sureAction: @escaping VIPAction, cancelAction: @escaping VIPAction) {
        guard let appDel = UIApplication.shared.delegate as? AppDelegate else { return }
        let showingView = RealtimeNickAlertOpenView(sureAction: sureAction, cancelAction: cancelAction)
        showingView.alpha = 0.0
        appDel.wd.addSubview(showingView)
        UIView.animate(withDuration: 0.2) {
            showingView.alpha = 1.0
        }
    }
}
