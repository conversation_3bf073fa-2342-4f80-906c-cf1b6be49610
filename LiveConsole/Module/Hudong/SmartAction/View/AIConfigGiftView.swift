//
//  AIConfigGiftView.swift
//  LivePlus
//
//  Created by simon on 16.1.25.
//

import Foundation

class AIConfigTextSetView: UIView {
    
    lazy var titleLab: UILabel = {
        let v = UILabel()
        v.font = UIFont.systemFont(ofSize: 14, weight: .regular)
        v.textColor = UIColor.white
        v.text = "互动时实时@用户昵称"
        return v
    }()
    
    
    lazy var bgView: UIView = {
        let v = UIView()
        v.backgroundColor = UIColor("#282828")
        v.cornerRadius = 12
        return v
    }()
    
  
    lazy var keyView: AIConfigReplyView = {
        let v = AIConfigReplyView(type: .keyword, isAudio: false)
        v.delegate = self
        return v
    }()
    
    lazy var giftView: AIConfigReplyView = {
        let v = AIConfigReplyView(type: .gift,isAudio: false)
        v.delegate = self
        return v
    }()
    
    lazy var guanzhuView: AIConfigReplyView = {
        let v = AIConfigReplyView(type: .follow,isAudio: false)
        v.delegate = self
        return v
    }()
    
    var model: AiConfigModel?
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        makeUI()
        business()
    }
    
    deinit {
        
    }
    
    convenience init() {
        self.init(frame: .zero)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func makeUI() {
        
        self.backgroundColor = UIColor.clear
        self.addSubview(bgView)
        
        bgView.snp.makeConstraints { make in
            make.bottom.equalToSuperview()
            make.top.equalToSuperview()
            make.leading.trailing.equalToSuperview().inset(16)
        }
        
        bgView.addSubviews([titleLab, keyView, giftView, guanzhuView])
        
        titleLab.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(18)
            make.height.equalTo(20)
            make.leading.equalToSuperview().inset(12)
        }
        
        guanzhuView.snp.makeConstraints { make in
            make.top.equalTo(titleLab.snp.bottom).offset(14)
            make.height.equalTo(75)
            make.leading.trailing.equalToSuperview()
        }
        
        giftView.snp.makeConstraints { make in
            make.top.equalTo(guanzhuView.snp.bottom).offset(14)
            make.height.equalTo(75)
            make.leading.trailing.equalToSuperview()
        }
        
        keyView.snp.makeConstraints { make in
            make.top.equalTo(giftView.snp.bottom).offset(14)
            make.height.equalTo(75)
            make.leading.trailing.equalToSuperview()
        }
        
        
    }
 
    
    func business() {
        
    }
    
    public func config(model: AiConfigModel) {
        self.keyView.bind(mode: model.keywordAtNameModel)
        self.giftView.bind(mode: model.giftAtNameModel)
        self.guanzhuView.bind(mode: model.followAtNameModel)
        
        guanzhuView.snp.updateConstraints { make in
            make.height.equalTo(model.followAtNameModel.tabViewHeight)
        }
        
        giftView.snp.updateConstraints { make in
            make.height.equalTo(model.giftAtNameModel.tabViewHeight)
        }
        
        keyView.snp.updateConstraints { make in
            make.height.equalTo(model.keywordAtNameModel.tabViewHeight)
        }
        
        self.model = model
        
        bgView.snp.makeConstraints { make in
            make.bottom.equalTo(keyView.snp.bottom).offset(16)
        }
        
    }
    
}

extension AIConfigTextSetView : AIConfigReplyViewDelegate {
    func updateModel(vi: AIConfigReplyView) {
        guard let model = model else { return  }
        
        guanzhuView.snp.updateConstraints { make in
            make.height.equalTo(model.followAtNameModel.tabViewHeight)
        }
        
        giftView.snp.updateConstraints { make in
            make.height.equalTo(model.giftAtNameModel.tabViewHeight)
        }
        
        keyView.snp.updateConstraints { make in
            make.height.equalTo(model.keywordAtNameModel.tabViewHeight)
        }
            
        
        bgView.snp.makeConstraints { make in
            make.bottom.equalTo(keyView.snp.bottom).offset(16)
        }
    }
    
}
