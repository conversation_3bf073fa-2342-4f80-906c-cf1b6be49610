//
//  SmartDetailFourNavigation.swift
//  LivePlus
//
//  Created by 郭炜 on 2025/2/10.
//

import UIKit

// MARK: - 四层导航栏
class SmartDetailFourNavigation: SmartBaseView {

    lazy var background: UIView = {
        let view = UIView()
        return view
    }()
    
    lazy var lastPageButton: UIButton = {
        let button = UIButton()
        button.setTitleColor(UIColor("#ACACAC"), for: .normal)
        button.titleLabel?.font = LCDevice.DIN_Font_PF_R(14)
        return button
    }()
    
    lazy var rightImage: UIImageView = {
        let imageView = UIImageView(image: UIImage(named: "ic_smart_nav_right"))
        return imageView
    }()
    
    lazy var secondPageButton: UIButton = {
        let button = UIButton()
        button.setTitleColor(UIColor("#ACACAC"), for: .normal)
        button.titleLabel?.font = LCDevice.DIN_Font_PF_R(14)
        return button
    }()
    
    lazy var rightSecondImage: UIImageView = {
        let imageView = UIImageView(image: UIImage(named: "ic_smart_nav_right"))
        return imageView
    }()
    
    lazy var threePageButton: UIButton = {
        let button = UIButton()
        button.setTitleColor(UIColor("#ACACAC"), for: .normal)
        button.titleLabel?.font = LCDevice.DIN_Font_PF_R(14)
        return button
    }()
    
    lazy var rightThreeImage: UIImageView = {
        let imageView = UIImageView(image: UIImage(named: "ic_smart_nav_right"))
        return imageView
    }()
    
    lazy var currentPageLabel: UILabel = {
        let label = UILabel()
        label.textColor = UIColor("#ADAAFF")
        label.font = LCDevice.DIN_Font_PF_M(16)
        return label
    }()

    override init(frame: CGRect) {
        super.init(frame: frame)
    }
    
    convenience init() {
        self.init(frame: .zero)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func makeUI() {
        super.makeUI()
        
        addSubview(background)
        background.addSubviews([lastPageButton, rightImage, secondPageButton, rightSecondImage, threePageButton, rightThreeImage, currentPageLabel])
        
        background.snp.makeConstraints { make in
            make.bottom.leading.trailing.equalToSuperview()
            make.height.equalTo(44)
        }
        lastPageButton.snp.makeConstraints { make in
            make.leading.equalToSuperview().inset(16)
            make.centerY.equalToSuperview()
            make.width.equalTo(60)
            make.height.equalTo(44)
        }
        rightImage.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.leading.equalTo(lastPageButton.snp.trailing).offset(5)
            make.size.equalTo(12)
        }
        secondPageButton.snp.makeConstraints { make in
            make.leading.equalTo(rightImage.snp.trailing).offset(5)
            make.centerY.equalToSuperview()
            make.height.equalTo(44)
        }
        rightSecondImage.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.leading.equalTo(secondPageButton.snp.trailing).offset(5)
            make.size.equalTo(12)
        }
        threePageButton.snp.makeConstraints { make in
            make.leading.equalTo(rightSecondImage.snp.trailing).offset(5)
            make.centerY.equalToSuperview()
            make.height.equalTo(44)
        }
        rightThreeImage.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.leading.equalTo(threePageButton.snp.trailing).offset(5)
            make.size.equalTo(12)
        }
        currentPageLabel.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.leading.equalTo(rightThreeImage.snp.trailing).offset(5)
        }
    }
    
    override func business() {
        super.business()
    }

}
