//
//  NickRealtimeView.swift
//  LiveConsole
//
//  Created by simon on 26.5.25.
//


import Foundation

class NickRealtimeView: UIView {
    
    weak var delegate:LangduNickViewDelegate?
    
    lazy var topbg: UIImageView = {
        let button = UIImageView()
        button.backgroundColor = .clear
        button.image = UIImage(named: "ic_nick_bg")
        return button
    }()
    
    
    lazy var potLab: UILabel = {
        let v = UILabel()
        v.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        v.textColor = .white
        v.textAlignment = .center
        v.text = "0"
        return v
    }()
    
    
    lazy var timeLab: UILabel = {
        let v = UILabel()
        v.font = UIFont.systemFont(ofSize: 12, weight: .regular)
        v.textColor = .white.alpha(value: 0.6)
        v.text = "开始时间:"
        return v
    }()
    
    
    lazy var timeLab1: UILabel = {
        let v = UILabel()
        v.font = UIFont.systemFont(ofSize: 12, weight: .medium)
        v.textColor = .white
        v.text = ""
        return v
    }()
    
    lazy var durationLab: UILabel = {
        let v = UILabel()
        v.font = UIFont.systemFont(ofSize: 12, weight: .regular)
        v.textColor = .white.alpha(value: 0.6)
        v.text = "本次时长："
        return v
    }()
    
    
    lazy var sepLab: UILabel = {
        let v = UILabel()
        v.font = UIFont.systemFont(ofSize: 18, weight: .medium)
        v.textColor = .white.alpha(value: 0.35)
        v.text = ":"
        v.textAlignment = .center
        return v
    }()
    
    lazy var sepLab1: UILabel = {
        let v = UILabel()
        v.font = UIFont.systemFont(ofSize: 18, weight: .medium)
        v.textColor = .white.alpha(value: 0.35)
        v.text = ":"
        v.textAlignment = .center
        return v
    }()
    
    
    lazy var hourLab1: RealtimeView = {
        let v = RealtimeView()
        return v
    }()
    
    lazy var hourLab2: RealtimeView = {
        let v = RealtimeView()
        return v
    }()
    
    
    lazy var minuteLab1: RealtimeView = {
        let v = RealtimeView()
        return v
    }()
    
    lazy var minuteLab2: RealtimeView = {
        let v = RealtimeView()
        return v
    }()
    
    lazy var secondLab1: RealtimeView = {
        let v = RealtimeView()
        return v
    }()
    
    lazy var secondLab2: RealtimeView = {
        let v = RealtimeView()
        return v
    }()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        makeUI()
    }
    
    deinit {
        
    }
   
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func makeUI() {
        
        self.backgroundColor = UIColor("#444444")
        self.cornerRadius = 6
        self.layer.masksToBounds = true
        
        addSubviews([topbg, potLab, timeLab, timeLab1, durationLab, sepLab, sepLab1, hourLab1, hourLab2, minuteLab1, minuteLab2, secondLab1, secondLab2])
                        
        topbg.snp.makeConstraints { make in
            make.top.leading.equalToSuperview()
            make.height.equalTo(30)
            make.width.equalTo(113)
        }
        
        potLab.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(3)
            make.leading.equalToSuperview().inset(75)
            make.width.equalTo(30)
        }
        
        timeLab1.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(9)
            make.trailing.equalToSuperview().inset(9)
            make.height.equalTo(15)
        }
        
        timeLab.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(9)
            make.trailing.equalTo(timeLab1.snp.leading).offset(-4)
            make.height.equalTo(15)
        }
        
        durationLab.snp.makeConstraints { make in
            make.bottom.equalToSuperview().inset(14)
            make.leading.equalToSuperview().inset(34)
            make.height.equalTo(15)
        }
        
        hourLab1.snp.makeConstraints { make in
            make.centerY.equalTo(durationLab.snp.centerY)
            make.leading.equalTo(durationLab.snp.trailing).offset(6)
            make.height.width.equalTo(21)
        }
        
        hourLab2.snp.makeConstraints { make in
            make.centerY.equalTo(durationLab.snp.centerY)
            make.leading.equalTo(hourLab1.snp.trailing).offset(4)
            make.height.width.equalTo(21)
        }
        
        sepLab.snp.makeConstraints { make in
            make.centerY.equalTo(durationLab.snp.centerY)
            make.leading.equalTo(hourLab2.snp.trailing)
            make.height.equalTo(21)
            make.width.equalTo(12)
        }
        
        minuteLab1.snp.makeConstraints { make in
            make.centerY.equalTo(durationLab.snp.centerY)
            make.leading.equalTo(sepLab.snp.trailing)
            make.height.width.equalTo(21)
        }
        
        minuteLab2.snp.makeConstraints { make in
            make.centerY.equalTo(durationLab.snp.centerY)
            make.leading.equalTo(minuteLab1.snp.trailing).offset(4)
            make.height.width.equalTo(21)
        }
        
        sepLab1.snp.makeConstraints { make in
            make.centerY.equalTo(durationLab.snp.centerY)
            make.leading.equalTo(minuteLab2.snp.trailing)
            make.height.equalTo(21)
            make.width.equalTo(12)
        }
        
        secondLab1.snp.makeConstraints { make in
            make.centerY.equalTo(durationLab.snp.centerY)
            make.leading.equalTo(sepLab1.snp.trailing)
            make.height.width.equalTo(21)
        }
        
        secondLab2.snp.makeConstraints { make in
            make.centerY.equalTo(durationLab.snp.centerY)
            make.leading.equalTo(secondLab1.snp.trailing).offset(4)
            make.height.width.equalTo(21)
        }
        
    }
    
    
}


extension NickRealtimeView {
    func reset() {
        hourLab1.titleLab.text = "0"
        hourLab2.titleLab.text = "0"
        
        minuteLab1.titleLab.text = "0"
        minuteLab2.titleLab.text = "0"
        
        secondLab1.titleLab.text = "0"
        secondLab2.titleLab.text = "0"
        
        timeLab1.text = ""
        potLab.text = ""
    }
}



class RealtimeView: UIView {
    
    lazy var titleLab: UILabel = {
        let v = UILabel()
        v.font = UIFont.systemFont(ofSize: 18, weight: .medium)
        v.textColor = UIColor("#22E9B9")
        v.textAlignment = .center
        v.text = "0"
        return v
    }()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        makeUI()
    }
    
    deinit {
        
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func makeUI() {
        
        self.backgroundColor = UIColor("#282828")
        self.cornerRadius = 4
        self.borderColor = UIColor("#111111")
        self.borderWidth = 1
        
        addSubviews([titleLab])
   
        titleLab.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
    }
    
    
}
