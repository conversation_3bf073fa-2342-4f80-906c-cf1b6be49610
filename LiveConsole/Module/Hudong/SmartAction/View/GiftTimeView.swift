//
//  GiftTimeView.swift
//  LivePlus
//
//  Created by simon on 16.1.25.
//

import Foundation

protocol GiftTimeViewProtocol: NSObjectProtocol {
    // 选中了一个
    func didSelected(mode: GiftSameGapModel)
}

class GiftTimeView: UIView {
       
    weak  var delegate: GiftTimeViewProtocol?
//    /// 当
    public var infos: [GiftSameGapModel] = []
    
    lazy var line: UIView = {
        let v = UIView()
        v.backgroundColor = UIColor("#645F75")
        return v
    }()
    
    lazy var titleLab: UILabel = {
        let v = UILabel()
        v.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        v.textColor = .white
        v.text = "100钻以下礼物等待时长"
        return v
    }()
    
    
    // MARK: - Getter
  
    private lazy var myCollection: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        layout.minimumLineSpacing = 18
        layout.minimumInteritemSpacing = 16
        layout.itemSize = CGSize(width: (LCDevice.screenW - 66) / 3.0, height: 40)
        let collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        collectionView.backgroundColor = UIColor("#59536C")
        collectionView.register(cellWithClass: GiftTimeCell.self)
        collectionView.delegate = self
        collectionView.dataSource = self
        collectionView.showsHorizontalScrollIndicator = false
        collectionView.isScrollEnabled = true
        collectionView.contentInset = UIEdgeInsets(top: 15, left: 16, bottom: 15, right: 16)
        return collectionView
    }()
    
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        self.backgroundColor = UIColor("#59536C")
        self.cornerRadius = 12
        setupCollectionView()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    
    func setupCollectionView() {
        addSubviews([titleLab, line, myCollection])
        
        titleLab.snp.makeConstraints { make in
            make.leading.equalToSuperview().inset(20)
            make.top.equalToSuperview().inset(15)
            make.height.equalTo(22)
        }
        
        line.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview()
            make.top.equalToSuperview().inset(52)
            make.height.equalTo(1)
        }
        
        myCollection.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(53)
            make.left.right.equalToSuperview()
            make.bottom.equalToSuperview()
        }
        
    }
   
    func updateView(infos: [GiftSameGapModel]) {
        self.infos = infos
        self.myCollection.reloadData()
    }
    
    func reloadData() {
        self.myCollection.reloadData()
    }
    
}




// MARK: - UICollectionViewDataSource UICollectionViewDelegateFlowLayout
extension GiftTimeView: UICollectionViewDataSource, UICollectionViewDelegateFlowLayout {
        
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return infos.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withClass: GiftTimeCell.self, for: indexPath)
        let model = infos[indexPath.row]
        cell.bind(to: model)
        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        collectionView.deselectItem(at: indexPath, animated: true)
        let model = infos[indexPath.row]
        self.delegate?.didSelected(mode: model)
    }
}
