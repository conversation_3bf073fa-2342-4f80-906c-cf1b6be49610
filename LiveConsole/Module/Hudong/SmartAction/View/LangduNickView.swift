//
//  LangduNickView.swift
//  LiveConsole
//
//  Created by simon on 12.5.25.
//

import Foundation


protocol LangduNickViewDelegate: NSObjectProtocol {
    func actionRlue()
    func actionOn_off()
}

class LangduNickView: UIView {
    
    weak var delegate:LangduNickViewDelegate?
    
    lazy var topbg: UIView = {
        let button = UIView()
        button.backgroundColor = .clear
        return button
    }()
    
    ///封面图
   private lazy var on_offButton: UIButton = {
        let button = UIButton()
        button.setImage(UIImage(named: "icon_互动_关"), for: .normal)
        button.setImage(UIImage(named: "icon_互动_开"), for: .selected)
        button.addTarget(self, action: #selector(on_offAction), for: .touchUpInside)
        return button
    }()
    
    lazy var ruleButton: UIButton = {
        let button = UIButton()
        button.setTitle("计费规则", for: .normal)
        button.setTitleColor(UIColor("#ADAAFF"), for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 14, weight: .regular)
        button.addTarget(self, action: #selector(ruleAction), for: .touchUpInside)
        return button
    }()
    
    lazy var titleLab: UILabel = {
        let v = UILabel()
        v.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        v.textColor = UIColor("#FCFCFC")
        v.text = "互动时实时朗读用户昵称"
        return v
    }()
    
    lazy var retimeView: NickRealtimeView = {
        let v = NickRealtimeView()
        return v
    }()
    
    
    private var timer: DispatchSourceTimer?
    
    private var isTimerRunning = false
    
    var duration: Int = 0
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        makeUI()
    }
    
    deinit {
        
    }
    
    @objc func ruleAction() {
        self.delegate?.actionRlue()
    }
    
    @objc func  on_offAction() {
        self.delegate?.actionOn_off()
    }
    
    
    convenience init() {
        self.init(frame: .zero)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func makeUI() {
        
        self.backgroundColor = UIColor("#282828")
        self.cornerRadius = 12
        self.borderColor = UIColor("#010101")
        self.borderWidth = 1
        
        addSubviews([topbg, retimeView])
                
        topbg.addSubviews([titleLab, ruleButton, on_offButton])
        
        topbg.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview()
            make.height.equalTo(51)
        }
        
        titleLab.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.leading.equalToSuperview().inset(12)
        }
        
        ruleButton.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.leading.equalTo(titleLab.snp.trailing).offset(6)
            make.width.equalTo(60)
            make.height.equalTo(40)
        }
        
        on_offButton.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.trailing.equalToSuperview().inset(12)
            make.width.equalTo(55)
            make.height.equalTo(40)
        }
        
        retimeView.snp.makeConstraints { make in
            make.leading.trailing.bottom.equalToSuperview().inset(12)
            make.height.equalTo(71)
        }
    }
    
}

extension LangduNickView {
    public  func updateView(open: Bool) {
        self.on_offButton.isSelected = open
        self.retimeView.isHidden = !open
    }
    
    public func start() {
        startUpdateTimer()
        
        let timeFormatter = DateFormatter.init()
//        4/28 13:00:00
        timeFormatter.dateFormat = "MM/dd HH:mm:ss"
        let strNowTime = timeFormatter.string(from: Date()) as String
        
        retimeView.timeLab1.text = strNowTime
    }
    
    public func stop() {
        stopUpdateTimer()
    }
    
    private func startUpdateTimer() {
        // 先停止已存在的定时器
        stopUpdateTimer()
        let queue = DispatchQueue(label: "com.precision.timer", qos: .userInteractive, attributes: .concurrent)
        // 创建新的定时器
        let timer = DispatchSource.makeTimerSource(queue: queue)
        self.timer = timer
        
        // 配置定时器
        timer.schedule(deadline: .now(), repeating: .milliseconds(1000))
        
        // 设置回调
        timer.setEventHandler { [weak self] in
            guard let self = self else {
                timer.cancel()
                return
            }
            DispatchQueue.main.async {
                self.executeTask()
            }
        }
        
        // 启动定时器
        isTimerRunning = true
        timer.resume()
    }
    
    private func stopUpdateTimer() {
        if isTimerRunning {
            timer?.cancel()
            isTimerRunning = false
        }
        timer = nil
        self.duration = 0
        self.retimeView.reset()
    }
    
    private func executeTask() {
        self.duration = self.duration  + 1
        // 更新UI
        let timestr =  LCTools.second2String(second: Int(duration))
        
        let arr = timestr.components(separatedBy: ":")
        
        if let h = arr.first {
            retimeView.hourLab1.titleLab.text = h.firstCharacterAsString
            retimeView.hourLab2.titleLab.text = h.lastCharacterAsString
        } else {
            retimeView.hourLab1.titleLab.text = "0"
            retimeView.hourLab2.titleLab.text = "0"
        }
       
        if let m = arr[safe: 1] {
            retimeView.minuteLab1.titleLab.text = m.firstCharacterAsString
            retimeView.minuteLab2.titleLab.text = m.lastCharacterAsString
        } else {
            retimeView.minuteLab1.titleLab.text = "0"
            retimeView.minuteLab2.titleLab.text = "0"
        }
        
        if let s = arr.last {
            retimeView.secondLab1.titleLab.text = s.firstCharacterAsString
            retimeView.secondLab2.titleLab.text = s.lastCharacterAsString
        } else {
            retimeView.secondLab1.titleLab.text = "0"
            retimeView.secondLab2.titleLab.text = "0"
        }
        
        
    }
    
}
