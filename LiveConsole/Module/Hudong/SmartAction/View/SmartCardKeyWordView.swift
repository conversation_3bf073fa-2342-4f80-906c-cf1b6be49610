//
//  SmartCardKeyWordView.swift
//  LivePlus
//
//  Created by simon on 17.2.25.
//

import Foundation

class SmartCardKeyWordView: UIView {
        
    lazy var bgV: UIImageView = {
        let v = UIImageView()
        v.backgroundColor = .clear
        v.image = UIImage(named: "smart_card_sanjaox")?.byTintColor(UIColor("#444444"))
        return v
    }()
    
    lazy var contentView: UIView = {
        let v = UIView()
        v.backgroundColor = UIColor.clear
        return v
    }()
    
    
    weak var model: AiKeywordVoiceReplyItemModel?
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        makeUI()
    }
    
    deinit {
        
        
    }
    
    convenience init() {
        self.init(frame: .zero)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    
    func makeUI() {
        
        self.backgroundColor = UIColor.clear
        addSubviews([bgV, contentView])
        
        bgV.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.width.equalTo(6)
            make.height.equalTo(9)
            make.leading.equalToSuperview()
        }
        
        contentView.snp.makeConstraints { make in
            make.top.bottom.trailing.equalToSuperview()
            make.leading.equalToSuperview().inset(6)
        }
        
    }
    
    
    
    public func config(model: AiKeywordVoiceReplyItemModel) {
        for (idx, v) in contentView.subviews.enumerated() {
            v.removeFromSuperview()
        }
        guard let keywords = model.keywords else {
            bgV.isHidden = true
            return
        }
        var keys = keywords.filter({$0.isEmpty == false})
        
        if keys.isEmpty {
            bgV.isHidden = true
            return
        }
        
        bgV.isHidden = false
        
        var sumw: Float = 0
        for (idx, key) in keys.enumerated() {
            
            var str = key
            var w: Float = 48
            if key.count <= 2 {
                w = 48
            } else if key.count == 3 {
                w = 62
            } else  {
                w = 80
                str = String(str.prefix(5))
            }
            
            let lab = UILabel()
            lab.text = str
            lab.font = UIFont.systemFont(ofSize: 14, weight: .medium)
            lab.textColor = .white
            lab.textAlignment = .center
            lab.tag = 1000 + idx
            lab.cornerRadius = 6
            lab.backgroundColor = UIColor("#444444")
            self.contentView.addSubview(lab)
            
            lab.snp.makeConstraints { make in
                make.leading.equalToSuperview().inset(sumw)
                make.width.equalTo(w)
                make.top.bottom.equalToSuperview()
            }
            
            sumw = sumw + w + 6
        }
    }
    
}
