//
//  SmartCardTextView.swift
//  LiveConsole
//
//  Created by simon on 14.5.25.
//

// 文本回复的内容
import Foundation

class SmartCardTextView: UIView {
        
    private lazy var myCollection: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        layout.minimumLineSpacing = 6
        layout.minimumInteritemSpacing = 6
        layout.scrollDirection = .horizontal
        let collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        collectionView.backgroundColor = UIColor.clear
        collectionView.register(cellWithClass: SmartCardTextCell.self)
        collectionView.delegate = self
        collectionView.dataSource = self
        collectionView.showsHorizontalScrollIndicator = false
        collectionView.isScrollEnabled = false
        collectionView.contentInset = UIEdgeInsets(top: 0, left: 0, bottom: 0, right: 0)
        return collectionView
    }()
    
    weak var model: AiKeywordVoiceReplyItemModel?
    
    var resources: [ResFileModel]  {
        if let replyItem = model?.replyItem, replyItem.itemType == .text, let rs = replyItem.resources {
            return rs
        }
        return []
    }
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        makeUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func makeUI() {
        
        addSubviews([myCollection])
       
        myCollection.snp.makeConstraints { make in
            make.top.leading.bottom.trailing.equalToSuperview()
            make.trailing.equalToSuperview().inset(8)
        }
                
    }
    
     func business() {
        
       
    }

}

extension SmartCardTextView {
    
    func bind(to model: AiKeywordVoiceReplyItemModel) {
        self.model = model
        self.myCollection.reloadData()
    }
}


// MARK: - UICollectionViewDataSource UICollectionViewDelegateFlowLayout
extension SmartCardTextView: UICollectionViewDataSource, UICollectionViewDelegateFlowLayout {
        
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        let model = resources[indexPath.row]
        let text: String = model.text ?? ""
        if text.count < 3 {
            return CGSize(width: 100, height: 37)
        } else if  text.count < 6  {
            return CGSize(width: 120, height: 37)
        } else {
            return CGSize(width: 138, height: 37)
        }
    }
    
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return resources.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {

        let cell = collectionView.dequeueReusableCell(withClass: SmartCardTextCell.self, for: indexPath)
        let model = resources[indexPath.row]
        cell.bind(to: model, isFirst: indexPath.row == 0)
        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {

        
    }
}

