//
//  AIBackupView.swift
//  LivePlus
//
//  Created by simon on 8.2.25.
//

// 智能互动和智能讲解的备份

import Foundation

protocol AIBackupViewDelegate: NSObjectProtocol {
    // 关闭
    func invokeClose()
    // 开始导出
    func invokeStartExpose(model: AIBackupModel, title: String)
  
    // 保存文件到本地
    func invokeGotoLocalDocument()
    
}

/// 导出状态
enum BackupOutType {
    case start
    case pending
    case success
}

class AIBackupView: UIView {
    
    weak var delegate: AIBackupViewDelegate?
    
    // 素材数量
    var sourceCount: Int = 0
    var fileSize: Int64 = 0
    
    //改名 弹窗
    lazy var renameAlert: InputAlert = {
        return InputAlert(frame: UIScreen.main.bounds)
    }()
    
    lazy var background: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor("#F5F5F5")
        view.cornerRadius = 20
        return view
    }()
    
    lazy var closeButton: UIButton = {
        let button = UIButton()
        button.setImage(UIImage(named: "icon_close_black"), for: .normal)
        button.zl_enlargeValidTouchArea(inset: 30)
        return button
    }()
    
    lazy var outTitle: UILabel = {
        let label = UILabel()
        label.text = "导出备份"
        label.textColor = UIColor("#4D4E52")
        label.font = LCDevice.DIN_Font_PF_M(16)
        label.textAlignment = .center
        return label
    }()
    
    lazy var roomTitle: UILabel = {
        let label = UILabel()
        label.textColor = UIColor("#4D4E52")
        label.font = LCDevice.DIN_Font_PF_M(14)
        label.lineBreakMode = .byTruncatingMiddle
        return label
    }()
    
    lazy var desTitle: UILabel = {
        let label = UILabel()
        label.textColor = UIColor("#4D4E52")
        label.font = LCDevice.DIN_Font_PF_M(14)
        label.isHidden = true
        return label
    }()
   
    
    lazy var stepView: BackupStepView = {
        let view = BackupStepView()
        view.backgroundColor = .clear
        return view
    }()
    
    
    // 文件的名称
    lazy var nameView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        return view
    }()
    
    lazy var nameTipImageView: UIView = {
        let imageView = UIView()
        imageView.backgroundColor = .white
        imageView.cornerRadius = 8
        return imageView
    }()
    
    lazy var nameTipLabel: UILabel = {
        let label = UILabel()
        label.text = "文件名称"
        label.textColor = UIColor("#1E1F20")
        label.font = LCDevice.DIN_Font_PF_R(14)
        return label
    }()
    
    lazy var nameTipLabel1: UILabel = {
        let label = UILabel()
        label.text = ".zip"
        label.textColor = UIColor("#4D4E52")
        label.font = LCDevice.DIN_Font_PF_M(14)
        return label
    }()
    
    lazy var nameLabel: UILabel = {
        let label = UILabel()
        label.textColor = UIColor("#4D4E52")
        label.font = LCDevice.DIN_Font_PF_M(14)
        label.backgroundColor = .clear
        return label
    }()
    
    lazy var renameButton: UIButton = {
        let button = UIButton()
        button.setImage(UIImage(named: "backup_name"), for: .normal)
        return button
    }()
    
    lazy var namedesTitle: UILabel = {
        let label = UILabel()
        label.textColor = UIColor("#4D4E52")
        label.font = LCDevice.DIN_Font_PF_M(14)
        label.isHidden = true
        return label
    }()
    
    lazy var exposeButton: UIButton = {
        let button = UIButton()
        button.setTitle("开始备份", for: .normal)
        button.cornerRadius = 25
        button.setTitleColor(.white, for: .normal)
        button.titleLabel?.font = LCDevice.DIN_Font_PF_M(16)
        button.backgroundColor = UIColor("#6974F2")
        button.applyGradient()
        return button
    }()
    
   
    lazy var tipLabel: UILabel = {
        let label = UILabel()
        label.textColor = UIColor("#1E1F20")
        label.font = UIFont.systemFont(ofSize: 14, weight: .regular)
        label.backgroundColor = .clear
        label.text = " 使用说明：可将设置好的智能互动模版备份/导入至其他设备或账号使用"
        label.numberOfLines = 0
        return label
    }()
    
    lazy var tipIcon: UIImageView = {
        let label = UIImageView()
        label.image = UIImage(named: "backup_tip")
        return label
    }()
  
    
    /// 导出中状态
    lazy var pendingView: UIView = {
        let view = UIView()
        return view
    }()
    
    /// 导出中动画
    lazy var progressView: BackupProgressView = {
        let view = BackupProgressView()
        return view
    }()
    
    lazy var animationTitle: UILabel = {
        let label = UILabel()
        label.text = "打包备份中，请耐心等待..."
        label.font = LCDevice.DIN_Font_PF_M(15)
        label.textColor = UIColor("#6974F2")
        return label
    }()
    
    lazy var successView: UIView = {
        let view = UIView()
        return view
    }()
    
    lazy var successButton: UIImageView = {
        let button = UIImageView()
        button.image = UIImage(named: "backup_success")
        return button
    }()
    
    lazy var successDes: UILabel = {
        let label = UILabel()
        label.textColor = UIColor("#4D4E52")
        label.font = LCDevice.DIN_Font_PF_R(16)
        label.text = "导出成功"
        return label
    }()
    
    lazy var gotoLocalDocumentButton: UIButton = {
        let button = UIButton()
        button.frame = CGRect(x: 0, y: 0, width: 185, height: 50)
        button.setTitle("保存备份文件", for: .normal)
        button.titleLabel?.font = LCDevice.DIN_Font_PF_M(16)
        button.setTitleColor(.white, for: .normal)
        button.cornerRadius = 25
        button.applyGradient()
        return button
    }()
    
    
    lazy var successTitle: UILabel = {
        let label = UILabel()
        label.textColor = UIColor("#797B7D")
        label.font = LCDevice.DIN_Font_PF_R(13)
        label.text = "本次导出共花费 "
        label.textAlignment = .center
        label.isHidden = true
        return label
    }()
    
    
    var backupType: BackupOutType = .start
    
    var model: AIBackupModel?
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        
        makeUI()
        business()
    }
    
    deinit {
        LCLog.d("BackupOutView deinit")
    }
    
    convenience init() {
        self.init(frame: .zero)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func makeUI() {
        addSubview(background)
        background.addSubviews([closeButton, outTitle, roomTitle, stepView, nameView, exposeButton, desTitle, tipIcon, tipLabel])

        nameView.addSubviews([nameTipImageView, nameTipLabel, nameTipLabel1, namedesTitle])
        
        nameTipImageView.addSubviews([nameLabel, renameButton])
        
        background.addSubview(pendingView)
        pendingView.addSubviews([progressView, animationTitle])
        
        background.addSubview(successView)
        successView.addSubviews([successButton, gotoLocalDocumentButton, successTitle, successDes])
        
        self.makeConstraints()
      
        
    }
    
    func business() {
                
        exposeButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self else { return }

                if let roomModel = self.model {
                    let text = (self.roomTitle.text ?? "").replacingOccurrences(of: "文件名称：", with: "")
                    self.delegate?.invokeStartExpose(model: roomModel, title: text)
                }
            }.disposed(by: rx.disposeBag)
        

        gotoLocalDocumentButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self else { return }
                self.delegate?.invokeGotoLocalDocument()
            }.disposed(by: rx.disposeBag)
        
        closeButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self else { return }
                self.delegate?.invokeClose()
            }.disposed(by: rx.disposeBag)
        
        renameButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self else { return }

                self.showRoomTitleChangeAlert()
            }.disposed(by: rx.disposeBag)
    }

   
    
    
    ///显示修改名称弹窗
    func showRoomTitleChangeAlert() {
        guard let text = self.nameLabel.text else { return }
        renameAlert.show(text, 40, selectAll: false)
        renameAlert.sureBtnActionBlock = {[weak self] (_ newName) in
            if newName.isEmpty {
                HUD.showFail("请输入文件名称")
                return
            }
            guard let self = self else { return }
            self.roomTitle.text = "文件名称：" + newName + ".zip"
            self.nameLabel.text = newName
        }
    }
}

// MARK: - 业务逻辑
extension AIBackupView {
    // 绑定智能互动的数据
    func bind(to model: AIBackupModel) {
        
        self.model = model
        
        self.clear()
      
        var count: Int = 0
        var sumFileSize: Int64 = 0
        
        var tmpSources: [AreaItemModel] = []
                
        model.dataSource.cardList.forEach { replymode in
            if let replyItem = replymode.replyItem, let resources =  replyItem.resources {
                resources.forEach { file in
                    let areaItem = file.toAreaItem()
                    
                   if let name = areaItem.name {
                        if !tmpSources.contains(where: {$0.name == name }) {
                            tmpSources.append(areaItem)
                        }
                    }
                }
            }
        }
        
        tmpSources.forEach { areaItem in
           if let path = areaItem.path, let size = XCFileManager.sizeOfFile(atPath: path) as? Int64 {
                sumFileSize = sumFileSize + Int64(size)
            }
        }
        
        
        count = tmpSources.count
        self.fileSize = sumFileSize
        let size = sumFileSize == 0 ? "20KB" : LCTools.formatFileSize(fileS: sumFileSize)
        LCLog.d("---------素材数量：\(count)")
        LCLog.d("---------素材总大小：\(size)")
        sourceCount = count
       
        self.desTitle.text = "预估文件大小：\(size) "
        self.namedesTitle.text = self.desTitle.text
        
        var title = "ZNHD".toBackupTitle()
        
        self.roomTitle.text = "文件名称：" + title
        
        self.nameLabel.text = title.replacingOccurrences(of: ".zip", with: "")
       
        self.updateShowingType(type: .start)
    }
    
}

extension AIBackupView {
    func makeConstraints() {
        background.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        closeButton.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(21)
            make.trailing.equalToSuperview().inset(15)
            make.width.height.equalTo(24)
        }
        outTitle.snp.makeConstraints { make in
            make.centerY.equalTo(closeButton.snp.centerY)
            make.centerX.equalToSuperview()
            make.width.equalTo(200)
            make.height.equalTo(24)
        }
        
        stepView.snp.makeConstraints { make in
            make.top.equalTo(outTitle.snp.bottom).offset(30)
            make.centerX.equalToSuperview()
            make.width.equalTo(108 * 3 - 20)
            make.height.equalTo(20)
        }
        
        roomTitle.snp.makeConstraints { make in
            make.top.equalTo(stepView.snp.bottom).offset(25)
            make.leading.trailing.equalToSuperview().inset(22)
        }
        
        desTitle.snp.makeConstraints { make in
            make.top.equalTo(roomTitle.snp.bottom).offset(16)
            make.leading.trailing.equalToSuperview().inset(22)
        }
        
        
        nameView.snp.makeConstraints { make in
            make.top.equalTo(stepView.snp.bottom).offset(25)
            make.leading.trailing.equalToSuperview()
            make.height.equalTo(125)
        }
        
        exposeButton.snp.makeConstraints { make in
            make.bottom.equalToSuperview().inset(LCDevice.BOTTOM_BAR)
            make.leading.trailing.equalToSuperview().inset(22)
            make.height.equalTo(50)
        }
        
        tipIcon.snp.makeConstraints { make in
            make.top.equalTo(exposeButton.snp.bottom).offset(30)
            make.leading.equalToSuperview().inset(24)
            make.height.width.equalTo(18)
        }
        
        tipLabel.snp.makeConstraints { make in
            make.top.equalTo(tipIcon.snp.top)
            make.leading.equalTo(tipIcon.snp.trailing).offset(8)
            make.trailing.equalToSuperview().inset(24)
        }
        
        nameTipLabel.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.leading.equalToSuperview().inset(22)
            make.height.equalTo(20)
        }
        
        nameTipImageView.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(30)
            make.leading.equalToSuperview().inset(22)
            make.trailing.equalToSuperview().inset(55)
            make.height.equalTo(40)
        }
        nameTipLabel1.snp.makeConstraints { make in
            make.centerY.equalTo(nameTipImageView.snp.centerY)
            make.leading.equalTo(nameTipImageView.snp.trailing).offset(10)
        }
        nameLabel.snp.makeConstraints { make in
            make.centerY.equalTo(nameTipImageView.snp.centerY)
            make.leading.equalToSuperview().inset(15)
            make.trailing.equalToSuperview().inset(45)
        }
        renameButton.snp.makeConstraints { make in
            make.trailing.equalToSuperview().inset(4)
            make.width.height.equalTo(40)
            make.centerY.equalTo(nameTipImageView.snp.centerY)
        }
        
        namedesTitle.snp.makeConstraints { make in
            make.bottom.equalToSuperview()
            make.leading.trailing.equalToSuperview().inset(22)
            make.height.equalTo(20)
        }
        
        pendingView.snp.makeConstraints { make in
            make.top.equalTo(desTitle.snp.bottom).offset(30)
            make.leading.trailing.equalToSuperview().inset(30)
            make.height.equalTo(230)
        }
        animationTitle.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalToSuperview()
        }
        
        progressView.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview()
            make.height.equalTo(150)
            make.top.equalToSuperview().inset(60)
        }
        
        successView.snp.makeConstraints { make in
            make.bottom.leading.trailing.equalToSuperview()
            make.height.equalTo(200)
        }
        
        
        successButton.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.centerX.equalToSuperview()
            make.height.width.equalTo(36)
        }
        
        successDes.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalTo(successButton.snp.bottom).offset(14)
            make.height.equalTo(26)
        }
        
        gotoLocalDocumentButton.snp.makeConstraints { make in
            make.bottom.equalToSuperview().inset(40)
            make.leading.trailing.equalToSuperview().inset(31)
            make.height.equalTo(50)
        }
        
       
        successTitle.snp.makeConstraints { make in
            make.top.equalTo(gotoLocalDocumentButton.snp.bottom).offset(30)
            make.centerX.equalToSuperview()
            make.width.equalTo(330)
            make.height.equalTo(16)
        }
        
     
    }
}


extension AIBackupView {
    func updateShowingType(type: BackupOutType, size: String = "", time: Int64 = 0) {
        self.closeButton.isHidden = false
        switch type {
        case .start:
            self.pendingView.isHidden = true
            self.successView.isHidden = true
            self.roomTitle.isHidden = true
            self.desTitle.isHidden = true
            self.showOrHideStartViews(hidden: false)
            self.stepView.bind(to: .one)
        case .pending:
            self.pendingView.isHidden = false
            self.successView.isHidden = true
            self.roomTitle.isHidden = false
//            self.desTitle.isHidden = false


            self.showOrHideStartViews(hidden: true)
            self.stepView.bind(to: .two)
        case .success:
            self.closeButton.isHidden = true
            self.pendingView.isHidden = true
            self.successView.isHidden = false
            self.roomTitle.isHidden = false
//            self.desTitle.isHidden = false
            self.stepView.bind(to: .three)
//            self.desTitle.text = "预估文件大小：\(size) "

            self.showOrHideStartViews(hidden: true)
            let attributes1 = [NSAttributedString.Key.font: LCDevice.DIN_Font_PF_M(13),
                              NSAttributedString.Key.foregroundColor: UIColor("#ABABAB")]
            let attribeString1 = NSAttributedString(string: "本次导出共花费 ", attributes: attributes1)
            
            let attributes2 = [NSAttributedString.Key.font: LCDevice.DIN_Font_PF_M(13),
                              NSAttributedString.Key.foregroundColor: UIColor("#6863F7")]
            let attribeString2 = NSAttributedString(string: "\(time.getFormatPlayTime())", attributes: attributes2)
            let attString = NSMutableAttributedString()
            attString.append(attribeString1)
            attString.append(attribeString2)
            self.successTitle.attributedText =  attString
            
        }
    }
    
    func showOrHideStartViews(hidden: Bool) {
        self.nameView.isHidden = hidden
        self.exposeButton.isHidden = hidden
        self.tipIcon.isHidden = hidden
        self.tipLabel.isHidden = hidden
    }
    
    // 更新进度
    func process(progress: CGFloat, fileSize: Int64) {
        self.fileSize = fileSize
        self.progressView.progressView(progress: progress, fileSize: fileSize)
//        self.progressView.progressView.progress = progress
//        let lasttime = CGFloat(fileSize) * (1.0 - 0.1 - progress) / CGFloat(LCSingleton.zipSpeed)
//        self.progressView.expectedTimeLabel.text = "预计还需\(Int64(lasttime).getFormatPlayTime())"
    }
    
    func clear() {
        self.progressView.progressView.progress = 0.0
        self.progressView.expectedTimeLabel.text = "预计还需"
        self.progressView.sendedTimeLabel.text = "已传输"
        self.progressView.releaseTimer()
    }
    
}



extension String {
    func toBackupTitle() -> String {
        // 需要编码 否则服务端会报错
        return "\(self)_\(Date().string(withFormat: "yyyyMMdd_hhmmss")).zip"
    }
}
