//
//  SmartActionNavigation.swift
//  LivePlus
//
//  Created by 郭炜 on 2025/1/13.
//

import UIKit

enum SmartNavigationType {
    case download
    case upload
    case bindOrUnbind // 绑定或者是解绑
    case memberPotins // 积分
    case on_offsmart// 开启和关闭
}

protocol SmartActionNavigationDelegate: NSObjectProtocol {
    func didAction(type: SmartNavigationType)
}

class SmartActionNavigation: SmartBaseView {
    weak var delegate: SmartActionNavigationDelegate?

    
    lazy var background: UIView = {
        let view = UIView()
        return view
    }()
    
    
    lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.text = "智能互动"
        label.textColor = .white
        label.font = UIFont.systemFont(ofSize: 17)
        return label
    }()
    
    lazy var on_offButton: UIButton = {
        let button = UIButton()
        button.setImage(UIImage(named: "ic_smart_off"), for: .normal)
        button.setImage(UIImage(named: "ic_smart_on"), for: .selected)
        button.setTitle("停用", for: .normal)
        button.setTitle("开启", for: .selected)
        button.setTitleColor(UIColor.white.alpha(value: 0.6), for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 12, weight: .regular)
        button.zl_enlargeValidTouchArea(inset: 20)
        return button
    }()
    
    lazy var potionsButton: UIButton = {
        let button = UIButton()
        button.setImage(UIImage(named: "积分"), for: .normal)
        button.zl_enlargeValidTouchArea(inset: 8)
        return button
    }()
    
    // 连接状态的UI
    lazy var statusButton: UIButton = {
        let button = UIButton()
        button.setImage(UIImage(named: "ic_smart_disconnect"), for: .normal)
        button.setTitleColor(UIColor.white, for: .normal)
        button.setTitle("未登录", for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 12, weight: .regular)
        button.zl_enlargeValidTouchArea(inset: 8)
        return button
    }()

    override init(frame: CGRect) {
        super.init(frame: frame)
    }
    
    convenience init() {
        self.init(frame: .zero)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func makeUI() {
        super.makeUI()
        
        addSubview(background)
        background.addSubviews([ titleLabel, on_offButton, statusButton, potionsButton])
        
        background.snp.makeConstraints { make in
            make.bottom.leading.trailing.equalToSuperview()
            make.height.equalTo(44)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.leading.equalToSuperview().inset(16)
            make.height.equalTo(22)
        }
        
        potionsButton.snp.makeConstraints { make in
            make.leading.equalTo(titleLabel.snp.trailing).offset(6)
            make.centerY.equalToSuperview()
            make.height.equalTo(28)
            make.width.equalTo(50)
        }
        
        statusButton.snp.makeConstraints { make in
            make.trailing.equalToSuperview().inset(16)
            make.centerY.equalToSuperview()
            make.height.equalTo(32)
            make.width.equalTo(50)
        }
        
        on_offButton.snp.makeConstraints { make in
            make.trailing.equalTo(statusButton.snp.leading).offset(-12)
            make.centerY.equalToSuperview()
            make.height.equalTo(32)
            make.width.equalTo(50)
        }
        on_offButton.addTarget(self, action: #selector(on_offAction), for: .touchUpInside)
        statusButton.addTarget(self, action: #selector(statusAction), for: .touchUpInside)
        
    }
    
    @objc func on_offAction() {
        self.delegate?.didAction(type: .on_offsmart)
    }
    
    @objc func statusAction() {
        self.delegate?.didAction(type: .bindOrUnbind)
    }
    
    override func business() {
        super.business()
        
        potionsButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self else { return }
                self.delegate?.didAction(type: .memberPotins)
            }.disposed(by: rx.disposeBag)
    }

}
