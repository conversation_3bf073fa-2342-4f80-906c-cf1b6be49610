//
//  AIConfigSKPView.swift
//  LivePlus
//
//  Created by simon on 21.1.25.
//

import Foundation

class AIConfigSKPView: UIView {
       
    lazy var yinseLab: UILabel = {
        let v = UILabel()
        v.font = UIFont.systemFont(ofSize: 14, weight: .regular)
        v.textColor = UIColor.white.alpha(value: 0.6)
        v.textAlignment = .center
        v.text = "朗读音色"
        return v
    }()
 
    lazy var yinseView: AudioYinSeSelectionView = {
        let v = AudioYinSeSelectionView()
        v.backgroundColor = UIColor("#282828")
        v.contentView.backgroundColor = UIColor("#282828")
        v.presetView.backgroundColor = UIColor("#282828")
        v.presetView.collectionView.backgroundColor = UIColor("#282828")
        v.cloneView.backgroundColor = UIColor("#282828")
        v.cloneView.collectionView.backgroundColor = UIColor("#282828")
        v.presetView.itemWidth = (LCDevice.screenW - 16 * 5) / 2.0 - 1
        v.cloneView.itemWidth = (LCDevice.screenW - 16 * 5) / 2.0 - 1
        v.cloneView.cellColor = "#111111"
        v.presetView.cellColor = "#111111"
        return v
    }()
    
    var model: AiConfigModel?
    
    
    var dataSource: [AIAudioResultModel] = []
    var playingModel: AIAudioResultModel?

    
    override init(frame: CGRect) {
        super.init(frame: frame)
        makeUI()
        business()
    }
    
    deinit {
        
    }
    
    convenience init() {
        self.init(frame: .zero)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
     func makeUI() {
         self.backgroundColor = UIColor("#282828")
        addSubviews([yinseLab, yinseView])
                
         yinseLab.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.leading.equalToSuperview().inset(16)
            make.height.equalTo(32)
        }
        
        yinseView.snp.makeConstraints { make in
            make.top.equalTo(yinseLab.snp.bottom)
            make.leading.trailing.bottom.equalToSuperview()
        }
    }
    
    
    func business() {
        
    }
    
}


extension AIConfigSKPView {
    
    /// 音色列表（预置+克隆）
    func updateYinseView(audioSpkIDModel: AudioSpkIdModel?) {
        yinseView.presetView.bind(to: audioSpkIDModel?.preSpk ?? [],
                                  selectionVoiceType: AIReplyDataManager.shared.spkIdCache.currentPreAudioModel?.voiceType ?? "")
        yinseView.cloneView.bind(to: audioSpkIDModel?.userSpk ?? [],
                                 selectionSpeakId: AIReplyDataManager.shared.spkIdCache.currentCloneAudioModel?.spkId ?? "")
    }
    
    // 选中之后要更新
    func updateSelectedYinseView() {
        yinseView.presetView.updateSelected(selectionVoiceType: AIReplyDataManager.shared.spkIdCache.currentPreAudioModel?.voiceType ?? "")
        yinseView.cloneView.updateSelected(selectionSpeakId: AIReplyDataManager.shared.spkIdCache.currentCloneAudioModel?.spkId ?? "")
    }

}
