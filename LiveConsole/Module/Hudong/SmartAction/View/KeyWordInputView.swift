//
//  KeyWordInputView.swift
//  LivePlus
//
//  Created by simon on 11.4.25.
//

import Foundation
// 新建一个 自定义回复
import RSKPlaceholderTextView


protocol KeyWordInputViewDelegate: NSObjectProtocol {
    func doneKeyWord(model: AiAutoTtsModel)
}

class KeyWordInputView: UIView {
    
    weak var delegate: KeyWordInputViewDelegate?
         
    let maxCount = 100
    
    var nonEditableRanges = [NSRange]() // 存储受保护的范围
    
    var itemModel: AiAutoTtsModel?
        
    lazy var cancelButton: UIButton = {
        let button = UIButton()
        button.setTitle("取消", for: .normal)
        button.setTitleColor(UIColor("#4D4E52"), for: .normal)
        button.titleLabel?.font = LCDevice.DIN_Font_PF_R(14)
        button.backgroundColor = .white
        button.addTarget(self, action: #selector(cancelButtonAction), for: .touchUpInside)
        button.cornerRadius = 22
        button.borderWidth = 1
        button.borderColor = UIColor("#6974F2")
        return button
    }()
    
    lazy var okButton: UIButton = {
        let button = UIButton()
        button.setTitle("确定", for: .normal)
        button.setTitleColor(UIColor.white, for: .normal)
        button.titleLabel?.font = LCDevice.DIN_Font_PF_R(14)
        button.backgroundColor = UIColor("#6974F2")
        button.addTarget(self, action: #selector(okButtonAction), for: .touchUpInside)
        button.cornerRadius = 22
        return button
    }()
    
    lazy var titleLab: UILabel = {
        let label = UILabel()
        label.textAlignment = .center
        label.textColor = UIColor("#19191A")
        label.text = "编辑内容"
        label.font = UIFont.systemFont(ofSize: 18, weight: .bold)
        return label
    }()
    
    lazy var subLab: UILabel = {
        let label = UILabel()
        label.textColor = UIColor("#ACACAC")
        label.text = "【】中的内容不可删除或进行修改"
        label.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        return label
    }()
    
    
    private lazy var bgview: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        view.cornerRadius = 12
        return view
    }()
    

    
    private lazy var keytextView: RSKPlaceholderTextView = {
        let textView = RSKPlaceholderTextView()
        textView.isScrollEnabled = true
        textView.delegate = self
        textView.borderColor = UIColor("#EAEAEA")
        textView.borderWidth = 1
        textView.backgroundColor =  .white
        textView.font = UIFont.systemFont(ofSize: 17, weight: .regular)
        textView.textColor = UIColor("#1A1A1A")
        textView.placeholderColor = UIColor("#BBBBBB")
        textView.placeholder = "请输入关键词"
        textView.cornerRadius = 8
        textView.returnKeyType = .done
        return textView
    }()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        makeUI()
        business()
        
    }
    
    convenience init() {
        self.init(frame: .zero)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func makeUI() {
        backgroundColor = UIColor("#000000").alpha(value: 0.4)
        addSubview(bgview)
        
        bgview.addSubviews([keytextView,titleLab, subLab, cancelButton, okButton])
        
        bgview.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(tapAction)))
        
        titleLab.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview()
            make.top.equalToSuperview().inset(30)
        }
        
        keytextView.snp.makeConstraints { make in
            make.trailing.leading.equalToSuperview().inset(30)
            make.top.equalToSuperview().inset(85)
            make.height.equalTo(76)
        }
     
        subLab.snp.makeConstraints { make in
            make.leading.equalToSuperview().inset(30)
            make.top.equalTo(keytextView.snp.bottom).offset(8)
            make.trailing.leading.equalToSuperview().inset(30)
            make.height.equalTo(24)
        }
        
        
        cancelButton.snp.makeConstraints { make in
            make.leading.equalToSuperview().inset(40)
            make.bottom.equalToSuperview().inset(30)
            make.height.equalTo(44)
            make.width.equalTo(110)
        }
        
        okButton.snp.makeConstraints { make in
            make.trailing.equalToSuperview().inset(40)
            make.bottom.equalToSuperview().inset(30)
            make.height.equalTo(44)
            make.width.equalTo(110)
        }
        
        bgview.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(LCDevice.Nav_H + 40)
            make.leading.trailing.equalToSuperview().inset(28)
            make.height.equalTo(288)
        }
        
    }
    
    func business() {
        
    }
    
    @objc func tapAction() {
        self.endEditing(true)
    }
    
    
    @objc func okButtonAction() {
        self.resignFirstResponder()
        
        // 校验关键词
        guard let key = self.keytextView.text, !key.isBlank, !key.isEmpty else {
            HUD.showFail("请填写回复内容")
            return
        }
        itemModel?.text = key
        self.isHidden = true
        self.removeFromSuperview()
        guard let itemModel = itemModel else { return  }
        self.delegate?.doneKeyWord(model: itemModel)
        
    }
    
    @objc public func cancelButtonAction() {
        self.resignFirstResponder()
        self.isHidden = true
        self.removeFromSuperview()
    }
  
    
    func show(model:AiAutoTtsModel) {
        self.keytextView.becomeFirstResponder()
        self.itemModel = model
        self.keytextView.attributedText = LCTools.checkNickConten(text: model.text, color: UIColor("#4D4E52"))
        updateNonEditableRanges()
    }
 
}

extension KeyWordInputView: UITextViewDelegate {

    func textView(_ textView: UITextView, shouldChangeTextIn range: NSRange, replacementText text: String) -> Bool {
        // First, update non-editable ranges based on current text
        updateNonEditableRangesWithoutStyling()
        
        // Check if we're trying to edit within protected ranges
        for nonEditableRange in nonEditableRanges {
            // Only check if the range is completely inside the non-editable range
            // Allow editing at the boundaries (before and after the protected content)
            if range.location > nonEditableRange.location &&
               range.location < nonEditableRange.location + nonEditableRange.length {
                return false // Prohibit editing inside non-editable areas
            }
            
            // If we're replacing text that spans across or includes protected content
            if range.location <= nonEditableRange.location &&
               range.location + range.length > nonEditableRange.location {
                return false
            }
        }
        
        if text == "\n" {
            textView.resignFirstResponder()
            return false
        }
        
        if let result = (textView.text as NSString?)?.replacingCharacters(in: range, with: text) {
            // Check length limit
            if result.count > maxCount {
                HUD.showFail("超过最大的字数限制")
                return false
            }
        }
        
        return true
    }
   
    func textViewDidChange(_ textView: UITextView) {
        updateNonEditableRanges()
    }
    
    // Update non-editable ranges without applying styling (for range checking)
    func updateNonEditableRangesWithoutStyling() {
        guard let text = keytextView.text else { return }
        let pattern = "【.*?】"
        guard let regex = try? NSRegularExpression(pattern: pattern) else { return }
        
        nonEditableRanges = regex.matches(in: text, range: NSRange(text.startIndex..., in: text))
            .map { $0.range }
    }
    
    // Update non-editable ranges with styling
    func updateNonEditableRanges() {
        guard let text = keytextView.text else { return }
        let pattern = "【.*?】"
        guard let regex = try? NSRegularExpression(pattern: pattern) else { return }
        
        // Get all non-editable ranges
        nonEditableRanges = regex.matches(in: text, range: NSRange(text.startIndex..., in: text))
            .map { $0.range }
        
        // Preserve cursor position to avoid cursor jumping after style refresh
        let selectedRange = keytextView.selectedRange
        self.keytextView.attributedText = LCTools.checkNickConten(text: text, color: UIColor("#4D4E52"))
        keytextView.selectedRange = selectedRange
    }
}
