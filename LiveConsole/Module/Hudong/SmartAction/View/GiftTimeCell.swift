//
//  GiftTimeCell.swift
//  LivePlus
//
//  Created by simon on 16.1.25.
//

import Foundation
import UIKit


class GiftTimeCell: UICollectionViewCell {
            
    private lazy var titleLab: UILabel = {
        let label = UILabel()
        label.textColor = .white
        label.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        label.backgroundColor = .clear
        label.textAlignment = .center
        return label
    }()
    
    private lazy var bgview: UIView = {
        let label = UIView()
        label.backgroundColor = UIColor("#352E4D")
        return label
    }()

    override func awakeFromNib() {
        super.awakeFromNib()
    }

    
    override init(frame: CGRect) {
        super.init(frame: frame)
        contentView.backgroundColor = UIColor.clear
        makeUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    func makeUI() {
        contentView.addSubviews([bgview])
        bgview.cornerRadius = 6
        bgview.borderColor = .clear
        bgview.borderWidth = 1
        
        bgview.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        bgview.addSubviews([titleLab])

        titleLab.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview()
        }
    }
    
}

extension GiftTimeCell {
    func bind(to model: GiftSameGapModel) {
        titleLab.text = model.gap.title
        bgview.borderColor =  model.selected ? UIColor("#ADAAFF") : .clear
    }
}
