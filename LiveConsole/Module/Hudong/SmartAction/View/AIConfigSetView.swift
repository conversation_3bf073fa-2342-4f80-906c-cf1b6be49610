//
//  AIConfigSetView.swift
//  LivePlus
//
//  Created by simon on 20.1.25.
// 智能互动设置View

import Foundation
import RSKPlaceholderTextView

protocol AIConfigSetViewDelegate: NSObjectProtocol {
    func didAction()
}

class AIConfigSetView: UIView {
    
    weak var delegate: AIConfigSetViewDelegate?
    
    lazy var titleLab: UILabel = {
        let v = UILabel()
        v.font = UIFont.systemFont(ofSize: 14, weight: .regular)
        v.textColor = UIColor.white
        v.text = "互动时实时朗读用户昵称"
        return v
    }()
    
    lazy var desLab: UILabel = {
        let v = UILabel()
        v.font = UIFont.systemFont(ofSize: 12, weight: .regular)
        v.textColor = UIColor.white.alpha(value: 0.7)
        v.text = "注：该功能为按时计费功能，需在智能互动页面开启后才能\n使用"
        v.numberOfLines = 0
        return v
    }()
    
    lazy var fanView: UIStackView = {
        let stack = UIStackView()
        stack.axis = .horizontal
        stack.spacing = 12
        stack.distribution = .fillEqually
        stack.backgroundColor =  UIColor("#111111")
        stack.cornerRadius = 6
        return stack
    }()
    
    
    lazy var bgView: UIView = {
        let v = UIView()
        v.backgroundColor = UIColor("#282828")
        v.cornerRadius = 12
        return v
    }()
    
    /// 音色列表（预置+克隆）
    var audioSpkIDModel: AudioSpkIdModel?

    
    lazy var spkView: AIConfigSKPView = {
        let v = AIConfigSKPView()
        return v
    }()
    
    lazy var keyView: AIConfigReplyView = {
        let v = AIConfigReplyView(type: .keyword)
        return v
    }()
    
    lazy var giftView: AIConfigReplyView = {
        let v = AIConfigReplyView(type: .gift)
        return v
    }()
    
    lazy var guanzhuView: AIConfigReplyView = {
        let v = AIConfigReplyView(type: .follow)
        return v
    }()
    
    
    
    lazy var nameLab: UILabel = {
        let v = UILabel()
        v.font = UIFont.systemFont(ofSize: 14, weight: .regular)
        v.textColor = UIColor.white.alpha(value: 0.6)
        v.text = "使用范围"
        return v
    }()
    
    
    lazy var keyButton: UIButton = {
        let button = UIButton()
        button.setTitle("关键词回复", for: .normal)
        button.setImage(UIImage(named: "reply_nor"), for: .normal)
        button.setImage(UIImage(named: "reply_sel"), for: .selected)
        button.addTarget(self, action: #selector(keyAction), for: .touchUpInside)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 14)
        button.setTitleColor(UIColor.white, for: .normal)
        button.imagePosition(style: .left, spacing: 8)
        return button
    }()
    
    
    lazy var giftButton: UIButton = {
        let button = UIButton()
        button.setTitle("送礼回复", for: .normal)
        button.setImage(UIImage(named: "reply_nor"), for: .normal)
        button.setImage(UIImage(named: "reply_sel"), for: .selected)
        button.setTitleColor(UIColor.white, for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 14)
        button.imagePosition(style: .left, spacing: 8)
        button.addTarget(self, action: #selector(giftAction), for: .touchUpInside)
        return button
    }()
    
    lazy var guanzhuButton: UIButton = {
        let button = UIButton()
        button.setTitle("关注回复", for: .normal)
        button.setImage(UIImage(named: "reply_nor"), for: .normal)
        button.setImage(UIImage(named: "reply_sel"), for: .selected)
        button.setTitleColor(.white, for: .normal)
        button.addTarget(self, action: #selector(guanzhuAction), for: .touchUpInside)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 14)
        button.imagePosition(style: .left, spacing: 8)
        return button
    }()
    
    var model: AiConfigModel?
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        makeUI()
        business()
        getSpeakIDList()
    }
    
    deinit {
        
    }
    
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func makeUI() {
        
        self.backgroundColor = UIColor.clear
        self.addSubview(bgView)
        
        bgView.snp.makeConstraints { make in
            make.bottom.top.equalToSuperview()
            make.leading.trailing.equalToSuperview().inset(16)
        }
        
        bgView.addSubviews([titleLab, desLab, nameLab, fanView])
        
        fanView.addArrangedSubview(guanzhuButton)
        fanView.addArrangedSubview(giftButton)
        fanView.addArrangedSubview(keyButton)
        
        bgView.addSubviews([keyView, giftView, guanzhuView, spkView])
        
        titleLab.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(18)
            make.height.equalTo(20)
            make.leading.equalToSuperview().inset(12)
        }
        
        desLab.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview().inset(12)
            make.top.equalTo(titleLab.snp.bottom).offset(14)
        }
        
        nameLab.snp.makeConstraints { make in
            make.top.equalTo(desLab.snp.bottom).offset(14)
            make.leading.trailing.equalToSuperview().inset(12)
        }
        
        fanView.snp.makeConstraints { make in
            make.top.equalTo(nameLab.snp.bottom).offset(10)
            make.leading.trailing.equalToSuperview().inset(12)
            make.height.equalTo(45)
        }
        
        
        guanzhuView.snp.makeConstraints { make in
            make.top.equalTo(fanView.snp.bottom).offset(14)
            make.height.equalTo(96)
            make.leading.trailing.equalToSuperview()
        }
        
        giftView.snp.makeConstraints { make in
            make.top.equalTo(guanzhuView.snp.bottom).offset(14)
            make.height.equalTo(96)
            make.leading.trailing.equalToSuperview()
        }
        
        
        keyView.snp.makeConstraints { make in
            make.top.equalTo(giftView.snp.bottom).offset(14)
            make.height.equalTo(75)
            make.leading.trailing.equalToSuperview()
        }
        
        spkView.snp.makeConstraints { make in
            make.top.equalTo(keyView.snp.bottom).offset(14)
            make.height.equalTo(300)
            make.leading.trailing.equalToSuperview()
        }
        
    }
    
    @objc func giftAction() {
        self.giftButton.isSelected =  !self.giftButton.isSelected
        self.model?.goodTtsModel.isOpen = self.giftButton.isSelected
        self.delegate?.didAction()
    }
    
    @objc func keyAction() {
        self.keyButton.isSelected =  !self.keyButton.isSelected
        self.model?.keyTtsModel.isOpen = self.keyButton.isSelected
        self.delegate?.didAction()
    }
    
    @objc func guanzhuAction() {
        self.guanzhuButton.isSelected =  !self.guanzhuButton.isSelected
        self.model?.attTtsModel.isOpen = self.guanzhuButton.isSelected
        self.delegate?.didAction()
    }
    
    
    func business() {
        spkView.yinseView.presetView.delegate = self
        spkView.yinseView.delegate = self
    }
    
    public func config(model: AiConfigModel) {
        
        self.giftButton.isSelected =  model.goodTtsModel.isOpen
        self.keyButton.isSelected =  model.keyTtsModel.isOpen
        self.guanzhuButton.isSelected =  model.attTtsModel.isOpen
        
        self.keyView.bind(mode: model.keyTtsModel)
        self.giftView.bind(mode: model.goodTtsModel)
        self.guanzhuView.bind(mode: model.attTtsModel)
        
        self.model = model
        
    }
    
}

// MARK: - 接口请求
extension AIConfigSetView {
    
    /// 获取预制音色、克隆音色
    public func getSpeakIDList() {
        MiddleRequestNet.getSpeakIDList { [weak self] data in
            guard let self = self else { return }
            self.audioSpkIDModel = data
            print("获取到的数据为：\(data)")
            // 如果没有预制音色， 也没有克隆音色，那么就默认选中预制音色吧
            if !AIReplyDataManager.shared.spkIdCache.hasSpeak {
                AIReplyDataManager.shared.spkIdCache.currentPreAudioModel = data?.preSpk.first
            }
            self.spkView.updateYinseView(audioSpkIDModel: data)
        }
    }
}

extension AIConfigSetView: AudioYinSePresetViewDelegate, AudioYinSeSelectionViewDelegate {
    func actionForRetryRequestSpk() {
        getSpeakIDList()
    }
    
    
    /// 选择了预设音色
    func actionForYinsePresetSelection(model: AudioPreSpeakModel) {
        AIReplyDataManager.shared.spkIdCache.currentPreAudioModel = model
        self.spkView.updateSelectedYinseView()
    }
    
    /// 选择了克隆音色
    func actionForYinseCloneSelection(model: AudioUserSpeakModel?) {
        AIReplyDataManager.shared.spkIdCache.currentCloneAudioModel = model
        self.spkView.updateSelectedYinseView()
    }
    
    
}


// MARK: - 设置开关选项
protocol AIConfigTitleViewDelegate: NSObjectProtocol {
    func didhlep(view:AIConfigTitleView)
    func didSwitch(view:AIConfigTitleView, on: Bool)
}

class AIConfigTitleView: UIView {
    
    weak var delegate: AIConfigTitleViewDelegate?
    
    lazy var helpButton: UIButton = {
        let button = UIButton()
        button.setImage(UIImage(named: "Animation_帮助"), for: .normal)
        button.addTarget(self, action: #selector(helpAction), for: .touchUpInside)
        return button
    }()
    
    
    lazy var line: UIView = {
        let v = UIView()
        v.backgroundColor = UIColor("#ADAAFF")
        v.cornerRadius = 1
        return v
    }()
    
    lazy var titleLab: UILabel = {
        let v = UILabel()
        v.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        v.textColor = .white
        v.text = "送礼回复最长等待时间"
        return v
    }()
    
    public lazy var muteSwitch: UISwitch = {
        let muteSwitch = UISwitch()
        muteSwitch.onTintColor = UIColor("#756AFF")
        muteSwitch.addTarget(self, action: #selector(switchAction), for: .valueChanged)
        return muteSwitch
    }()

    
    override init(frame: CGRect) {
        super.init(frame: frame)
        makeUI()
        business()
    }
    
    deinit {
        
    }
    
    convenience init(title: String, on: Bool) {
        self.init(frame: .zero)
        self.titleLab.text = title
        self.muteSwitch.isOn = on
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func makeUI() {
        
        self.backgroundColor = UIColor.clear
        addSubviews([line, titleLab, helpButton, muteSwitch])
        
        line.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.height.equalTo(14)
            make.width.equalTo(4)
            make.leading.equalToSuperview().inset(18)
        }
        
        titleLab.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.height.equalTo(20)
            make.leading.equalToSuperview().inset(32)
        }
        
        helpButton.snp.makeConstraints { make in
            make.leading.equalTo(titleLab.snp.trailing).offset(4)
            make.height.width.equalTo(30)
            make.centerY.equalToSuperview()
        }
        
        muteSwitch.snp.makeConstraints { make in
            make.trailing.equalToSuperview().inset(16)
            make.centerY.equalToSuperview()
        }
        
        
    }
    
    @objc func helpAction() {
        self.delegate?.didhlep(view: self)
    }
    
    
    @objc func switchAction() {
        self.delegate?.didSwitch(view: self, on: self.muteSwitch.isOn)
    }

    
    func business() {
        muteSwitch.transform = CGAffineTransform(scaleX: 0.8, y: 0.8)
    }
    
   
}
