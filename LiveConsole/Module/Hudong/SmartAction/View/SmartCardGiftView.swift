//
//  SmartCardGiftView.swift
//  LivePlus
//
//  Created by simon on 14.2.25.
//

// MARK: - 回复的礼物view

import Foundation

class SmartCardGiftView: UIView {
        
    ///封面图
    lazy var coverV: UIImageView = {
        let v = UIImageView()
        return v
    }()
    
    lazy var bgV: UIImageView = {
        let v = UIImageView()
        v.backgroundColor = .clear
        v.image = UIImage(named: "smart_card_sanjaox")?.byTintColor(UIColor("#444444"))
        return v
    }()
    
    lazy var contentView: UIView = {
        let v = UIView()
        v.backgroundColor = UIColor("#444444")
        v.cornerRadius = 6
        return v
    }()
    
    
    lazy var nameLab: UILabel = {
        let v = UILabel()
        v.font = UIFont.systemFont(ofSize: 14, weight: .regular)
        v.textColor = UIColor.white
        return v
    }()
    
    var gift: DouyinGiftInfo?
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        makeUI()
    }
    
    deinit {
        
        
    }
    
    convenience init() {
        self.init(frame: .zero)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func makeUI() {
        
        self.backgroundColor = UIColor.clear
        addSubviews([bgV, contentView])
        
        contentView.addSubviews([coverV, nameLab])
        
        bgV.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.width.equalTo(6)
            make.height.equalTo(9)
            make.leading.equalToSuperview()
        }
        
        contentView.snp.makeConstraints { make in
            make.top.bottom.equalToSuperview()
            make.leading.equalToSuperview().inset(6)
            make.width.equalTo(71)
        }
        
        coverV.snp.makeConstraints { make in
            make.height.width.equalTo(32)
            make.centerY.equalToSuperview()
            make.leading.equalTo(5)
        }
        
        nameLab.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.trailing.equalToSuperview()
            make.leading.equalTo(coverV.snp.trailing).offset(5)
        }
    
    }
    
   
    
    public func config(model: DouyinGiftInfo) {
        self.gift = model
        if let urlstr = model.image.urlList.last, let url = URL(string: urlstr) {
            coverV.setImageWith(url, placeholder: UIImage(named: "占位图_首页_无直播间"))
            self.nameLab.text = model.name
        } else {
            coverV.image = nil
        }
        // 计算宽度
        var w = model.name.boundingRect(font: UIFont.systemFont(ofSize: 14, weight: .medium), limitSize: CGSize(width: CGFloat.greatestFiniteMagnitude, height: 22)).width + 43 + 8
        if w < 71 {
            w = 71
        }
        
        if w > 240 {
            w = 240
        }
        
        contentView.snp.updateConstraints { make in
            make.width.equalTo(w)
        }
        
    }
    
}

