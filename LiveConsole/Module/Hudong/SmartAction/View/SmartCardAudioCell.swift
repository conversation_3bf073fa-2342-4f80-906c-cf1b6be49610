//
//  SmartCardVoiceCell.swift
//  LivePlus
//
//  Created by simon on 17.2.25.
//

import Foundation

class SmartCardVoiceCell: UICollectionViewCell {
        
    lazy var background: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor("#444444")
        view.cornerRadius = 6
        return view
    }()
    
    lazy var progressView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor("#ADAAFF")
        return view
    }()
    
    lazy var itemImage: UIImageView = {
        let imageView = UIImageView(image: UIImage(named: "ic_smart_audio_icon"))
        return imageView
    }()
    
    lazy var itemDuration: UILabel = {
        let label = UILabel()
        label.textColor = UIColor("#FCFCFC")
        label.font = .systemFont(ofSize: 14, weight: .regular)
        return label
    }()
    
    lazy var bgV: UIImageView = {
        let v = UIImageView()
        v.backgroundColor = .clear
        v.image = UIImage(named: "smart_card_sanjaox")?.byTintColor(UIColor("#444444"))
        return v
    }()
    
    var progress: Float = 0.0

    override func awakeFromNib() {
        super.awakeFromNib()
    }
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        contentView.backgroundColor = UIColor.clear
        makeUI()
    }
    
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func makeUI() {
        
        backgroundColor = .clear
        contentView.addSubviews([background, bgV])
        background.addSubviews([ progressView, itemImage, itemDuration])
        
        bgV.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.width.equalTo(6)
            make.height.equalTo(9)
            make.leading.equalToSuperview()
        }
        
        background.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.leading.equalToSuperview()
            make.trailing.equalToSuperview()
            make.height.equalTo(36)
        }
        
        itemImage.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.leading.equalToSuperview().inset(10)
            make.size.equalTo(20)
        }
        
        
        itemDuration.snp.makeConstraints { make in
            make.leading.equalTo(itemImage.snp.trailing).offset(10)
            make.centerY.equalToSuperview()
        }
        
        
        progressView.snp.makeConstraints { make in
            make.bottom.leading.top.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(self.progress)
        }
        
      
    }

    func updateProgress(value: Float) {
        self.progress = value
        progressView.snp.remakeConstraints { make in
            make.bottom.leading.top.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(value)
        }
        UIView.animate(withDuration: 0.1) {
            self.layoutIfNeeded()
        }
    }
}

extension SmartCardVoiceCell {
    
    func bind(to model: ResFileModel, isPlaying: Bool, isFirst: Bool) {

        if isFirst {
            self.bgV.isHidden = false
            background.snp.remakeConstraints { make in
                make.center.equalToSuperview()
                make.leading.equalToSuperview().inset(6)
                make.trailing.equalToSuperview()
                make.height.equalTo(36)
            }
        } else {
            self.bgV.isHidden = true
            background.snp.remakeConstraints { make in
                make.center.equalToSuperview()
                make.leading.equalToSuperview()
                make.trailing.equalToSuperview()
                make.height.equalTo(36)
            }
        }
        
        itemDuration.text = "\(model.duration / 1000)″"
        
        if !isPlaying {
            progressView.snp.remakeConstraints { make in
                make.bottom.leading.equalToSuperview()
                make.height.equalTo(2.5)
                make.width.equalTo(0)
            }
        } else {
            progressView.snp.remakeConstraints { make in
                make.bottom.leading.equalToSuperview()
                make.height.equalTo(2.5)
                make.width.equalToSuperview().multipliedBy(self.progress)
            }
            self.layoutIfNeeded()
        }
    }
}


