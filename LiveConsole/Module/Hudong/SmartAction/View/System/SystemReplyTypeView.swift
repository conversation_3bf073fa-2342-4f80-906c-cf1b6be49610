//
//  SystemReplyTypeView.swift
//  LivePlus
//
//  Created by simon on 11.2.25.
//

import Foundation


protocol SystemReplyTypeViewProtocol: NSObjectProtocol {
    // 选中了一个
    func didSelected(mode: SystemInteractionType)
    
}


class SystemReplyTypeView: UIView {
       
    weak  var delegate: SystemReplyTypeViewProtocol?
//    /// 当
    public var infos: [SystemInteractionType] = [.gift, .follow]
    
    lazy var closeButton: UIButton = {
        let button = UIButton()
        button.setImage(UIImage(named: "gift_close"), for: .normal)
        button.zl_enlargeValidTouchArea(inset: 8)
        button.addTarget(self, action: #selector(closeAction), for: .touchUpInside)
        return button
    }()
    
    lazy var bgview: UIView = {
        let v = UIView()
        v.backgroundColor = UIColor("#282828")
        v.cornerRadius = 12
        return v
    }()
    
    
    lazy var titleLab: UILabel = {
        let v = UILabel()
        v.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        v.textColor = .white
        v.text = "请选择创建类型"
        return v
    }()
    
    
    // MARK: - Getter
  
    private lazy var myCollection: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        layout.minimumLineSpacing = 16
        layout.minimumInteritemSpacing = 16
        layout.itemSize = CGSize(width: (LCDevice.screenW - 40) , height: 74)
        let collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        collectionView.backgroundColor = UIColor("#282828")
        collectionView.register(cellWithClass: SystemReplyTypeCell.self)
        collectionView.delegate = self
        collectionView.dataSource = self
        collectionView.showsHorizontalScrollIndicator = false
        collectionView.isScrollEnabled = true
        collectionView.contentInset = UIEdgeInsets(top: 15, left: 20, bottom: 15, right: 20)
        return collectionView
    }()
    
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        self.backgroundColor = UIColor.black.alpha(value: 0.6)
        setupCollectionView()
        reloadData()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    @objc func closeAction() {
        self.isHidden = true
        self.removeFromSuperview()
    }
    
    func setupCollectionView() {
        addSubview(bgview)
        bgview.addSubviews([titleLab, closeButton, myCollection])
        
        bgview.snp.makeConstraints { make in
            make.bottom.leading.trailing.equalToSuperview()
            make.height.equalTo(300)
        }
        
        titleLab.snp.makeConstraints { make in
            make.leading.equalToSuperview().inset(20)
            make.top.equalToSuperview().inset(15)
            make.height.equalTo(22)
        }
        
        closeButton.snp.makeConstraints { make in
            make.trailing.equalToSuperview().inset(12)
            make.top.equalToSuperview().inset(10)
            make.height.width.equalTo(30)
        }
        
        myCollection.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(53)
            make.left.right.equalToSuperview()
            make.bottom.equalToSuperview()
        }
        
    }
   
    func reloadData() {
        self.myCollection.reloadData()
    }
    
}




// MARK: - UICollectionViewDataSource UICollectionViewDelegateFlowLayout
extension SystemReplyTypeView: UICollectionViewDataSource, UICollectionViewDelegateFlowLayout {
        
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return infos.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withClass: SystemReplyTypeCell.self, for: indexPath)
        let model = infos[indexPath.row]
        cell.bind(to: model)
        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        collectionView.deselectItem(at: indexPath, animated: true)
        let model = infos[indexPath.row]
        closeAction()
        self.delegate?.didSelected(mode: model)
    }
}
