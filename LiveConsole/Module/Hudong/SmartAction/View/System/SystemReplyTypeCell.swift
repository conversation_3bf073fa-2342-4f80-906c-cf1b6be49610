//
//  SystemReplyTypeCell.swift
//  LivePlus
//
//  Created by simon on 11.2.25.
//

import Foundation
import UIKit


class SystemReplyTypeCell: UICollectionViewCell {
            
    private lazy var titleLab: UILabel = {
        let label = UILabel()
        label.textColor = .white
        label.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        label.backgroundColor = .clear
        return label
    }()
    
    private lazy var desLab: UILabel = {
        let label = UILabel()
        label.textColor = UIColor("#ACACAC")
        label.font = UIFont.systemFont(ofSize: 12, weight: .medium)
        label.backgroundColor = .clear
        return label
    }()
    
    private lazy var bgview: UIView = {
        let label = UIView()
        label.backgroundColor = UIColor("#444444")
        label.cornerRadius = 6
        return label
    }()
    
    private lazy var iconView: UIImageView = {
        let label = UIImageView()
        label.backgroundColor = .clear
        return label
    }()

    override func awakeFromNib() {
        super.awakeFromNib()
    }

    
    override init(frame: CGRect) {
        super.init(frame: frame)
        contentView.backgroundColor = UIColor.clear
        makeUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    func makeUI() {
        contentView.addSubviews([bgview])
        
        bgview.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        bgview.addSubviews([titleLab, iconView, desLab])

        iconView.snp.makeConstraints { make in
            make.leading.equalToSuperview().inset(20)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(40)
        }
        
        titleLab.snp.makeConstraints { make in
            make.leading.equalToSuperview().inset(75)
            make.top.equalToSuperview().inset(20)
            make.height.equalTo(24)
        }
        
        desLab.snp.makeConstraints { make in
            make.leading.equalToSuperview().inset(75)
            make.top.equalToSuperview().inset(49)
            make.trailing.equalToSuperview().inset(25)
        }
    }
    
}

extension SystemReplyTypeCell {
    func bind(to model: SystemInteractionType) {
        titleLab.text = model.title
        desLab.text = model.des
        iconView.image = UIImage(named: model.imageName)
    }
}
