//
//  SmartCardTextCell.swift
//  LiveConsole
//
//  Created by simon on 14.5.25.
//

import Foundation

class SmartCardTextCell: UICollectionViewCell {
        
    lazy var background: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor("#444444")
        view.cornerRadius = 6
        return view
    }()
    
    lazy var itemImage: UIImageView = {
        let imageView = UIImageView(image: UIImage(named: "ic_text_icon"))
        return imageView
    }()
    
    lazy var itemDuration: UILabel = {
        let label = UILabel()
        label.textColor = .white
        label.font = .systemFont(ofSize: 14, weight: .regular)
        return label
    }()
    
    lazy var bgV: UIImageView = {
        let v = UIImageView()
        v.backgroundColor = .clear
        v.image = UIImage(named: "smart_card_sanjaox")?.byTintColor(UIColor("#444444"))
        return v
    }()
    
    var progress: Float = 0.0

    override func awakeFromNib() {
        super.awakeFromNib()
    }
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        contentView.backgroundColor = UIColor.clear
        makeUI()
    }
    
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func makeUI() {
        
        backgroundColor = .clear
        contentView.addSubviews([background, bgV])
        background.addSubviews([itemImage, itemDuration])
        
        bgV.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.width.equalTo(6)
            make.height.equalTo(9)
            make.leading.equalToSuperview()
        }
        
        background.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.leading.equalToSuperview()
            make.trailing.equalToSuperview()
            make.height.equalTo(37)
        }
        
        itemImage.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.leading.equalToSuperview().inset(10)
            make.size.equalTo(20)
        }
        
        
        itemDuration.snp.makeConstraints { make in
            make.leading.equalTo(itemImage.snp.trailing).offset(10)
            make.trailing.equalToSuperview().inset(8)
            make.centerY.equalToSuperview()
        }
    }

    
}

extension SmartCardTextCell {
    
    func bind(to model: ResFileModel, isFirst: Bool) {

        if isFirst {
            self.bgV.isHidden = false
            background.snp.remakeConstraints { make in
                make.center.equalToSuperview()
                make.leading.equalToSuperview().inset(6)
                make.trailing.equalToSuperview()
                make.height.equalTo(36)
            }
        } else {
            self.bgV.isHidden = true
            background.snp.remakeConstraints { make in
                make.center.equalToSuperview()
                make.leading.equalToSuperview()
                make.trailing.equalToSuperview()
                make.height.equalTo(36)
            }
        }
        
        let text: String = model.text ?? ""
            
        itemDuration.text = text
       
       

    }
}


