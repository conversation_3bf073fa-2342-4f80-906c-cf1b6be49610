//
//  SmartDetailThreeNavigation.swift
//  LivePlus
//
//  Created by 郭炜 on 2025/1/14.
//

import UIKit

// MARK: - 三层导航栏
class SmartDetailThreeNavigation: SmartBaseView {
    
    lazy var background: UIView = {
        let view = UIView()
        return view
    }()
    
    lazy var lastPageButton: UIButton = {
        let button = UIButton()
        button.setTitleColor(UIColor("#ACACAC"), for: .normal)
        button.titleLabel?.font = LCDevice.DIN_Font_PF_R(14)
        return button
    }()
    
    lazy var rightImage: UIImageView = {
        let imageView = UIImageView(image: UIImage(named: "ic_smart_nav_right"))
        return imageView
    }()
    
    lazy var secondPageButton: UIButton = {
        let button = UIButton()
        button.setTitleColor(UIColor("#ACACAC"), for: .normal)
        button.titleLabel?.font = LCDevice.DIN_Font_PF_R(14)
        return button
    }()
    
    lazy var rightSecondImage: UIImageView = {
        let imageView = UIImageView(image: UIImage(named: "ic_smart_nav_right"))
        return imageView
    }()
    
    lazy var currentPageLabel: UILabel = {
        let label = UILabel()
        label.textColor = UIColor("#ADAAFF")
        label.font = LCDevice.DIN_Font_PF_M(16)
        return label
    }()
    
    lazy var balanceWordLabel: UILabel = {
        let label = UILabel()
        label.textColor = UIColor("#FFC260")
        label.font = .systemFont(ofSize: 12)
        label.textAlignment = .right
        return label
    }()

    override init(frame: CGRect) {
        super.init(frame: frame)
    }
    
    convenience init() {
        self.init(frame: .zero)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func makeUI() {
        super.makeUI()
        
        addSubview(background)
        background.addSubviews([lastPageButton, rightImage, secondPageButton, rightSecondImage, currentPageLabel, balanceWordLabel])
        
        background.snp.makeConstraints { make in
            make.bottom.leading.trailing.equalToSuperview()
            make.height.equalTo(44)
        }
        lastPageButton.snp.makeConstraints { make in
            make.leading.equalToSuperview().inset(16)
            make.centerY.equalToSuperview()
            make.width.equalTo(68)
            make.height.equalTo(44)
        }
        rightImage.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.leading.equalTo(lastPageButton.snp.trailing).offset(5)
            make.size.equalTo(12)
        }
        secondPageButton.snp.makeConstraints { make in
            make.leading.equalTo(rightImage.snp.trailing).offset(5)
            make.centerY.equalToSuperview()
//            make.width.equalTo(72)
            make.height.equalTo(44)
        }
        rightSecondImage.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.leading.equalTo(secondPageButton.snp.trailing).offset(5)
            make.size.equalTo(12)
        }
        currentPageLabel.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.leading.equalTo(rightSecondImage.snp.trailing).offset(5)
        }
        balanceWordLabel.snp.makeConstraints { make in
            make.trailing.equalToSuperview().inset(16)
            make.centerY.equalToSuperview()
        }
    }
    
    override func business() {
        super.business()
        balanceWordLabel.text = "积分:\(UserInfo.points)"
    }
    
}
