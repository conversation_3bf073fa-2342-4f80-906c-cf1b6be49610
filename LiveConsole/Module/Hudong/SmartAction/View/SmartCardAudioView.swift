//
//  SmartCardAudioView.swift
//  LivePlus
//
//  Created by simon on 14.2.25.
//

// MARK: - 回复的音频view
import Foundation
   

class SmartCardAudioView: UIView {
        
    private lazy var myCollection: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        layout.minimumLineSpacing = 6
        layout.minimumInteritemSpacing = 6
        layout.itemSize = CGSize(width: 75, height: 36)
        layout.scrollDirection = .horizontal
        let collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        collectionView.backgroundColor = UIColor.clear
        collectionView.register(cellWithClass: SmartCardVoiceCell.self)
        collectionView.delegate = self
        collectionView.dataSource = self
        collectionView.showsHorizontalScrollIndicator = false
        collectionView.isScrollEnabled = false
        collectionView.contentInset = UIEdgeInsets(top: 0, left: 0, bottom: 0, right: 0)
        return collectionView
    }()
    
    weak var model: AiKeywordVoiceReplyItemModel?
    
    var playingModel: ResFileModel?

    var resources: [ResFileModel]  {
        if let replyItem = model?.replyItem, replyItem.itemType == .musicLocal, let rs = replyItem.resources {
            return rs
        }
        return []
    }
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        makeUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func makeUI() {
        
        addSubviews([myCollection])
       
        myCollection.snp.makeConstraints { make in
            make.top.leading.bottom.equalToSuperview()
            make.trailing.equalToSuperview().inset(8)
        }
        
        AudioPlayManager.shared.delegate = self
        
    }
    
     func business() {
        
       
    }

    func clearProgress() {
        for index in 0..<resources.count {
            if let cell = myCollection.cellForItem(at: IndexPath(row: index, section: 0)) as? SmartCardVoiceCell {
                cell.updateProgress(value: 0)
            }
        }
        self.myCollection.reloadData()
    }
}

extension SmartCardAudioView {
    
    func bind(to model: AiKeywordVoiceReplyItemModel) {
        self.model = model
        self.myCollection.reloadData()
    }

}


// MARK: - UICollectionViewDataSource UICollectionViewDelegateFlowLayout
extension SmartCardAudioView: UICollectionViewDataSource, UICollectionViewDelegateFlowLayout {
        
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return resources.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {

        let cell = collectionView.dequeueReusableCell(withClass: SmartCardVoiceCell.self, for: indexPath)
        let model = resources[indexPath.row]
        cell.bind(to: model, isPlaying: playingModel?.id == model.id, isFirst: indexPath.row == 0)
        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        AudioPlayManager.shared.delegate = self
        let model = resources[indexPath.row]
        if let playingModel = self.playingModel {
            // 如果已经有播放的
            if model.id == playingModel.id {
                // 并且是相同的 那么暂停
                AudioPlayManager.shared.stop()
                self.playingModel = nil
            } else {
                // 不相同 先暂停再切换
                AudioPlayManager.shared.stop()
                let _ = try? AudioPlayManager.shared.play(url: model.toAudioPlayUrl, rate: Float(model.voidespeed) ?? 1.0)
                self.playingModel = model
            }
        } else {
            // 没有播放的
            AudioPlayManager.shared.stop()
            let _ = try? AudioPlayManager.shared.play(url: model.toAudioPlayUrl, rate: Float(model.voidespeed) ?? 1.0)
            self.playingModel = model
        }
        self.clearProgress()
    }
}



extension SmartCardAudioView: AudioPlayManagerDelegate {
    func audioPlayManager(_ manager: AudioPlayManager, didFinishPlaying successfully: Bool) {
        self.playingModel = nil
        self.clearProgress()
        self.myCollection.reloadData()
    }
    
    func audioPlayManager(_ manager: AudioPlayManager, didUpdateProgress progress: Float) {
        guard let index = resources.firstIndex(where: { $0.id == playingModel?.id }), let cell = myCollection.cellForItem(at: IndexPath(row: index, section: 0)) as? SmartCardVoiceCell else {
            return
        }
        cell.updateProgress(value: progress)
    }
    
    func audioPlayManager(_ manager: AudioPlayManager, didUpdateCurrentTime currentTime: TimeInterval) {
        
    }
    
    func audioPlayManager(_ manager: AudioPlayManager, didEncounterError error: Error) {
        
    }
}
