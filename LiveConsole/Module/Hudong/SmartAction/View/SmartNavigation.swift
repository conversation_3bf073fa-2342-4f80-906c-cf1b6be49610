//
//  SmartNavigation.swift
//  LiveConsole
//
//  Created by 郭炜 on 2025/4/23.
//

import UIKit

class SmartNavigation: SmartBaseView {

    lazy var background: UIView = {
        let view = UIView()
        return view
    }()
    
    lazy var backButton: UIButton = {
        let button = UIButton()
        button.setImage(UIImage(named: "icon_nav_back_white"), for: .normal)
        button.zl_enlargeValidTouchArea(inset: 40)
        return button
    }()
        
    lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.textColor = UIColor("#FFFFFF")
        label.font = LCDevice.DIN_Font_PF_M(16)
        return label
    }()

    override init(frame: CGRect) {
        super.init(frame: frame)
    }
    
    convenience init() {
        self.init(frame: .zero)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    var needHookBack: Bool = false
    
    lazy var balanceWordLabel: UILabel = {
        let label = UILabel()
        label.textColor = UIColor("#FFC260")
        label.font = .systemFont(ofSize: 12)
        label.textAlignment = .right
        label.isHidden = true
        label.isUserInteractionEnabled = true
        return label
    }()
    
    override func makeUI() {
        super.makeUI()
        
        addSubview(background)
        background.addSubviews([backButton, titleLabel, balanceWordLabel])
        
        background.snp.makeConstraints { make in
            make.bottom.leading.trailing.equalToSuperview()
            make.height.equalTo(44)
        }
        backButton.snp.makeConstraints { make in
            make.leading.equalToSuperview().inset(16)
            make.centerY.equalToSuperview()
            make.size.equalTo(24)
        }
        titleLabel.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.leading.equalTo(backButton.snp.trailing).offset(12)
        }
        
        balanceWordLabel.snp.makeConstraints { make in
            make.trailing.equalToSuperview().inset(16)
            make.centerY.equalToSuperview()
        }
    }
    
    override func business() {
        super.business()
        
        backButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self else { return }
                if self.needHookBack {
                    return
                }
                AppDelegate.curDisplayVC().navigationController?.popViewController()
            }.disposed(by: rx.disposeBag)
    }

}
