//
//  SmartCardVideoView.swift
//  LivePlus
//
//  Created by simon on 14.2.25.
//

// MARK: - 回复的视频view
import Foundation

class SmartCardVideoView: UIView {
    
    ///封面图
    lazy var itemView: VideoReplyItemView = {
        let v = VideoReplyItemView()
        return v
    }()
    
    lazy var bgV: UIImageView = {
        let v = UIImageView()
        v.backgroundColor = .clear
        v.image = UIImage(named: "smart_card_kuang")
        return v
    }()
    
    lazy var titleLab: UILabel = {
        let v = UILabel()
        v.font = UIFont.systemFont(ofSize: 12, weight: .regular)
        v.textColor = UIColor("#FCFCFC")
        v.text = "视频"
        return v
    }()
    
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        makeUI()
    }
    
    deinit {
        
    }
    
    convenience init() {
        self.init(frame: .zero)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func makeUI() {
        
        self.backgroundColor = UIColor.clear
        addSubviews([bgV])
        
        bgV.addSubviews([itemView, titleLab])
        
        bgV.snp.makeConstraints { make in
            make.top.leading.bottom.equalToSuperview()
            make.width.equalTo(81)
        }
        
        
        itemView.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.height.width.equalTo(36)
            make.leading.equalToSuperview().inset(6)
        }
        
        titleLab.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.trailing.equalToSuperview()
            make.leading.equalTo(itemView.snp.trailing).offset(5)
        }
    }
   
    public func config(item: AreaItemModel) {
      
        self.itemView.config(with: item)
    }
}


