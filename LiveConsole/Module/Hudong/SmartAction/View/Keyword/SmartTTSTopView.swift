//
//  SmartTTSTopView.swift
//  LivePlus
//
//  Created by 郭炜 on 2025/1/16.
//

import UIKit
import RSKPlaceholderTextView
//import QMUIKit

public protocol SmartTTSTopViewDelegate: AnyObject {
    func textViewDidChange(count: Int)
}

// MARK: - AI合成顶部视图
class SmartTTSTopView: SmartBaseView {

    weak var delegate: SmartTTSTopViewDelegate?
    
    let maxCount = 200
    
    lazy var audioNameLabel: UILabel = {
        let label = UILabel()
        label.text = "语音名称：语音01"
        label.textColor = .white
        label.font = .systemFont(ofSize: 15, weight: .medium)
        return label
    }()
    
    lazy var editButton: UIButton = {
        let button = UIButton()
        button.setImage(UIImage(named: "ic_clone_edit"), for: .normal)
        button.zl_enlargeValidTouchArea(inset: 10)
        return button
    }()
    
    
    lazy var ruleButton: UIButton = {
        let button = UIButton()
        button.setTitle("计费规则", for: .normal)
        button.setTitleColor(UIColor("#ADAAFF"), for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 13, weight: .regular)
        button.addTarget(self, action: #selector(ruleAction), for: .touchUpInside)
        return button
    }()
    
    
    lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.text = "文案内容（必填）"
        label.textColor = .white
        label.font = .systemFont(ofSize: 14, weight: .regular)
        return label
    }()
    
    lazy var textView: RSKPlaceholderTextView = {
        let textView = RSKPlaceholderTextView()
        textView.textColor = .white
        textView.font = .systemFont(ofSize: 14)
        textView.placeholder = "请输入文案"
//        textView.placeholderColor = UIColor("#ACACAC")
        textView.contentInset = UIEdgeInsets(top: 12, left: 15, bottom: 20, right: 15)
        textView.backgroundColor = UIColor("#282828")
        textView.cornerRadius = 6
//        textView.maximumTextLength = 200
        textView.delegate = self
        return textView
    }()
    
    lazy var textCountLabel: UILabel = {
        let label = UILabel()
        label.textColor = UIColor("#ACACAC")
        label.font = .systemFont(ofSize: 12)
        label.textAlignment = .right
        label.text = "0/200"
        return label
    }()

    var gift: DouyinGiftInfo?
    
    var nonEditableRanges = [NSRange]() // 存储受保护的范围
    
    override init(frame: CGRect) {
        super.init(frame: frame)
    }
    
    convenience init() {
        self.init(frame: .zero)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func makeUI() {
        super.makeUI()
        
        addSubviews([editButton, audioNameLabel, titleLabel, textView, textCountLabel, ruleButton])
        
        audioNameLabel.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.leading.equalToSuperview().inset(16)
            make.height.equalTo(22)
        }
        
        editButton.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.leading.equalTo(audioNameLabel.snp.trailing).offset(6)
            make.height.width.equalTo(18)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(42)
            make.leading.equalToSuperview().inset(16)
            make.height.equalTo(22)
        }
        
        ruleButton.snp.makeConstraints { make in
            make.centerY.equalTo(titleLabel.snp.centerY)
            make.leading.equalTo(titleLabel.snp.trailing).offset(8)
            make.height.equalTo(26)
            make.width.equalTo(55)
        }
        
        textView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(10)
            make.leading.trailing.equalToSuperview().inset(16)
            make.bottom.equalToSuperview()
        }
        textCountLabel.snp.makeConstraints { make in
            make.centerY.equalTo(titleLabel.snp.centerY)
            make.trailing.equalToSuperview().inset(16)
        }
    }
    
    override func business() {
        super.business()
    }
    
    @objc func ruleAction() {
        PointRuleAlert.show(sureAction: {[weak self] in
            
        }) {
            
        }
    }

}

extension SmartTTSTopView: UITextViewDelegate {
    func textViewDidChange(_ textView: UITextView) {
        self.textCountLabel.text = "\(textView.text.count)/200"
        updateNonEditableRanges()
        var count = textView.text.count
        if textView.text == "" || textView.text.isBlank {
            count = 0
        }
        self.delegate?.textViewDidChange(count: count)
    }
    
    func textViewDidEndEditing(_ textView: UITextView) {
        var count = textView.text.count
        if textView.text == "" || textView.text.isBlank {
            count = 0
        }
        self.delegate?.textViewDidChange(count: count)
    }
    
    // Update non-editable ranges without applying styling (for range checking)
    func updateNonEditableRangesWithoutStyling() {
        guard let text = textView.text else { return }
        let pattern = "【.*?】"
        guard let regex = try? NSRegularExpression(pattern: pattern) else { return }
        
        nonEditableRanges = regex.matches(in: text, range: NSRange(text.startIndex..., in: text))
            .map { $0.range }
    }
    
    // 更新不可编辑区域的范围和样式
    func updateNonEditableRanges() {
        guard let text = textView.text, let gift = self.gift else { return }
        let pattern = "【.*?】" // 非贪婪匹配，确保匹配独立的【】块
        guard let regex = try? NSRegularExpression(pattern: pattern) else { return }
        
        // 获取所有匹配的不可编辑范围
        nonEditableRanges = regex.matches(in: text, range: NSRange(text.startIndex..., in: text))
            .map { $0.range }
        
        // 保留光标位置，避免刷新样式后光标跳动
        let selectedRange = textView.selectedRange
        textView.attributedText = LCTools.attributedContent(words: ["【你/你们】","【\(gift.name)】"], text: text, color: UIColor("#ACACAC"))
        textView.selectedRange = selectedRange
    }
    
    
    // 检测编辑操作
    func textView(_ textView: UITextView, shouldChangeTextIn range: NSRange, replacementText text: String) -> Bool {
        // First, update non-editable ranges based on current text
        updateNonEditableRangesWithoutStyling()
        
        // Check if we're trying to edit within protected ranges
        for nonEditableRange in nonEditableRanges {
            // Only check if the range is completely inside the non-editable range
            // Allow editing at the boundaries (before and after the protected content)
            if range.location > nonEditableRange.location &&
               range.location < nonEditableRange.location + nonEditableRange.length {
                return false // 禁止编辑不可编辑区域内部
            }
            
            // If we're replacing text that spans across or includes protected content
            if range.location <= nonEditableRange.location &&
               range.location + range.length > nonEditableRange.location {
                return false
            }
        }
        
        if text == "\n" {
            textView.resignFirstResponder()
            return false
        }
        
        // 检测输入文本是否包含表情符号
        if text.unicodeScalars.contains(where: { scalar in
            return scalar.properties.isEmoji && scalar.value > 0x238C
        }) {
            return false // 阻止输入
        }
        
        if let result = (textView.text as NSString?)?.replacingCharacters(in: range, with: text) {
            // 更新高度
            if result.count > maxCount {
                HUD.showFail("超过最大的字数限制")
                return false
            }
        }
        
        return true
    }

   
}
