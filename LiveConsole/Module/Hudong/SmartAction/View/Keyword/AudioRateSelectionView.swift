//
//  AudioRateSelectionView.swift
//  LivePlus
//
//  Created by 郭炜 on 2025/1/15.
//

import UIKit

class AudioRateSelectionView: UIView {
    
    var onRateSelected: ((Float) -> Void)?
    private let rates: [Float] = [0.5, 0.75, 1.0, 1.25, 1.5, 2.0]
    
    private var rate: Float?
    
    // MARK: - UI Components
    private lazy var backgroundView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.black.withAlphaComponent(0.4)
        let tap = UITapGestureRecognizer(target: self, action: #selector(hide))
        view.addGestureRecognizer(tap)
        return view
    }()
    
    private lazy var contentView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor("#282828")
        view.layer.cornerRadius = 16
        view.layer.maskedCorners = [.layerMinXMinYCorner, .layerMaxXMinYCorner]
        return view
    }()
    
    lazy var yusuLabel: UILabel = {
        let label = UILabel()
        label.text = "语速"
        label.textColor = .white
        label.font = .systemFont(ofSize: 16, weight: .medium)
        return label
    }()
    
    lazy var seperator: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor("#FFFFFF").withAlphaComponent(0.15)
        return view
    }()
    
    private lazy var stackView: UIStackView = {
        let stack = UIStackView()
        stack.axis = .vertical
        stack.spacing = 20
        stack.distribution = .fillEqually
        return stack
    }()
    
    private lazy var row1Stack: UIStackView = {
        let stack = UIStackView()
        stack.axis = .horizontal
        stack.spacing = 16
        stack.distribution = .fillEqually
        return stack
    }()
    
    private lazy var row2Stack: UIStackView = {
        let stack = UIStackView()
        stack.axis = .horizontal
        stack.spacing = 12
        stack.distribution = .fillEqually
        return stack
    }()
    
    // MARK: - Init
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    convenience init(rate: Float?) {
        self.init(frame: .zero)
        self.rate = rate
        setupUI()
    }
    
    
    // MARK: - Setup
    private func setupUI() {
        backgroundColor = .clear
        
        addSubview(backgroundView)
        addSubviews([contentView])
        contentView.addSubviews([yusuLabel, seperator, stackView])
        stackView.addArrangedSubview(row1Stack)
        stackView.addArrangedSubview(row2Stack)
        
        backgroundView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        contentView.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview()
            make.bottom.equalToSuperview().offset(300) // 初始位置在屏幕下方
            make.height.equalTo(300)
        }
        
        yusuLabel.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.leading.equalToSuperview().inset(20)
            make.height.equalTo(52)
        }
        seperator.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview()
            make.top.equalTo(yusuLabel.snp.bottom)
            make.height.equalTo(1)
        }
        
        stackView.snp.makeConstraints { make in
            make.top.equalTo(seperator.snp.bottom).offset(30)
            make.leading.trailing.equalToSuperview().inset(16)
            make.height.equalTo(100)
        }
        
       for (_, v)  in row1Stack.subviews.enumerated(){
           v.removeFromSuperview()
        }
        
        for (_, v)  in row2Stack.subviews.enumerated(){
            v.removeFromSuperview()
         }
        
        // 创建并添加按钮
        for i in 0..<3 {
            let button = createRateButton(rate: rates[i])
            row1Stack.addArrangedSubview(button)
        }
        
        for i in 3..<6 {
            let button = createRateButton(rate: rates[i])
            row2Stack.addArrangedSubview(button)
        }
    }
    
    private func createRateButton(rate: Float) -> UIButton {
        let button = UIButton()
        button.backgroundColor = UIColor("#444444")
        button.setTitle("\(rate)X", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.titleLabel?.font = .systemFont(ofSize: 14)
        button.layer.cornerRadius = 6
        button.tag = rates.firstIndex(of: rate) ?? 0
        button.addTarget(self, action: #selector(rateButtonTapped(_:)), for: .touchUpInside)
        button.borderWidth = 2
        
        if let currate = self.rate, currate == rate {
            button.borderColor = UIColor("#ADAAFF")
        } else {
            button.borderColor = .clear
        }
        return button
    }
    
    // MARK: - Actions
    @objc private func rateButtonTapped(_ sender: UIButton) {
        let rate = rates[sender.tag]
        onRateSelected?(rate)
        hide()
    }
    
    // MARK: - Public Methods
    func show() {
        // 确保视图在window上
        if let window = UIApplication.shared.windows.first {
            window.addSubview(self)
            self.frame = window.bounds
            
            // 显示动画
            self.backgroundView.alpha = 0
            self.contentView.snp.updateConstraints { make in
                make.bottom.equalToSuperview().offset(300)
            }
            self.layoutIfNeeded()
            
            UIView.animate(withDuration: 0.3) {
                self.backgroundView.alpha = 1
                self.contentView.snp.updateConstraints { make in
                    make.bottom.equalToSuperview()
                }
                self.layoutIfNeeded()
            }
        }
    }
    
    @objc func hide() {
        UIView.animate(withDuration: 0.3, animations: {
            self.backgroundView.alpha = 0
            self.contentView.snp.updateConstraints { make in
                make.bottom.equalToSuperview().offset(300)
            }
            self.layoutIfNeeded()
        }) { _ in
            self.removeFromSuperview()
        }
    }
}
