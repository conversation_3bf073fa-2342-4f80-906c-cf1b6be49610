//
//  SmartRecordBotView.swift
//  LivePlus
//
//  Created by 郭炜 on 2025/1/14.
//

import UIKit
import QMUIKit
//import QMUIKit

enum SmartRecordStatus {
    case unStart // 未开始
    case recording // 录音中
    case recordPausing // 录音暂停中
    case recordComplete // 录音完成
}

// MARK: - 录音按钮等
protocol SmartRecordBotViewDelegate: NSObjectProtocol {
    func actionForRecordFinish(url: URL)
}

class SmartRecordBotView: SmartBaseView {
    
    weak var delegate: SmartRecordBotViewDelegate?
    
    // 如果是克隆需要弹出协议
//    var isClone: Bool = false
    
    var status: SmartRecordStatus = .unStart {
        didSet {
            self.updateRecordingUI()
        }
    }
    
    lazy var background: UIView = {
        let view = UIView()
        return view
    }()
    
    lazy var startButton: UIButton = {
        let button = UIButton(frame: CGRect(x: 0, y: 0, width: 120, height: 120))
        button.setImage(UIImage(named: "ic_smart_record_start"), for: .normal)
        button.setTitle("点击开始录音", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.titleLabel?.font = .systemFont(ofSize: 14)
        button.imagePosition(style: .top, spacing: 18)
        return button
    }()
    
    lazy var recordingView: UIView = {
        let view = UIView()
        return view
    }()
    
    lazy var stopRecordButton: UIButton = {
        let button = UIButton(frame: CGRect(x: 0, y: 0, width: 120, height: 120))
        button.setImage(UIImage(named: "ic_smart_record_stop"), for: .normal)
        button.setTitle("停止录音", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.titleLabel?.font = .systemFont(ofSize: 14)
        button.imagePosition(style: .top, spacing: 18)
        return button
    }()
    
    lazy var pauseRecordButton: UIButton = {
        let button = UIButton()
        button.setImage(UIImage(named: "ic_smart_record_pause"), for: .normal)
//        button.setTitle("暂停", for: .normal)
        button.setImage(UIImage(named: "ic_smart_record_resume"), for: .selected)
//        button.setTitle("继续", for: .selected)
        button.setTitleColor(.white, for: .normal)
        button.titleLabel?.font = .systemFont(ofSize: 14)
//        button.imagePosition(style: .top, spacing: 18)
        return button
    }()
    
    lazy var countdownLabel: RecordCountDownLabel = {
        let label = RecordCountDownLabel()
        return label
    }()
    
    lazy var dotView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor("#F03F71")
        view.cornerRadius = 4
        return view
    }()
    
    lazy var recordCompleteView: UIView = {
        let view = UIView()
        return view
    }()
    
    lazy var timeLabel: UILabel = {
        let label = UILabel()
        label.textColor = .white
        label.font = .systemFont(ofSize: 17, weight: .medium)
        return label
    }()
    
    lazy var playPauseButton: QMUIButton = {
        let button = QMUIButton()
        button.setImage(UIImage(named: "ic_smart_record_play_play"), for: .normal)
        button.setImage(UIImage(named: "ic_smart_record_play_pause"), for: .selected)
        return button
    }()
    
    lazy var restartRecordButton: QMUIButton = {
        let button = QMUIButton()
        button.setImage(UIImage(named: "ic_smart_record_restart"), for: .normal)
//        button.setTitle("重新录音", for: .normal)
        button.titleLabel?.font = .systemFont(ofSize: 14)
        button.setTitleColor(.white, for: .normal)
        button.imagePosition = .top
        button.spacingBetweenImageAndTitle = 18
        return button
    }()
    
    lazy var finishRecordButton: QMUIButton = {
        let button = QMUIButton()
        button.setImage(UIImage(named: "ic_smart_record_finish"), for: .normal)
        button.setTitle("确定", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.titleLabel?.font = .systemFont(ofSize: 14)
        button.imagePosition = .top
        button.spacingBetweenImageAndTitle = 18
        return button
    }()
    
    lazy var playingView: UIView = {
        let view = UIView()
        view.isHidden = true
        return view
    }()
    
    lazy var playingSeprate: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor("#555555")
        return view
    }()
    
    lazy var playingTimeLabel: UILabel = {
        let label = UILabel()
        label.textColor = UIColor("#555555")
        label.font = .systemFont(ofSize: 17, weight: .medium)
        return label
    }()
        
    private var timer: DispatchSourceTimer?
    private var isTimerRunning = false
    var duration: TimeInterval = 0.0
    var fileUrl: URL? {
        didSet {
            if let fileUrl = fileUrl {
                AudioDurationHelper.getAudioDuration(from: fileUrl) { [weak self] result in
                    guard let self = self else { return }
                    switch result {
                    case .success(let duration):
                        print("音频时长: \(duration)秒")
                        self.duration = duration
                        let durationString = AudioDurationHelper.formatDuration(duration)
                        print("格式化时长: \(durationString)")
                        self.timeLabel.text = durationString
                    case .failure(let error):
                        print("获取时长失败: \(error.localizedDescription)")
                    }
                }
            }
        }
    }
    
    var limitSecond: TimeInterval = 10

    override init(frame: CGRect) {
        super.init(frame: frame)
    }
    
    convenience init() {
        self.init(frame: .zero)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func makeUI() {
        super.makeUI()
        
        AudioPlayManager.shared.delegate = self
        
        addSubview(background)
        background.addSubviews([startButton, recordingView, recordCompleteView])
        recordingView.addSubviews([dotView, countdownLabel, stopRecordButton, pauseRecordButton])
        
        recordCompleteView.addSubviews([timeLabel, playPauseButton, restartRecordButton, finishRecordButton, playingView])
        
        playingView.addSubviews([playingSeprate, playingTimeLabel])
        
        background.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        startButton.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.bottom.equalToSuperview().inset(30)
            make.size.equalTo(120)
        }
        
        // 录制中
        recordingView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        dotView.snp.makeConstraints { make in
            make.centerY.equalTo(countdownLabel.snp.centerY)
            make.trailing.equalTo(countdownLabel.snp.leading).offset(-8)
            make.size.equalTo(8)
        }
        countdownLabel.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalToSuperview().inset(20)
        }
        stopRecordButton.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.bottom.equalToSuperview().inset(30)
            make.width.height.equalTo(120)
        }
        pauseRecordButton.snp.makeConstraints { make in
            make.centerY.equalTo(stopRecordButton.snp.centerY)
            make.leading.equalTo(stopRecordButton.snp.trailing).offset(10)
            make.size.equalTo(100)
        }
        
        // 录制完成view
        recordCompleteView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        timeLabel.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalToSuperview().inset(20)
        }
        
        playingView.snp.makeConstraints { make in
            make.centerY.equalTo(timeLabel.snp.centerY)
            make.leading.equalTo(timeLabel.snp.trailing).offset(6)
            make.width.equalTo(100)
            make.height.equalTo(28)
        }
        
        playingSeprate.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.leading.equalToSuperview()
            make.height.equalTo(18)
            make.width.equalTo(1.2)
        }
        
        playingTimeLabel.snp.makeConstraints { make in
            make.leading.equalTo(playingSeprate.snp.trailing).offset(6)
            make.centerY.equalToSuperview()
        }
        
        finishRecordButton.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.bottom.equalToSuperview().inset(30)
            make.width.height.equalTo(120)
        }
        
        restartRecordButton.snp.makeConstraints { make in
            make.centerY.equalTo(finishRecordButton.snp.centerY)
            make.size.equalTo(100)
            make.trailing.equalTo(finishRecordButton.snp.leading).offset(-10)
        }
        
        playPauseButton.snp.makeConstraints { make in
            make.centerY.equalTo(finishRecordButton.snp.centerY)
            make.leading.equalTo(finishRecordButton.snp.trailing).offset(10)
            make.size.equalTo(100)
        }
        

    }
    
    override func business() {
        super.business()
        self.updateRecordingUI()
        
        startButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self else { return }
                willStartRecord()
                
            }.disposed(by: rx.disposeBag)
        
        stopRecordButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self else { return }
                // 完成录音
                self.status = .recordComplete
                let url = AudioRecordManager.shared.stopRecording()
                print("录音完成：\(String(describing: url))")
                self.fileUrl = url
            }.disposed(by: rx.disposeBag)
        
        pauseRecordButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self else { return }
                if self.pauseRecordButton.isSelected {
                    // 继续录音
                    self.status = .recording
                    AudioRecordManager.shared.resumeRecording()
                } else {
                    // 暂停录音
                    self.status = .recordPausing
                    AudioRecordManager.shared.pauseRecording()
                }
            }.disposed(by: rx.disposeBag)
        
        playPauseButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self, let fileUrl = self.fileUrl else { return }
                // 播放、暂停播放
                if !self.playPauseButton.isSelected {
                    let duration = try? AudioPlayManager.shared.play(url: fileUrl)
                    print("开始播放 音频时长为：\(duration)")
                    self.playingView.isHidden = false
                    self.playingTimeLabel.text = "00:00"
                } else {
                    AudioPlayManager.shared.pause()
                }
                self.playPauseButton.isSelected = !self.playPauseButton.isSelected
            }.disposed(by: rx.disposeBag)
        
        restartRecordButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self else { return }
                // 重新录制
                AudioPlayManager.shared.stop()
                self.status = .recording
                AudioRecordManager.shared.deleteCurrentRecording()
                self.startRecord()
            }.disposed(by: rx.disposeBag)
        
        finishRecordButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self else { return }
                // 完成录制
                AudioPlayManager.shared.stop()
                if let fileUrl = fileUrl {
                    self.delegate?.actionForRecordFinish(url: fileUrl)
                }
            }.disposed(by: rx.disposeBag)
    }
    
    func willStartRecord() {
//        if !isClone {
            // 开始录制
        AudioPermissionManager.shared.checkAndRequestPermission { [weak self] granted in
            guard let self = self else { return }
            if !granted  {
                HUD.showFail("请开启录音权限")
                return
            }
            self.status = .recording
            self.startRecord()
        }
        
    }

    /// 开始录制
    func startRecord() {
        AudioRecordManager.shared.startRecording { success, error in
            print("开始录制音频 \(success)")
            if success {
                self.startUpdateTimer()
            } else if let error = error {
                HUD.showFail(error.localizedDescription)
                self.status = .unStart
            }
        }
    }
    
    private func startUpdateTimer() {
        // 先停止已存在的定时器
        stopUpdateTimer()
        
        // 创建新的定时器
        let timer = DispatchSource.makeTimerSource(queue: .main)
        self.timer = timer
        
        // 配置定时器
        timer.schedule(deadline: .now(), repeating: .milliseconds(60))
        
        // 设置回调
        timer.setEventHandler { [weak self] in
            guard let self = self else {
                timer.cancel()
                return
            }
            let currentTime = AudioRecordManager.shared.currentRecordingTime
            if currentTime >= self.limitSecond {
                self.status = .recordComplete
                let url = AudioRecordManager.shared.stopRecording()
                print("录音完成：\(String(describing: url))")
                self.fileUrl = url
                HUD.showFail("录制时长已达上限")
            }
        }
        
        // 启动定时器
        isTimerRunning = true
        timer.resume()
    }
    
    private func stopUpdateTimer() {
        if isTimerRunning {
            timer?.cancel()
            isTimerRunning = false
        }
        timer = nil
    }
}

extension SmartRecordBotView {
    func updateRecordingUI() {
        self.startButton.isHidden = true
        self.recordingView.isHidden = true
        self.recordCompleteView.isHidden = true
        switch self.status {
        case .unStart:
            startButton.isHidden = false
        case .recording:
            recordingView.isHidden = false
            countdownLabel.isRecording = true
            countdownLabel.isPausing = false
            pauseRecordButton.isSelected = false
            self.dotView.isHidden = false
        case .recordPausing:
            recordingView.isHidden = false
            countdownLabel.isPausing = true
            pauseRecordButton.isSelected = true
            self.dotView.isHidden = true
        case .recordComplete:
            countdownLabel.isRecording = false
            countdownLabel.isPausing = false
            recordCompleteView.isHidden = false
            self.playingView.isHidden = true
            self.playingTimeLabel.text = ""

            self.stopUpdateTimer()
        }
    }
}

extension SmartRecordBotView: AudioPlayManagerDelegate {
    func audioPlayManager(_ manager: AudioPlayManager, didFinishPlaying successfully: Bool) {
        self.playingTimeLabel.text = self.timeLabel.text
        self.playPauseButton.isSelected = false
    }
    
    func audioPlayManager(_ manager: AudioPlayManager, didUpdateProgress progress: Float) {
        
    }
    
    func audioPlayManager(_ manager: AudioPlayManager, didEncounterError error: Error) {
        
    }
    
    func audioPlayManager(_ manager: AudioPlayManager, didUpdateCurrentTime currentTime: TimeInterval) {
        self.playingTimeLabel.text = currentTime.toUIString
    }
}
