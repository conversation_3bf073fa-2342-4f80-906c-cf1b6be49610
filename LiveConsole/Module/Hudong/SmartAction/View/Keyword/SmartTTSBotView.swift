//
//  SmartTTSBotView.swift
//  LivePlus
//
//  Created by 郭炜 on 2025/1/16.
//

import UIKit

// MARK: - 底部的音色选择、语速选择、合成结果列表、合成按钮与添加按钮
class SmartTTSBotView: SmartBaseView {
    
    lazy var yinseLabel: UILabel = {
        let label = UILabel()
        label.text = "朗读音色"
        label.textColor = .white
        label.font = .systemFont(ofSize: 16, weight: .medium)
        return label
    }()
    
    
    lazy var buttonHorizonStack: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .horizontal
        stackView.spacing = 20
        stackView.alignment = .fill
        stackView.distribution = .fillEqually
        return stackView
    }()
    
    lazy var hechengButton: UIButton = {
        let button = UIButton()
        button.setTitle("合成语音", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.titleLabel?.font = .systemFont(ofSize: 18, weight: .bold)
        button.cornerRadius = 25
        button.applyGradient()
        button.isEnabled = false
        button.alpha = 0.6
        return button
    }()
    
    lazy var yinseView: AudioYinSeSelectionView = {
        let v = AudioYinSeSelectionView()
        return v
    }()
   
    
    var dataSource: [AIAudioResultModel] = []
    var playingModel: AIAudioResultModel?

    override init(frame: CGRect) {
        super.init(frame: frame)
    }
    
    convenience init() {
        self.init(frame: .zero)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func makeUI() {
        super.makeUI()
        
        addSubviews([yinseLabel, yinseView, buttonHorizonStack])
        
        buttonHorizonStack.addArrangedSubviews([hechengButton])
        
        yinseLabel.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.leading.equalToSuperview().inset(16)
            make.height.equalTo(32)
        }
       
        buttonHorizonStack.snp.makeConstraints { make in
            make.bottom.equalToSuperview()
            make.leading.trailing.equalToSuperview().inset(16)
            make.height.equalTo(50)
        }
        
        yinseView.snp.makeConstraints { make in
            make.top.equalTo(yinseLabel.snp.bottom)
            make.leading.trailing.equalToSuperview()
            make.bottom.equalTo(buttonHorizonStack.snp.top).offset(-24)
        }
    }
    
    override func business() {
        super.business()
                        
    }
    
    /// 音色列表（预置+克隆）
    func updateYinseView(audioSpkIDModel: AudioSpkIdModel?) {
        yinseView.presetView.bind(to: audioSpkIDModel?.preSpk ?? [],
                                  selectionVoiceType: AIReplyDataManager.shared.spkIdCache.currentPreAudioModel?.voiceType ?? "")
        yinseView.cloneView.bind(to: audioSpkIDModel?.userSpk ?? [],
                                 selectionSpeakId: AIReplyDataManager.shared.spkIdCache.currentCloneAudioModel?.spkId ?? "")
        yinseView.editView.isHidden = yinseView.cloneView.dataSource.isEmpty
    }
    
    func updateSelectedYinseView() {
        yinseView.presetView.updateSelected(selectionVoiceType: AIReplyDataManager.shared.spkIdCache.currentPreAudioModel?.voiceType ?? "")
        yinseView.cloneView.updateSelected(selectionSpeakId: AIReplyDataManager.shared.spkIdCache.currentCloneAudioModel?.spkId ?? "")
        yinseView.editView.isHidden = yinseView.cloneView.dataSource.isEmpty
    }

}
