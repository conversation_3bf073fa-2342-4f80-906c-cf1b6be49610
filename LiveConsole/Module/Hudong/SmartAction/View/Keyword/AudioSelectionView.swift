//
//  AudioSelectionView.swift
//  LivePlus
//
//  Created by 郭炜 on 2025/1/14.
//

import UIKit

// MARK: - Enum
enum AudioAddType: Int {
    case record
    case aiGenerate
    case importFile
}

// MARK: - 选择音频添加模式
class AudioSelectionView: UIView {
    
    // MARK: - Callback
    var onSelection: ((AudioAddType) -> Void)?
        
    // MARK: - UI Components
    private let containerView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor("#59536C")
        view.layer.cornerRadius = 30
        return view
    }()
    
    private let stackView: UIStackView = {
        let stack = UIStackView()
        stack.axis = .vertical
        stack.spacing = 30
        stack.alignment = .center
        return stack
    }()
    
    private let titleLabel: UILabel = {
        let label = UILabel()
        label.text = "请选择语音添加方式"
        label.textColor = .white
        label.font = .systemFont(ofSize: 17, weight: .medium)
        return label
    }()
    
    private let buttonStackView: UIStackView = {
        let stack = UIStackView()
        stack.axis = .horizontal
        stack.spacing = 15
        stack.distribution = .fillEqually
        return stack
    }()
    
    // MARK: - Init
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }
    
    // MARK: - Setup
    private func setupUI() {
        backgroundColor = UIColor.black.withAlphaComponent(0.4)
        
        addSubview(containerView)
        containerView.addSubview(stackView)
        
        stackView.addArrangedSubview(titleLabel)
        stackView.addArrangedSubview(buttonStackView)
        
        // 添加三个按钮
        let buttons = [
            createButton(title: "现在录音", type: .record),
            createButton(title: "AI合成", type: .aiGenerate),
            createButton(title: "导入语音", type: .importFile)
        ]
        
        buttons.forEach { buttonStackView.addArrangedSubview($0) }
        
        setupConstraints()
        
        // 添加背景点击手势
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(backgroundTapped))
        self.addGestureRecognizer(tapGesture)
    }
    
    private func createButton(title: String, type: AudioAddType) -> UIButton {
        let button = UIButton(type: .custom)
        button.setTitle(title, for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.titleLabel?.font = .systemFont(ofSize: 14)
        button.backgroundColor = UIColor("#352E4D")
        button.layer.cornerRadius = 8
        button.tag = type.rawValue
        button.addTarget(self, action: #selector(buttonTapped(_:)), for: .touchUpInside)
        
        // 设置按钮大小约束
        button.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            button.heightAnchor.constraint(equalToConstant: 40),
            button.widthAnchor.constraint(equalToConstant: 84)
        ])
        
        return button
    }
    
    private func setupConstraints() {
        containerView.translatesAutoresizingMaskIntoConstraints = false
        stackView.translatesAutoresizingMaskIntoConstraints = false
        buttonStackView.translatesAutoresizingMaskIntoConstraints = false
        
        NSLayoutConstraint.activate([
            containerView.centerXAnchor.constraint(equalTo: centerXAnchor),
            containerView.centerYAnchor.constraint(equalTo: centerYAnchor),
            
            stackView.topAnchor.constraint(equalTo: containerView.topAnchor, constant: 30),
            stackView.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 25),
            stackView.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -25),
            stackView.bottomAnchor.constraint(equalTo: containerView.bottomAnchor, constant: -30)
        ])
    }
    
    // MARK: - Actions
    @objc private func buttonTapped(_ sender: UIButton) {
        guard let type = AudioAddType(rawValue: sender.tag) else { return }
        onSelection?(type)
        dismiss()
    }
    
    @objc private func backgroundTapped() {
        dismiss()
    }
    
    // MARK: - Public Methods
    func show(in view: UIView) {
        view.addSubview(self)
        self.frame = view.bounds
        
        // 添加显示动画
        containerView.transform = CGAffineTransform(scaleX: 0.1, y: 0.1)
        UIView.animate(withDuration: 0.3, delay: 0, options: .curveEaseOut) {
            self.containerView.transform = .identity
        }
    }
    
    private func dismiss() {
        UIView.animate(withDuration: 0.2, animations: {
            self.alpha = 0
        }) { _ in
            self.removeFromSuperview()
        }
    }
}
