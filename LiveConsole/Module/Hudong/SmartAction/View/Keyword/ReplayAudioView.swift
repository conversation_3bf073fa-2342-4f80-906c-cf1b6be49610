//
//  ReplayAudioView.swift
//  LivePlus
//
//  Created by 郭炜 on 2025/1/13.
//

import UIKit
import QMUIKit

enum ReplyAudioReplyStyle: Int {
    case audio = 0
    case text = 1
}

protocol ReplayAudioViewDelegate: NSObjectProtocol {
    func endEdit()
}

// MARK: - 音频回复
class ReplayAudioView: SmartBaseView {
    
    weak var delegate: ReplayAudioViewDelegate?
    
    /// 回复形式
    var replyStyle: ReplyAudioReplyStyle = .audio {
        didSet {
            switch replyStyle {
            case .audio:
                self.modeSelecteButton.isSelected = false
                self.editStackView.alpha = 1.0
                self.audioStackView.alpha = 1.0
                self.textEditStackView.alpha = 0.0
            case .text:
                self.modeSelecteButton.isSelected = true
                self.editStackView.alpha = 0.0
                self.audioStackView.alpha = 0.0
                self.textEditStackView.alpha = 1.0
            }
            self.updateTable()
        }
    }
    
    lazy var background: UIView = {
        let view = UIView()
        return view
    }()
    
    lazy var tipImage: UIImageView = {
        let imageView = UIImageView(image: UIImage(named: "ic_smart_tip"))
        return imageView
    }()
    
    lazy var tipLabel: UILabel = {
        let label = UILabel()
        label.text = "回复内容"
        label.textColor = .white
        label.font = LCDevice.DIN_Font_PF_M(16)
        return label
    }()
    
    lazy var questionButton: UIButton = {
        let button = UIButton()
        button.setImage(UIImage(named: "ic_smart_question"), for: .normal)
        return button
    }()
    
    lazy var replaceLabel: UILabel = {
        let label = UILabel()
        label.text = "回复形式："
        label.textAlignment = .right
        label.textColor = .white
        label.font = LCDevice.DIN_Font_PF_R(12)
        return label
    }()
    
    lazy var modeSelecteButton: QMUIButton = {
        let button = QMUIButton()
        button.setTitle("语音", for: .normal)
        button.setTitle("文字", for: .selected)
        button.setTitleColor(.white, for: .normal)
        button.setImage(UIImage(named: "ic_smart_zhankai"), for: .normal)
        button.titleLabel?.font = .systemFont(ofSize: 14)
        button.imagePosition = .right
        button.spacingBetweenImageAndTitle = 4
        button.cornerRadius = 15
        button.backgroundColor = UIColor("#282828")
        return button
    }()
    
    /// 语音情况下的视图展示
    lazy var audioStackView: UIStackView = {
        let stackView = UIStackView(arrangedSubviews: [recordButton, combineButton, uploadButton])
        stackView.axis = .horizontal
        stackView.spacing = 8
        recordButton.snp.makeConstraints { make in
            make.width.equalTo(82)
            make.height.equalTo(32)
        }
        combineButton.snp.makeConstraints { make in
            make.width.equalTo(82)
            make.height.equalTo(32)
        }
        uploadButton.snp.makeConstraints { make in
            make.width.equalTo(82)
            make.height.equalTo(32)
        }
        stackView.alpha = 0.0
        return stackView
    }()
    
    lazy var recordButton: QMUIButton = {
        let button = QMUIButton()
        button.setTitle("录音", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.titleLabel?.font = .systemFont(ofSize: 14, weight: .medium)
        button.setImage(UIImage(named: "sm_plus"), for: .normal)
        button.imagePosition = .left
        button.spacingBetweenImageAndTitle = 5
        button.backgroundColor = UIColor("#282828")
        button.cornerRadius = 6
        return button
    }()
    
    lazy var combineButton: QMUIButton = {
        let button = QMUIButton()
        button.setTitle("合成", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.titleLabel?.font = .systemFont(ofSize: 14, weight: .medium)
        button.setImage(UIImage(named: "sm_plus"), for: .normal)
        button.imagePosition = .left
        button.spacingBetweenImageAndTitle = 5
        button.backgroundColor = UIColor("#282828")
        button.cornerRadius = 6
        return button
    }()
    
    lazy var uploadButton: QMUIButton = {
        let button = QMUIButton()
        button.setTitle("导入", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.titleLabel?.font = .systemFont(ofSize: 14, weight: .medium)
        button.setImage(UIImage(named: "sm_plus"), for: .normal)
        button.imagePosition = .left
        button.spacingBetweenImageAndTitle = 5
        button.backgroundColor = UIColor("#282828")
        button.cornerRadius = 6
        return button
    }()
        
    lazy var audioTableView: UITableView = {
        let tableView = UITableView()
        tableView.register(cellWithClass: SmartAudioItem.self)
        tableView.delegate = self
        tableView.dataSource = self
        tableView.separatorStyle = .none
        tableView.backgroundColor = .clear
        tableView.dragDelegate = self     // 添加拖拽代理
        tableView.dropDelegate = self     // 添加放置代理
        tableView.dragInteractionEnabled = true  // 启用拖拽
        return tableView
    }()
    
    /// 删除和改名
    lazy var editStackView: UIStackView = {
        let stackView = UIStackView(arrangedSubviews: [renameButton, deleteButton])
        stackView.axis = .horizontal
        stackView.spacing = 18
        stackView.alpha = 0.0
        return stackView
    }()
    
    lazy var deleteButton: QMUIButton = {
        let button = QMUIButton()
        button.setImage(UIImage(named: "sm_delete"), for: .normal)
        button.zl_enlargeValidTouchArea(inset: 20)
        return button
    }()
    
    lazy var renameButton: QMUIButton = {
        let button = QMUIButton()
        button.setImage(UIImage(named: "ic_clone_edit"), for: .normal)
        button.zl_enlargeValidTouchArea(inset: 20)
        return button
    }()
    
    /// 空视图
    lazy var emptyImage: UIImageView = {
        let view = UIImageView()
        view.image = UIImage(named: "占位图_上传素材_空空如也")
        return view
    }()
    
    lazy var renameAlert: InputAlert = {
        return InputAlert(frame: UIScreen.main.bounds)
    }()
    
    
    lazy var emptyLable: UILabel = {
        let label = UILabel()
        label.text = "暂无内容"
        label.textColor = UIColor("#ACACAC")
        label.font = .systemFont(ofSize: 14)
        return label
    }()
    
    lazy var textEditStackView: UIStackView = {
        let stackView = UIStackView(arrangedSubviews: [textPlusButton, textEditButton, textDeleteButton])
        stackView.axis = .horizontal
        stackView.spacing = 18
        stackView.alpha = 0.0
        return stackView
    }()
    
    lazy var textPlusButton: QMUIButton = {
        let button = QMUIButton()
        button.setImage(UIImage(named: "sm_text_plus"), for: .normal)
        button.zl_enlargeValidTouchArea(inset: 20)
        return button
    }()
    
    lazy var textEditButton: QMUIButton = {
        let button = QMUIButton()
        button.setImage(UIImage(named: "sm_text_edit"), for: .normal)
        button.zl_enlargeValidTouchArea(inset: 20)
        return button
    }()
    
    lazy var textDeleteButton: QMUIButton = {
        let button = QMUIButton()
        button.setImage(UIImage(named: "sm_text_delete"), for: .normal)
        button.zl_enlargeValidTouchArea(inset: 20)
        return button
    }()
    
    lazy var textTableView: UITableView = {
        let tableView = UITableView()
        tableView.register(cellWithClass: SmartTextItem.self)
        tableView.delegate = self
        tableView.dataSource = self
        tableView.separatorStyle = .none
        tableView.backgroundColor = .clear
        tableView.dragDelegate = self     // 添加拖拽代理
        tableView.dropDelegate = self     // 添加放置代理
        tableView.dragInteractionEnabled = true  // 启用拖拽
        return tableView
    }()
    
    weak var model: AiKeywordVoiceReplyItemModel?
    
    var playingModel: ResFileModel?
    var selectionModel: ResFileModel?

    override init(frame: CGRect) {
        super.init(frame: frame)
    }
    
    convenience init() {
        self.init(frame: .zero)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func makeUI() {
        super.makeUI()
        
        addSubviews([background])
        background.addSubviews([tipImage, tipLabel, questionButton, replaceLabel, modeSelecteButton, audioStackView, audioTableView, textTableView, editStackView, textEditStackView, emptyImage, emptyLable])
        
        background.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview()
            make.top.bottom.equalToSuperview()
        }
        tipImage.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(6)
            make.leading.equalToSuperview().inset(16)
            make.size.equalTo(16)
        }
        tipImage.isHidden = true
        tipLabel.snp.makeConstraints { make in
            make.leading.equalToSuperview().inset(16)
//            make.centerY.equalTo(tipImage.snp.centerY)
            make.height.equalTo(30)
            make.top.equalToSuperview().inset(2)
        }
        questionButton.snp.makeConstraints { make in
            make.leading.equalTo(tipLabel.snp.trailing).offset(4)
            make.centerY.equalTo(tipLabel.snp.centerY)
        }
        replaceLabel.snp.makeConstraints { make in
            make.centerY.equalTo(tipLabel.snp.centerY)
            make.trailing.equalTo(modeSelecteButton.snp.leading).offset(-6)
        }
        modeSelecteButton.snp.makeConstraints { make in
            make.trailing.equalToSuperview().inset(16)
            make.centerY.equalTo(tipLabel.snp.centerY)
            make.width.equalTo(80)
            make.height.equalTo(30)
        }
        audioStackView.snp.makeConstraints { make in
            make.top.equalTo(modeSelecteButton.snp.bottom).offset(14)
            make.leading.equalToSuperview().inset(16)
            make.height.equalTo(32)
        }
        editStackView.snp.makeConstraints { make in
            make.centerY.equalTo(audioStackView.snp.centerY)
            make.trailing.equalToSuperview().inset(16)
            make.height.equalTo(24)
        }
        audioTableView.snp.makeConstraints { make in
            make.top.equalTo(audioStackView.snp.bottom).offset(12)
            make.leading.trailing.equalToSuperview()
            make.bottom.equalToSuperview()
        }
        emptyImage.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(126)
            make.centerX.equalToSuperview()
            make.size.equalTo(136)
        }
        emptyLable.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalTo(emptyImage.snp.bottom)
        }
        textTableView.snp.makeConstraints { make in
            make.edges.equalTo(audioTableView)
        }
        textEditStackView.snp.makeConstraints { make in
            make.centerY.equalTo(audioStackView.snp.centerY)
            make.trailing.equalToSuperview().inset(16)
            make.height.equalTo(24)
        }
        
    }
    
    override func business() {
        super.business()
        
        self.replyStyle = .audio
        
        modeSelecteButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self else { return }
                self.replyButtonAction()
            }.disposed(by: rx.disposeBag)
        
        deleteButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self else { return }
                guard let selectionModel = self.selectionModel else {
                    HUD.showFail("请选择要删除的语音")
                    return
                }
                if selectionModel.audioState == .waitting {
                    HUD.showFail("AI合成中语音不支持删除")
                    return
                }
                AlertView.show(leftOption: .gray(title: "取消", action: nil), rightOption: .main(title: "确定", action: { [weak self] in
                    guard let self = self else { return }
                    self.model?.replyItem?.resources?.removeAll(where: { $0.id == selectionModel.id })
                    AudioPlayManager.shared.stop()
                    self.clearProgress()
                    self.updateTable()
                    self.model?.modifyUpdateTime()
                    self.selectionModel = nil
                }), title: "要删除已选回复内容吗？", message: "删除后不可恢复")
            }.disposed(by: rx.disposeBag)
        textDeleteButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self else { return }
                guard let selectionModel = self.selectionModel else {
                    HUD.showFail("请选择要删除的文本")
                    return
                }
                AlertView.show(leftOption: .gray(title: "取消", action: nil), rightOption: .main(title: "确定", action: { [weak self] in
                    guard let self = self else { return }
                    self.model?.replyItem?.resources?.removeAll(where: { $0.id == selectionModel.id })
                    self.updateTable()
                    self.model?.modifyUpdateTime()
                    self.selectionModel = nil
                }), title: "要删除已选回复内容吗？", message: "删除后不可恢复")
            }.disposed(by: rx.disposeBag)
        
        renameButton.addTarget(self, action: #selector(renameAction), for: .touchUpInside)
        
    }
    
    @objc func renameAction() {
        guard let selectionModel = self.selectionModel else {
            HUD.showFail("请选择语音")
            return
        }
        
        self.renameAlert.show(selectionModel.title ?? "语音01", 10)
        self.renameAlert.sureBtnActionBlock = {[weak self] (_ newName) in
            guard let self = self else { return }
            guard !newName.isEmpty else { return }
            self.selectionModel?.title = newName
            self.updateTable()
        }
    }

}

extension ReplayAudioView {
    
    func bind(to model: AiKeywordVoiceReplyItemModel) {
        if model.itemType == .text {
            self.replyStyle = .text
        } else {
            self.replyStyle = .audio
        }
        self.model = model
        self.updateTable()
    }
    
    func updateTable() {
       
        self.audioTableView.reloadData()
        self.textTableView.reloadData()
        self.emptyLable.isHidden = false
        self.emptyImage.isHidden = false
        self.editStackView.isHidden = true
        self.textEditStackView.isHidden = true
        self.textTableView.isHidden = true
        self.audioTableView.isHidden = true
        self.audioStackView.isHidden = true
        
        if let replyItem = model?.replyItem,
            let resources = replyItem.resources,
            !resources.isEmpty {
            self.emptyLable.isHidden = true
            self.emptyImage.isHidden = true
            self.updateReplyStyleUI()
        }
        
        switch self.replyStyle {
        case .text:
            self.textEditStackView.isHidden = false
        case .audio:
            self.audioStackView.isHidden = false
        }
    }
    
    override func hitTest(_ point: CGPoint, with event: UIEvent?) -> UIView? {
        guard isUserInteractionEnabled else { return nil }
        guard !isHidden else { return nil }
        self.delegate?.endEdit()
        return super.hitTest(point, with: event)
    }
    
    /// 更新 根据回复内容显示不同的UI
    func updateReplyStyleUI() {
        switch self.replyStyle {
        case .text:
            self.textEditStackView.isHidden = false
            self.textTableView.isHidden = false
        case .audio:
            self.editStackView.isHidden = false
            self.audioTableView.isHidden = false
            self.audioStackView.isHidden = false
        }
    }
}

extension ReplayAudioView: UITableViewDelegate, UITableViewDataSource {
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        if tableView.isEqual(self.audioTableView) {
            return model?.replyItem?.audioResources.count ?? 0
        }
        if tableView.isEqual(self.textTableView) {
            return model?.replyItem?.textResources.count ?? 0
        }
        return 0
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        if tableView.isEqual(self.textTableView) {
            guard let resources = model?.replyItem?.textResources else { return UITableViewCell() }
            let cell = tableView.dequeueReusableCell(withClass: SmartTextItem.self)
            guard let model = resources[safe: indexPath.row] else { return cell }
            cell.bind(to: model, isSelected: selectionModel?.id == model.id)
            return cell
        }
        
        if tableView.isEqual(self.audioTableView) {
            guard let resources = model?.replyItem?.audioResources else { return UITableViewCell() }
            let cell = tableView.dequeueReusableCell(withClass: SmartAudioItem.self)
            guard let model = resources[safe: indexPath.row] else { return cell }
            cell.bind(to: model, isPlaying: playingModel?.id == model.id, isSelected: selectionModel?.id == model.id)
            
            cell.rateAction = { [weak self] in
                guard let self = self else { return }
                // 创建和显示
                let rateView = AudioRateSelectionView()
                rateView.onRateSelected = { [weak self] rate in
                    guard let self = self else { return }
                    print("Selected rate: \(rate)")
                    model.voidespeed = "\(rate)"
                    self.playingModel = nil
                    self.clearProgress()
                    AudioPlayManager.shared.stop()
                    self.audioTableView.reloadData()
                }
                rateView.show()
            }
            cell.playPauseAction = { [weak self] in
                guard let self = self else { return }
                if let playingModel = self.playingModel {
                    // 如果已经有播放的
                    if model.id == playingModel.id {
                        // 并且是相同的 那么暂停
                        AudioPlayManager.shared.stop()
                        self.playingModel = nil
                    } else {
                        // 不相同 先暂停再切换
                        AudioPlayManager.shared.stop()
                        let _ = try? AudioPlayManager.shared.play(url: model.toAudioPlayUrl, rate: Float(model.voidespeed) ?? 1.0)
                        self.playingModel = model
                    }
                } else {
                    // 没有播放的
                    AudioPlayManager.shared.stop()
                    let _ = try? AudioPlayManager.shared.play(url: model.toAudioPlayUrl, rate: Float(model.voidespeed) ?? 1.0)
                    self.playingModel = model
                }
                self.clearProgress()
            }
            
            // 重试
            cell.retryAction = { [weak self] in
                guard let self = self else { return }
                if model.audioState == .waitting, let taskid = model.taskId {
                    HUD.showFail("正在获取音频数据...")
                    AIReplyDataManager.shared.geTTSVoiceSynStatus(taskId: taskid)
                }
            }
            
            return cell
        }
        return UITableViewCell()
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        if tableView.isEqual(self.textTableView) {
            guard let resources = model?.replyItem?.textResources else { return 50 + 16 }
            let model = resources[indexPath.row]
            return model.tabViewHeight
        }
        return 50 + 16
    }
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        if tableView.isEqual(self.textTableView) {
            guard let resources = model?.replyItem?.textResources else { return }
            var oldIndex = resources.firstIndex(where: { $0.id == selectionModel?.id })
            let model = resources[indexPath.row]
            if self.selectionModel?.id == model.id {
                self.selectionModel = nil
            } else {
                self.selectionModel = model
            }
            if let oldIndex = oldIndex {
                self.textTableView.reloadRows(at: [IndexPath(row: oldIndex, section: 0), indexPath], with: .automatic)
            } else {
                self.textTableView.reloadRows(at: [indexPath], with: .automatic)
            }
        }
        
        if tableView.isEqual(self.audioTableView) {
            guard let resources = model?.replyItem?.audioResources else { return }
            var oldIndex = resources.firstIndex(where: { $0.id == selectionModel?.id })
            let model = resources[indexPath.row]
            if self.selectionModel?.id == model.id {
                self.selectionModel = nil
            } else {
                self.selectionModel = model
            }
    //        self.tableView.reloadData()
            if let oldIndex = oldIndex {
                self.audioTableView.reloadRows(at: [IndexPath(row: oldIndex, section: 0), indexPath], with: .automatic)
            } else {
                self.audioTableView.reloadRows(at: [indexPath], with: .automatic)
            }
        }
    }
    
    func clearProgress() {
        guard let resources = model?.replyItem?.resources else { return }
        for index in 0..<resources.count {
            if let cell = audioTableView.cellForRow(at: IndexPath(row: index, section: 0)) as? SmartAudioItem {
                cell.updateProgress(value: 0)
            }
        }
        self.audioTableView.reloadData()
    }
    
    func tableView(_ tableView: UITableView, canMoveRowAt indexPath: IndexPath) -> Bool {
        return true
    }
    
    func tableView(_ tableView: UITableView, canEditRowAt indexPath: IndexPath) -> Bool {
        return true
    }
    
    func tableView(_ tableView: UITableView, moveRowAt sourceIndexPath: IndexPath, to destinationIndexPath: IndexPath) {
        if tableView.isEqual(self.textTableView) {
            guard var resources = model?.replyItem?.textResources else { return }
            guard let movedItem = resources[safe: sourceIndexPath.row] else { return }
            resources.remove(at: sourceIndexPath.row)
            resources.insert(movedItem, at: destinationIndexPath.row)
            model?.replyItem?.resources = resources
            self.textTableView.reloadData()
        }
        
        if tableView.isEqual(self.audioTableView) {
            guard var resources = model?.replyItem?.audioResources else { return }
            guard let movedItem = resources[safe: sourceIndexPath.row] else { return }
            resources.remove(at: sourceIndexPath.row)
            resources.insert(movedItem, at: destinationIndexPath.row)
            model?.replyItem?.resources = resources
            self.audioTableView.reloadData()
        }
    }
}

// 添加拖拽相关代理
extension ReplayAudioView: UITableViewDragDelegate, UITableViewDropDelegate {
    // 设置可拖拽的项
    func tableView(_ tableView: UITableView, itemsForBeginning session: UIDragSession, at indexPath: IndexPath) -> [UIDragItem] {
        guard let resources = model?.replyItem?.resources else { return [] }
        let item = resources[indexPath.row]
        let itemProvider = NSItemProvider()
        let dragItem = UIDragItem(itemProvider: itemProvider)
        dragItem.localObject = item
        return [dragItem]
    }
    
    // 处理放置操作
    func tableView(_ tableView: UITableView, performDropWith coordinator: UITableViewDropCoordinator) {

    }
    
    // 自定义拖动预览的外观
    func tableView(_ tableView: UITableView, dragPreviewParametersForRowAt indexPath: IndexPath) -> UIDragPreviewParameters? {
        let parameters = UIDragPreviewParameters()
        parameters.backgroundColor = .clear  // 设置预览背景为透明
        return parameters
    }
    
    func tableView(_ tableView: UITableView, dropPreviewParametersForRowAt indexPath: IndexPath) -> UIDragPreviewParameters? {
        let parameters = UIDragPreviewParameters()
        parameters.backgroundColor = .clear  // 设置预览背景为透明
        return parameters
    }
}

extension ReplayAudioView: AudioPlayManagerDelegate {
    func audioPlayManager(_ manager: AudioPlayManager, didFinishPlaying successfully: Bool) {
        self.playingModel = nil
        self.clearProgress()
        self.audioTableView.reloadData()
    }
    
    func audioPlayManager(_ manager: AudioPlayManager, didUpdateProgress progress: Float) {
        guard let index = model?.replyItem?.resources?.firstIndex(where: { $0.id == playingModel?.id }), let cell = audioTableView.cellForRow(at: IndexPath(row: index, section: 0)) as? SmartAudioItem else {
            return
        }
        cell.updateProgress(value: progress)
    }
    
    func audioPlayManager(_ manager: AudioPlayManager, didUpdateCurrentTime currentTime: TimeInterval) {
        
    }
    
    func audioPlayManager(_ manager: AudioPlayManager, didEncounterError error: Error) {
        
    }
}

extension ReplayAudioView {
    func replyButtonAction() {
        if let replyItem = model?.replyItem,
            let resources = replyItem.resources,
           !resources.isEmpty {
            HUD.showFail("请先删除所有内容")
            return
        }
        let popupView = QMUIPopupMenuView()
        popupView.minimumWidth = 76
        popupView.maximumWidth = 76
        popupView.itemHeight = 44
        popupView.itemTitleColor = .white
        popupView.itemTitleFont = LCDevice.DIN_Font_PF_M(14)
        popupView.backgroundColor = UIColor("#444444")
        popupView.sourceView = self.modeSelecteButton
        popupView.automaticallyHidesWhenUserTap = true
        popupView.arrowSize = .zero
        popupView.highlightedBackgroundColor = .clear
        popupView.preferLayoutDirection = .below
        
        let allButton = QMUIPopupMenuButtonItem(image: nil, title: "  语音") { [weak self] _ in
            guard let self = self else { return }
            guard let model = self.model else { return }
            self.replyStyle = .audio
            popupView.hideWith(animated: true)
        }
        let usedButton = QMUIPopupMenuButtonItem(image: nil, title: "  文字") { [weak self] _ in
            guard let self = self else { return }
            guard let model = self.model else { return }
            self.replyStyle = .text
            popupView.hideWith(animated: true)
        }
        allButton.button.highlightedBackgroundColor = .clear
        usedButton.button.highlightedBackgroundColor = .clear

        allButton.button.setTitleColor(UIColor.white, for: .normal)
        allButton.button.setTitleColor(UIColor("#ADAAFF"), for: .selected)
        usedButton.button.setTitleColor(UIColor.white, for: .normal)
        usedButton.button.setTitleColor(UIColor("#ADAAFF"), for: .selected)

        switch self.replyStyle {
        case .audio:
            allButton.button.isSelected = true
        case .text:
            usedButton.button.isSelected = true
        }
        popupView.items = [allButton, usedButton]
        popupView.showWith(animated: true)
    }
}
