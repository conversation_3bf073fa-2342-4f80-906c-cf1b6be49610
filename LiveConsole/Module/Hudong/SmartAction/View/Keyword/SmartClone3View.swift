//
//  SmartClone3View.swift
//  LiveConsole
//
//  Created by simon on 21.5.25.
//

import QMUIKit

// 这是克隆的第三步操作View
public protocol SmartClone3ViewDelegate: AnyObject {
    func textViewDidChange(count: Int)
}

class SmartClone3View: SmartBaseView {
    
    weak var delegate:SmartClone3ViewDelegate?
    
    let maxCount = 100
    
    lazy var textCountLabel: UILabel = {
        let label = UILabel()
        label.textColor = UIColor("#ACACAC")
        label.font = .systemFont(ofSize: 12)
        label.textAlignment = .right
        label.text = "0/100"
        return label
    }()
    
    lazy var textBackground: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor("#282828")
        view.cornerRadius = 6
        return view
    }()
    
    public lazy var textView: QMUITextView = {
        let textView = QMUITextView()
        textView.textColor = .white
        textView.font = .systemFont(ofSize: 14)
        textView.placeholder = "请输入文案"
        textView.placeholderColor = UIColor("#ACACAC")
        textView.backgroundColor = .clear
        textView.delegate = self
        return textView
    }()
    
    
    lazy var iconImage: UIImageView = {
        let imageView = UIImageView(image: UIImage(named: "ic_clone_tip"))
        return imageView
    }()
    
    
    lazy var tipLabel: UILabel = {
        let label = UILabel()
        label.text = "1. 请您务必确保在使用我们的服务时遵守所有适用的法律法规、行政规章、国家政策以及社会公德和伦理道德。\n2. 该文案也会作为音色创建后，该音色的试听音频"
        label.textColor = UIColor("#ACACAC")
        label.font = .systemFont(ofSize: 12)
        label.numberOfLines = 0
        return label
    }()

    override init(frame: CGRect) {
        super.init(frame: frame)
    }
    
    convenience init() {
        self.init(frame: .zero)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func makeUI() {
        super.makeUI()
        
        addSubviews([textCountLabel, textBackground, iconImage, tipLabel])
        textBackground.addSubviews([textView])
        
        textView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(16)
        }
        
        textBackground.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(30)
            make.leading.trailing.equalToSuperview().inset(16)
            make.bottom.equalTo(tipLabel.snp.top).offset(-10)
        }
        
        
        tipLabel.snp.makeConstraints { make in
            make.leading.equalToSuperview().inset(45)
            make.trailing.equalToSuperview().inset(16)
            make.bottom.equalToSuperview()
            make.height.equalTo(70)
        }
        
        iconImage.snp.makeConstraints { make in
            make.leading.equalToSuperview().inset(16)
            make.top.equalTo(tipLabel.snp.top).offset(6)
            make.size.equalTo(20)
        }
        
        textCountLabel.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.trailing.equalToSuperview().inset(16)
            make.height.equalTo(15)
        }
    }
    
    override func business() {
        super.business()
    }

}


extension SmartClone3View: QMUITextViewDelegate {
    
    func textViewDidChange(_ textView: UITextView) {
        self.textCountLabel.text = "\(textView.text.count)/100"
        var count = textView.text.count
        if textView.text == "" || textView.text.isBlank {
            count = 0
        }
        self.delegate?.textViewDidChange(count: count)
    }
    
    func textViewDidEndEditing(_ textView: UITextView) {
        var count = textView.text.count
        if textView.text == "" || textView.text.isBlank {
            count = 0
        }
        self.delegate?.textViewDidChange(count: count)
    }
    
    // 检测编辑操作
    func textView(_ textView: UITextView, shouldChangeTextIn range: NSRange, replacementText text: String) -> Bool {       
        
        if text == "\n" {
            textView.resignFirstResponder()
            return false
        }
        
        if let result = (textView.text as NSString?)?.replacingCharacters(in: range, with: text) {
            // 更新高度
            if result.count > maxCount {
                HUD.showFail("上限100字")
                return false
            }
        }
        
        return true
    }

   
}
