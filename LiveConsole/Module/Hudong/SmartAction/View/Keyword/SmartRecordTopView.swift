//
//  SmartRecordTopView.swift
//  LivePlus
//
//  Created by 郭炜 on 2025/1/14.
//

import UIKit
import RSKPlaceholderTextView
//import QMUIKit

// MARK: - 现在录音 - 提示文案
class SmartRecordTopView: SmartBaseView {
    
    lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.text = "提示文案（选填）"
        label.textColor = .white
        label.font = .systemFont(ofSize: 14)
        return label
    }()
    
    lazy var textView: RSKPlaceholderTextView = {
        let textView = RSKPlaceholderTextView()
        textView.textColor = .white
        textView.font = .systemFont(ofSize: 20)
        textView.placeholder = "请输入文案"
//        textView.placeholderColor = UIColor("#ACACAC")
        textView.contentInset = UIEdgeInsets(top: 12, left: 15, bottom: 20, right: 15)
        textView.backgroundColor = UIColor("#282828")
        textView.cornerRadius = 6
        return textView
    }()

    override init(frame: CGRect) {
        super.init(frame: frame)
    }
    
    convenience init() {
        self.init(frame: .zero)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func makeUI() {
        super.makeUI()
        
        addSubviews([titleLabel, textView])
        
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.leading.trailing.equalToSuperview().inset(16)
            make.height.equalTo(22)
        }
        textView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(10)
            make.leading.trailing.equalToSuperview().inset(16)
            make.bottom.equalToSuperview()
        }
    }
    
    override func business() {
        super.business()
    }

}
