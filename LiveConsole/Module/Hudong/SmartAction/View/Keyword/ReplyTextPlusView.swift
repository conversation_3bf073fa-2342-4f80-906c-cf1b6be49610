//
//  ReplyTextPlusView.swift
//  LiveConsole
//
//  Created by 郭炜 on 2025/4/23.
//


import UIKit
import QMUIKit
import SnapKit

class ReplyTextPlusView: UIView {
    
    var confirmCallback: ((_ text: String?) -> Void)?
    
    // 如果是礼物的恢复内容 有插入模板
    var isGift: Bool = false
    
    var nonEditableRanges = [NSRange]() // 存储受保护的范围
    
    var replyType: ReplyMsgType = .attention
    
    var maxCount: Int = 45
    
    // MARK: - Properties
    private let containerView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        view.layer.cornerRadius = 16
        view.clipsToBounds = true
        return view
    }()
    
    
    private let subView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        return view
    }()
    
    private let stackView: UIStackView = {
        let stack = UIStackView()
        stack.axis = .vertical
        stack.spacing = 12
        stack.alignment = .leading
        return stack
    }()
    
    private let titleLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 18, weight: .medium)
        label.textAlignment = .left
        label.textColor = UIColor("#1E1F20")
        return label
    }()
    
    lazy var textView: QMUITextView = {
        let textView = QMUITextView()
        textView.textColor = .black
        textView.font = .systemFont(ofSize: 14)
        textView.placeholder = "请输入内容，例：欢迎来到我的直播间"
        textView.placeholderColor = UIColor("#1E1F204D")
        textView.backgroundColor = UIColor("#F5F5F8")
        textView.cornerRadius = 6
        textView.delegate = self
        return textView
    }()
    
    private let contentLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 12)
        label.textColor = UIColor("#1E1F2099")
        label.numberOfLines = 2
        label.textAlignment = .left
        label.text = "因抖音的评论字数限制，单条内容建议40字以内（最多45字）"
        return label
    }()
    
    lazy var buttonStackView: UIStackView = {
        let stackView = UIStackView(arrangedSubviews: [cancelButton, confirmButton])
        stackView.axis = .horizontal
        stackView.spacing = 23
        cancelButton.snp.makeConstraints { make in
            make.width.equalTo(116)
            make.height.equalTo(40)
        }
        confirmButton.snp.makeConstraints { make in
            make.width.equalTo(116)
            make.height.equalTo(40)
        }
        return stackView
    }()
    
    private let cancelButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitle("取消", for: .normal)
        button.setTitleColor(.black, for: .normal)
        button.titleLabel?.font = .systemFont(ofSize: 14, weight: .medium)
        button.cornerRadius = 20
        button.borderWidth = 1.5
        button.borderColor = UIColor("#E5E3EB")
        return button
    }()
    
    private let confirmButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitle("确定", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.backgroundColor = UIColor("#AC63F9")
        button.titleLabel?.font = .systemFont(ofSize: 14, weight: .medium)
        button.applyGradient()
        button.cornerRadius = 20
        return button
    }()
    
    // 插入
    private let insertButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitle("插入礼物变量", for: .normal)
        button.setTitleColor(UIColor("#6763F6"), for: .normal)
        button.backgroundColor = .clear
        button.titleLabel?.font = .systemFont(ofSize: 14, weight: .regular)
        return button
    }()
    
    
    private let sampleButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitle("使用系统模版", for: .normal)
        button.setTitleColor(UIColor("#6763F6"), for: .normal)
        button.backgroundColor = .clear
        button.titleLabel?.font = .systemFont(ofSize: 14, weight: .regular)
        return button
    }()
    
    // MARK: - Init
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }
    
    // MARK: - Setup
    private func setupUI() {
        backgroundColor = UIColor.black.withAlphaComponent(0.4)
        
        addSubviews([containerView])
        containerView.addSubviews([stackView, buttonStackView])
        
        stackView.addArrangedSubview(titleLabel)
        stackView.addArrangedSubview(subView)
        stackView.addArrangedSubview(textView)
        stackView.addArrangedSubview(contentLabel)
        
        subView.addSubviews([insertButton, sampleButton])
        
        insertButton.snp.makeConstraints { make in
            make.leading.equalToSuperview()
            make.top.bottom.equalToSuperview()
            make.width.equalTo(100)
        }
        
        sampleButton.snp.makeConstraints { make in
            make.trailing.equalToSuperview()
            make.top.bottom.equalToSuperview()
            make.width.equalTo(100)
        }
        
        
        textView.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview()
        }
        titleLabel.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview()
            make.height.equalTo(28)
        }
        subView.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview()
            make.height.equalTo(28)
        }
        
        setupConstraints()
        
        buttonStackView.snp.makeConstraints { make in
            make.height.equalTo(40)
            make.bottom.equalToSuperview().inset(24)
            make.centerX.equalToSuperview()
        }
        confirmButton.addTarget(self, action: #selector(dismissAlert), for: .touchUpInside)
        
        self.setupKeyboardObservers()
        
        confirmButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self else { return }
                if let text = self.textView.text {
                    
                    if self.isGift {
                        if !text.contains("【礼物名称】") {
                            HUD.showFail("请插入礼物变量")
                            return
                        }
                    }
                    
                    if text.count > maxCount {
                        HUD.showFail("上限45字")
                        return
                    }
                    self.confirmCallback?(text)
                }
                self.dismissAlert()
            }.disposed(by: rx.disposeBag)
        
        cancelButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self else { return }
                self.dismissAlert()
            }.disposed(by: rx.disposeBag)
        
        insertButton.addTarget(self, action: #selector(insertAction), for: .touchUpInside)
        sampleButton.addTarget(self, action: #selector(sampleAction), for: .touchUpInside)
        
    }
    
    // 【礼物名称】
    @objc func insertAction() {
        // 获得光标所在的位置
        if let content = self.textView.text {
            
            
            
            let location = self.textView.selectedRange.location
            var count =  content.count - location
            if count < 0 {
                count = 0
            }
            textView.text = "\(content.prefix(location))【礼物名称】\(content.suffix(count))"
        } else {
            textView.text = "【礼物名称】"
        }
        
        if let text = textView.text {
            self.textView.attributedText =  LCTools.checkNickConten(text: text, color: UIColor("#4D4E52"))
        }
    }
    
    @objc func sampleAction() {
        if let text =  ConfigModel.geConfigTemplate(type: replyType) {
            self.textView.text = text
            self.textView.attributedText =  LCTools.checkNickConten(text: text, color: UIColor("#4D4E52"))
        }
    }
    
    private func setupConstraints() {
        containerView.translatesAutoresizingMaskIntoConstraints = false
        stackView.translatesAutoresizingMaskIntoConstraints = false
        confirmButton.translatesAutoresizingMaskIntoConstraints = false
        
        containerView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.equalTo(320)
            make.height.equalTo(340)
        }
        stackView.snp.makeConstraints { make in
            make.bottom.equalTo(buttonStackView.snp.top).offset(-18)
            make.top.equalToSuperview().inset(20)
            make.leading.trailing.equalToSuperview().inset(24)
        }
    }
    
    // MARK: - Public Methods
    func show(title: String, in view: UIView, isGift: Bool = false, text: String? = nil, replyType: ReplyMsgType = ReplyMsgType.attention) {
        self.replyType = replyType
        titleLabel.text = title
        self.isGift = isGift
        self.insertButton.isHidden = !isGift
        
        self.sampleButton.isHidden = replyType == .keyword
        
        self.textView.text = text
        if let text = text {
            self.textView.attributedText =  LCTools.checkNickConten(text: text, color: UIColor("#4D4E52"))
        }
        
        
        view.addSubview(self)
        self.frame = view.bounds
        
        // 添加显示动画
        containerView.transform = CGAffineTransform(scaleX: 0.1, y: 0.1)
        UIView.animate(withDuration: 0.3, delay: 0, options: .curveEaseOut) {
            self.containerView.transform = .identity
        }
    }
    
    @objc private func dismissAlert() {
        UIView.animate(withDuration: 0.2, animations: {
            self.alpha = 0
        }) { _ in
            self.removeFromSuperview()
        }
    }
}

// MARK: - KeyBoard监听
extension ReplyTextPlusView {
    // 在适当的地方（如viewDidLoad）注册通知
    func setupKeyboardObservers() {
        // 监听键盘显示
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(keyboardWillShow),
            name: UIResponder.keyboardWillShowNotification,
            object: nil
        )
        
        // 监听键盘隐藏
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(keyboardWillHide),
            name: UIResponder.keyboardWillHideNotification,
            object: nil
        )
    }

    // 键盘显示时调用
    @objc func keyboardWillShow(_ notification: Notification) {
        // 获取键盘高度
        if let keyboardFrame = notification.userInfo?[UIResponder.keyboardFrameEndUserInfoKey] as? CGRect {
            let keyboardHeight = keyboardFrame.height
            // 获取动画时间
            let duration = notification.userInfo?[UIResponder.keyboardAnimationDurationUserInfoKey] as? Double ?? 0.25
            containerView.snp.remakeConstraints { make in
                make.centerX.equalToSuperview()
                make.bottom.equalToSuperview().inset(keyboardHeight + 16)
                make.width.equalTo(320)
                make.height.equalTo(340)
            }
            UIView.animate(withDuration: duration) {
                self.layoutIfNeeded()
            } completion: { _ in
                
            }
        }
    }

    // 键盘隐藏时调用
    @objc func keyboardWillHide(_ notification: Notification) {
        // 获取动画时间
        let duration = notification.userInfo?[UIResponder.keyboardAnimationDurationUserInfoKey] as? Double ?? 0.35
        let keyboardFrame = notification.userInfo?[UIResponder.keyboardFrameEndUserInfoKey] as? CGRect
        
        containerView.snp.remakeConstraints { make in
            make.center.equalToSuperview()
            make.width.equalTo(320)
            make.height.equalTo(340)
        }
        UIView.animate(withDuration: duration) {
            self.layoutIfNeeded()
        } completion: { _ in
            
        }
    }

}


extension ReplyTextPlusView: QMUITextViewDelegate {
   
    func textView(_ textView: UITextView, shouldChangeTextIn range: NSRange, replacementText text: String) -> Bool {
//        // 遍历所有不可编辑区域，检查编辑范围是否与它们有交集
//        for nonEditableRange in nonEditableRanges {
//            if NSIntersectionRange(range, nonEditableRange).length > 0 {
//                return false // 禁止编辑不可编辑区域
//            }
//        }
        
        if text == "\n" {
            textView.resignFirstResponder()
            return false
        }
        
        print("输入：\(text)")
        return true

    }
   
    
    func textViewDidChange(_ textView: UITextView) {
        guard var text = textView.text else { return  }
        updateNonEditableRanges()
    }
    
    // 更新不可编辑区域的范围和样式
    func updateNonEditableRanges() {
        guard let text = textView.text else { return }
        let pattern = "【.*?】" // 非贪婪匹配，确保匹配独立的【】块
        guard let regex = try? NSRegularExpression(pattern: pattern) else { return }
        
        // 获取所有匹配的不可编辑范围
        nonEditableRanges = regex.matches(in: text, range: NSRange(text.startIndex..., in: text))
            .map { $0.range }
        
        // 保留光标位置，避免刷新样式后光标跳动
        let selectedRange = textView.selectedRange
        self.textView.attributedText = LCTools.checkNickConten(text: text, color: UIColor("#4D4E52"))
        textView.selectedRange = selectedRange
    }

}
