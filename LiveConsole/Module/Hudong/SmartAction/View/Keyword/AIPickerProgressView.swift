//
//  AIPickerProgressView.swift
//  LivePlus
//
//  Created by 郭炜 on 2025/2/7.
//

import UIKit
//import QMUIKit

class AIPickerProgressView: CenterAlertView {
    
    var progress: Float = 0.0 {
        didSet {
            self.progressView.setProgress(progress, animated: true)
            self.progressLabel.text = "\(Int(progress * 100))%"
        }
    }
    
    lazy var progressView: UIProgressView = {
        let view = UIProgressView()
//        view.shape = .ring
//        view.lineWidth = 4
        view.backgroundColor = UIColor("#C1C1C1").withAlphaComponent(0.3)
        view.tintColor = UIColor("#6763F6")
        return view
    }()
    
    lazy var progressLabel: UILabel = {
        let label = UILabel()
        label.textAlignment = .center
        label.textColor = UIColor("#6763F6")
        label.font = .systemFont(ofSize: 18)
        return label
    }()
    
    lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.text = "生成中"
        label.textColor = UIColor("#4D4E52")
        label.textAlignment = .center
        label.font = .systemFont(ofSize: 18)
        return label
    }()
    
    var timer: Timer?
    
    var timeInterVar: TimeInterval = 0.2

    override init(frame: CGRect) {
        super.init(frame: frame)
    }
    
    convenience init() {
        self.init(frame: .zero)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func makeUI() {
        super.makeUI()
        
        contentSize = CGSize(width: 218, height: 235)
        contentView.addSubviews([progressView, progressLabel, titleLabel])
        progressView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.width.height.equalTo(110)
            make.top.equalToSuperview().inset(38)
        }
        progressLabel.snp.makeConstraints { make in
            make.center.equalTo(progressView.snp.center)
        }
        titleLabel.snp.makeConstraints { make in
            make.bottom.equalToSuperview().inset(40)
            make.centerX.equalToSuperview()
        }
    }
    
    override func business() {
        super.business()
    }

    override func dismiss() {
        self.progress = 0
        self.stopTimer()
        super.dismiss()
    }
    
    override func show() {
        super.show()
        self.startTimer()
    }
    
    func startTimer() {
        self.stopTimer()
        timer = Timer.scheduledTimer(withTimeInterval: timeInterVar, repeats: true, block: { [weak self] _ in
            guard let self = self else { return }
            let progress = self.progress + 0.01
            self.progress = min(0.98, progress)
        })
    }
    
    func stopTimer() {
        timer?.invalidate()
        timer = nil
    }
}


class CenterAlertView: UIView {
    
    lazy var background: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor("#000000").withAlphaComponent(0.6)
        return view
    }()
    
    lazy var contentView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        view.cornerRadius = 20
        return view
    }()
    
    var contentSize: CGSize = .zero {
        didSet {
            contentView.snp.remakeConstraints { make in
                make.center.equalToSuperview()
                make.width.equalTo(contentSize.width)
                make.height.equalTo(contentSize.height)
            }
        }
    }
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        
        makeUI()
        business()
    }
    
    convenience init() {
        self.init(frame: .zero)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func makeUI() {
        
        addSubview(background)
        background.addSubview(contentView)
        background.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        contentView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.equalTo(318)
            make.height.equalTo(378)
        }
    }
    
    func business() {
        
        self.isHidden = true
        
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(tapGestrueAction))
        tapGesture.delegate = self
//        self.addGestureRecognizer(tapGesture)
    }
    
    func show() {
        DispatchQueue.main.async {
            self.isHidden = false
        }
    }
    
    func dismiss() {
        DispatchQueue.main.async {
            self.isHidden = true
        }
    }
    
    @objc func tapGestrueAction() {
        self.dismiss()
    }
}

extension CenterAlertView: UIGestureRecognizerDelegate {
    func gestureRecognizer(_ gestureRecognizer: UIGestureRecognizer, shouldReceive touch: UITouch) -> Bool {
        let touchLocation = touch.location(in: self)
        if self.contentView.frame.contains(touchLocation) {
            return false
        }
        return true
    }
}
