//
//  CloneStepView.swift
//  LiveConsole
//
//  Created by simon on 21.5.25.
//


class CloneStepView: UIView {
    
    lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.text = ""
        label.textColor = .white
        label.font = .systemFont(ofSize: 14, weight: .regular)
        return label
    }()
    
    lazy var iconView: UIImageView = {
        let button = UIImageView()
        return button
    }()


    override init(frame: CGRect) {
        super.init(frame: frame)
        makeUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func makeUI() {
        
        addSubviews([titleLabel, iconView])
        
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.leading.equalToSuperview().inset(44)
            make.height.equalTo(24)
        }
        
        iconView.snp.makeConstraints { make in
            make.leading.equalToSuperview().inset(18)
            make.top.equalToSuperview()
            make.height.width.equalTo(24)
        }
        
    }
    
   

}
