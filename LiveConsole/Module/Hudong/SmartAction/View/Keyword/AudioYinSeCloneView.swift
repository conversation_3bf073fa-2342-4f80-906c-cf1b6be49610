//
//  AudioYinSeCloneView.swift
//  LivePlus
//
//  Created by 郭炜 on 2025/1/16.
//

import UIKit
import JXSegmentedView
import QMUIKit

// MARK: - 克隆预设
protocol AudioYinSeCloneViewDelegate: NSObjectProtocol {
    func actionForYinseCloneSelection(model: AudioUserSpeakModel?)
    
    func actionForRetryRequestSpk()
    
}

class AudioYinSeCloneView: SmartBaseView {
    
    weak var delegate: AudioYinSeCloneViewDelegate?
    
    lazy var addButton: QMUIButton = {
        let button = QMUIButton()
        button.setTitle("新建", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.setImage(UIImage(named: "ic_clone_add"), for: .normal)
        button.titleLabel?.font = .systemFont(ofSize: 12)
        button.imagePosition = .left
        button.spacingBetweenImageAndTitle = 6
        button.backgroundColor = UIColor("#444444")
        button.cornerRadius = 20
        return button
    }()

    // 当前播放的
    var playingModel: AudioUserSpeakModel?
    
    public var cellColor: String = "#282828"
    
    public var itemWidth = (LCDevice.screenW - 16 * 3) / 2.0 - 1
    lazy var collectionView: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        layout.minimumLineSpacing = 16
        layout.minimumInteritemSpacing = 12
        layout.itemSize = CGSize(width: itemWidth, height: 40)
        layout.scrollDirection = .vertical
        let collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        collectionView.contentInset = UIEdgeInsets(top: 20, left: 16, bottom: 20, right: 16)
        collectionView.register(cellWithClass: TTSPresetItem.self)
        collectionView.delegate = self
        collectionView.dataSource = self
        collectionView.showsVerticalScrollIndicator = false
        collectionView.backgroundColor = UIColor("#111111")
        return collectionView
    }()
    
    lazy var emptyLable: UILabel = {
        let label = UILabel()
        label.text = "暂无克隆音色"
        label.textColor = UIColor("#ACACAC")
        label.font = .systemFont(ofSize: 14)
        label.textAlignment = .center
        return label
    }()
    
    var dataSource: [AudioUserSpeakModel] = []
    var selectionSpeakId: String?

    override init(frame: CGRect) {
        super.init(frame: frame)
    }
    
    convenience init() {
        self.init(frame: .zero)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func makeUI() {
        super.makeUI()
        
        backgroundColor = UIColor("#111111")

        addSubviews([collectionView,addButton , emptyLable])
        
      
        collectionView.snp.makeConstraints { make in
            make.top.leading.trailing.bottom.equalToSuperview()
        }
        
        addButton.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview()
            make.width.equalTo(100)
            make.height.equalTo(40)
        }
        
        emptyLable.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.bottom.equalTo(addButton.snp.top).offset(-16)
        }
        
    }
    
    override func business() {
        super.business()
        
    }

}

extension AudioYinSeCloneView {
    func bind(to models: [AudioUserSpeakModel], selectionSpeakId: String) {
        self.dataSource = models
        self.selectionSpeakId = selectionSpeakId
        self.collectionView.reloadData()
        
        self.emptyLable.isHidden = !models.isEmpty
        self.addButton.isHidden = !models.isEmpty
        self.collectionView.isHidden = models.isEmpty
    }
    
    func updateSelected( selectionSpeakId: String) {
        self.selectionSpeakId = selectionSpeakId
        self.collectionView.reloadData()
    }
    
    func reloadView() {
        self.emptyLable.isHidden = !dataSource.isEmpty
        self.addButton.isHidden = !dataSource.isEmpty
        self.collectionView.isHidden = dataSource.isEmpty
    }
}

extension AudioYinSeCloneView: UICollectionViewDelegate, UICollectionViewDataSource,UICollectionViewDelegateFlowLayout {
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        return CGSize(width: itemWidth, height: 44)
    }
    
    
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return dataSource.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withClass: TTSPresetItem.self, for: indexPath)
        let model = dataSource[indexPath.row]
        cell.bindClone(to: model, selected: model.spkId == selectionSpeakId, color: cellColor, playing: self.playingModel?.spkId == model.spkId)
        
        cell.playPauseAction = { [weak self] in
            guard let self = self else { return }
            AudioPlayManager.shared.stop()
            AVPlayManager.shared.delegate = self
            
            if let playingModel = self.playingModel {
                // 如果已经有播放的
                if model.spkId == playingModel.spkId {
                    // 并且是相同的 那么暂停
                    AVPlayManager.shared.stop()
                    self.playingModel = nil
                } else {
                    // 不相同 先暂停再切换
                    AVPlayManager.shared.stop()
                    if let url = URL(string: model.previewUrl) {
                        AVPlayManager.shared.videoPlayer(url: url)
                        self.playingModel = model
                    }
                }
            } else {
                // 没有播放的
                AVPlayManager.shared.stop()
                if let url = URL(string: model.previewUrl) {
                    AVPlayManager.shared.videoPlayer(url: url)
                    self.playingModel = model
                }
            }
            self.collectionView.reloadData()
        }
        
        
        cell.retryAction  = { [weak self] in
            guard let self = self else { return }
            self.delegate?.actionForRetryRequestSpk()
        }
        
        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        let model = dataSource[indexPath.row]
        self.selectionSpeakId = model.spkId
        self.delegate?.actionForYinseCloneSelection(model: model)
        collectionView.reloadData()
    }
}

// MARK: - JXSegmentedListContainerViewListDelegate
extension AudioYinSeCloneView: JXSegmentedListContainerViewListDelegate {
    /// 返回当前视图作为列表内容
    func listView() -> UIView {
        return self
    }
}


extension AudioYinSeCloneView: AVPlayManagerDelegate {
    func avPlayManager(_ manager: AVPlayManager, didFinishPlaying successfully: Bool) {
        self.playingModel = nil
        self.collectionView.reloadData()
    }
    
}
