//
//  KeywordListView.swift
//  LivePlus
//
//  Created by 郭炜 on 2025/1/13.
//

import UIKit

// MARK: - 关键词列表
class KeywordListView: SmartBaseView {
    
    lazy var background: UIView = {
        let view = UIView()
        return view
    }()
    
    lazy var tipImage: UIImageView = {
        let imageView = UIImageView(image: UIImage(named: "ic_smart_tip"))
        imageView.isHidden = true
        return imageView
    }()
    
    lazy var tipLabel: UILabel = {
        let label = UILabel()
        label.text = "触发关键词"
        label.textColor = .white
        label.font = LCDevice.DIN_Font_PF_M(16)
        return label
    }()
    
    lazy var questionButton: UIButton = {
        let button = UIButton()
        button.setImage(UIImage(named: "ic_smart_question"), for: .normal)
        return button
    }()
    
    lazy var tableBackground: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor("#282828")
        view.cornerRadius = 12
        return view
    }()
    
    lazy var tableView: UITableView = {
        let tableView = UITableView()
        tableView.delegate = self
        tableView.dataSource = self
        tableView.separatorStyle = .none
        tableView.backgroundColor = .clear
        tableView.register(cellWithClass: SmartKeywordItem.self)
        return tableView
    }()
    
    weak var model: AiKeywordVoiceReplyItemModel?

    override init(frame: CGRect) {
        super.init(frame: frame)
    }
    
    convenience init() {
        self.init(frame: .zero)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func makeUI() {
        super.makeUI()
        
        addSubviews([background])
        background.addSubviews([tipImage, tipLabel, questionButton, tableBackground])
        tableBackground.addSubview(tableView)
        
        // 12 + 16 + 40 * 5 + 16
        background.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview().inset(16)
            make.top.bottom.equalToSuperview()
        }
        tipImage.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(6)
            make.leading.equalToSuperview()
            make.size.equalTo(16)
        }
        tipLabel.snp.makeConstraints { make in
            make.leading.equalToSuperview()
            make.centerY.equalTo(tipImage.snp.centerY)
        }
        questionButton.snp.makeConstraints { make in
            make.leading.equalTo(tipLabel.snp.trailing).offset(4)
            make.centerY.equalTo(tipLabel.snp.centerY)
        }
        tableBackground.snp.makeConstraints { make in
            make.leading.trailing.bottom.equalToSuperview()
            make.top.equalTo(tipImage.snp.bottom).offset(20)
        }
        tableView.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview()
            make.top.bottom.equalToSuperview().inset(8)
        }
    }
    
    override func business() {
        super.business()
    }

}

extension KeywordListView {
    
    func bind(to model: AiKeywordVoiceReplyItemModel) {
        self.model = model
        self.tableView.reloadData()
    }
}

extension KeywordListView: UITableViewDelegate, UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return 5
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        guard let model = model else { return UITableViewCell() }
        let cell = tableView.dequeueReusableCell(withClass: SmartKeywordItem.self, for: indexPath)
        cell.bind(to: model.keywords?[safe: indexPath.row], index: indexPath.row, model: model)
        return cell
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 34
    }
}
