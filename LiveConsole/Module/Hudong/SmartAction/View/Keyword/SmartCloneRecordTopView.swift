//
//  SmartCloneRecordTopView.swift
//  LivePlus
//
//  Created by 郭炜 on 2025/2/10.
//

import UIKit
import QMUIKit

// MARK: - 现在录音 - 提示文案
class SmartCloneRecordTopView: SmartBaseView {
    
    lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.text = "请朗读以下例句"
        label.textColor = .white.alpha(value: 0.6)
        label.font = .systemFont(ofSize: 14)
        return label
    }()
    
    lazy var textBackground: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor("#282828")
        view.cornerRadius = 6
        return view
    }()
    
    lazy var textView: QMUITextView = {
        let textView = QMUITextView()
        textView.textColor = .white
        textView.font = .systemFont(ofSize: 20)
        textView.placeholder = "请输入文案"
        textView.placeholderColor = UIColor("#ACACAC")
        textView.isEditable = false
        textView.backgroundColor = .clear
        return textView
    }()
    
    
    lazy var iconImage: UIImageView = {
        let imageView = UIImageView(image: UIImage(named: "ic_clone_tip"))
        return imageView
    }()
    
    lazy var huanButton: UIButton = {
        let button = UIButton()
        button.setTitle("换一句", for: .normal)
        button.setTitleColor(UIColor.white.alpha(value: 0.8), for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 14)
        button.setImage(UIImage(named: "ic_clone_换"), for: .normal)
        button.imagePosition(style: .left, spacing: 3)
        return button
    }()
    
    
    lazy var tipLabel: UILabel = {
        let label = UILabel()
        label.text = "1. 请您务必确保在使用我们的服务时遵守所有适用的法律法规、行政规章、国家政策以及社会公德和伦理道德。\n2. 录制时的语气和情感也会被克隆，请按期望的音色效果进行朗读"
        label.textColor = UIColor("#ACACAC")
        label.font = .systemFont(ofSize: 12)
        label.numberOfLines = 0
        return label
    }()

    override init(frame: CGRect) {
        super.init(frame: frame)
    }
    
    convenience init() {
        self.init(frame: .zero)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func makeUI() {
        super.makeUI()
        
        addSubviews([titleLabel, textBackground, iconImage, tipLabel])
        textBackground.addSubviews([textView, huanButton])
        
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.leading.trailing.equalToSuperview().inset(16)
            make.height.equalTo(22)
        }
        
        textView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(16)
        }
        
        textBackground.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(10)
            make.leading.trailing.equalToSuperview().inset(16)
            make.bottom.equalTo(tipLabel.snp.top).offset(-10)
        }
        
        huanButton.snp.makeConstraints { make in
            make.trailing.bottom.equalToSuperview().inset(8)
            make.width.equalTo(80)
            make.height.equalTo(30)
        }
        
        tipLabel.snp.makeConstraints { make in
            make.leading.equalToSuperview().inset(45)
            make.trailing.equalToSuperview().inset(16)
            make.bottom.equalToSuperview()
            make.height.equalTo(90)
        }
        
        iconImage.snp.makeConstraints { make in
            make.leading.equalToSuperview().inset(16)
            make.top.equalTo(tipLabel.snp.top).offset(6)
            make.size.equalTo(20)
        }
    }
    
    override func business() {
        super.business()
    }

}
