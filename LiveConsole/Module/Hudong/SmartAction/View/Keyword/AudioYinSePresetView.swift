//
//  AudioYinSePresetView.swift
//  LivePlus
//
//  Created by 郭炜 on 2025/1/16.
//

import UIKit
import JXSegmentedView

// MARK: - 音色预设
protocol AudioYinSePresetViewDelegate: NSObjectProtocol {
    func actionForYinsePresetSelection(model: AudioPreSpeakModel)
}

class AudioYinSePresetView: SmartBaseView {
    
    weak var delegate: AudioYinSePresetViewDelegate?
    
    public var cellColor: String = "#282828"
    
    public var itemWidth = (LCDevice.screenW - 16 * 3) / 2.0 - 1
    lazy var collectionView: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        layout.minimumLineSpacing = 16
        layout.minimumInteritemSpacing = 12
        layout.itemSize = CGSize(width: itemWidth, height: 40)
        layout.scrollDirection = .vertical
        let collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        collectionView.contentInset = UIEdgeInsets(top: 20, left: 16, bottom: 20, right: 16)
        collectionView.register(cellWithClass: TTSPresetItem.self)
        collectionView.delegate = self
        collectionView.dataSource = self
        collectionView.showsVerticalScrollIndicator = false
        collectionView.backgroundColor = UIColor("#111111")
        return collectionView
    }()
    
    var dataSource: [AudioPreSpeakModel] = []
    var selectionVoiceType: String?

    // 当前播放的
    var playingModel: AudioPreSpeakModel?
    
    override init(frame: CGRect) {
        super.init(frame: frame)
    }
    
    convenience init() {
        self.init(frame: .zero)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func makeUI() {
        super.makeUI()
        
        backgroundColor = UIColor("#59536C")
        
        addSubview(collectionView)
        collectionView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }
    
    override func business() {
        super.business()
    }

}

extension AudioYinSePresetView {
    func bind(to models: [AudioPreSpeakModel], selectionVoiceType: String) {
        self.dataSource = models
        self.selectionVoiceType = selectionVoiceType
        self.collectionView.reloadData()
    }
    
    func updateSelected( selectionVoiceType: String) {
        self.selectionVoiceType = selectionVoiceType
        self.collectionView.reloadData()
    }
    
}

extension AudioYinSePresetView: UICollectionViewDelegate, UICollectionViewDataSource, UICollectionViewDelegateFlowLayout{
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        return CGSize(width: itemWidth, height: 44)
    }
    
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return dataSource.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withClass: TTSPresetItem.self, for: indexPath)
        let model = dataSource[indexPath.row]
        cell.bind(to: model, selected: model.voiceType == self.selectionVoiceType, playing: self.playingModel?.voiceType == model.voiceType, color: cellColor)
        
        cell.playPauseAction = { [weak self] in
            guard let self = self else { return }
            AudioPlayManager.shared.stop()
            
            AVPlayManager.shared.delegate = self
            
            if let playingModel = self.playingModel {
                // 如果已经有播放的
                if model.voiceType == playingModel.voiceType {
                    // 并且是相同的 那么暂停
                    AVPlayManager.shared.stop()
                    self.playingModel = nil
                } else {
                    // 不相同 先暂停再切换
                    AVPlayManager.shared.stop()
                    if let url = URL(string: model.voiceUrl) {
                        AVPlayManager.shared.videoPlayer(url: url)
                        self.playingModel = model
                    }
                }
            } else {
                // 没有播放的
                AVPlayManager.shared.stop()
                if let url = URL(string: model.voiceUrl) {
                    AVPlayManager.shared.videoPlayer(url: url)
                    self.playingModel = model
                }
            }
            self.collectionView.reloadData()
        }
        
        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        let model = dataSource[indexPath.row]
        self.selectionVoiceType = model.voiceType
        self.delegate?.actionForYinsePresetSelection(model: model)
        self.collectionView.reloadData()
    }
}

// MARK: - JXSegmentedListContainerViewListDelegate
extension AudioYinSePresetView: JXSegmentedListContainerViewListDelegate {
    /// 返回当前视图作为列表内容
    func listView() -> UIView {
        return self
    }
}

extension AudioYinSePresetView: AVPlayManagerDelegate {
    func avPlayManager(_ manager: AVPlayManager, didFinishPlaying successfully: Bool) {
        self.playingModel = nil
        self.collectionView.reloadData()
    }
    
}
