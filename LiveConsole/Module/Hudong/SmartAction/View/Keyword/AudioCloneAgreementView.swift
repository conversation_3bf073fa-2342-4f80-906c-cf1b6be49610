//
//  AudioCloneAgreementView.swift
//  LivePlus
//
//  Created by simon on 10.4.25.
//

import Foundation
import WebKit
// MARK: - 选择预设音色、克隆音色
protocol AudioCloneAgreementViewDelegate: NSObjectProtocol {
    func actionStartClone()
}

class AudioCloneAgreementView: UIView {
        
    let text = "已阅读并同意《“快瓴中控台“AI功能使用规范》"
    
    let h = 420
    
    weak var delegate: AudioCloneAgreementViewDelegate?
    
    lazy var scrollView: UIScrollView = {
        let label = UIScrollView()
        return label
    }()
    
    lazy var contenLab: UILabel = {
        let label = UILabel()
        label.numberOfLines = 0
        return label
    }()
    
    lazy var checkButton: UIButton = {
        let button = UIButton { [weak self] in
            guard let self = self else { return }
            self.checkButton.isSelected = !self.checkButton.isSelected
        }
        button.setImage(UIImage(named: "登录流程_组件_cell_勾选_nor"), for: .normal)
        button.setImage(UIImage(named: "icon_勾选_sel"), for: .selected)
        button.zl_enlargeValidTouchArea(inset: 22)
        return button
    }()
    
    lazy var loginProtocol: YYLabel = {
        let label = YYLabel()
        label.text = text
        return label
    }()
    
    
    // MARK: - UI Components
    private lazy var backgroundView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.black.withAlphaComponent(0.4)
        let tap = UITapGestureRecognizer(target: self, action: #selector(hide))
        view.addGestureRecognizer(tap)
        return view
    }()
    
    private lazy var contentView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.white
        view.layer.cornerRadius = 16
        view.layer.maskedCorners = [.layerMinXMinYCorner, .layerMaxXMinYCorner]
        return view
    }()
    
    lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.textAlignment = .left
        label.textColor = UIColor.black
        label.font = .systemFont(ofSize: 16, weight: .medium)
        label.text = "AI功能提示"
        return label
    }()
    
    lazy var doneButton: UIButton = {
        let button = UIButton(frame: CGRect(x: 0, y: 0, width: 210, height: 46))
        button.setTitle("去克隆", for: .normal)
        button.cornerRadius = 23
        button.addTarget(self, action: #selector(doneAction), for: .touchUpInside)
        button.applyGradient()
        return button
    }()
    
    
    // MARK: - Init
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
        deployProtocol()
    
        let paraph = NSMutableParagraphStyle()
        paraph.lineSpacing = 6
        
        let attributes1 = [NSAttributedString.Key.paragraphStyle: paraph, NSAttributedString.Key.foregroundColor: UIColor("#555555"), NSAttributedString.Key.font: UIFont.systemFont(ofSize: 14, weight: .bold)]
                
        let attributes2 = [NSAttributedString.Key.paragraphStyle: paraph, NSAttributedString.Key.foregroundColor: UIColor("#555555"), NSAttributedString.Key.font: UIFont.systemFont(ofSize: 14, weight: .regular)]
        
        let attributes3 = [NSAttributedString.Key.paragraphStyle: paraph, NSAttributedString.Key.foregroundColor: UIColor("#555555"), NSAttributedString.Key.font: UIFont.systemFont(ofSize: 24, weight: .bold)]
        
        let attributedText1 = NSMutableAttributedString(string: "“系统音色”、“克隆音色”功能使用特别责任与限制： ", attributes: attributes1)
        
        let attributedText2 = NSMutableAttributedString(string: " 用户在使用“系统音色”、“克隆音色”功能时，除遵守本规范其他通用约定外，还应特别承诺并遵守以下规定：\n", attributes: attributes2)
        
        
        let attributedText3 = NSMutableAttributedString(string: "· ", attributes: attributes3)
        
        let attributedText4 = NSMutableAttributedString(string: "内容负责：", attributes: attributes1)
        
        let attributedText5 = NSMutableAttributedString(string: "用户应对其输入的用于生成音频的文本内容独立承担全部责任，并保证其拥有合法、完整、无瑕疵的权利，或已获得必要的授权，确保用户内容不侵犯任何第三方的合法权益（包括但不限于著作权、商标权、名誉权、隐私权等）。\n", attributes: attributes2)
        
        
        let attributedText6 = NSMutableAttributedString(string: "· ", attributes: attributes3)
        
        let attributedText7 = NSMutableAttributedString(string: "禁止行为：", attributes: attributes1)
        
        let attributedText8 = NSMutableAttributedString(string: "用户不得利用“系统音色”、“克隆音色”功能及生成的音频从事以下任何行为：\n", attributes: attributes2)
        
        
        let attributedText9 = NSMutableAttributedString(string: "· ", attributes: attributes3)
        let attributedText10 = NSMutableAttributedString(string: "制作、复制、发布、传播含有反对宪法所确定的基本原则、危害国家安全、泄露国家秘密、颠覆国家政权、破坏国家统一的；\n", attributes: attributes2)
        
        
        let attributedText11 = NSMutableAttributedString(string: "· ", attributes: attributes3)
        let attributedText12 = NSMutableAttributedString(string: "制作、复制、发布、传播含有损害国家荣誉和利益、煽动民族仇恨、民族歧视、破坏民族团结的；\n", attributes: attributes2)
        
        
        let attributedText13 = NSMutableAttributedString(string: "· ", attributes: attributes3)
        let attributedText14 = NSMutableAttributedString(string: "制作、复制、发布、传播含有破坏国家宗教政策、宣扬邪教和封建迷信的；\n", attributes: attributes2)
        
        
        let attributedText15 = NSMutableAttributedString(string: "· ", attributes: attributes3)
        let attributedText16 = NSMutableAttributedString(string: "制作、复制、发布、传播含有散布谣言、扰乱社会秩序、破坏社会稳定的；\n", attributes: attributes2)
        
        let attributedText17 = NSMutableAttributedString(string: "· ", attributes: attributes3)
        let attributedText18 = NSMutableAttributedString(string: "制作、复制、发布、传播含有淫秽、色情、赌博、暴力、凶杀、恐怖或者教唆犯罪的；\n", attributes: attributes2)
        
        let attributedText19 = NSMutableAttributedString(string: "· ", attributes: attributes3)
        let attributedText20 = NSMutableAttributedString(string: " 制作、复制、发布、传播含有侮辱或者诽谤他人、侵害他人名誉权、肖像权、隐私权等合法权益的；\n", attributes: attributes2)
        
        let attributedText21 = NSMutableAttributedString(string: "· ", attributes: attributes3)
        let attributedText22 = NSMutableAttributedString(string: " 冒用他人名义，或以可能引起混淆的方式使用，使他人误认为该生成音频与特定自然人、法人或其他组织存在关联或背书；\n", attributes: attributes2)
        
        
        let attributedText23 = NSMutableAttributedString(string: "· ", attributes: attributes3)
        let attributedText24 = NSMutableAttributedString(string: "用于任何欺诈、误导性宣传或不正当竞争行为；\n", attributes: attributes2)
        
        
        let attributedText25 = NSMutableAttributedString(string: "· ", attributes: attributes3)
        let attributedText26 = NSMutableAttributedString(string: "侵犯任何第三方的知识产权或其他合法权益；\n", attributes: attributes2)
        
        
        let attributedText27 = NSMutableAttributedString(string: "· ", attributes: attributes3)
        let attributedText28 = NSMutableAttributedString(string: "其他任何违反法律法规、平台规则或社会公共利益的行为。\n", attributes: attributes2)
        
        
        
        attributedText1.append(attributedText2)
        attributedText1.append(attributedText3)
        attributedText1.append(attributedText4)
        attributedText1.append(attributedText5)
        attributedText1.append(attributedText6)
        attributedText1.append(attributedText7)
        attributedText1.append(attributedText8)
        attributedText1.append(attributedText9)
        attributedText1.append(attributedText10)
        
        attributedText1.append(attributedText11)
        attributedText1.append(attributedText12)
        attributedText1.append(attributedText13)
        attributedText1.append(attributedText14)
        attributedText1.append(attributedText15)
        attributedText1.append(attributedText16)
        attributedText1.append(attributedText17)
        attributedText1.append(attributedText18)
        attributedText1.append(attributedText19)
        attributedText1.append(attributedText20)
        
        
        attributedText1.append(attributedText21)
        attributedText1.append(attributedText22)
        attributedText1.append(attributedText23)
        attributedText1.append(attributedText24)
        attributedText1.append(attributedText25)
        attributedText1.append(attributedText26)
        attributedText1.append(attributedText27)
        attributedText1.append(attributedText28)
        
        self.contenLab.attributedText = attributedText1
    }
    
    @objc func doneAction() {
        if !checkButton.isSelected  {
            HUD.showFail("请先同意《“快瓴中控台“AI功能使用规范》")
            return
        }
        self.delegate?.actionStartClone()
        hide()
    }
    
    override func layoutSubviews() {
        super.layoutSubviews()
       
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Setup
    private func setupUI() {
        backgroundColor = .clear
        
        addSubview(backgroundView)
        addSubviews([contentView])
        
        contentView.addSubviews([ titleLabel, scrollView, checkButton,loginProtocol, doneButton])
       
        scrollView.addSubviews([contenLab])
        
        scrollView.contentSize = CGSize(width: LCDevice.screenW - 24, height: 900)
        backgroundView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        contentView.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview()
            make.bottom.equalToSuperview().offset(h) // 初始位置在屏幕下方
            make.height.equalTo(h)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.leading.equalToSuperview().inset(24)
            make.height.equalTo(22)
        }
        
        scrollView.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(53)
            make.bottom.equalToSuperview().inset(124)
            make.leading.equalToSuperview().inset(12)
            make.width.equalTo(LCDevice.screenW - 24)
        }
        
        
        contenLab.snp.makeConstraints { make in
            make.top.leading.equalToSuperview()
            make.width.equalTo(LCDevice.screenW - 24)
            make.height.equalTo(900)
        }
        
        checkButton.snp.makeConstraints { make in
            make.leading.equalToSuperview().inset(30)
            make.top.equalTo(scrollView.snp.bottom).offset(12)
            make.size.equalTo(22)
        }
        
        loginProtocol.snp.makeConstraints { make in
            make.leading.equalTo(checkButton.snp.trailing).offset(4)
            make.trailing.equalToSuperview().inset(40)
            make.top.equalTo(scrollView.snp.bottom).offset(12)
            make.height.equalTo(22)
        }
        
        
        doneButton.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.bottom.equalToSuperview().inset(40)
            make.height.equalTo(46)
            make.leading.trailing.equalToSuperview().inset(24)
        }
     
    }
    
    
    // MARK: - Public Methods
    func show() {
        // 确保视图在window上
        let vc = AppDelegate.curDisplayVC()
        
        vc.view.addSubview(self)
        self.frame = vc.view.bounds
        
        // 显示动画
        self.backgroundView.alpha = 0
        self.contentView.snp.updateConstraints { make in
            make.bottom.equalToSuperview().offset(h)
        }
        self.layoutIfNeeded()
        
        UIView.animate(withDuration: 0.3) {
            self.backgroundView.alpha = 1
            self.contentView.snp.updateConstraints { make in
                make.bottom.equalToSuperview()
            }
            self.layoutIfNeeded()
        }
        
    }
    
    @objc func hide() {
        UIView.animate(withDuration: 0.3, animations: {
            self.backgroundView.alpha = 0
            self.contentView.snp.updateConstraints { make in
                make.bottom.equalToSuperview().offset(self.h)
            }
            self.layoutIfNeeded()
        }) { _ in
            self.removeFromSuperview()
        }
    }
}

extension AudioCloneAgreementView {
    func deployProtocol() {
        let infoString = text
        let agreementFS = "《“快瓴中控台“AI功能使用规范》"
        let agreementRange = infoString.range(of: agreementFS)!
        let agreementLinkRange = NSRange(agreementRange, in: infoString)
                
         
        let attString = NSMutableAttributedString.init(string: infoString)
        attString.font = LCDevice.DIN_Font_PF_R(12)
        attString.alignment = .left
        attString.color = UIColor("#898B8F")
        attString.setTextHighlight(agreementLinkRange, color: UIColor("#6863F7"), backgroundColor: nil) { (_ containerView, _ attText, _ range, _ rect) in
            Router.openAudioCloneAgreement()
        }
        
        loginProtocol.attributedText = attString
    }
}
