//
//  SmartTextItem.swift
//  LiveConsole
//
//  Created by 郭炜 on 2025/4/23.
//

import UIKit
import QMUIKit

class SmartTextItem: UITableViewCell {
        
    lazy var background: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor("#282828")
        view.cornerRadius = 6
        view.borderWidth = 2
        view.borderColor = UIColor("#ADAAFF")
        return view
    }()
    
    lazy var itemImage: UIImageView = {
        let imageView = UIImageView(image: UIImage(named: "ic_text_icon"))
        return imageView
    }()
    
    lazy var itemTitle: UILabel = {
        let label = UILabel()
        label.textColor = .white
        label.font = .systemFont(ofSize: 14)
        label.numberOfLines = 3
        return label
    }()
    
    lazy var itemDragButton: UIButton = {
        let button = UIButton()
        button.setImage(UIImage(named: "ic_smart_audio_list_drag"), for: .normal)
        button.isUserInteractionEnabled = false
        return button
    }()
    
    override func awakeFromNib() {
        super.awakeFromNib()
    }
    
    override func setSelected(_ selected: Bool, animated: Bool) {
        super.setSelected(selected, animated: animated)
    }
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        makeUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func makeUI() {
        
        selectionStyle = .none
        backgroundColor = .clear
        contentView.addSubview(background)
        background.addSubviews([itemImage, itemTitle, itemDragButton])
        
        background.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.leading.trailing.equalToSuperview().inset(16)
        }
        itemImage.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.leading.equalToSuperview().inset(20)
            make.size.equalTo(20)
        }
        itemTitle.snp.makeConstraints { make in
            make.leading.equalTo(itemImage.snp.trailing).offset(8)
            make.centerY.equalToSuperview()
            make.top.bottom.equalToSuperview().inset(14)
            make.trailing.equalTo(itemDragButton.snp.leading).offset(-16)
        }
        itemDragButton.snp.makeConstraints { make in
            make.trailing.equalToSuperview().inset(10)
            make.centerY.equalToSuperview()
            make.size.equalTo(28)
        }
    }

}

extension SmartTextItem {
    
    func bind(to model: ResFileModel, isSelected: Bool) {
        itemTitle.text = model.text ?? ""
        itemTitle.attributedText = LCTools.checkNickConten(text: model.text ?? "", color: .white)
        self.background.borderWidth = isSelected ? 2.0 : 0.0
    }
}
