//
//  SmartTTSAudioItem.swift
//  LivePlus
//
//  Created by 郭炜 on 2025/1/16.
//

import UIKit

class SmartTTSAudioItem: UITableViewCell {
    
    var playPauseAction: (() -> Void)?
    var selectedAction: (() -> Void)?
    var rateAction: (() -> Void)?

    lazy var background: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor("#352E4D")
        view.cornerRadius = 6
        return view
    }()
    
    lazy var progressView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor("#ADAAFF")
        return view
    }()
        
    lazy var itemImage: UIImageView = {
        let imageView = UIImageView(image: UIImage(named: "ic_smart_audio_icon"))
        return imageView
    }()
    
    lazy var itemTitle: UILabel = {
        let label = UILabel()
        label.textColor = .white
        label.font = .systemFont(ofSize: 14)
        return label
    }()
    
    lazy var itemDuration: UILabel = {
        let label = UILabel()
        label.textColor = UIColor("#ACACAC")
        label.font = .systemFont(ofSize: 14)
        return label
    }()
    
    lazy var itemRateButton: UIButton = {
        let button = UIButton()
        button.backgroundColor = UIColor("#59536C")
        button.setTitleColor(.white, for: .normal)
        button.titleLabel?.font = .systemFont(ofSize: 12, weight: .medium)
        button.cornerRadius = 12
        button.zl_enlargeValidTouchArea(inset: 14)
//        button.isUserInteractionEnabled = false
        return button
    }()
    
    lazy var itemPlayPauseButton: UIButton = {
        let button = UIButton()
        button.setImage(UIImage(named: "ic_smart_record_resume"), for: .normal)
        button.setImage(UIImage(named: "ic_smart_record_pause"), for: .selected)
        button.zl_enlargeValidTouchArea(inset: 14)
        return button
    }()
    
    lazy var selectedButton: UIButton = {
        let button = UIButton()
        button.setImage(UIImage(named: "ic_smart_tts_unsel"), for: .normal)
        button.setImage(UIImage(named: "ic_smart_tts_sel"), for: .selected)
        button.zl_enlargeValidTouchArea(inset: 14)
        return button
    }()
    
    var progress: Float = 0.0
    
    // 等待合成的画面
    
    
    override func awakeFromNib() {
        super.awakeFromNib()
    }
    
    override func setSelected(_ selected: Bool, animated: Bool) {
        super.setSelected(selected, animated: animated)
    }
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        makeUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func makeUI() {
        selectionStyle = .none
        backgroundColor = .clear
        contentView.addSubview(background)
        background.addSubviews([itemImage, itemTitle, itemDuration, itemRateButton, itemPlayPauseButton, selectedButton, progressView])
        
        background.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.leading.trailing.equalToSuperview().inset(16)
            make.height.equalTo(50)
        }
        itemImage.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.leading.equalToSuperview().inset(20)
            make.size.equalTo(20)
        }
        itemTitle.snp.makeConstraints { make in
            make.leading.equalTo(itemImage.snp.trailing).offset(10)
            make.centerY.equalToSuperview()
        }
        itemDuration.snp.makeConstraints { make in
            make.leading.equalTo(itemTitle.snp.trailing).offset(10)
            make.centerY.equalToSuperview()
        }
        selectedButton.snp.makeConstraints { make in
            make.trailing.equalToSuperview().inset(10)
            make.centerY.equalToSuperview()
            make.size.equalTo(32)
        }
        itemPlayPauseButton.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.trailing.equalTo(selectedButton.snp.leading).offset(-15)
            make.size.equalTo(28)
        }
        itemRateButton.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.trailing.equalTo(itemPlayPauseButton.snp.leading).offset(-15)
            make.height.equalTo(24)
            make.width.equalTo(46)
        }
        progressView.snp.makeConstraints { make in
            make.bottom.leading.equalToSuperview()
            make.height.equalTo(2.5)
            make.width.equalToSuperview().multipliedBy(self.progress)
        }
        
        itemPlayPauseButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self else { return }
                self.playPauseAction?()
            }.disposed(by: rx.disposeBag)
        selectedButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self else { return }
                self.selectedAction?()
            }.disposed(by: rx.disposeBag)
        itemRateButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self else { return }
                self.rateAction?()
            }.disposed(by: rx.disposeBag)
    }

    func updateProgress(value: Float) {
        self.progress = value
        progressView.snp.remakeConstraints { make in
            make.bottom.leading.equalToSuperview()
            make.height.equalTo(2.5)
            make.width.equalToSuperview().multipliedBy(value)
        }
        UIView.animate(withDuration: 0.1) {
            self.layoutIfNeeded()
        }
    }
}

extension SmartTTSAudioItem {
    
    func bind(to model: AIAudioResultModel, isPlaying: Bool) {
        itemTitle.text = model.title
        itemDuration.text = "\(model.duration)″"
        itemRateButton.setTitle("\(model.speed)X", for: .normal)
        itemPlayPauseButton.isSelected = isPlaying
        selectedButton.isSelected = model.selected
        
        if !isPlaying {
            progressView.snp.remakeConstraints { make in
                make.bottom.leading.equalToSuperview()
                make.height.equalTo(2.5)
                make.width.equalTo(0)
            }
        } else {
            progressView.snp.remakeConstraints { make in
                make.bottom.leading.equalToSuperview()
                make.height.equalTo(2.5)
                make.width.equalToSuperview().multipliedBy(self.progress)
            }
            self.layoutIfNeeded()
        }
    }
}
