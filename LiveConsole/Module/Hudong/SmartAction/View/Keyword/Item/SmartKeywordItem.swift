//
//  SmartKeywordItem.swift
//  LivePlus
//
//  Created by 郭炜 on 2025/1/13.
//

import UIKit
import QMUIKit

// MARK: - 关键词的item
class SmartKeywordItem: UITableViewCell {
    
    lazy var indexLabel: UILabel = {
        let label = UILabel()
        label.textColor = UIColor("#ADAAFF")
        label.font = LCDevice.DIN_Font_PF_R(14)
        label.textAlignment = .right
        return label
    }()
    
    lazy var textField: QMUITextField = {
        let textField = QMUITextField()
        textField.placeholder = "请输入关键词"
        textField.placeholderColor = UIColor("#555555")
        textField.textColor = .white
        textField.font = LCDevice.DIN_Font_PF_M(14)
        textField.tintColor = UIColor("#ADAAFF")
        textField.maximumTextLength = 8
        textField.delegate = self
        textField.returnKeyType = .done
        return textField
    }()

    override func awakeFromNib() {
        super.awakeFromNib()
    }
    
    override func setSelected(_ selected: <PERSON><PERSON>, animated: Bool) {
        super.setSelected(selected, animated: animated)
    }
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        makeUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    weak var model: AiKeywordVoiceReplyItemModel?
    
    var index: Int  = 0
    
    func makeUI() {
        selectionStyle = .none
        backgroundColor = .clear
        contentView.addSubviews([indexLabel, textField])
        
        indexLabel.snp.makeConstraints { make in
            make.top.bottom.equalToSuperview()
            make.leading.equalToSuperview().inset(8)
            make.width.equalTo(20)
        }
        textField.snp.makeConstraints { make in
            make.top.bottom.equalToSuperview()
            make.leading.equalTo(indexLabel.snp.trailing).offset(4)
            make.trailing.equalToSuperview().inset(16)
        }
    }

}

extension SmartKeywordItem: QMUITextFieldDelegate {
    func bind(to text: String?, index: Int, model: AiKeywordVoiceReplyItemModel) {
        self.model = model
        self.index = index

        self.textField.text = text
        self.indexLabel.text = "\(index + 1)."
        textField.textColor = .white
    }
    
//    private func textField(_ textField: UITextField!, didPreventTextChangeIn range: NSRange, replacementString: String!) {
//        HUD.showFail("每个关键词最多包含8个字\n（每个汉字、英文字母或数字都算1个字）")
//    }

    func textField(_ textField: QMUITextField!, didPreventTextChangeIn range: NSRange, replacementString: String!) {
        HUD.showFail("每个关键词最多包含8个字\n（每个汉字、英文字母或数字都算1个字）")
    }
    
    func textFieldDidBeginEditing(_ textField: UITextField) {
        textField.textColor = .white
    }
    
    func textFieldShouldReturn(_ textField: UITextField) -> Bool {
        textField.resignFirstResponder()
        return true
    }
    
    func textFieldDidEndEditing(_ textField: UITextField) {
        guard let model = self.model else { return }
        // 关键词是否可用
        guard var text = textField.text else { return  }
        text = text.replacingOccurrences(of: " ", with: "")
        
        if text.isEmpty {
            return
        }
        
      
        // 查找其他卡片
        if AIReplyDataManager.shared.keywordExist(keyword: text, model: model) || isExist(text: text) {
           
            textField.textColor = UIColor("#F03F71")
            HUD.showFail("关键词已被其他回复使用")
            return
        }
        
        model.keywords?[index] = textField.text ?? ""
        model.modifyUpdateTime()
        
        AIReplyDataManager.shared.addNewCardItem(replyItem: model)
    }
    
    // 当前卡片是否有
    func isExist(text: String) -> Bool {
        guard let model = self.model, let keywords = model.keywords else { return false}
        
        for (idx, key) in keywords.enumerated() {
            if idx != index, key == text {
                return true
            }
        }
        return false
    }
    
}
