//
//  TTSPresetItem.swift
//  LivePlus
//
//  Created by 郭炜 on 2025/2/7.
//

import UIKit
import FLAnimatedImage

// MARK: - 预设音色的item
class TTSPresetItem: UICollectionViewCell {
    
    var playPauseAction: (() -> Void)?
    
    var retryAction: (() -> Void)?
    
    lazy var background: UIView = {
        let view = UIView()
        view.cornerRadius = 6
        view.backgroundColor = UIColor("#282828")
        view.borderColor = UIColor("#ADAAFF")
        return view
    }()
    
    lazy var playButton: UIButton = {
        let button = UIButton()
        button.setImage(UIImage(named: "ic_yinse_播放"), for: .normal)
        button.setImage(UIImage(named: "ic_yinse_暂停"), for: .selected)
        button.zl_enlargeValidTouchArea(inset: 10)
        return button
    }()
    
    public lazy var imagePlayer: FLAnimatedImageView = {
        let imgView = FLAnimatedImageView(frame: bounds)
        imgView.contentMode = .scaleAspectFit
        imgView.autoresizingMask = [.flexibleWidth, .flexibleHeight]
        return imgView
    }()
    
    
    // 重试
    lazy var tryButton: UIButton = {
        let button = UIButton()
        button.backgroundColor = .clear
        button.setImage(UIImage(named: "刷新_24_白色"), for: .normal)
        button.zl_enlargeValidTouchArea(inset: 14)
        button.isHidden = true
        return button
    }()
    
    lazy var itemTitle: UILabel = {
        let label = UILabel()
        label.textColor = .white
        label.font = .systemFont(ofSize: 14)
        return label
    }()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        
        makeUI()
        business()
    }
    
    convenience init() {
        self.init(frame: .zero)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func makeUI() {
        
        backgroundColor = .clear
        contentView.addSubviews([background, itemTitle,playButton, imagePlayer, tryButton])
        
        background.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        playButton.snp.makeConstraints { make in
            make.leading.equalToSuperview().inset(8)
            make.width.height.equalTo(32)
            make.centerY.equalToSuperview()
        }
        
        itemTitle.snp.makeConstraints { make in
            make.trailing.equalToSuperview().inset(40)
            make.leading.equalToSuperview().inset(45)
            make.centerY.equalToSuperview()
        }
        
        imagePlayer.snp.makeConstraints { make in
            make.width.equalTo(18)
            make.height.equalTo(18)
            make.trailing.equalToSuperview().inset(16)
            make.centerY.equalToSuperview()
        }
        
        tryButton.snp.makeConstraints { make in
            make.width.equalTo(24)
            make.height.equalTo(24)
            make.trailing.equalToSuperview().inset(12)
            make.centerY.equalToSuperview()
        }
        
        
    }
    
    func business() {
        
        playButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self else { return }
                self.playPauseAction?()
            }.disposed(by: rx.disposeBag)
        
        tryButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self else { return }
                self.retryAction?()
            }.disposed(by: rx.disposeBag)
        
    }
    
    /// 开始加载
    public func startLoading() {
        guard let path = Bundle.main.path(forResource: "播放中", ofType: "gif") else { return }
        guard let data = try? Data(contentsOf: URL(fileURLWithPath: path)) else { return }
        guard let gifImage = FLAnimatedImage(animatedGIFData: data) else { return }
        self.imagePlayer.animatedImage = gifImage
        self.imagePlayer.isHidden = false
    }
    
    /// 停止加载
    public func endLoading() {
        imagePlayer.isHidden = true
        imagePlayer.animatedImage = nil
    }
}

extension TTSPresetItem {
    func bind(to model: AudioPreSpeakModel, selected: Bool, playing: Bool = false, color: String = "#282828") {
        self.itemTitle.text = model.voiceName
        background.borderWidth = selected ? 1.5 : 0
        background.backgroundColor = UIColor(color)
        if playing {
            self.startLoading()
        } else {
            self.endLoading()
        }
        self.playButton.isSelected = playing
    }
    
    func bindClone(to model: AudioUserSpeakModel, selected: Bool, color: String = "#282828",  playing: Bool = false) {
        var placeholder = model.spkName ?? "未命名"
        placeholder = placeholder.isEmpty ? "未命名" : placeholder
        self.itemTitle.text = placeholder
        background.borderWidth = selected ? 2 : 0
        background.backgroundColor = UIColor(color)
        if model.statusStr == "Training" {
            self.itemTitle.text = "音色克隆中.."
            self.tryButton.isHidden = false
            self.playButton.isHidden = true
            itemTitle.snp.remakeConstraints { make in
                make.leading.equalToSuperview().inset(12)
                make.trailing.equalToSuperview().inset(60)
                make.centerY.equalToSuperview()
            }
        } else {
            self.tryButton.isHidden = true
            self.playButton.isHidden = false
            itemTitle.snp.remakeConstraints { make in
                make.leading.equalToSuperview().inset(45)
                make.trailing.equalToSuperview().inset(40)
                make.centerY.equalToSuperview()
            }
        }
        
        if playing {
            self.startLoading()
        } else {
            self.endLoading()
        }
        self.playButton.isSelected = playing
    }
}
