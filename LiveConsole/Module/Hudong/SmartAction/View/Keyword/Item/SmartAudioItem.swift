//
//  SmartAudioItem.swift
//  LivePlus
//
//  Created by 郭炜 on 2025/1/15.
//

import UIKit
import QMUIKit

// MARK: - 每个录音的item
class SmartAudioItem: UITableViewCell {
    
    var playPauseAction: (() -> Void)?
    var rateAction: (() -> Void)?
    var retryAction: (() -> Void)?
    
    lazy var background: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor("#282828")
        view.cornerRadius = 6
        view.borderWidth = 2
        view.borderColor = UIColor("#ADAAFF")
        return view
    }()
    
    lazy var progressView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor("#ADAAFF")
        return view
    }()
    
    lazy var itemImage: UIImageView = {
        let imageView = UIImageView(image: UIImage(named: "ic_smart_audio_icon"))
        return imageView
    }()
    
    lazy var itemTitle: UILabel = {
        let label = UILabel()
        label.textColor = .white
        label.font = .systemFont(ofSize: 14)
        return label
    }()
    
    lazy var itemDuration: UILabel = {
        let label = UILabel()
        label.textColor = UIColor("#ACACAC")
        label.font = .systemFont(ofSize: 14)
        return label
    }()
    
    lazy var itemRateButton: UIButton = {
        let button = UIButton()
        button.backgroundColor = UIColor("#444444")
        button.setTitleColor(.white, for: .normal)
        button.titleLabel?.font = .systemFont(ofSize: 12, weight: .medium)
        button.cornerRadius = 12
        button.zl_enlargeValidTouchArea(inset: 14)
        return button
    }()
    
    lazy var itemPlayPauseButton: UIButton = {
        let button = UIButton()
        button.setImage(UIImage(named: "ic_smart_record_resume"), for: .normal)
        button.setImage(UIImage(named: "ic_smart_record_pause"), for: .selected)
        button.zl_enlargeValidTouchArea(inset: 14)
        return button
    }()
    
    lazy var itemDragButton: UIButton = {
        let button = UIButton()
        button.setImage(UIImage(named: "ic_smart_audio_list_drag"), for: .normal)
        button.isUserInteractionEnabled = false
        return button
    }()
    
    var progress: Float = 0.0
    
    // 等待生成中
    lazy var waittingView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor("#282828")
        view.isHidden = true
        return view
    }()
    
    //
    lazy var statusLab: UILabel = {
        let label = UILabel()
        label.textColor = UIColor("#ACACAC")
        label.font = .systemFont(ofSize: 13)
        label.text = "合成中"
        label.backgroundColor = UIColor("#444444")
        label.cornerRadius = 14
        label.textAlignment = .center
        return label
    }()
    
    // 重试
    lazy var tryButton: UIButton = {
        let button = UIButton()
        button.backgroundColor = .clear
        button.setImage(UIImage(named: "刷新_24_白色"), for: .normal)
        button.zl_enlargeValidTouchArea(inset: 14)
        return button
    }()
    

    override func awakeFromNib() {
        super.awakeFromNib()
    }
    
    override func setSelected(_ selected: Bool, animated: Bool) {
        super.setSelected(selected, animated: animated)
    }
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        makeUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func makeUI() {
        
        selectionStyle = .none
        backgroundColor = .clear
        contentView.addSubview(background)
        background.addSubviews([itemImage, itemTitle, itemDuration, itemRateButton, itemPlayPauseButton, itemDragButton, progressView, waittingView])
        
        background.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.leading.trailing.equalToSuperview().inset(16)
            make.height.equalTo(50)
        }
        itemImage.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.leading.equalToSuperview().inset(20)
            make.size.equalTo(20)
        }
        itemTitle.snp.makeConstraints { make in
            make.leading.equalTo(itemImage.snp.trailing).offset(10)
            make.centerY.equalToSuperview()
        }
        itemDuration.snp.makeConstraints { make in
            make.leading.equalTo(itemTitle.snp.trailing).offset(10)
            make.centerY.equalToSuperview()
        }
        itemDragButton.snp.makeConstraints { make in
            make.trailing.equalToSuperview().inset(10)
            make.centerY.equalToSuperview()
            make.size.equalTo(28)
        }
        itemPlayPauseButton.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.trailing.equalTo(itemDragButton.snp.leading).offset(-15)
            make.size.equalTo(28)
        }
        itemRateButton.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.trailing.equalTo(itemPlayPauseButton.snp.leading).offset(-15)
            make.height.equalTo(24)
            make.width.equalTo(58)
        }
        progressView.snp.makeConstraints { make in
            make.bottom.leading.equalToSuperview()
            make.height.equalTo(2.5)
            make.width.equalToSuperview().multipliedBy(self.progress)
        }
        
        
        waittingView.snp.makeConstraints { make in
            make.trailing.equalToSuperview()
            make.bottom.top.equalToSuperview()
            make.leading.equalTo(itemRateButton.snp.leading)
        }
        
        waittingView.addSubviews([statusLab, tryButton])
        tryButton.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.trailing.equalToSuperview().inset(12)
            make.size.equalTo(28)
        }
        
        statusLab.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.leading.equalToSuperview().inset(12)
            make.width.equalTo(80)
            make.height.equalTo(28)
        }
        
        
        
        itemRateButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self else { return }
                self.rateAction?()
            }.disposed(by: rx.disposeBag)
        
        itemPlayPauseButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self else { return }
                self.playPauseAction?()
            }.disposed(by: rx.disposeBag)
        tryButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self else { return }
                
                self.retryAction?()
            }.disposed(by: rx.disposeBag)
    }

    func updateProgress(value: Float) {
        self.progress = value
        progressView.snp.remakeConstraints { make in
            make.bottom.leading.equalToSuperview()
            make.height.equalTo(2.5)
            make.width.equalToSuperview().multipliedBy(value)
        }
        UIView.animate(withDuration: 0.1) {
            self.layoutIfNeeded()
        }
    }
}

extension SmartAudioItem {
    
    func bind(to model: ResFileModel, isPlaying: Bool, isSelected: Bool) {
        itemTitle.text = model.title ?? ""
        itemDuration.text = "\(model.duration / 1000)″"
        itemRateButton.setTitle("\(model.voidespeed)X", for: .normal)
        itemPlayPauseButton.isSelected = isPlaying
        self.background.borderWidth = isSelected ? 2.0 : 0.0
        
        if let audioState = model.audioState {
            switch audioState {
                
            case .none:
                self.waittingView.isHidden = true
                self.itemDuration.isHidden = false
            case .waitting:
                self.waittingView.isHidden = false
                self.itemDuration.isHidden = true
                self.statusLab.text = "合成中"
                self.tryButton.isHidden = true
            case .success:
                self.waittingView.isHidden = true
                self.itemDuration.isHidden = false
            case .fail:
                self.waittingView.isHidden = false
                self.itemDuration.isHidden = true
                self.statusLab.text = "合成失败"
                self.tryButton.isHidden = false
            }
        } else {
            self.waittingView.isHidden = true
            self.itemDuration.isHidden = false
        }
        
        
        if !isPlaying {
            progressView.snp.remakeConstraints { make in
                make.bottom.leading.equalToSuperview()
                make.height.equalTo(2.5)
                make.width.equalTo(0)
            }
        } else {
            progressView.snp.remakeConstraints { make in
                make.bottom.leading.equalToSuperview()
                make.height.equalTo(2.5)
                make.width.equalToSuperview().multipliedBy(self.progress)
            }
            self.layoutIfNeeded()
        }
    }
}
