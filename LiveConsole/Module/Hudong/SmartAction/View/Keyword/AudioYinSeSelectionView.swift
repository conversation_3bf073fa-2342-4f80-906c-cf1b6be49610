//
//  AudioPresetSelectionView.swift
//  LivePlus
//
//  Created by 郭炜 on 2025/1/16.
//

import UIKit
import JXSegmentedView

// MARK: - 选择预设音色、克隆音色
protocol AudioYinSeSelectionViewDelegate: NSObjectProtocol {
    func actionForYinseCloneSelection(model: AudioUserSpeakModel?)
    
    func actionForRetryRequestSpk()
    
}

class AudioYinSeSelectionView: UIView {
        
    weak var delegate: AudioYinSeSelectionViewDelegate?
    
    // MARK: - UI Components
    
    lazy var contentView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor("#111111")
        return view
    }()
    
    lazy var editView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        view.isHidden = true
        return view
    }()
    
    lazy var addButton: UIButton = {
        let button = UIButton()
        button.setImage(UIImage(named: "ic_clone_add"), for: .normal)
        return button
    }()
    
    lazy var editButton: UIButton = {
        let button = UIButton()
        button.setImage(UIImage(named: "ic_clone_edit"), for: .normal)
        return button
    }()
    
    lazy var deletButton: UIButton = {
        let button = UIButton()
        button.setImage(UIImage(named: "ic_clone_delete"), for: .normal)
        return button
    }()
    
    /// 顶部分段控制器
    private lazy var segmentedView: JXSegmentedView = {
        let view = JXSegmentedView()
        view.backgroundColor = .clear
        view.delegate = self
        
        // 添加背景指示器
        let indicator = JXSegmentedIndicatorLineView()
        indicator.indicatorColor = UIColor("#ADAAFF")
        indicator.indicatorCornerRadius = 1.2
        indicator.indicatorHeight = 2.4
        indicator.indicatorWidth = 58
        indicator.indicatorPosition = .bottom
        indicator.verticalOffset = 11
        view.indicators = [indicator]
        return view
    }()
    
    /// 分段控制器数据源
    private lazy var segmentedDataSource: JXSegmentedTitleDataSource = {
        let dataSource = JXSegmentedTitleDataSource()
        dataSource.titleNormalColor = .white
        dataSource.titleSelectedColor = UIColor("#ADAAFF")
        dataSource.titleNormalFont = .systemFont(ofSize: 14)
        dataSource.titleSelectedFont = .systemFont(ofSize: 14)
        
        // 设置间距
        dataSource.itemSpacing = 16        // item之间的间距
        return dataSource
    }()
    
    /// 分组内容容器视图
    private lazy var listContainerView: JXSegmentedListContainerView = {
        let view = JXSegmentedListContainerView(dataSource: self)
        return view
    }()
    
        
    /// 音色预设
    lazy var presetView: AudioYinSePresetView = {
        let view = AudioYinSePresetView()
        return view
    }()
    
    /// 克隆音色
    lazy var cloneView: AudioYinSeCloneView = {
        let view = AudioYinSeCloneView()
        view.delegate = self
        return view
    }()
    
    lazy var renameAlert: InputAlert = {
        return InputAlert(frame: UIScreen.main.bounds)
    }()
    
    var agreementView: AudioCloneAgreementView?
    
    // MARK: - Init
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
        business()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Setup
    private func setupUI() {
        backgroundColor = .clear
        addSubviews([contentView])
        contentView.addSubviews([segmentedView, listContainerView, editView])
        
        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        segmentedView.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.height.equalTo(52)
            make.leading.equalToSuperview().inset(16)
            make.width.equalTo(180)
        }
        
        listContainerView.snp.makeConstraints { make in
            make.top.equalTo(segmentedView.snp.bottom)
            make.leading.trailing.bottom.equalToSuperview()
        }
        
        
        editView.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.height.equalTo(52)
            make.trailing.equalToSuperview()
            make.width.equalTo(200)
        }
        
        editView.addSubviews([addButton, editButton, deletButton])
        
        deletButton.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.height.equalTo(52)
            make.trailing.equalToSuperview().inset(12)
            make.width.equalTo(32)
        }
        
        editButton.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.height.equalTo(52)
            make.trailing.equalTo(deletButton.snp.leading).offset(-8)
            make.width.equalTo(32)
        }
        
        addButton.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.height.equalTo(52)
            make.trailing.equalTo(editButton.snp.leading).offset(-8)
            make.width.equalTo(32)
        }
        
        
        // 配置分段控制器数据
        let titles = ["系统音色", "克隆音色"]
        segmentedDataSource.titles = titles
        segmentedView.dataSource = segmentedDataSource
        // 关联listContainer
        segmentedView.listContainer = listContainerView
        
     
//        self.delegate?.actionForYinseCloneSelection(model: self.cloneView.dataSource.first(where: { $0.spkId == self.cloneView.selectionSpeakId }))
           
    }
    
}

extension AudioYinSeSelectionView {
     func business() {
        
         cloneView.addButton.rx.tap
             .subscribe { [weak self] _ in
                 guard let self = self else { return }
                 self.addAction()
             }.disposed(by: rx.disposeBag)
         
        addButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self else { return }
                self.addAction()
            }.disposed(by: rx.disposeBag)
        
         editButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self else { return }
                guard let model = self.cloneView.dataSource.first(where: { $0.spkId == self.cloneView.selectionSpeakId }) else {
                    HUD.showFail("请先选择音色")
                    return
                }
                var placeholder = model.spkName ?? "未命名"
                placeholder = placeholder.isEmpty ? "未命名" : placeholder
                self.renameAlert.show(placeholder)
                self.renameAlert.sureBtnActionBlock = {[weak self] (_ newName) in
                    guard !newName.isEmpty else {
                        return
                    }
                    MiddleRequestNet.updateCloneName(name: newName,
                                                     spkId: model.spkId) { [weak self] successed in
                        guard let self = self else { return }
                        if successed {
                            model.spkName = newName
                            self.cloneView.collectionView.reloadData()
                        } else {
                            HUD.showFail("修改失败")
                        }
                    }
                }
            }.disposed(by: rx.disposeBag)
        
        deletButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self else { return }
                guard let model = self.cloneView.dataSource.first(where: { $0.spkId == self.cloneView.selectionSpeakId }) else {
                    HUD.showFail("请先选择音色")
                    return
                }

                AlertView.show(leftOption: .gray(title: "取消", action: {
                    
                }), rightOption: .main(title: "确定", action: {
                    MiddleRequestNet.deleteCloneName(name: model.spkName ?? "未命名",
                                                     spkId: model.spkId) { [weak self] successed in
                        guard let self = self else { return }
                        if successed {
                            self.cloneView.dataSource.removeAll(where: { $0.spkId == model.spkId })
                            self.cloneView.collectionView.reloadData()
                        } else {
                            HUD.showFail("删除失败")
                        }
                        resetPreAudio()
                    }
                }), title: "要删除克隆音色吗？", attMessage: self.createAttributedText())
            }.disposed(by: rx.disposeBag)

    }

    func resetPreAudio() {
        self.cloneView.selectionSpeakId = nil
        AIReplyDataManager.shared.spkIdCache.currentPreAudioModel = self.presetView.dataSource.first
        self.delegate?.actionForRetryRequestSpk()
    }
    
    func createAttributedText() -> NSAttributedString {
        let text = "删除后不可恢复"
        let attributedString = NSMutableAttributedString(string: text)
        
        // 设置基础属性（整体文本）
        let baseAttributes: [NSAttributedString.Key: Any] = [
            .font: UIFont.systemFont(ofSize: 15),
            .foregroundColor: UIColor("#4D4E52") // 灰色
        ]
        attributedString.addAttributes(baseAttributes, range: NSRange(location: 0, length: text.count))
        
        return attributedString
    }
    
    func createTitleNotAttributedText() -> NSAttributedString {
        let text = "本次编辑需消耗1个修改次数，您的可消耗次数不足！"
        let attributedString = NSMutableAttributedString(string: text)
        
        // 设置基础属性（整体文本）
        let baseAttributes: [NSAttributedString.Key: Any] = [
            .font: UIFont.systemFont(ofSize: 18, weight: .medium),
            .foregroundColor: UIColor.black
        ]
        attributedString.addAttributes(baseAttributes, range: NSRange(location: 0, length: text.count))
        
        // 设置高亮部分
        let highlightColor = UIColor("#6763F6") // 紫色
        
        // 高亮 "1"
        if let range1 = text.range(of: "1") {
            let nsRange1 = NSRange(range1, in: text)
            attributedString.addAttribute(.foregroundColor, value: highlightColor, range: nsRange1)
        }
        
        // 高亮 "消耗次数"
        if let range2 = text.range(of: "消耗次数") {
            let nsRange2 = NSRange(range2, in: text)
            attributedString.addAttribute(.foregroundColor, value: highlightColor, range: nsRange2)
        }
        
        return attributedString
    }
    
    func createTitleAttributedText() -> NSAttributedString {
        let text = "本次编辑需消耗1个修改次数，您确定编辑吗？"
        let attributedString = NSMutableAttributedString(string: text)
        
        // 设置基础属性（整体文本）
        let baseAttributes: [NSAttributedString.Key: Any] = [
            .font: UIFont.systemFont(ofSize: 18, weight: .medium),
            .foregroundColor: UIColor.black
        ]
        attributedString.addAttributes(baseAttributes, range: NSRange(location: 0, length: text.count))
        
        // 设置高亮数字 "1"
        if let range = text.range(of: "1") {
            let nsRange = NSRange(range, in: text)
            let highlightAttributes: [NSAttributedString.Key: Any] = [
                .foregroundColor: UIColor("#6763F6") // 紫色
            ]
            attributedString.addAttributes(highlightAttributes, range: nsRange)
        }
        
        return attributedString
    }
    
    // 新增一个克隆音色
    func addAction() {
        
        if self.cloneView.dataSource.count >= 5 {
            HUD.showFail("上限添加5个")
            return
        }
        
        if let v = self.agreementView, v.checkButton.isSelected {
            // 提示
            let viewController = SmartCloneEditVC()
            AppDelegate.curDisplayVC().navigationController?.pushViewController(viewController)
            return
        }
        
        // 音色弹窗
        let yinseView = AudioCloneAgreementView()
        yinseView.delegate = self
        self.agreementView = yinseView
        yinseView.show()
        
       
    }
}

extension AudioYinSeSelectionView: AudioYinSeCloneViewDelegate {
    func actionForYinseCloneSelection(model: AudioUserSpeakModel?) {
        self.delegate?.actionForYinseCloneSelection(model: model)
    }
    
    func actionForRetryRequestSpk() {
        self.delegate?.actionForRetryRequestSpk()
    }
}


// MARK: - JXSegmentedViewDelegate
extension AudioYinSeSelectionView: JXSegmentedViewDelegate {
    /// 分段控制器选中项变化
    func segmentedView(_ segmentedView: JXSegmentedView, didSelectedItemAt index: Int) {
        // 处理选中事件
        
    }
}

// MARK: - JXSegmentedListContainerViewDataSource
extension AudioYinSeSelectionView: JXSegmentedListContainerViewDataSource {
    /// 返回分组数量
    func numberOfLists(in listContainerView: JXSegmentedListContainerView) -> Int {
        return 2
    }
    
    /// 返回对应索引的列表页面
    func listContainerView(_ listContainerView: JXSegmentedListContainerView, initListAt index: Int) -> JXSegmentedListContainerViewListDelegate {
        switch index {
        case 0:
            /// 音色预设
            return self.presetView
        case 1:
            /// 克隆音色
            return self.cloneView
        default: return presetView
        }
    }
    
    func segmentedView(_ segmentedView: JXSegmentedView, didClickSelectedItemAt index: Int) {
        switch index {
        case 0:
            editView.isHidden = true
        case 1:
            editView.isHidden = false
            editView.isHidden = self.cloneView.dataSource.isEmpty
            self.cloneView.reloadView()
        default: editView.isHidden = true
        }
    }
}


extension AudioYinSeSelectionView: AudioCloneAgreementViewDelegate {
    func actionStartClone() {
        // 新建克隆
        let viewController = SmartCloneEditVC()
        AppDelegate.curDisplayVC().navigationController?.pushViewController(viewController)
    }
    
    
}
