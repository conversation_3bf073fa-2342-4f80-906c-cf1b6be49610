//
//  SmartTTSAudioVC+Request.swift
//  LivePlus
//
//  Created by 郭炜 on 2025/1/16.
//

import UIKit

// MARK: - 接口请求
extension SmartTTSAudioVC {
    
    /// 获取预制音色、克隆音色
    func requestAudioSpkIDList() {
        MiddleRequestNet.getSpeakIDList { [weak self] data in
            guard let self = self else { return }
            self.audioSpkIDModel = data

            if !AIReplyDataManager.shared.spkIdCache.hasSpeak {
                AIReplyDataManager.shared.spkIdCache.currentPreAudioModel = data?.preSpk.first
            }
            
            self.bottomView.updateYinseView(audioSpkIDModel: data)
            
        }
    }
}

// MARK: - 代理
extension SmartTTSAudioVC: AudioYinSePresetViewDelegate, AudioYinSeSelectionViewDelegate {
    func actionForRetryRequestSpk() {
        self.requestAudioSpkIDList()
    }
    
    /// 选择了预设音色
    func actionForYinsePresetSelection(model: AudioPreSpeakModel) {
        AIReplyDataManager.shared.spkIdCache.currentPreAudioModel = model
        self.bottomView.updateSelectedYinseView()
    }
    
    /// 选择了克隆银色
    func actionForYinseCloneSelection(model: AudioUserSpeakModel?) {
        AIReplyDataManager.shared.spkIdCache.currentCloneAudioModel = model
        self.bottomView.updateSelectedYinseView()
    }
}

// MARK: - 合成
extension SmartTTSAudioVC {
    // 准备合成
    func willTTSAction() {
        guard let text = self.textTipView.textView.text, !text.isEmpty else {
            HUD.showFail("请输入文案内容")
            return
        }
        
        // 积分校验
        if UserInfo.points < UserInfo.pointsConsumed_aisynthesis(text: text) {
            showPointsAlert(count1: UserInfo.points, count2: UserInfo.pointsConsumed_aisynthesis(text: text))
            return
        }
        
        if UserDefaults.standard.bool(forKey: LCKey.UD_Point_Alert_TTS) {
            self.hechengTTSAction(text: text)
            return
        }
        // 积分消耗提示
        showPointsAlert(count: UserInfo.pointsConsumed_aisynthesis(text: text), text: text)
        
    }
    
    
    func showPointsAlert(count1: Int64, count2: Int64) {
        let paraph = NSMutableParagraphStyle()
        paraph.lineSpacing = 6
        
        let attributes1 = [NSAttributedString.Key.paragraphStyle: paraph, NSAttributedString.Key.foregroundColor: UIColor("#1E1F20"), NSAttributedString.Key.font: UIFont.systemFont(ofSize: 16, weight: .regular)]
        
        let attributes2 = [NSAttributedString.Key.paragraphStyle: paraph, NSAttributedString.Key.foregroundColor: UIColor("#6763F6"), NSAttributedString.Key.font: UIFont.systemFont(ofSize: 36, weight: .bold)]
        
        let attributes3 = [NSAttributedString.Key.paragraphStyle: paraph, NSAttributedString.Key.foregroundColor: UIColor("#6763F6"), NSAttributedString.Key.font: UIFont.systemFont(ofSize: 16, weight: .medium)]
        
        let attributedText1 = NSMutableAttributedString(string: "本次合成需消耗", attributes: attributes1)
        
        let attributedText2 = NSMutableAttributedString(string: "\n\(count2)", attributes: attributes2)
        
        let attributedText3 = NSMutableAttributedString(string: "   积分", attributes: attributes1)
        
        let attributedText4 = NSMutableAttributedString(string: "\n ", attributes: attributes2)
        
        let attributedText5 = NSMutableAttributedString(string: "您当前仅剩\(count1)积分 ", attributes: attributes1)

        let attributedText6 = NSMutableAttributedString(string: "已不足抵扣\n", attributes: attributes3)
        
        let attributedText7 = NSMutableAttributedString(string: "请先充值，再继续使用该服务！", attributes: attributes1)
        
        
        attributedText1.append(attributedText2)
        attributedText1.append(attributedText3)
        attributedText1.append(attributedText4)
        attributedText1.append(attributedText5)
        attributedText1.append(attributedText6)
        attributedText1.append(attributedText7)
        
        TTSPointsAlert_2.show(sureAction: {[weak self] in
            guard let self = self else { return  }
            self.goPayPointsVC()
        }, cancelAction: {
            
        }, attStr: attributedText1)
    }
    
    func showPointsAlert(count: Int64, text: String) {
        let paraph = NSMutableParagraphStyle()
        paraph.lineSpacing = 6
        
        let attributes1 = [NSAttributedString.Key.paragraphStyle: paraph, NSAttributedString.Key.foregroundColor: UIColor("#1E1F20"), NSAttributedString.Key.font: UIFont.systemFont(ofSize: 16, weight: .regular)]
        
        let attributes2 = [NSAttributedString.Key.paragraphStyle: paraph, NSAttributedString.Key.foregroundColor: UIColor("#6763F6"), NSAttributedString.Key.font: UIFont.systemFont(ofSize: 36, weight: .bold)]
        
        let attributes3 = [NSAttributedString.Key.paragraphStyle: paraph, NSAttributedString.Key.foregroundColor: UIColor("#1E1F20"), NSAttributedString.Key.font: UIFont.systemFont(ofSize: 18, weight: .semibold)]
        
        let attributedText1 = NSMutableAttributedString(string: "本次合成需消耗", attributes: attributes1)
        
        let attributedText2 = NSMutableAttributedString(string: "\n\(count)", attributes: attributes2)
        
        let attributedText3 = NSMutableAttributedString(string: "   积分", attributes: attributes1)
        
        let attributedText4 = NSMutableAttributedString(string: "\n ", attributes: attributes2)
        
        let attributedText5 = NSMutableAttributedString(string: "您确定开始合成吗？", attributes: attributes3)

                
        
        attributedText1.append(attributedText2)
        attributedText1.append(attributedText3)
        attributedText1.append(attributedText4)
        attributedText1.append(attributedText5)
        
        TTSPointsAlert_3.show(sureAction: {[weak self] in
            guard let self = self else { return  }
            self.hechengTTSAction(text: text)
        }, cancelAction: {
            
        }, attStr: attributedText1, isClone: false)
    }
    
    
    func goPayPointsVC() {
        self.navigationController?.pushViewController(MemberPointsPayVC())
    }
    
    /// 合成语音
    func hechengTTSAction(text: String)  {
       
        // 注意： 如果选的是礼物，这里可能需要合成2条
        if let gift = self.gift {
            hecheng2TTS(text: text)
            return
        }
        
        HUD.showWait()
        AIReplyDataManager.shared.submitTTSVoiceAsyn(text: text) { [weak self] mode in
            guard let self = self else { return }
            HUD.hideAllHUD()
            if let taskId = mode?.taskId {
                self.delegate?.didGetTTSCompletion(taskId: taskId, audioName: self.audioName)
                self.navigationController?.popViewController()
                HUD.showSuccess("已提交合成任务，请耐心等待")
            }
        }
        
    }
    
    // 注意： 如果选的是礼物，这里可能需要合成2条
    func hecheng2TTS(text: String) {
        
        // 第一条： 只保留你
        var str1 = text.replacingOccurrences(of: "【你/你们】", with: "你")
        
        // 第二条： 只保留你们
        var str2 = text.replacingOccurrences(of: "【你/你们】", with: "你们")
        
        // 清洗第一条
        str1 = str1.replacingOccurrences(of: "】", with: "").replacingOccurrences(of: "【", with: "")
        // 清洗第二条
        str2 = str2.replacingOccurrences(of: "】", with: "").replacingOccurrences(of: "【", with: "")
        
        if str1.isEmpty {
            HUD.showFail("请输入内容")
            return
        }
        
        // 准备合成第一条
        HUD.showWait()
        AIReplyDataManager.shared.submitTTSVoiceAsyn(text: str1) { [weak self] mode in
            guard let self = self else { return }
            HUD.hideAllHUD()
            if let taskId = mode?.taskId {
                self.delegate?.didGetTTSCompletion(taskId: taskId, audioName: self.audioName)
                self.navigationController?.popViewController()
                HUD.showSuccess("已提交合成任务，请耐心等待")
                // 执行第二个
                if str2.isEmpty {
                    return
                }
                AIReplyDataManager.shared.submitSubTask(text: str2, taskId: taskId)
            }
        }
    }
    
}
