//
//  SmartRecordAudioVC.swift
//  LivePlus
//
//  Created by 郭炜 on 2025/1/14.
//

import UIKit
import SnapKit

// MARK: - 现在录音
protocol SmartRecordAudioVCDelegate: NSObjectProtocol {
    func actionForRecordAudioCompletion(url: URL, duration: TimeInterval, isLocal: Bool)
}

class SmartRecordAudioVC: SmartBaseViewController {
    
    weak var delegate: SmartRecordAudioVCDelegate?
    
    /// 导航栏
    lazy var navigation: SmartNavigation = {
        let view = SmartNavigation()
        view.titleLabel.text = "现在录音"
        view.needHookBack = true
        view.balanceWordLabel.isHidden = true
        return view
    }()
    
    /// 提示文案部分
    lazy var tipView: SmartRecordTopView = {
        let view = SmartRecordTopView()
        return view
    }()
    
    /// 录音按钮操作部分
    lazy var bottomView: SmartRecordBotView = {
        let view = SmartRecordBotView()
        view.delegate = self
        return view
    }()
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
    }
    
    override func viewDidDisappear(_ animated: Bool) {
        super.viewDidDisappear(animated)
        AudioPlayManager.shared.stop()
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        fd_interactivePopDisabled = true

    }

    override func makeUI() {
        super.makeUI()
        
        view.addSubviews([navigation, tipView, bottomView])
        
        navigation.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview()
            make.height.equalTo(LCDevice.Nav_H)
        }
        tipView.snp.makeConstraints { make in
            make.top.equalTo(navigation.snp.bottom).offset(14)
            make.leading.trailing.equalToSuperview()
            make.bottom.equalTo(bottomView.snp.top).offset(-40)
        }
        bottomView.snp.makeConstraints { make in
            make.bottom.equalToSuperview().inset(LCDevice.X_BOTTOM_INSET)
            make.leading.trailing.equalToSuperview()
            make.height.equalTo(260)
        }
    }

    override func business() {
        super.business()
        
        navigation.backButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self else { return }
                self.hookBackAction()
            }.disposed(by: rx.disposeBag)

    }
    
    override func bindViewModel() {
        super.bindViewModel()
        
        // 添加右滑手势
        let panGesture = UIPanGestureRecognizer(target: self, action: #selector(handlePanGesture(_:)))
        view.addGestureRecognizer(panGesture)
    }
    
    override func touchesBegan(_ touches: Set<UITouch>, with event: UIEvent?) {
        super.touchesBegan(touches, with: event)
        self.view.endEditing(true)
    }
    
    /// 防止直接返回了
    func hookBackAction() {
        if AudioRecordManager.shared.currentState == .recording { return }
        guard let fileUrl = bottomView.fileUrl else {
            AppDelegate.curDisplayVC().navigationController?.popViewController()
            return
        }
        AlertView.show(leftOption: .gray(title: "取消", action: {
            AppDelegate.curDisplayVC().navigationController?.popViewController()
            AudioRecordManager.shared.deleteCurrentRecording()
        }), rightOption: .main(title: "确定", action: {
            AppDelegate.curDisplayVC().navigationController?.popViewController()
            self.delegate?.actionForRecordAudioCompletion(url: fileUrl, duration: self.bottomView.duration, isLocal: false)
        }), title: "要保存该录音吗？", message: "")
    }
    
    @objc private func handlePanGesture(_ gesture: UIPanGestureRecognizer) {
        // 获取手势的横向移动距离
        let translation = gesture.translation(in: view)
        
        if gesture.state == .changed {
            // 如果向右滑动超过50点
            if translation.x > 50 {
                // 重置手势状态,避免重复触发
                gesture.isEnabled = false
                gesture.isEnabled = true
                
                // 触发返回逻辑
                hookBackAction()
            }
        }
    }
}

extension SmartRecordAudioVC: SmartRecordBotViewDelegate {
    /// 处理完成后的保存逻辑
    func actionForRecordFinish(url: URL) {
        self.delegate?.actionForRecordAudioCompletion(url: url, duration: self.bottomView.duration, isLocal: false)
        self.navigationController?.popViewController()
    }
}
