//
//  SmartTTSAudioVC.swift
//  LivePlus
//
//  Created by 郭炜 on 2025/1/16.
//

import UIKit

// MARK: - AI语音合成
protocol SmartTTSAudioVCDelegate: NSObjectProtocol {

    // 获取到了一个任务ID
    func didGetTTSCompletion(taskId: String, audioName: String)
}

class SmartTTSAudioVC: SmartBaseViewController {
    
    weak var delegate: SmartTTSAudioVCDelegate?

    /// 导航栏
    lazy var navigation: SmartNavigation = {
        let view = SmartNavigation()
        view.titleLabel.text = "AI合成"
        view.balanceWordLabel.isHidden = false
        return view
    }()
    
    /// 文案内容
    lazy var textTipView: SmartTTSTopView = {
        let view = SmartTTSTopView()
        view.delegate = self
        return view
    }()
    
    /// 底部视图：音色 合成 按钮
    lazy var bottomView: SmartTTSBotView = {
        let view = SmartTTSBotView()
        return view
    }()
    
    /// 音色列表（预置+克隆）
    var audioSpkIDModel: AudioSpkIdModel?
    
    /// 提取进度
    lazy var progressView: AIPickerProgressView = {
        let view = AIPickerProgressView()
        view.titleLabel.text = "生成中"
        view.timeInterVar = 0.3
        return view
    }()
    
    lazy var renameAlert: InputAlert = {
        return InputAlert(frame: UIScreen.main.bounds)
    }()
    
    // 如果是用户选了礼物合成， 那么需要合成2条， 感谢【你们】送的【小黄鸭】  ：  感谢【你】送的【小黄鸭】
    var gift: DouyinGiftInfo?
    
    var audioName: String = "语音01"
    
    override func viewDidLoad() {
        super.viewDidLoad()
        textTipView.gift = gift
        
        if let gift = self.gift {
            let text = "感谢【你/你们】送的【\(gift.name)】"
            textTipView.textView.attributedText = LCTools.attributedContent(words: ["【你/你们】","【\(gift.name)】"], text: text, color: UIColor("#ACACAC"))
            self.textTipView.textCountLabel.text = "\(text.count)/200"
            self.textTipView.updateNonEditableRanges()
            textViewDidChange(count: text.count)
        }
        
        self.textTipView.audioNameLabel.text = "语音名称：\(audioName)"
        let tap = UITapGestureRecognizer(target: self, action: #selector(tapAction))
        self.navigation.balanceWordLabel.addGestureRecognizer(tap)
        
       
    }
    
    override func makeUI() {
        super.makeUI()
        
        view.addSubviews([navigation, textTipView, bottomView, progressView])
        
        navigation.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview()
            make.height.equalTo(LCDevice.Nav_H)
        }
        textTipView.snp.makeConstraints { make in
            make.top.equalTo(navigation.snp.bottom).offset(14)
            make.leading.trailing.equalToSuperview()
            make.bottom.equalTo(bottomView.snp.top).offset(-24)
        }
        bottomView.snp.makeConstraints { make in
            make.bottom.equalToSuperview().inset(LCDevice.X_BOTTOM_INSET + 16)
            make.leading.trailing.equalToSuperview()
            make.height.equalTo(400)
        }
        progressView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }

    override func business() {
        super.business()
        
        bottomView.hechengButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self else { return }
                self.willTTSAction()
            }.disposed(by: rx.disposeBag)
        

        bottomView.yinseView.presetView.delegate = self
        bottomView.yinseView.delegate = self
        
        textTipView.editButton.addTarget(self, action: #selector(editTitle), for: .touchUpInside)
        
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        requestAudioSpkIDList()
        MiddleRequestNet.getUserInfoApiData()
        self.navigation.balanceWordLabel.text = "积分:\(UserInfo.points)"
    }
    
    @objc func tapAction() {
        Router.openPointsRulesAgreement()
    }
    
    override func bindViewModel() {
        super.bindViewModel()
        self.requestAudioSpkIDList()
    }
    
    override func touchesBegan(_ touches: Set<UITouch>, with event: UIEvent?) {
        super.touchesBegan(touches, with: event)
        self.view.endEditing(true)
    }
    
    
    @objc func editTitle() {
        self.renameAlert.show(audioName, 10)
        self.renameAlert.sureBtnActionBlock = {[weak self] (_ newName) in
            guard let self = self else { return }
            
            guard !newName.isEmpty else { return }
            
            self.audioName = newName
            self.textTipView.audioNameLabel.text = "语音名称：\(newName)"
        }
    }
    
}

extension SmartTTSAudioVC: SmartTTSTopViewDelegate {
    func textViewDidChange(count: Int) {
        self.bottomView.hechengButton.isEnabled = count > 0
        
        if count > 0 {
            self.bottomView.hechengButton.alpha = 1.0
        } else {
            self.bottomView.hechengButton.alpha = 0.5
        }
        let paraph = NSMutableParagraphStyle()
        paraph.lineSpacing = 6
        
        let attributes1 = [NSAttributedString.Key.paragraphStyle: paraph, NSAttributedString.Key.foregroundColor: UIColor.white, NSAttributedString.Key.font: UIFont.systemFont(ofSize: 18, weight: .bold)]
                
        let attributes2 = [NSAttributedString.Key.paragraphStyle: paraph, NSAttributedString.Key.foregroundColor: UIColor.white, NSAttributedString.Key.font: UIFont.systemFont(ofSize: 16, weight: .regular)]
        
        let attributedText1 = NSMutableAttributedString(string: "合成语音", attributes: attributes1)
        
        let attributedText2 = NSMutableAttributedString(string: "（本次预计消耗", attributes: attributes2)
        
        let attributedText3 = NSMutableAttributedString(string: " \(UserInfo.pointsConsumed_aisynthesis(count: count)) ", attributes: attributes1)
        
        let attributedText4 = NSMutableAttributedString(string: "积分）", attributes: attributes2)
        
        if count > 0 {
            attributedText1.append(attributedText2)
            attributedText1.append(attributedText3)
            attributedText1.append(attributedText4)
            
        }
        bottomView.hechengButton.setAttributedTitle(attributedText1, for: .normal)
    }
    
}
