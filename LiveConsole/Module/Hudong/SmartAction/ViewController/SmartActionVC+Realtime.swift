//
//  SmartActionVC+Realtime.swift
//  LiveConsole
//
//  Created by simon on 12.5.25.
//

import Foundation

extension SmartActionVC: LangduNickViewDelegate {
    
    func actionRlue() {
        // 显示 规则
        RealtimeNickAlertView.show {
            
        } cancelAction: {
            
        }
    }
    
    func actionOn_off() {
        if AIReplyDataManager.shared.ttsNickenable {
            willClose_tts_Nick()
        } else {
            willOpen_tts_Nick()
        }
    }
    
    func willOpen_tts_Nick() {
        // 先校验会员
        if !LCTools.checkMember() {
            return
        }
        
        // 校验是否绑定了抖音账号
        if !isLogin {
            go_binddy()
            return
        }
        
        // 右上角的开关和音频回复资源
        if !professionalEnable, !isContainAudioReplayItem() {
            showProfessionalEnableAlert2()
            return
        }
        
        // 校验是否开了右上角的开关和 回复内容是否有语音
        if !professionalEnable {
            showProfessionalEnableAlert()
            return
        }
        
        // 是否有音频回复资源
        if !isContainAudioReplayItem() {
            showProfessionalEnableAlert1()
            return
        }
        
        guard let user = UserInfo.currentUser() else { return }
        let p: Int64 = UserInfo.points
        if p <= 0 {
            showProfessionalEnableAlert3()
            return
        }
        
        if  p <= 30 {
            showProfessionalEnableAlert4()
            return
        }
        
        
        realtimeNickOpen()
    }
    
    func realtimeNickOpen() {
       
        if self.showOpenView()  {
            RealtimeNickAlertOpenView.show {[weak self] in
                guard let self = self else { return  }
                self.open_tts_Nick()
            } cancelAction: {
                
            }
        } else {
            self.open_tts_Nick()
        }
        
    }
    
    func showOpenView() -> Bool {
        if let rule = UserDefaults.standard.value(forKey: LCKey.UD_Realtime_Rlue) as? String {
            return rule == "1"
        } else {
            return true
        }
        
    }
    
    func willClose_tts_Nick() {
        close_tts_Nick()
    }
    
    // 打开实时的昵称合并
    func open_tts_Nick() {
        self.sumPoints = 0
        AIReplyDataManager.shared.ttsNickenable = true
        self.realtimeView.start()
        self.start_tts_Timer()
        self.updateRealtimeView()
        
    }
    
    // 关闭
    func close_tts_Nick() {
        self.sumPoints = 0
        AIReplyDataManager.shared.ttsNickenable = false
        self.realtimeView.stop()
        self.stop_tts_Timer()
        self.updateRealtimeView()
    }
    
    func updateRealtimeView() {
        self.realtimeView.updateView(open: AIReplyDataManager.shared.ttsNickenable)
        self.realtimeView.snp.updateConstraints { make in
            make.height.equalTo( AIReplyDataManager.shared.ttsNickenable ? 134 : 52 )
        }
    }
   
    func showProfessionalEnableAlert2() {
        let paraph = NSMutableParagraphStyle()
        paraph.lineSpacing = 6
        let attributes = [NSAttributedString.Key.paragraphStyle: paraph, NSAttributedString.Key.foregroundColor: UIColor("#1E1F20"), NSAttributedString.Key.font: UIFont.systemFont(ofSize: 16, weight: .regular)]
        
        let attributedText = NSMutableAttributedString(string: " 实时朗读用户昵称当前不可用，请先完成以下操作后再开启：\n\n1.点击右上角开启/停用按钮\n2.创建并激活语音回复卡片", attributes: attributes)
        
        attributedText.setAttributes([NSAttributedString.Key.foregroundColor: UIColor("#6974F2"), NSAttributedString.Key.font: UIFont.systemFont(ofSize: 16, weight: .medium)], range: NSRange(location: 17, length: 6))
        
        HudongOpenNickTTSAlert_1.show(sureAction: {[weak self] in
            
        }, cancelAction: {
            
        }, attStr: attributedText)
    }
    
    func showProfessionalEnableAlert() {
        let paraph = NSMutableParagraphStyle()
        paraph.lineSpacing = 6
        let attributes = [NSAttributedString.Key.paragraphStyle: paraph, NSAttributedString.Key.foregroundColor: UIColor("#1E1F20"), NSAttributedString.Key.font: UIFont.systemFont(ofSize: 16, weight: .regular)]
        
        let attributedText = NSMutableAttributedString(string: "实时朗读用户昵称当前不可用请先点击右上角开启/停用按钮后再开启", attributes: attributes)
        
        attributedText.setAttributes([NSAttributedString.Key.foregroundColor: UIColor("#6974F2"), NSAttributedString.Key.font: UIFont.systemFont(ofSize: 16, weight: .medium)], range: NSRange(location: 15, length: 12))
        
        HudongOpenNickTTSAlert_1.show(sureAction: {[weak self] in
            
        }, cancelAction: {
            
        }, attStr: attributedText)
    }
    
    
    func showProfessionalEnableAlert1() {
        let paraph = NSMutableParagraphStyle()
        paraph.lineSpacing = 6
        let attributes = [NSAttributedString.Key.paragraphStyle: paraph, NSAttributedString.Key.foregroundColor: UIColor("#1E1F20"), NSAttributedString.Key.font: UIFont.systemFont(ofSize: 16, weight: .regular)]
        
        let attributedText = NSMutableAttributedString(string: "实时朗读用户昵称当前不可用请先创建并激活语音回复卡片后再开启", attributes: attributes)
        
        attributedText.setAttributes([NSAttributedString.Key.foregroundColor: UIColor("#6974F2"), NSAttributedString.Key.font: UIFont.systemFont(ofSize: 16, weight: .medium)], range: NSRange(location: 15, length: 11))
        
        HudongOpenNickTTSAlert_1.show(sureAction: {[weak self] in
            
        }, cancelAction: {
            
        }, attStr: attributedText)
    }
    
    func showProfessionalEnableAlert3() {
        let paraph = NSMutableParagraphStyle()
        paraph.lineSpacing = 6
        let attributes = [NSAttributedString.Key.paragraphStyle: paraph, NSAttributedString.Key.foregroundColor: UIColor("#1E1F20"), NSAttributedString.Key.font: UIFont.systemFont(ofSize: 16, weight: .regular)]
        
        let attributedText = NSMutableAttributedString(string: "您的积分 已用完\n请先充值，再继续使用该服务！", attributes: attributes)
        
        attributedText.setAttributes([NSAttributedString.Key.foregroundColor: UIColor("#6974F2"), NSAttributedString.Key.font: UIFont.systemFont(ofSize: 16, weight: .medium)], range: NSRange(location: 5, length: 3))
        
        HudongOpenNickTTSAlert_2.show(sureAction: {[weak self] in
            guard let self = self else { return  }
            self.goMember()
        }, cancelAction: {
            
        }, attStr: attributedText)
    }
    
    func showProfessionalEnableAlert4() {
        let paraph = NSMutableParagraphStyle()
        paraph.lineSpacing = 6
        let attributes = [NSAttributedString.Key.paragraphStyle: paraph, NSAttributedString.Key.foregroundColor: UIColor("#1E1F20"), NSAttributedString.Key.font: UIFont.systemFont(ofSize: 16, weight: .regular)]
        
        let attributedText = NSMutableAttributedString(string: "您的当前积分 不足30\n为避免影响功能使用，请及时充值", attributes: attributes)
        
        attributedText.setAttributes([NSAttributedString.Key.foregroundColor: UIColor("#6974F2"), NSAttributedString.Key.font: UIFont.systemFont(ofSize: 16, weight: .medium)], range: NSRange(location: 7, length: 4))
        
        HudongOpenNickTTSAlert_2.show(sureAction: {[weak self] in
            guard let self = self else { return  }
            self.goMember()
        }, cancelAction: {[weak self] in
            guard let self = self else { return  }
            self.realtimeNickOpen()
        }, attStr: attributedText, leftTitle: "暂不充值")
    }
    
    func showStopLivingAlert() {
        let paraph = NSMutableParagraphStyle()
        paraph.lineSpacing = 8
        paraph.alignment = .center
        let attributes = [NSAttributedString.Key.paragraphStyle: paraph, NSAttributedString.Key.foregroundColor: UIColor("#1E1F20"), NSAttributedString.Key.font: UIFont.systemFont(ofSize: 16, weight: .regular)]
        
        let text = "系统检测到您已下播\n实时朗读昵称已关闭\n如需继续使用请重新开启"
        
        let attributedText = NSMutableAttributedString(string: text, attributes: attributes)
        
        attributedText.setAttributes([NSAttributedString.Key.foregroundColor: UIColor("#6974F2"), NSAttributedString.Key.font: UIFont.systemFont(ofSize: 16, weight: .bold)], range: NSRange(location: text.count - 4, length: 4))
        
        HudongOpenNickTTSAlert_1.show(sureAction: {[weak self] in
            
        }, cancelAction: {
            
        }, attStr: attributedText)
    }
    
    // 是否有音频的回复资源
    func isContainAudioReplayItem() -> Bool {
        return self.model.cardList.contains(where: {$0.itemType == .musicLocal && $0.enable == true })
    }
}


// MARK: - 积分消耗上报
extension SmartActionVC {
    
    func start_tts_Timer() {
        stop_tts_Timer()
        ttsTimer = Timer.scheduledTimer(timeInterval: TimeInterval(60), target: self, selector: #selector(timeAction), userInfo: nil, repeats: true)
        ttsTimer?.fire()
    }
    
    
    func stop_tts_Timer() {
        ttsTimer?.invalidate()
        ttsTimer = nil
    }
    
    @objc func timeAction() {
        // 上报 如果上报失败了 需要关闭实时合成
        MiddleRequestNet.ttsPiontsDeduct { [weak self] result in
            guard let self = self else { return }
            guard let result = result else {
                self.willClose_tts_Nick()
                self.showPointsAlert()
                return
            }
            
            self.sumPoints =  sumPoints + result.neededPoints
            self.realtimeView.retimeView.potLab.text = "\(self.sumPoints)"
        }
    }
    
    
    func showPointsAlert() {
        HUD.showFail("您的积分已用完\n实时朗读用户昵称已关闭")
//        let paraph = NSMutableParagraphStyle()
//        paraph.lineSpacing = 6
//        
//        let attributes1 = [NSAttributedString.Key.paragraphStyle: paraph, NSAttributedString.Key.foregroundColor: UIColor("#1E1F20"), NSAttributedString.Key.font: UIFont.systemFont(ofSize: 16, weight: .regular)]
//                
//        let attributes2 = [NSAttributedString.Key.paragraphStyle: paraph, NSAttributedString.Key.foregroundColor: UIColor("#6763F6"), NSAttributedString.Key.font: UIFont.systemFont(ofSize: 16, weight: .medium)]
//        
//        let attributedText1 = NSMutableAttributedString(string: "您的积分 ", attributes: attributes1)
//        
//        let attributedText2 = NSMutableAttributedString(string: "已用完\n", attributes: attributes2)
//        
//        let attributedText3 = NSMutableAttributedString(string: "请先充值，再继续使用该服务！", attributes: attributes1)
//        
//        attributedText1.append(attributedText2)
//        attributedText1.append(attributedText3)
//        
//        TTSPointsAlert_2.show(sureAction: {[weak self] in
//            guard let self = self else { return  }
//            self.navigationController?.pushViewController(MemberPointsPayVC())
//        }, cancelAction: {
//            
//        }, attStr: attributedText1)
    }
}
