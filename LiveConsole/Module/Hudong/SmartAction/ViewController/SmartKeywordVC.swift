//
//  SmartKeywordVC.swift
//  LivePlus
//
//  Created by 郭炜 on 2025/1/13.
//

import UIKit

// MARK: - 关键词回复
class SmartKeywordVC: SmartBaseViewController {
    
    var model: AiKeywordVoiceReplyItemModel
    
    lazy var navigation: SmartNavigation = {
        let view = SmartNavigation()
        view.titleLabel.text = "根据关键词"
        return view
    }()
    
    /// 关键词回复
    lazy var keywordView: KeywordListView = {
        let view = KeywordListView()
        return view
    }()
    
    /// 语音回复内容
    lazy var audioReplyView: ReplayAudioView = {
        let view = ReplayAudioView()
        view.delegate = self
        return view
    }()
    
    var  isNew: Bool = false
    
    init(model: AiKeywordVoiceReplyItemModel, isNew: Bool = false) {
        self.isNew = isNew
        self.model = model
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        /// 需要重置代理 因为录制页面也会抢代理
        AudioPlayManager.shared.delegate = self.audioReplyView
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()

    }
    
    override func makeUI() {
        super.makeUI()
        
        view.addSubviews([navigation, keywordView, audioReplyView])
        
        navigation.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview()
            make.height.equalTo(LCDevice.Nav_H)
        }
        keywordView.snp.makeConstraints { make in
            make.top.equalTo(navigation.snp.bottom).offset(24)
            make.leading.trailing.equalToSuperview()
            make.height.equalTo(6 + 20 + 16 + 34 * 5 + 16)
        }
        audioReplyView.snp.makeConstraints { make in
            make.top.equalTo(keywordView.snp.bottom).offset(22)
            make.leading.trailing.equalToSuperview()
            make.bottom.equalToSuperview().inset(LCDevice.X_BOTTOM_INSET)
        }
    }

    override func business() {
        super.business()
        
        keywordView.questionButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self else { return }
                self.keywordQuestion()
            }.disposed(by: rx.disposeBag)
        
        audioReplyView.questionButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self else { return }
                self.audioReplyQuestion()
            }.disposed(by: rx.disposeBag)
        
        audioReplyView.recordButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self else { return }
                self.audioReplyPlusAction(addType: .record)
            }.disposed(by: rx.disposeBag)
        audioReplyView.combineButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self else { return }
                self.audioReplyPlusAction(addType: .aiGenerate)
            }.disposed(by: rx.disposeBag)
        audioReplyView.uploadButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self else { return }
                self.audioReplyPlusAction(addType: .importFile)
            }.disposed(by: rx.disposeBag)
        audioReplyView.deleteButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self else { return }
                self.audioReplyMinusAction()
            }.disposed(by: rx.disposeBag)
        
        /// 文字部分
        audioReplyView.textPlusButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self else { return }
                self.plusTextReplyAction()
            }.disposed(by: rx.disposeBag)
        audioReplyView.textEditButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self else { return }
                self.editTextReplyAction()
            }.disposed(by: rx.disposeBag)
        audioReplyView.textDeleteButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self else { return }
                self.endEditing()
            }.disposed(by: rx.disposeBag)
    }
    
    override func bindViewModel() {
        super.bindViewModel()
        
        self.keywordView.bind(to: model)
        self.audioReplyView.bind(to: model)
    }
    
    override func touchesBegan(_ touches: Set<UITouch>, with event: UIEvent?) {
        super.touchesBegan(touches, with: event)
        endEditing()

    }
    
    func endEditing() {
        self.keywordView.resignFirstResponder()
        self.view.endEditing(true)
    }
    
    
}

extension SmartKeywordVC: ReplayAudioViewDelegate {
    func endEdit() {
        endEditing()
    }
}
