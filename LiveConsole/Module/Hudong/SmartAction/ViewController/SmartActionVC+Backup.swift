//
//  SmartActionVC+Backup.swift
//  LivePlus
//
//  Created by simon on 8.2.25.
//

import Foundation


// MARK: - 备份和恢复
extension SmartActionVC: SmartActionNavigationDelegate {
    func didAction(type: SmartNavigationType) {
        //
        guard let _ = UserInfo.currentUser() else {
            loginApp()
            return
        }
        
        switch type {
        case .download:
            break
        case .upload:
            break
        case .bindOrUnbind:
            if !LCTools.checkMember() {
                return
            }
            remoteAction()
        case .memberPotins:
            if LCTools.checkMember() {
                self.navigationController?.pushViewController(MemberPointsVC())
            }
        case .on_offsmart:
            if !LCTools.checkMember() {
                return
            }
           
            willon_offsmart()
        }
    }
    
    func willon_offsmart() {
        // 如果没登录就登录
        if !isLogin {
            go_binddy()
            return
        }
        
        if professionalEnable {
            closeProfessionalEnable()
            return
        }
        
        HudongOpenLiveAlert.show(sureAction: {[weak self] in
            guard let self = self else { return }
            professionalEnable =  true
            self.navigation.on_offButton.isSelected = professionalEnable
            HUD.showSuccess("已开启")?.isUserInteractionEnabled = false
        }, cancelAction: {
            
        })
      
    }
    
    func closeProfessionalEnable() {
        AlertView.show(leftOption: .gray(title: "取消", action: {[weak self] in
            guard let self = self else { return }
            
        }), rightOption: .main(title: "停用", action: { [weak self] in
            guard let self = self else { return }
            self.professionalEnable =  false
            self.navigation.on_offButton.isSelected = professionalEnable
            self.willClose_tts_Nick()
        }), title: "确认停用智能互动吗？", message: "停用后互动回复将被停止")
    }
    
    
    func showVipAlert() {
        FreeAlert.show { [weak self] in
            guard let self = self else { return  }
            self.goMember()
        } cancelAction: {
            
        }
    }
    
    func goMember(){
        let ovc = MemberPointsPayVC()
        self.navigationController?.pushViewController(ovc)
    }
    
    func loginApp() {

        let loginCtrl = LoginVC()
        loginCtrl.needAutoBackAfterDone = true
        loginCtrl.popToRoot = true
        
        loginCtrl.loginSuccessBlock = {[weak self] in
            
        }
        
        self.navigationController?.pushViewController(loginCtrl)
        
    }
    
    func remoteAction() {
        if isLogin {
            AlertView.show(leftOption: .gray(title: "退出帐号", action: {[weak self] in
                guard let self = self else { return }
                self.go_unbind()
                self.isLogin = false
                self.willClose_tts_Nick()
                self.updateUserView()
            }), rightOption: .main(title: "取消", action: { [weak self] in
                
            }), title: "确认退出登录的帐号吗？", message: "退出后商品讲解、智能互动功能需重新授权")
            
          
        } else {
            go_binddy()
        }
        
    }
    

}



extension SmartActionVC: AIReplyDataManagerDelegate {
    
    func didBackup_znhd() {
        HUD.showWait()
        toolBar.bind(to: self.model)
        self.reloadData()
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.25) {
            HUD.hideAllHUD()
        }
    }
    
}
