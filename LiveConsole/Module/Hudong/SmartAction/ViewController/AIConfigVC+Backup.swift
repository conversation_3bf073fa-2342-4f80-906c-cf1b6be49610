//
//  AIConfigVC+Backup.swift
//  LiveConsole
//
//  Created by si<PERSON> on 15.5.25.
//

import Foundation

// 直播间备份内部的zip解压密码
let Backup_App_Key = "HBbZV^SMrwEz3VO@voiPJvqxn2hlgP56xndd"
// 外层的zip解压密码
let Backup_Zip_Key = "T2OKOBNgXOasBm$q03iwOXQH12&9B%dhJqb%"

//智能互动外部压缩的key
let Hudong_Zip_Key = "dWNoMXDEepEQMuSHNJvhShwk1LAPSYaL"
//智能互动配置内部压缩的key
let Hudong_App_Key = "9m3d6E8kLieqzkRMm2rjwTLxWEnSsGgm"

//智能讲解配置外部压缩的key
let Jiangjie_Zip_Key = "tDNXaCpw7QREevvDj8sybafwRZEnkxsp"
//智能讲解内部压缩的key
let Jiangjie_App_Key = "Qz6ZFdpBAtnybBhvpENeekJQYBsZkXFm"

enum ZIP_Type {
    case hudong
    case jiangjie
    
    /// 外层的key
    var zipKey: String {
        switch self {
        case .hudong:
            Hudong_Zip_Key
        case .jiangjie:
            Jiangjie_Zip_Key
        }
    }
    
    // 内层的key
    var appKey: String {
        switch self {
        case .hudong:
            Hudong_App_Key
        case .jiangjie:
            Jiangjie_App_Key
        }
    }
}

// MARK: - 备份和恢复
extension AIConfigVC {
    func didAction(type: SmartNavigationType) {
        
        switch type {
        case .download:
            willGoDownload()
        case .upload:
            goUpload()
        case .bindOrUnbind:
           break
        case .memberPotins:
           break
        case .on_offsmart:
            break
        }
    }
    
  
    func goUpload() {
        guard let interactionModel = AIReplyDataManager.shared.interactionModel else { return }
       let backup =  AIBackupModel(configModel: AIReplyDataManager.shared.aiConfig, dataSource: interactionModel)
        self.backupOutView.bind(to: backup)
        showPop(view: self.backupOutView, dismissOnBackgroundTouch: false)
    }
    
    func willGoDownload() {
        HudongImportAlertView.show { [weak self] in
            guard let self = self else { return }
            self.openFileAction()
        }
    }
    
    func openFileAction()  {
        let vc = UIDocumentPickerViewController(documentTypes: ["org.gnu.gnu-zip-archve", "org.gnu.gnu-zip-archive", "org.gnu.gnu-zip-tar-archive", "com.pkware.zip-archive"], in: .open)
        vc.delegate = self
        vc.modalPresentationStyle = .fullScreen
        self.present(vc, animated: true)
    }
    
    // 文件的沙盒路径
    func saveFileToPhone(path: String) {
        let url = URL(fileURLWithPath: path)
        let picker = UIDocumentPickerViewController(url: url, in: .exportToService)
        picker.delegate = self
        picker.modalPresentationStyle = .fullScreen
        self.present(picker, animated: true)
    }
}


// MARK: - 系统文件
extension AIConfigVC: UIDocumentPickerDelegate {
    
    func documentPickerWasCancelled(_ controller: UIDocumentPickerViewController) {
        controller.dismiss(animated: true)
    }
    
    func documentPicker(_ controller: UIDocumentPickerViewController, didPickDocumentsAt urls: [URL]) {
        // 把文件拷贝到沙盒
        guard let url = urls.first else { return }
        
        if controller.documentPickerMode  == .exportToService {
            LCLog.d("备份直播间已保存到:\(url)")
            HUD.showSuccess("已保存备份")
            dismissPop(view: backupOutView)
            return
        }
        LCTools.documentSmartPicker(url: url)
        controller.dismiss(animated: true)
        
    }

}

// MARK: - 导出的事件
extension AIConfigVC: AIBackupViewDelegate {
    
    /// 关闭
    func invokeClose() {
        self.dismissPop(view: self.backupOutView)
    }
    
    /// 开始导出
    func invokeStartExpose(model: AIBackupModel, title: String) {
        
        self.backupOutView.clear()
                
        // 更新页面 导出中
        self.backupOutView.updateShowingType(type: .pending)
        
        // 文件大小
        let fileSize = self.backupOutView.fileSize
        // 设置文件大小 启动计时器
        self.backupOutView.progressView.fileSize = fileSize
        self.backupOutView.progressView.startTimer()
        
        self.backupOutView.process(progress: 0.0, fileSize: fileSize)
        
        let zipQueue = DispatchQueue(label: "com.liveplus.ai.zip", qos: .default, attributes: .concurrent, autoreleaseFrequency: .inherit, target: nil)
        
        
        zipQueue.async {
            self.generaRoomZip(model: model, title: title, type: .hudong, queue: zipQueue, progress: { [weak self]  progress in
                guard let self = self else {  return  }
                DispatchQueue.main.async {
                    self.backupOutView.process(progress: progress, fileSize: fileSize)
                    LCLog.d("整体打包总进度：\(progress)")
                }
                
            }) {[weak self] zipPath in
                
                guard let self = self else {  return  }
                
                guard let zipPath = zipPath, !zipPath.isEmpty else {
                    DispatchQueue.main.async {
                        self.backupOutView.progressView.releaseTimer()
                        HUD.showFail("备份失败")
                        self.backupOutView.updateShowingType(type: .start)
                    }
                    
                    return
                }
                DispatchQueue.main.async {
                    self.backupOutView.process(progress: 1.0, fileSize: fileSize)
                    self.backupOutView.progressView.releaseTimer()
                    self.zipPath = zipPath
                    //  计算大小
                    if let zipPath = self.zipPath, let size = XCFileManager.sizeOfFile(atPath: zipPath) as? Int64 {
                        self.backupOutView.updateShowingType(type: .success, size: LCTools.formatFileSize(fileS: size), time: self.backupOutView.progressView.progressSecond)
                    }
                }
                
            }
        }
        
    }
    
    
    /// 保存文件到本地
    func invokeGotoLocalDocument() {
        // 把文件保存下来
        if let zipPath = self.zipPath {
            self.saveFileToPhone(path: zipPath)
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                HUD.showCustomView()
            }
        } else {
            HUD.showFail("暂无导出的直播间，请先导出直播间再保存")
        }
    }
    
    
}

extension AIConfigVC {
    
    /// 导出下层的zip包：room.zip + room.json
    func generaRoomZip(model: AIBackupModel, title: String, type: ZIP_Type, queue: DispatchQueue, progress: @escaping ((CGFloat) -> Void), completion: ((_ zipPath: String?) -> Void)? = nil) {
        guard let userInfo = UserInfo.currentUser(), let userId = userInfo.userId else {
            completion?(nil)
            return
        }

        // 生成两个key
        let timeStamp = LCTools.milliStamp()

        // 注意  title roomID 都是空
        var passwordKey = ("__\(timeStamp)_\(type.appKey)" as NSString).md5()?.uppercased() ?? ""
        passwordKey = passwordKey.sha1().uppercased()

        // 导出zip包
        self.exportRoomZip(model: model,
                           type: type,
                           passwordKey: passwordKey,
                           timeStamp: timeStamp,
                           title: title,
                           userId: "\(userId)",
                           queue: queue,
                           progress: { value in
            progress(value)
        }) { zipPath in
            print("打包完成，zip最终路径为：\(zipPath)")
            completion?(zipPath)
        }
        
    }
}

extension AIConfigVC {
    
    // 把系统相册的素材保存到APP内
    func exportPhotoSource(model: AIBackupModel, queue: DispatchQueue, completion: @escaping (() -> Void)) {
        
        let group = DispatchGroup()
        
        // 把本地的素材找到
        var localModels: [AreaItemModel] = []
        
        model.dataSource.cardList.forEach { card in
            if let replyItem = card.replyItem, let resources = replyItem.resources, !resources.isEmpty {
                
                resources.forEach { file in
                    let areaItem = file.toAreaItem()
                    if let name = areaItem.name, !name.isEmpty {
                        LCTools.copyLocalMaterialSource(name, toRoom: "", isAudio: areaItem.type == .musicLocal)
                    }
                }
            }
        }
        
       
        group.notify(queue: queue, execute: {
            completion()
        })
        
    }
    
    // 导出直播间的zip
    func exportRoomZip(model: AIBackupModel, type: ZIP_Type, passwordKey: String, timeStamp: String, title: String, userId: String, queue: DispatchQueue, progress: @escaping ((CGFloat) -> Void), completion: ((_ zipPath: String?) -> Void)? = nil){
                
        LCTools.removeBackupFolderAll()
        
        exportPhotoSource(model: model, queue: queue) { [weak self]  in
            guard let self = self else { return }
            // 素材回调0.1
            progress(0.1)
            // 素材压缩完成，才能继续
            self.createZipFile(model: model, type:type , passwordKey: passwordKey, timeStamp: timeStamp,title: title, userId: userId, progress: { value in
                progress(value)
            }) { zipPath in
                completion?(zipPath)
            }
        }
        
    }
    
    func createZipFile(model: AIBackupModel, type:ZIP_Type, passwordKey: String, timeStamp: String, title: String, userId: String,  progress: @escaping ((CGFloat) -> Void), complete: @escaping ((String) -> Void)) {
        let coverStartTime = CFAbsoluteTimeGetCurrent()

        let roomId = ""
     
        let jsonString = JsonTool.model2String(model)
        
        debugPrint("直播间的备份json: \(jsonString)")
        LCTools.saveRoomJson(with: roomId, and: jsonString)
        
        let filePath = LCTools.getRoomTempStoreFloder(withRoomId: roomId)
        let zipPath = LCTools.getBackupSourceDownloadFolder() + "/" + "room.zip"
        
        print("开始打包...\(passwordKey)")
       if SSZipArchive.createZipFile(atPath: zipPath, withContentsOfDirectory: filePath, keepParentDirectory: true, compressionLevel: Z_BEST_SPEED, password: passwordKey, aes: true, progressHandler: {entryNumber, total in
           let p = CGFloat(entryNumber)/CGFloat(total)
           LCLog.d("---------素材打包进度：\(p)")
           // 最外回调
           progress(0.1 + p * 0.4 )
           
        }) {
            // zipPath：/xx/xx/room.zip
            let endTime = CFAbsoluteTimeGetCurrent()
            LCLog.d("---------素材打包耗时：\(endTime - coverStartTime)")
            // 去生成 最终的zip: room.zip + config.json 打包成一个zip
            LCTools.removeBackupFolder(roomId: roomId) // 移除掉文件夹
            // 构建外层json
            let backupModel = RoomBackUpModel(title: "",
                                              roomId: roomId,
                                              time: timeStamp,
                                              minLevel: 1,
                                              userId: userId)
            let configjs = JsonTool.model2String(backupModel)
            LCTools.saveConfigJson(jsonString: configjs)
            
            // 构建外层zip
            let endFilePath = LCTools.getBackupSourceDownloadFolder()
            // 最外层zip的名称
            let endZipName = title
            let endZipPath = LCTools.getBackupSourceDownloadFolder() + "/" + endZipName
           GCDServices.asyncDelay(1.0) {
                
               if SSZipArchive.createZipFile(atPath: endZipPath, withContentsOfDirectory: endFilePath, keepParentDirectory: false, compressionLevel: Z_BEST_SPEED, password: type.zipKey, aes: true, progressHandler: { entryNumber, total in
                   
                    let p = CGFloat(entryNumber)/CGFloat(total)
                    LCLog.d("---------zip打包进度：\(p)")
                    // 最外回调
                    progress(0.5 + p * 0.4 )
                    
                }) {
                    // 移动到最后的备份文件夹
                    LCTools.copyBackUPZipToBackUpFolder(zipPath: endZipPath, zipName: endZipName)
                    LCTools.removeBackupFolderAll()
                    let coverEndTime = CFAbsoluteTimeGetCurrent()
                    LCLog.d("---------zip打包时间：\(coverEndTime - coverStartTime)")
                    let zipPath = LCTools.getBackupAllRoomsFolder() + "/" + endZipName
                    complete(zipPath)
                } else {
                    DispatchQueue.main.async {
                        HUD.showFail("打包失败，请确认直播间的名称是否含有特殊字符")
                    }
                    complete("")
                }
            }
        }
    }
    
}

