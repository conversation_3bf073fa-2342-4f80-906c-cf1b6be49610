//
//  SmartCloneEditVC.swift
//  LivePlus
//
//  Created by 郭炜 on 2025/2/10.
//

import UIKit

// MARK: - 克隆音色新增
class SmartCloneEditVC: SmartBaseViewController {
    
    lazy var navigation: SmartNavigation = {
        let view = SmartNavigation()
        view.titleLabel.text = "克隆音色"
        view.balanceWordLabel.isHidden = false
        return view
    }()
    
    /// 提示文案部分
    lazy var tipView: SmartCloneRecordTopView = {
        let view = SmartCloneRecordTopView()
        return view
    }()
    
    /// 录音按钮操作部分
    lazy var bottomView: SmartCloneRecordBotView = {
        let view = SmartCloneRecordBotView()
        view.delegate = self
        view.limitSecond = 1000
        return view
    }()
    
    
    lazy var step1View: CloneStepView = {
        let view = CloneStepView()
        view.iconView.image = UIImage(named: "ic_clone_step1")
        view.titleLabel.text = "录音"
        return view
    }()
    
    lazy var step2View: CloneStepView = {
        let view = CloneStepView()
        view.iconView.image = UIImage(named: "ic_clone_step2")
        view.titleLabel.text = "请输入100字以内文案以试听合成音频"
        view.titleLabel.textColor = UIColor.white.alpha(value: 0.4)
        return view
    }()
    
    lazy var lineView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.white.alpha(value: 0.4)
//        view.backgroundColor = UIColor("#22E9B9")
        return view
    }()
    
    // 试听部分
    lazy var clone3View: SmartClone3View = {
        let view = SmartClone3View()
        view.isHidden = true
        view.delegate = self
        return view
    }()
    
    
    lazy var cloneButton: UIButton = {
        let button = UIButton()
        
        
        button.setTitleColor(.white, for: .normal)
        button.titleLabel?.font = .systemFont(ofSize: 18, weight: .bold)
        button.cornerRadius = 25
        button.addTarget(self, action: #selector(cloneAction), for: .touchUpInside)
        button.applyGradient()
        button.isHidden = true
        button.isEnabled = false
        button.alpha = 0.6
        let paraph = NSMutableParagraphStyle()
        paraph.lineSpacing = 6
        
        let attributes1 = [NSAttributedString.Key.paragraphStyle: paraph, NSAttributedString.Key.foregroundColor: UIColor.white, NSAttributedString.Key.font: UIFont.systemFont(ofSize: 18, weight: .bold)]
                
        let attributes2 = [NSAttributedString.Key.paragraphStyle: paraph, NSAttributedString.Key.foregroundColor: UIColor.white, NSAttributedString.Key.font: UIFont.systemFont(ofSize: 16, weight: .regular)]
        
        let attributedText1 = NSMutableAttributedString(string: "开始克隆", attributes: attributes1)
        
        let attributedText2 = NSMutableAttributedString(string: "（本次预计消耗", attributes: attributes2)
        
        let attributedText3 = NSMutableAttributedString(string: " 5 ", attributes: attributes1)
        
        let attributedText4 = NSMutableAttributedString(string: "积分）", attributes: attributes2)
        
        attributedText1.append(attributedText2)
        attributedText1.append(attributedText3)
        attributedText1.append(attributedText4)
        button.setAttributedTitle(attributedText1, for: .normal)
        return button
    }()
        
    //  所有的文本
    var texts:[AudioCloneTextModel]?
    
    var textModel:AudioCloneTextModel?
    
    var audioUrl: URL?
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
    }
    
    override func viewDidDisappear(_ animated: Bool) {
        super.viewDidDisappear(animated)
        AudioPlayManager.shared.stop()
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        fd_interactivePopDisabled = true
        
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        self.navigation.balanceWordLabel.text = "积分:\(UserInfo.points)"
    }
    
    @objc func cloneAction() {
        //
        self.clone3View.resignFirstResponder()
            
        guard let textModel = textModel else {
            HUD.showFail("无录音文本")
            return
        }
        
        guard let audioUrl = audioUrl else {
            HUD.showFail("请先录制音频")
            return
        }
        
        guard let text = self.clone3View.textView.text, text.count > 9 else {
            HUD.showFail("请输入至少10个字")
            return
        }
        
        // 积分校验
        if UserInfo.points < UserInfo.pointsClone() {
            showPointsAlert(count1: UserInfo.points, count2: UserInfo.pointsClone())
            return
        }
        
        if UserDefaults.standard.bool(forKey: LCKey.UD_Point_Alert_Clone) {
            self.goClone(text: text, url: audioUrl, textModel: textModel)
            return
        }
        // 积分消耗提示
        showCloneAlert(text: text, url:audioUrl, textModel: textModel)

    }
    
    func showPointsAlert(count1: Int64, count2: Int64) {
        let paraph = NSMutableParagraphStyle()
        paraph.lineSpacing = 6
        
        let attributes1 = [NSAttributedString.Key.paragraphStyle: paraph, NSAttributedString.Key.foregroundColor: UIColor("#1E1F20"), NSAttributedString.Key.font: UIFont.systemFont(ofSize: 16, weight: .regular)]
        
        let attributes2 = [NSAttributedString.Key.paragraphStyle: paraph, NSAttributedString.Key.foregroundColor: UIColor("#6763F6"), NSAttributedString.Key.font: UIFont.systemFont(ofSize: 36, weight: .bold)]
        
        let attributes3 = [NSAttributedString.Key.paragraphStyle: paraph, NSAttributedString.Key.foregroundColor: UIColor("#6763F6"), NSAttributedString.Key.font: UIFont.systemFont(ofSize: 16, weight: .medium)]
        
        let attributedText1 = NSMutableAttributedString(string: "本次克隆需消耗", attributes: attributes1)
        
        let attributedText2 = NSMutableAttributedString(string: "\n\(count2)", attributes: attributes2)
        
        let attributedText3 = NSMutableAttributedString(string: "   积分", attributes: attributes1)
        
        let attributedText4 = NSMutableAttributedString(string: "\n ", attributes: attributes2)
        
        let attributedText5 = NSMutableAttributedString(string: "您当前仅剩\(count1)积分 ", attributes: attributes1)

        let attributedText6 = NSMutableAttributedString(string: "已不足抵扣\n", attributes: attributes3)
        
        let attributedText7 = NSMutableAttributedString(string: "请先充值，再继续使用该服务！", attributes: attributes1)
        
        
        attributedText1.append(attributedText2)
        attributedText1.append(attributedText3)
        attributedText1.append(attributedText4)
        attributedText1.append(attributedText5)
        attributedText1.append(attributedText6)
        attributedText1.append(attributedText7)
        
        TTSPointsAlert_2.show(sureAction: {[weak self] in
            guard let self = self else { return  }
            self.goPayPointsVC()
        }, cancelAction: {
            
        }, attStr: attributedText1)
    }

    
    func showCloneAlert(text: String, url: URL, textModel:AudioCloneTextModel) {
        let paraph = NSMutableParagraphStyle()
        paraph.lineSpacing = 6
        
        let attributes1 = [NSAttributedString.Key.paragraphStyle: paraph, NSAttributedString.Key.foregroundColor: UIColor("#1E1F20"), NSAttributedString.Key.font: UIFont.systemFont(ofSize: 16, weight: .regular)]
        
        let attributes2 = [NSAttributedString.Key.paragraphStyle: paraph, NSAttributedString.Key.foregroundColor: UIColor("#6763F6"), NSAttributedString.Key.font: UIFont.systemFont(ofSize: 36, weight: .bold)]
        
        let attributes3 = [NSAttributedString.Key.paragraphStyle: paraph, NSAttributedString.Key.foregroundColor: UIColor("#1E1F20"), NSAttributedString.Key.font: UIFont.systemFont(ofSize: 18, weight: .medium)]
        
        let attributedText1 = NSMutableAttributedString(string: "本次克隆需消耗", attributes: attributes1)
        
        let attributedText2 = NSMutableAttributedString(string: "\n5", attributes: attributes2)
        
        let attributedText3 = NSMutableAttributedString(string: "   积分", attributes: attributes1)
        
        let attributedText4 = NSMutableAttributedString(string: "\n ", attributes: attributes2)
        
        let attributedText5 = NSMutableAttributedString(string: "您确定开始克隆吗？", attributes: attributes3)
                
        
        attributedText1.append(attributedText2)
        attributedText1.append(attributedText3)
        attributedText1.append(attributedText4)
        attributedText1.append(attributedText5)
        
        TTSPointsAlert_3.show(sureAction: {[weak self] in
            guard let self = self else { return  }
            self.goClone(text: text, url: url, textModel: textModel)
        }, cancelAction: {
            
        }, attStr: attributedText1)
    }
    
    func goPayPointsVC() {
        self.navigationController?.pushViewController(MemberPointsPayVC())
    }
    
    
    
    override func makeUI() {
        super.makeUI()
        
        view.addSubviews([navigation, tipView, bottomView,clone3View, cloneButton, step1View, step2View, lineView])
        
        navigation.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview()
            make.height.equalTo(LCDevice.Nav_H)
        }
        tipView.snp.makeConstraints { make in
            make.top.equalTo(navigation.snp.bottom).offset(40)
            make.trailing.equalToSuperview()
            make.leading.equalToSuperview().inset(28)
            make.height.equalTo(300)
        }
        
        bottomView.snp.makeConstraints { make in
            make.height.equalTo(200)
            make.trailing.equalToSuperview()
            make.leading.equalToSuperview().inset(28)
            make.top.equalTo(tipView.snp.bottom).offset(20)
        }
        
        clone3View.snp.makeConstraints { make in
            make.height.equalTo(230)
            make.trailing.equalToSuperview()
            make.leading.equalToSuperview().inset(28)
            make.top.equalTo(step2View.snp.bottom).offset(20)
        }
        
        cloneButton.snp.makeConstraints { make in
            make.height.equalTo(50)
            make.trailing.leading.equalToSuperview().inset(24)
            make.bottom.equalToSuperview().inset(LCDevice.BOTTOM_BAR)
        }
        
        
        step1View.snp.makeConstraints { make in
            make.height.equalTo(24)
            make.leading.equalToSuperview()
            make.top.equalTo(navigation.snp.bottom).offset(12)
        }
        
        lineView.snp.makeConstraints { make in
            make.width.equalTo(1.5)
            make.leading.equalToSuperview().inset(27)
            make.top.equalTo(step1View.snp.bottom).offset(4)
            make.bottom.equalTo(bottomView.snp.bottom)
        }
        
        step2View.snp.makeConstraints { make in
            make.height.equalTo(24)
            make.leading.equalToSuperview()
            make.top.equalTo(lineView.snp.bottom).offset(4)
        }
        
    }

    override func touchesBegan(_ touches: Set<UITouch>, with event: UIEvent?) {
        super.touchesBegan(touches, with: event)
        self.clone3View.endEditing(true)
    }
    
    override func business() {
        super.business()
        
        MiddleRequestNet.getCloneAudioTexts { [weak self] texts in
            guard let self = self else { return }
            self.texts = texts
            self.textModel = texts?.randomElement()
            self.tipView.textView.text = self.textModel?.content ?? "这段时间天气不错，晚上散散步，吹吹风，听听音乐，心情好了不少，真舒服啊!"
        }
        self.tipView.huanButton.addTarget(self, action: #selector(huanAction), for: .touchUpInside)

    }
    
    @objc func huanAction() {
        self.textModel = texts?.randomElement()
        self.tipView.textView.text = self.textModel?.content ?? "这段时间天气不错，晚上散散步，吹吹风，听听音乐，心情好了不少，真舒服啊!"
    }
    
    
    override func bindViewModel() {
        super.bindViewModel()
        
        // 添加右滑手势
        let panGesture = UIPanGestureRecognizer(target: self, action: #selector(handlePanGesture(_:)))
        view.addGestureRecognizer(panGesture)

    }
    
    /// 防止直接返回了
    func hookBackAction() {
        if AudioRecordManager.shared.currentState == .recording { return }
        guard let fileUrl = bottomView.fileUrl else {
            AppDelegate.curDisplayVC().navigationController?.popViewController()
            return
        }
        AlertView.show(leftOption: .gray(title: "取消返回", action: {
            AppDelegate.curDisplayVC().navigationController?.popViewController()
            AudioRecordManager.shared.deleteCurrentRecording()
        }), rightOption: .main(title: "确定使用", action: {
            // TODO: 音色生成
            self.actionForRecordFinish(url: fileUrl)
            AppDelegate.curDisplayVC().navigationController?.popViewController()
        }), title: "是否使用该语音？", message: "")
    }
    
    @objc private func handlePanGesture(_ gesture: UIPanGestureRecognizer) {
        // 获取手势的横向移动距离
        let translation = gesture.translation(in: view)
        
        if gesture.state == .changed {
            // 如果向右滑动超过50点
            if translation.x > 50 {
                // 重置手势状态,避免重复触发
                gesture.isEnabled = false
                gesture.isEnabled = true
                
                // 触发返回逻辑
                hookBackAction()
            }
        }
    }
    
    func goStep3() {
        self.tipView.isHidden = true
        self.tipView.snp.updateConstraints { make in
            make.height.equalTo(0)
        }
        
        self.bottomView.isHidden = true
        self.bottomView.snp.updateConstraints { make in
            make.height.equalTo(0)
        }
        
        self.clone3View.isHidden = false
        self.step1View.titleLabel.textColor = .white
        self.step1View.iconView.image = UIImage(named: "ic_clone_step1_ok")
        
        self.step2View.titleLabel.textColor = .white
        self.step2View.iconView.image = UIImage(named: "ic_clone_step2_ok")
        
        self.lineView.backgroundColor =  UIColor("#22E9B9")
        
        self.cloneButton.isHidden = false
    }
    
    func goClone(text: String, url: URL, textModel:AudioCloneTextModel) {
        
        
        /// 1、上传到bucket中
        DownloadFromBucket.upload(fileUrl: url) { [weak self] fileName in
            guard let self = self else { return }
            guard let fileName = fileName else {
                DispatchQueue.main.async {
                    HUD.showFail("训练失败")
                }
                return
            }
            
            /// 2、训练音色
            MiddleRequestNet.uploadCloneAudio(name: "",
                                              spkId: textModel.id,
                                              bucketFileName: fileName,
                                              text: text) { [weak self] result in
                guard let self = self else { return }
                DispatchQueue.main.async {
                    if let result = result {
                        HUD.showSuccess("已提交克隆任务，请耐心等待")
                        self.navigationController?.popViewController()
                    }
                }
            }
        }
    }

}

extension SmartCloneEditVC: SmartRecordBotViewDelegate {
    /// 处理完成后的保存逻辑
    func actionForRecordFinish(url: URL) {
        self.audioUrl = url
        self.goStep3()
    }
}


extension SmartCloneEditVC: SmartClone3ViewDelegate {
    func textViewDidChange(count: Int) {
        self.cloneButton.isEnabled = count > 0
        
        if count >= 10 {
            self.cloneButton.alpha = 1.0
        } else {
            self.cloneButton.alpha = 0.6
        }
        
    }
    
    
}
