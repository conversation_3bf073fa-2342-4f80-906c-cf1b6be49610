//
//  SmartKeywordVC+TextReplay.swift
//  LiveConsole
//
//  Created by 郭炜 on 2025/4/23.
//

import UIKit

// MARK: - 文字部分的操作
extension SmartKeywordVC {
    
    /// 添加文字回复内容
    func plusTextReplyAction() {
        // 只能添加5个
        if  self.model.itemType == .text, self.model.itemCount >= 5 {
            HUD.showFail("文字回复数量已达上限")
            return
        }
        
        let view = ReplyTextPlusView()
        view.confirmCallback = { [weak self] text in
            guard let self = self else { return }
            guard let text = text, !text.isEmpty else { return }
            let resFileModel = ResFileModel(path: nil, sourceType: .text, text: text)
            self.model.replyItem?.resources?.append(resFileModel)
            self.model.modifyUpdateTime()
            // 刷新页面
            self.audioReplyView.updateTable()
            if isNew {
                AIReplyDataManager.shared.addNewCardItem(replyItem: self.model)
            }
        }
        view.show(title: "添加新内容", in: self.view, replyType: .keyword)
    }
    
    /// 修改文字回复内容
    func editTextReplyAction() {
        guard let selectionModel = self.audioReplyView.selectionModel else {
            HUD.showFail("请选择要编辑的文本")
            return
        }
        let view = ReplyTextPlusView()

        view.confirmCallback = { [weak self] text in
            guard let self = self else { return }
            guard let text = text, !text.isEmpty else { return }
            selectionModel.text = text
            self.model.modifyUpdateTime()
            // 刷新页面
            self.audioReplyView.updateTable()
        }
        view.show(title: "编辑内容", in: self.view, text: selectionModel.text ?? "", replyType:  .keyword)
    }
}
