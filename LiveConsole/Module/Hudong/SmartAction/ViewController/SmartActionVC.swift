//
//  SmartActionVC.swift
//  LivePlus
//
//  Created by 郭炜 on 2025/1/10.
//

import UIKit
import WebKit
import RxSwift

// MARK: - 智能互动首页
class SmartActionVC: SmartBaseViewController {
    
    var currentTask: Task<Void, Never>?
    
    // 智能互动的开关 这个开关在nav 上
    var professionalEnable: Bool = false
    
    var isLogin: Bool = false 
    
    var isLiving: Bool = false
    
    lazy var timeView: SystemReplyTypeView = {
        let v = SystemReplyTypeView(frame: CGRect(x: 0, y: 0, width: LCDevice.screenW, height: LCDevice.screenH))
        v.isHidden = true
        v.delegate = self
        return v
    }()
    
    
    lazy var tableView: UITableView = {
        let tableView = UITableView()
        tableView.register(cellWithClass: SmartActionCardView.self)
        tableView.delegate = self
        tableView.dataSource = self
        tableView.separatorStyle = .none
        tableView.backgroundColor = .clear
        return tableView
    }()
    
    // 实时朗读的view 
    lazy var realtimeView: LangduNickView = {
        let v = LangduNickView()
        v.delegate = self
        return v
    }()
    
    
    var ttsTimer: Timer?
    
    // 总计消耗的积分 开启一次消耗的总积分
    var sumPoints: Int64 = 0
    
    // 导航栏
    lazy var navigation: SmartActionNavigation = {
        let view = SmartActionNavigation()
        view.delegate = self
        return view
    }()
    
    private lazy var speView: UIView = {
        let stack = UIView()
        stack.backgroundColor = UIColor("#282828")
        return stack
    }()
    
    // 顶部的工具条
    lazy var toolBar: SmartActionToolBar = {
        let view = SmartActionToolBar()
        view.delegate = self
        return view
    }()
    
    // 空视图
    lazy var smartEmptyImage: UIImageView = {
        let imageView = UIImageView(image: UIImage(named: "ic_smart_empty"))
        return imageView
    }()
    
    
    private lazy var logoutView: BuyinEmptyView = {
        let button = BuyinEmptyView()
        button.loginButton.addTarget(self, action: #selector(goLoginAction), for: .touchUpInside)
        button.isHidden = true
        return button
    }()
    
    /// 互动模型
    var model: InteractionModel {
        if let m = AIReplyDataManager.shared.interactionModel {
            return m
        }
        let m = InteractionModel()
        AIReplyDataManager.shared.interactionModel = m
        return m
    }
    
    var dataSource: [AiKeywordVoiceReplyItemModel] {
        
        var items: [AiKeywordVoiceReplyItemModel]  = self.model.cardList.filter({$0.interactionType == self.model.interactionType})
        
        // 取出选的类型 如果是系统互动的  还有二级选项
        if model.interactionType == .systemInteraction {
            if !model.isAllSystemInteraction, let type = model.systemInteractionTypeList.first {
                items = items.filter({$0.systemInteractionType == type})
            }
        } 
        
        
       // 预览的类型
        switch model.previewType {
        case .all:
            break
        case .onlyEffective:
            items = items.filter({$0.enable == true})
        case .onlyInvalid:
            items = items.filter({$0.enable == false})
        }
        
        // 排序
        switch model.sortType {
        case .createTime:
            switch model.orderType {
            case .aes:
                items = items.sorted { itm1, itm2 in
                    return itm1.createTime < itm2.createTime
                }
            case .des:
                items = items.sorted { itm1, itm2 in
                    return itm1.createTime > itm2.createTime
                }
            }
        case .updateTime:
            switch model.orderType {
            case .aes:
                items = items.sorted { itm1, itm2 in
                    return itm1.updateTime < itm2.updateTime
                }
            case .des:
                items = items.sorted { itm1, itm2 in
                    return itm1.updateTime > itm2.updateTime
                }
            }
        }
        return items
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        fd_interactivePopDisabled = true
        
        UIApplication.shared.isIdleTimerDisabled = true
        AIReplyDataManager.shared.delegate = self
        setupDanmu()
        
        updateRealtimeView()
    }
    
    
   
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        toolBar.bind(to: self.model)
        self.reloadData()
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        AIReplyDataManager.shared.saveToDataBase()
    }
    
    override func makeUI() {
        super.makeUI()
        
        view.addSubviews([navigation, toolBar, realtimeView,tableView, smartEmptyImage, logoutView])
        
        navigation.addSubviews([speView])
        speView.snp.makeConstraints { make in
            make.bottom.leading.trailing.equalToSuperview()
            make.height.equalTo(1.5)
        }
        
        navigation.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview()
            make.height.equalTo(LCDevice.Nav_H)
        }
        toolBar.snp.makeConstraints { make in
            make.top.equalTo(navigation.snp.bottom).offset(4)
            make.leading.trailing.equalToSuperview()
            make.height.equalTo(44)
        }
        
        realtimeView.snp.makeConstraints { make in
            make.top.equalTo(toolBar.snp.bottom).offset(12)
            make.leading.trailing.equalToSuperview().inset(16)
            make.height.equalTo(52)
        }
        
        tableView.snp.makeConstraints { make in
            make.top.equalTo(realtimeView.snp.bottom).offset(12)
            make.leading.trailing.bottom.equalToSuperview()
        }
        
        smartEmptyImage.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.size.equalTo(140)
        }
        
        logoutView.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(LCDevice.Nav_H)
            make.leading.trailing.bottom.equalToSuperview()
        }
    }
    
    override func business() {
        super.business()
        
    }
    
    override func bindViewModel() {
        super.bindViewModel()
        
    }
    
    func reloadData(){
        if let _ =  UserInfo.currentUser() {
            self.smartEmptyImage.isHidden = !self.dataSource.isEmpty
            self.tableView.reloadData()
            self.logoutView.isHidden = true
        } else {
            self.logoutView.isHidden = false
            self.smartEmptyImage.isHidden = true
            self.tableView.reloadData()
        }
       
    }
    
    @objc func goLoginAction() {
        // 如果未登录APP 就先登录APP
        guard let _ = UserInfo.currentUser() else {
            loginApp()
            return
        }
        
    }
}

extension SmartActionVC: UITableViewDelegate, UITableViewDataSource {
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return self.dataSource.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {

        let cell = tableView.dequeueReusableCell(withClass: SmartActionCardView.self)
        let model = dataSource[indexPath.row]
        cell.delegate = self
        cell.bind(to: model)
        
        return cell
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 194
    }
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        
    }
    
}


extension SmartActionVC: SmartActionCardViewDelegate {
    func didAction(to action: SmartActionCardViewAction, model: AiKeywordVoiceReplyItemModel) {
        
        switch action {
            
        case .edit:
            switch model.interactionType {
            case .userChat:
                let viewController = SmartKeywordVC(model: model)
                self.navigationController?.pushViewController(viewController, animated: true)
            case .systemInteraction:
                switch model.systemInteractionType {
                    
                case .gift:
                    let vc  = AIGiftReplyVc(model: model)
                    self.navigationController?.pushViewController(vc)

                case .follow:
                    let vc  = AIFollowReplyVc(model: model)
                    self.navigationController?.pushViewController(vc)
                }
                
            }
        case .delet:
            var  title: String = "要删该用户吗？"
            
            switch model.interactionType {
                
            case .userChat:
                title = "要删除该用户评论回复吗？"
            case .systemInteraction:
                switch model.systemInteractionType {
                case .gift:
                    title = "要删除该用户礼物回复吗？"
                case .follow:
                    title = "要删除该用户关注回复吗？"
                }
            }
            AlertView.show(leftOption: .gray(title: "取消", action: nil), rightOption: .main(title: "确定", action: { [weak self] in
                guard let self = self else { return }
                self.model.cardList.removeAll(where: {$0.id == model.id})
                self.reloadData()
            }), title: title, message: "删除后不可恢复")
            
        case .on_off:
            model.enable = !model.enable
            
            self.reloadData()
        }
        
    }
}
