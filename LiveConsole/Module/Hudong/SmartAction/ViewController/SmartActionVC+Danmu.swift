//
//  SmartActionVC+Danmu.swift
//  LiveConsole
//
//  Created by simon on 22.4.25.
//

import Foundation
import UIKit
import WebKit

extension SmartActionVC {
    
    func setupDanmu() {
        LCSingleton.shared.webDelegate = self
        
        NotificationCenter.default.addObserver(self, selector: #selector(userLogoutNoticeAction), name: LCKey.noti_logout, object: nil)

        
        /// 配置自动回复
        self.setupAutoReplyConfigs()
        
        self.requestAnchorInfo { [weak self] login in
            guard let self = self else { return }
            if login {
                // 如果登录了开始监听
                /// 监听主播开播状态
                LiveStatusListen.shared.startListening(interval: 3)
                self.listenAnchorDanmu()
            }
            self.isLogin = login
            self.updateUserView()
        }
        
        NotificationKey.add(.LoginExpiration) { [weak self] notification in
            guard let self = self else { return }
            self.logoutAndCleanCookies()
            LiveStatusListen.shared.stopListening()
            if self.isLogin {
                HUD.showFail("授权过期，请重新登录")
            }
            self.isLogin = false
        }
        
        NotificationKey.add(.livingStatusChanged) { [weak self] notification in
            guard let self = self, let isliving = notification.object as? Bool else { return }
            
            // 如果关播了 需要主动停止tts 的昵称合成
            if self.isLiving, !isliving, AIReplyDataManager.shared.ttsNickenable {
                self.willClose_tts_Nick()
                self.showStopLivingAlert()
            }
            self.isLiving = isliving
        }
    }
    
    @objc func userLogoutNoticeAction() {
        professionalEnable = false
        self.navigation.on_offButton.isSelected = professionalEnable
        // 关闭昵称朗读
        willClose_tts_Nick()
    }
    
    func updateUserinfo() {
        self.requestAnchorInfo { [weak self] login in
            guard let self = self else { return }
            LCSingleton.shared.qrView.isHidden = login
            if !login {
                self.logoutAndCleanCookies()
                LiveStatusListen.shared.stopListening()
                self.willClose_tts_Nick()
            } else {
                LiveStatusListen.shared.startListening(interval: 3)
                self.listenAnchorDanmu()
            }
            self.isLogin = login
            self.updateUserView()
        }
    }
    
    func updateUserView() {
        if isLogin {
            self.navigation.statusButton.setImage(UIImage(named: "ic_smart_connect"), for: .normal)
            let name: String = nickName ?? "           "
            
            self.navigation.statusButton.setTitle(String(name.prefix(5)), for: .normal)
            self.navigation.statusButton.setTitleColor(UIColor.white, for: .normal)
            
            self.navigation.statusButton.snp.updateConstraints { make in
                make.width.equalTo(100)
            }
        } else {
            self.navigation.statusButton.setImage(UIImage(named: "ic_smart_disconnect"), for: .normal)
            self.navigation.statusButton.setTitle("未登录", for: .normal)
            self.navigation.statusButton.setTitleColor(UIColor("#FFFFFF").alpha(value: 0.6), for: .normal)
            self.navigation.statusButton.snp.updateConstraints { make in
                make.width.equalTo(80)
            }
            self.professionalEnable = false
            self.navigation.on_offButton.isSelected = false
        }
    }
}


extension SmartActionVC {
    
    func go_binddy(){
        LCSingleton.shared.reloadWebView()
        LCSingleton.shared.showWebLogin(isHidden: false)
    }
    
    func go_unbind() {
        logoutAndCleanCookies()
        HUD.showSuccess("已解绑")?.isUserInteractionEnabled = false
    }

    /// 退出登录以及清空cookie
    func logoutAndCleanCookies() {
        let websiteDataTypes = WKWebsiteDataStore.allWebsiteDataTypes()
        let dateFrom = Date(timeIntervalSince1970: 0)
        
        WKWebsiteDataStore.default().removeData(
            ofTypes: websiteDataTypes,
            modifiedSince: dateFrom
        ) {
            print("已清除数据缓存")
        }
        
        // 清除所有 cookies
        let cookieStore = WKWebsiteDataStore.default().httpCookieStore
        cookieStore.getAllCookies { cookies in
            cookies.forEach { cookie in
                cookieStore.delete(cookie)
            }
        }
        
        // 清除 URLCache
        URLCache.shared.removeAllCachedResponses()
        
        // 清除 HTTPCookieStorage
        if let cookies = HTTPCookieStorage.shared.cookies {
            cookies.forEach { cookie in
                HTTPCookieStorage.shared.deleteCookie(cookie)
            }
        }
        CookieInfo.shared.clearCookies()
    }
}

extension SmartActionVC: LCSingletonWebDelegate {
    func webViewDidSuccess(cookies: [HTTPCookie]) {
        LCLog.d("授权成功")
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.7) {[weak self ] in
            self?.updateUserinfo()
            /// 监听弹幕
            self?.listenAnchorDanmu()
        }
       
    }
    
    func webViewDidFail(error: any Error) {
        self.updateUserinfo()
    }
    
}




/// 监听弹幕数据
extension SmartActionVC {
    
    /// 监听弹幕数据
    func listenAnchorDanmu() {
        print("---弹幕---开始监听")
        stopListening() // 确保之前的任务被清除
        
        // 创建新的任务
        currentTask = Task { [weak self] in
            guard let self = self else { return }
            
            print("---弹幕---Task 开始执行")
            while !Task.isCancelled {
                print("---弹幕---开始新的循环")
                if roomID == nil {
                    print("---弹幕---等待房间ID...")
                    // 当没有roomID时，等待1秒再检查
                    try? await Task.sleep(nanoseconds: UInt64(2 * 1_000_000_000))
                    continue // 继续下一次循环
                }
                
                do {
                    // 打开开关才会执行
                    if self.professionalEnable {
                        print("---弹幕---开始请求数据")
                        let response: MessageListResponse = try await RequestManager.shared.requestApi(
                            style: .message(
                                roomId: roomID!, // 这里可以安全解包，因为已经检查过nil
                                cursor: cursor,
                                last_rtt: last_rtt,
                                internal_ext: internal_ext
                            )
                        )
                        
                        cursor = response.data.cursor
                        last_rtt = response.data.currRtt
                        internal_ext = response.data.internalExt
                        //                        print("---弹幕---获取到房间弹幕111：\(response.data.messages)")
                        let data = Data(bytes: response.data.payload)
                        if let messages = DouyinDecode.shared.decodeMessage(data: data) {
                            let roomMessages = messages.filter { $0.type != .ONLINE }
                            print("---弹幕---获取到房间弹幕：\(roomMessages)")
                            self.sendMessage(messages: roomMessages)
                        }
                    }
                    
                    // 随机延迟5-10秒
                    let randomDelay = Double.random(in: 5...10)
                    print("---弹幕---等待 \(randomDelay) 秒后继续")
                    try await Task.sleep(nanoseconds: UInt64(randomDelay * 1_000_000_000))
                    
                } catch {
                    print("---弹幕---获取直播间弹幕失败: \(error)")
                    // 发生错误时等待2秒再重试
                    try? await Task.sleep(nanoseconds: UInt64(2 * 1_000_000_000))
                }
            }
            print("---弹幕---Task 结束执行")
        }
    }
    
    // 停止监听
    func stopListening() {
        currentTask?.cancel()
        currentTask = nil
    }
    
    func resetDatas() {
        roomID = nil
        cursor = nil
        last_rtt = nil
        internal_ext = nil
    }
}

var messageCount: Int = 0

// MARK: - 发送弹幕逻辑
extension SmartActionVC {
    
    /// 发送弹幕
    /// 关键词回复直接发送
    /// 礼物和关注 需要合并发送
    func sendMessage(messages: [LiveMessage]) {
        print("发送弹幕：\(messages)")
        
        messageCount += 1
        
        /// 构建真正需要发送的数据
        var willSendMessages: [LiveMessage] = []
        
        for message in messages {
            
            let messageItem = message.toMessageItem
            
            if let nickName = nickName,
               !nickName.isEmpty,
               let messageNickName = message.nickName,
               messageNickName.contains(nickName) {
                print("---弹幕---主播自己的消息 忽略")
            }
            
            switch message.type {
            case .GIFT: // 单独处理礼物
                if LiveMessageManager.shared.isMessageProcessed(message) {
                    print("---弹幕---跳过重复消息：\(message.data)")
                    continue
                }
                print("---弹幕=== \(messageCount) --- \(messageItem.item.originMsg ?? "")")

                // 在处理消息前就标记为已处理
                LiveMessageManager.shared.markMessageAsProcessed(message)
                willSendMessages.append(message)
            default:
                print("---弹幕=== \(messageCount) --- \(messageItem.item.originMsg ?? "")")

                willSendMessages.append(message)
            }
        }
        
        innerSendMessage(messages: willSendMessages)
    }
    
    func innerSendMessage(messages: [LiveMessage]) {
        
        let models = messages.map { $0.data }
        
        /// Chat类型直接发送
        let replyModels = models.filter { $0.msgType == .CHAT}
        for model in replyModels {
            AutoReplyManager.shared.enqueue(model)
        }
        
        // 礼物
        let giftModels = models.filter { $0.msgType == .GIFT }
        
        Task {
            await handlerGiftMessages(messageList: giftModels) {[weak self] model in
                DispatchQueue.global().asyncAfter(deadline: .now() + 0.5) {
                    print("giftmodel: \(model.user?.nickName)")
                    print("giftmodel: \(model.giftInfo?.gift?.diamondCount)")
                    self?.checkNeedReply(model)
                }
            }
        }

        let followModels = models.filter { $0.msgType == .SOCIAL }
        handlerFollowMessages(messageList: followModels) {[weak self] model in
            print("model: \(model)")
            DispatchQueue.global().asyncAfter(deadline: .now() + 0.5) {
                self?.checkNeedReply(model)
            }
        }
    }
        
    // 触发逻辑
    func sendReplyItem(item: AiKeywordVoiceReplyItemModel) {
        // 如果不是会员就不执行
        if !UserInfo.isMember {
            return
        }
        // 直播间需要处理这个消息
        AiAnswerFactory.shared.addItemForHuDong(model: item)
    }
    
    
    func setupAutoReplyConfigs() {
        // 配置消息回复规则
        AutoReplyManager.shared.getConfigManager().addConfig(ReplyConfig(
            eventType: .message,
            maxReplies: 5,
            maxFrequencyInSeconds: 30,
            userSpecific: true,
            globalReplyCooldown: 180,
            resetIntervalInSeconds: nil
        ))
        
        // 配置礼物回复规则
        AutoReplyManager.shared.getConfigManager().addConfig(ReplyConfig(
            eventType: .gift,
            maxReplies: 3,
            maxFrequencyInSeconds: 30,
            userSpecific: true,
            globalReplyCooldown: nil,
            resetIntervalInSeconds: 86400
        ))
        
        // 配置关注回复规则
        AutoReplyManager.shared.getConfigManager().addConfig(ReplyConfig(
            eventType: .follow,
            maxReplies: nil,
            maxFrequencyInSeconds: nil,
            userSpecific: true,
            globalReplyCooldown: nil,
            resetIntervalInSeconds: nil
        ))
        
        // 设置消息回复处理器
        AutoReplyManager.shared.setReplyChatRunnable { [weak self] messageData in
            guard let self = self else { return }
            DispatchQueue.main.async {
                self.checkNeedReply(messageData)
            }
        }
        
        // 设置礼物回复处理器
        AutoReplyManager.shared.setReplyGiftRunnable { [weak self] messageData in
            guard let self = self else { return }
            DispatchQueue.main.async {
                self.checkNeedReply(messageData)
            }
            
        }
        
        // 设置关注回复处理器
        AutoReplyManager.shared.setReplyFollowRunnable { [weak self] messageData in
            guard let self = self else { return }
            DispatchQueue.main.async {
                self.checkNeedReply(messageData)
            }
        }
    }
    
    // 检查是否需要回复的方法
    private func checkNeedReply(_ messageData: DouyinMessageData) {
        if let retCard = findTriggerCard(messageData) {
            if messageData.msgType == .CHAT {
                if let nickName = messageData.user?.nickName {
                    retCard.triggerDouyinUserName = JsonTool.model2String([nickName])
                }
            } else {
                if let nickName = messageData.user?.nickName {
                    retCard.triggerDouyinUserName = nickName
                }
            }
            
            sendReplyItem(item: retCard)
            print("需要回复的模型为：\(retCard)")
        }
    }
    
    private func findTriggerCard(_ messageData: DouyinMessageData) -> AiKeywordVoiceReplyItemModel? {
        // 拿到本地设置好的配置数据，判定是否需要触发回复
        let cardList = self.model.cardList.filter({ $0.enable })
        
        // 遍历所有聊天类型配置
        for cardItem in cardList {
            switch messageData.msgType {
            case .CHAT:
                if cardItem.interactionType == .userChat {
                    // 判断是否触发了关键词列表中的某个关键词
                    if let keywordList = cardItem.keywords, !keywordList.isEmpty {
                        for keyword in keywordList {
                            if keyword.isEmpty {
                                continue
                            }
                            
                            if let content = messageData.chatInfo?.content,
                               content.contains(keyword) {
                                LCLog.d("存在触发项，触发关键词回复keywords = \(cardItem.keywords ?? [])")
                                return cardItem
                            }
                        }
                    }
                }
                
            case .GIFT:
                if cardItem.interactionType == .systemInteraction &&
                    cardItem.systemInteractionType == .gift {
                    // 判断是否触发了礼物列表中的某个礼物
                    if let giftList = cardItem.gifts, let gift = giftList.first  {
                        
                        if messageData.giftInfo?.gift?.name == gift.name {
                            LCLog.d("giftmodel:存在触发项，触发礼物回复 gifts = \(cardItem.gifts ?? [])")
                            return cardItem
                        }
                    }
                }
                
            case .SOCIAL:
                if cardItem.interactionType == .systemInteraction &&
                    cardItem.systemInteractionType == .follow {
                    LCLog.d("存在触发项，触发关注回复 user = \(messageData.user?.displayId ?? "")")
                    // 记录下昵称
                    if let nickName = messageData.user?.nickName {
                        return cardItem
                    }
                }
                
            default:
                break
            }
        }
        
        return nil
    }
}


extension SmartActionVC {
    /// 获取主播信息
    func requestAnchorInfo(endBack: @escaping (Bool) -> Void)  {
        Task {
            do {
                let response: AnchorInfoResponse = try await RequestManager.shared.requestApi(style: .anchorBaseInfo)
                if response.statusCode ==  20011 ||  response.statusCode == 20003 {
                    LCLog.d("用户未登录")
                    
                    endBack(false)
                    return
                }
                guard let user = response.data?.user else {
                    endBack(false)
                    return
                }
                nickName = user.nickname
                
                // 更新头像
                
                endBack(true)
            } catch {
                endBack(false)
                LCLog.d("error:\(error)")
                print("获取主播信息失败")
            }
        }
        
    }
}

extension LiveMessage {
    var nickName: String? {
        var nickName: String = ""
        switch self.type {
        case .CHAT:
            nickName = self.data.chatInfo?.user?.nickName ?? ""
        case .ONLINE:
            break
        case .SOCIAL:
            nickName = self.data.socialInfo?.user.nickName ?? ""
        case .MEMBER:
            nickName = self.data.memberInfo?.user.nickName ?? ""
        case .LIKE:
            nickName = self.data.likeInfo?.user.nickName ?? ""
        case .GIFT:
            nickName = self.data.giftInfo?.user?.nickName ?? ""
        case .FANSCLUB:
            nickName = self.data.fansclubInfo?.user.nickName ?? ""
        case .EMOJI_CHAT:
            break
        case .CONTROL:
            break
        case .ROOM_RANK:
            break
        case .ROOM_STATS:
            break
        case .SUPER_CHAT:
            break
        }
        return nickName
    }
}

// 消息管理器
class LiveMessageManager {
    static let shared = LiveMessageManager()
    private init() {}
    
    private let messageCache = NSCache<NSString, NSDate>()
    private let queue = DispatchQueue(label: "com.app.messageManager")
    private let cacheTimeout: TimeInterval = 15.2
    
    // 添加日志功能便于调试
    private func log(_ message: String) {
        print("---弹幕---[LiveMessageManager] \(message)")
    }
    
    func isMessageProcessed(_ message: LiveMessage) -> Bool {
        let key = generateMessageKey(message)
        
        return queue.sync {
            if let processedTime = messageCache.object(forKey: key as NSString) {
                let timeInterval = Date().timeIntervalSince(processedTime as Date)
                log("检查消息: \(key), 上次处理时间: \(timeInterval)秒前")
                
                if timeInterval < cacheTimeout {
                    log("消息在缓存期内，跳过处理")
                    messageCache.removeObject(forKey: key as NSString)
                    return true
                }
                
                log("消息缓存已过期，将重新处理")
                messageCache.removeObject(forKey: key as NSString)
            }
            return false
        }
    }
    
    func markMessageAsProcessed(_ message: LiveMessage) {
        let key = generateMessageKey(message)
        queue.sync { // 改用同步确保立即写入
            log("标记消息已处理: \(key)")
            messageCache.setObject(Date() as NSDate, forKey: key as NSString)
        }
    }
    
    private func generateMessageKey(_ message: LiveMessage) -> String {
        let messageItem = message.toMessageItem
        let type = messageItem.type.rawValue
        let nickname = messageItem.item.nickname ?? ""
        let content = messageItem.item.content ?? ""
        let originMsg = messageItem.item.originMsg ?? ""
        
        // 组合更多的信息来确保唯一性
        let key = "\(type)_\(nickname)_\(content)_\(originMsg)"
        log("生成消息key: \(key)")
        return key
    }
}

struct ReplySendMessageModel {
    var item: MessageItem
    var type: BarrageMessageType
}

enum BarrageMessageType: Int, Codable {
    case bossIn = 0 // 大佬进入直播间
    case massIn // 草民进入直播间
    case gift // 送礼
    case discuss // 普通评论
    case follow // 关注
    case like // 点赞
    case fans // 加入粉丝团
    case order // 下单
}

// MARK: - 消息类型 重新包装了一层
class MessageItem: NSObject {
    var nickname: String?
    var nodeHashcode: Int?
    var giftName: String? = nil
    var content: String? = nil
    var originMsg: String? = nil
    
    init(nickname: String? = nil, nodeHashcode: Int? = nil, giftName: String? = nil, content: String? = nil, originMsg: String? = nil) {
        self.nickname = nickname
        self.nodeHashcode = nodeHashcode
        self.giftName = giftName
        self.content = content
        self.originMsg = originMsg
    }
}

extension LiveMessage {
    /// 转成安卓一样的自动回复的数据结构
    var toMessageItem: ReplySendMessageModel {
        var nickName: String = ""
        let nodeHashcode = Int(Date.millionSeconds)
        let giftName = self.data.giftInfo?.gift?.name ?? ""
        var content: String?
        var returnType: BarrageMessageType = .bossIn
        switch self.type {
        case .CHAT:
            nickName = self.data.chatInfo?.user?.nickName ?? ""
            content = nickName + ": " + (self.data.chatInfo?.content ?? "")
            returnType = .discuss
        case .ONLINE:
            break
        case .SOCIAL:
            nickName = self.data.socialInfo?.user.nickName ?? ""
            content = nickName + " " + "关注了主播"
            returnType = .follow
        case .MEMBER:
            nickName = self.data.memberInfo?.user.nickName ?? ""
            content = nickName + " " + "来了"
            returnType = .massIn
        case .LIKE:
            nickName = self.data.likeInfo?.user.nickName ?? ""
            content = nickName + " " + "为主播点赞了"
            returnType = .like
        case .GIFT:
            nickName = self.data.giftInfo?.user?.nickName ?? ""
            content = nickName + " 送出 " + giftName
            returnType = .gift
        case .FANSCLUB:
            nickName = self.data.fansclubInfo?.user.nickName ?? ""
            content = nickName + " 成为粉丝团成员"
            returnType = .fans
        case .EMOJI_CHAT:
            break
        case .CONTROL:
            break
        case .ROOM_RANK:
            break
        case .ROOM_STATS:
            break
        case .SUPER_CHAT:
            break
        }
        let originMsg: String? = content
        let messageItem = MessageItem(nickname: nickName,
                                      nodeHashcode: nodeHashcode,
                                      giftName: giftName,
                                      content: content,
                                      originMsg: originMsg)
        return ReplySendMessageModel(item: messageItem, type: returnType)
    }
}

extension Date {
    /// 当前的毫秒时间戳
    static var millionSeconds: TimeInterval {
        // 获取当前日期和时间
        let now = Date()
        // 获取自1970年以来的秒数
        let timeIntervalInSeconds = now.timeIntervalSince1970
        // 将秒数转换为毫秒并转换为整数
        let milliseconds = timeIntervalInSeconds * 1000
        return milliseconds
    }
}

// MARK: - 礼物处理
extension SmartActionVC {
    func handlerGiftMessages(
        messageList: [DouyinMessageData],
        outputCallback: @escaping (DouyinMessageData) async -> Void
    ) async {
        let allGiftMessageList = messageList.filter { $0.msgType == .GIFT }
        
        guard !allGiftMessageList.isEmpty else { return }
        
        // 测试代码（已注释）
        /*
        messageList.forEach { message in
            let newPrice: Int
            switch message.giftInfo?.gift?.name {
            case "人气票":
                newPrice = 1000
            case "玫瑰":
                newPrice = 9000
            case "抖音":
                newPrice = 10000
            default:
                newPrice = message.giftInfo?.gift?.diamondCount ?? 0
            }
            message.giftInfo?.gift?.diamondCount = newPrice
        }
        */
        
        print("handlerGiftMessages: \(allGiftMessageList.compactMap { $0.giftInfo?.gift?.name })")
        
        // 拆分高价值与低价值礼物
        let groupedGiftMessages = allGiftMessageList.partition { ($0.giftInfo?.gift?.diamondCount ?? 0) >= 100 }
        let highValueGiftMessageList = groupedGiftMessages.0.sorted {
            ($0.giftInfo?.gift?.diamondCount ?? 0) > ($1.giftInfo?.gift?.diamondCount ?? 0)
        } // 高价值礼物，按价值降序排列
        let lowValueGiftMessageList = groupedGiftMessages.1 // 低价值礼物
        
        // 高价值礼物直接进入队列等待处理
        for message in highValueGiftMessageList {
            // 消息进入自动回复队列，等待处理
            if let nickName = message.user?.nickName {
                message.user?.nickName = [nickName].toJson() ?? ""
            }
            await outputCallback(message)
            try? await Task.sleep(nanoseconds: UInt64(0.2 * 1_000_000_000))
        }
        
        // 低价值礼物等待合并处理
        let giftMessageList = lowValueGiftMessageList
        let giftStats = GiftStatistics()
        
        // 统计礼物数据
        for message in giftMessageList {
            if let nickname = message.user?.nickName,
               let giftName = message.giftInfo?.gift?.name,
               !giftName.isEmpty && !nickname.isEmpty {
                giftStats.addGift(name: giftName, from: nickname)
            }
        }
        
        // 处理每种礼物的感谢消息
        var items = giftStats.getAllGiftItems()
        // 按照礼物数量降序排序
        items = items.sorted { $0.count > $1.count }
        
        for giftItem in items {
            // 获取该礼物的所有赠送者
            let topGifters = giftItem.getAllUsers()
            
            // 组装多名赠送者昵称，发送一个独立的礼物消息
            if let combinedName = topGifters.toJson(), !combinedName.isEmpty {
                if let combinedDouyinMessageData = giftMessageList.first(where: { $0.giftInfo?.gift?.name == giftItem.giftName }) {
                    combinedDouyinMessageData.user?.nickName = combinedName
                    await outputCallback(combinedDouyinMessageData)
                }
            }
        }
    }

    func handlerFollowMessages(
        messageList: [DouyinMessageData],
        outputCallback: @escaping (DouyinMessageData) -> Void
    ) {
        let followMessageList = messageList.filter { $0.msgType == .SOCIAL }
        
        if !followMessageList.isEmpty {
            let recentFollowMessages = Array(followMessageList.suffix(5))
            let uniqueFollowNames = Array(Set(recentFollowMessages.compactMap { $0.user?.nickName }))
            
            if let combinedFollowName = uniqueFollowNames.toJson(),
               !combinedFollowName.isEmpty {
                if let replyItemModel = recentFollowMessages.first {
                    replyItemModel.user?.nickName = combinedFollowName
                    outputCallback(replyItemModel)
                }
            }
        }
    }
}

// Array扩展：实现partition功能
extension Array {
    func partition(by predicate: (Element) -> Bool) -> ([Element], [Element]) {
        var matching: [Element] = []
        var nonMatching: [Element] = []
        
        for element in self {
            if predicate(element) {
                matching.append(element)
            } else {
                nonMatching.append(element)
            }
        }
        
        return (matching, nonMatching)
    }
}

extension Array where Element == String {
    func toJson() -> String? {
        do {
            let jsonData = try JSONSerialization.data(withJSONObject: self, options: [])
            return String(data: jsonData, encoding: .utf8)
        } catch {
            print("JSON转换错误: \(error)")
            return nil
        }
    }
}
