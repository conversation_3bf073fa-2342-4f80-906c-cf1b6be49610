//
//  SmartActionVC+Tool.swift
//  LivePlus
//
//  Created by 郭炜 on 2025/1/13.
//

import UIKit

// MARK: - 顶部的toolbar
extension SmartActionVC: SmartActionToolBarDelegate {
    /// 创建新的关键词回复
    //  TODO: - 这里需要区分是添加的互动回复 还是关键词回复
    func actionForToolBarCreate() {
        switch self.model.interactionType {
        case .userChat:
            // 关键词回复
            addKeyWordReplyItem()
        case .systemInteraction:
            // 系统互动的回复
            willAddReplyItem()
        }
       
    }
    
    /// 过滤、排序、状态等修改
    func actionForToolBarStatusChanged() {
        toolBar.bind(to: self.model)
        self.reloadData()
    }
    
    /// 设置
    func actionForToolBarSetting() {
        self.navigationController?.pushViewController(AIConfigVC())
    }
}

extension SmartActionVC {
    
    func addKeyWordReplyItem() {
        let replyItem =  AiKeywordVoiceReplyItemModel.create()
        
        let viewController = SmartKeywordVC(model: replyItem, isNew: true)
        self.navigationController?.pushViewController(viewController, animated: true)
    }
    
    func willAddReplyItem() {
        // 弹窗 选择类型
        guard let appDel = UIApplication.shared.delegate as? AppDelegate else { return }
        self.timeView.isHidden = false
        appDel.wd.addSubview(self.timeView)
        
    }
    
}


extension SmartActionVC: SystemReplyTypeViewProtocol {
    func didSelected(mode: SystemInteractionType) {
        dismissAll()
        switch mode {
        case .gift:
            goGiftReplyVC()
        case .follow:
            if let _ =  self.model.cardList.first(where: {$0.systemInteractionType == .follow}) {
                HUD.showFail("关注回复已存在")?.isUserInteractionEnabled = false
                return
            }
            goFollowReplyVC()
        }
    }
    
    
    func goGiftReplyVC() {
        // 创一个新的卡片
        let replyItem = AiKeywordVoiceReplyItemModel(interactionType: .systemInteraction, systemInteractionType: .gift, replyItem: VoiceParagraphModel.create())
        
        let vc = AIGiftReplyVc(model: replyItem, isNew: true)

        self.navigationController?.pushViewController(vc)
    }
    
    
    func goFollowReplyVC() {
        // 创一个新的卡片
        let replyItem = AiKeywordVoiceReplyItemModel(interactionType: .systemInteraction, systemInteractionType: .follow, replyItem: VoiceParagraphModel.create())
        
        
        let vc  = AIFollowReplyVc(model: replyItem, isNew: true)

        self.navigationController?.pushViewController(vc)
    }
    
}
