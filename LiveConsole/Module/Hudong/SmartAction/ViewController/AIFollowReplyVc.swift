//
//  AIFollowReplyVc.swift
//  LivePlus
//
//  Created by simon on 11.2.25.
//

import Foundation


class AIFollowReplyVc: BaseVC {
    
    lazy var navigation: SmartNavigation = {
        let view = SmartNavigation()
        view.titleLabel.text = "添加关注回复"
        view.backgroundColor = .clear
        return view
    }()
    
    
    // 当收到      关注时，则触发以下回复内容
    lazy var desLab: UILabel = {
        let v = UILabel()
        v.font = UIFont.systemFont(ofSize: 14, weight: .regular)
        v.textColor = UIColor("#ACACAC")
        v.text = "当收到"
        return v
    }()
    
    lazy var desView: UIImageView = {
        let v = UIImageView()
        v.backgroundColor = .clear
        v.image = UIImage(named: "follow_icon")
        return v
    }()
    
    lazy var desLab2: UILabel = {
        let v = UILabel()
        v.font = UIFont.systemFont(ofSize: 14, weight: .regular)
        v.textColor = UIColor("#ACACAC")
        
        let paragraphStyle = NSMutableParagraphStyle()
        paragraphStyle.lineSpacing = 6
        let attribute = NSMutableAttributedString(string: "关注", attributes: [.foregroundColor: UIColor("#FF6262"), .font: UIFont.systemFont(ofSize: 14, weight: .medium), NSAttributedString.Key.paragraphStyle: paragraphStyle])
                
        attribute.append(NSMutableAttributedString(string: "时，则触发以下回复内容", attributes: [.foregroundColor: UIColor("#ACACAC"), .font: UIFont.systemFont(ofSize: 14, weight: .regular), NSAttributedString.Key.paragraphStyle: paragraphStyle]))
        
        v.textAlignment = .left
        v.attributedText = attribute
        
        return v
    }()
    
    
    /// 语音回复内容
    lazy var audioReplyView: ReplayAudioView = {
        let view = ReplayAudioView()
        view.delegate = self
        return view
    }()
    
    lazy var bgView: UIView = {
        let v = UIView(frame: CGRect(x: 0, y: 0, width: LCDevice.screenW, height: LCDevice.screenH))
        v.backgroundColor = UIColor("#111111")
        return v
    }()
    
    /// 渐变层
    private var gradientLayer: CAGradientLayer = {
        let leftColor = UIColor("#220E30")
        let cenColor = UIColor("#0E0B43")
        let rightColor = UIColor("#0A0116")
        let gradientColors = [leftColor.cgColor, cenColor.cgColor,rightColor.cgColor]
        
        let gradientLocations: [NSNumber] = [0.0, 0.5, 1.0]
        let gradientLayer = CAGradientLayer()
        gradientLayer.startPoint = CGPoint(x: 0, y: 0)
        gradientLayer.endPoint = CGPoint(x: 0, y: 1)
        gradientLayer.colors = gradientColors
        gradientLayer.locations = gradientLocations
        return gradientLayer
    }()
    
    // 回复的一个卡片模型
    var replyItemModel: AiKeywordVoiceReplyItemModel
    
    var  isNew: Bool = false
    
    init(model: AiKeywordVoiceReplyItemModel,  isNew : Bool = false) {
        self.isNew = isNew
        self.replyItemModel = model
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    
    override func viewDidLoad() {
        super.viewDidLoad()
        makeUI()
        business()
        bindViewModel()
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        /// 需要重置代理 因为录制页面也会抢代理
        AudioPlayManager.shared.delegate = self.audioReplyView
    }
    
    
    func makeUI() {
        
        view.addSubview(bgView)
        
//        bgView.layer.insertSublayer(gradientLayer, at: 0)
        
//        gradientLayer.frame = bgView.bounds
        
        view.addSubview(navigation)
        navigation.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview()
            make.height.equalTo(LCDevice.Nav_H)
        }
        
        bgView.addSubviews([desLab, desView, desLab2, audioReplyView])
        
        desLab.snp.makeConstraints { make in
            make.leading.equalToSuperview().inset(16)
            make.top.equalTo(navigation.snp.bottom).offset(16)
            make.height.equalTo(18)
        }
        
        desView.snp.makeConstraints { make in
            make.leading.equalToSuperview().inset(64)
            make.top.equalTo(navigation.snp.bottom).offset(16)
            make.height.width.equalTo(18)
        }
        
        desLab2.snp.makeConstraints { make in
            make.leading.equalTo(desView.snp.trailing).offset(8)
            make.top.equalTo(navigation.snp.bottom).offset(16)
            make.height.equalTo(18)
        }
        
        
        audioReplyView.snp.makeConstraints { make in
            make.leading.trailing.bottom.equalToSuperview()
            make.top.equalToSuperview().inset(137)
        }
        
    }
    
    
    
    func business() {
        //
        guard  replyItemModel.systemInteractionType == .follow else { return  }
    
        audioReplyView.questionButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self else { return }
                self.audioReplyQuestion()
            }.disposed(by: rx.disposeBag)
        
        audioReplyView.recordButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self else { return }
                self.audioReplyPlusAction(addType: .record)
            }.disposed(by: rx.disposeBag)
        
        audioReplyView.combineButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self else { return }
                self.audioReplyPlusAction(addType: .aiGenerate)
            }.disposed(by: rx.disposeBag)
        
        audioReplyView.uploadButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self else { return }
                self.audioReplyPlusAction(addType: .importFile)
            }.disposed(by: rx.disposeBag)
        
        audioReplyView.deleteButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self else { return }
                self.audioReplyMinusAction()
            }.disposed(by: rx.disposeBag)
        /// 文字部分
        audioReplyView.textPlusButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self else { return }
                self.plusTextReplyAction()
            }.disposed(by: rx.disposeBag)
        
        audioReplyView.textEditButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self else { return }
                self.editTextReplyAction()
            }.disposed(by: rx.disposeBag)
        
        audioReplyView.textDeleteButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self else { return }
                self.endEditing()
            }.disposed(by: rx.disposeBag)
    }
    
    func bindViewModel() {
        self.audioReplyView.bind(to: replyItemModel)
    }
    
    override func touchesBegan(_ touches: Set<UITouch>, with event: UIEvent?) {
        super.touchesBegan(touches, with: event)
        self.view.endEditing(true)
    }
    
    func endEditing() {
        self.view.endEditing(true)
    }
}

