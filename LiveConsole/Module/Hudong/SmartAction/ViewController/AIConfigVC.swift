//
//  AIConfigVC.swift
//  LivePlus
//
//  Created by simon on 16.1.25.
//

import Foundation

// MARK: - 智能互动设置
class AIConfigVC: BaseVC, UIScrollViewDelegate {
    
    lazy var scrollView: UIScrollView = {
        let v = UIScrollView()
        v.delegate = self
        return v
    }()
    
    lazy var scrollContentView: UIView = {
        let view = UIView()
        return view
    }()
    
    // 压缩之后 会返回一个文件的保存路径， 需要这个路径才能吊起系统的文件APP 把ZIP 保存到手机中
    public var zipPath: String?
    
    /// 备份导出页面
    lazy var backupOutView: AIBackupView = {
        let view = AIBackupView(frame: CGRect(x: 16, y: 0, width: LCDevice.screenW - 32, height: 400))
        view.delegate = self
        return view
    }()
    
    lazy var navigation: SmartNavigation = {
        let view = SmartNavigation()
        view.titleLabel.text = "设置"
        view.backgroundColor = .clear
        return view
    }()
    
    lazy var textSetView: AIConfigTextSetView = {
        let v = AIConfigTextSetView()
        return v
    }()
    
    
    lazy var audoiSetView: AIConfigSetView = {
        let v = AIConfigSetView()
        v.isHidden = true
//        v.delegate = self
        return v
    }()
    
    
    lazy var downloadButton: UIButton = {
        let button = UIButton()
        button.setImage(UIImage(named: "ic_smart_download"), for: .normal)
        button.setTitle("导入", for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 14, weight: .regular)
        button.imagePosition(style: .left, spacing: 4)
        return button
    }()
    
    lazy var uploadButton: UIButton = {
        let button = UIButton()
        button.setImage(UIImage(named: "ic_smart_upload"), for: .normal)
        button.setTitle("备份", for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 14, weight: .regular)
        button.imagePosition(style: .left, spacing: 4)
        return button
    }()
    
    
    lazy var textButton: UIButton = {
        let button = UIButton()
        button.setTitleColor(UIColor.white, for: .normal)
        button.setTitleColor(UIColor("#ADAAFF"), for: .selected)
        button.setTitle("@用户昵称", for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        button.imagePosition(style: .left, spacing: 4)
        button.addTarget(self, action: #selector(textAction), for: .touchUpInside)
        return button
    }()
    
    lazy var audoiButton: UIButton = {
        let button = UIButton()
        button.setTitleColor(UIColor("#ADAAFF"), for: .selected)
        button.setTitleColor(UIColor.white, for: .normal)
        button.setTitle("朗读用户昵称", for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        button.imagePosition(style: .left, spacing: 4)
        button.addTarget(self, action: #selector(aduioAction), for: .touchUpInside)
        return button
    }()
    
    private lazy var lineView: UIView = {
        let stack = UIView()
        stack.backgroundColor = UIColor("#ADAAFF")
        stack.cornerRadius = 1.5
        return stack
    }()
    
    
    lazy var timeView: GiftTimeView = {
        let v = GiftTimeView(frame: CGRect(x: 0, y: LCDevice.screenH, width: LCDevice.screenW, height: 310))
        v.delegate = self
        return v
    }()
    
    
    lazy var bgView: UIView = {
        let v = UIView(frame: CGRect(x: 0, y: 0, width: LCDevice.screenW, height: LCDevice.screenH))
        v.backgroundColor = UIColor("#111111")
        return v
    }()
    
   
    
    private lazy var speView: UIView = {
        let stack = UIView()
        stack.backgroundColor = UIColor("#282828")
        return stack
    }()
   
    override func viewDidLoad() {
        super.viewDidLoad()
        
        makeUI()
        business()
        textSetView.config(model: AIReplyDataManager.shared.aiConfig)
        audoiSetView.config(model: AIReplyDataManager.shared.aiConfig)
        AIReplyDataManager.shared.configDelegate = self
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        self.audoiSetView.getSpeakIDList()
        MiddleRequestNet.getUserInfoApiData()
    }
    
    
    func makeUI() {
        
        view.addSubview(bgView)
        
        
        view.addSubview(navigation)
        navigation.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview()
            make.height.equalTo(LCDevice.Nav_H)
        }
        
        navigation.addSubviews([speView, downloadButton, uploadButton])
        speView.snp.makeConstraints { make in
            make.bottom.leading.trailing.equalToSuperview()
            make.height.equalTo(1.5)
        }
        
        downloadButton.snp.makeConstraints { make in
            make.trailing.equalToSuperview().inset(24)
            make.bottom.equalToSuperview()
            make.width.equalTo(62)
            make.height.equalTo(32)
        }
        
        uploadButton.snp.makeConstraints { make in
            make.trailing.equalTo(downloadButton.snp.leading).offset(-24)
            make.bottom.equalToSuperview()
            make.width.equalTo(62)
            make.height.equalTo(32)
        }
        
        bgView.addSubviews([textButton, audoiButton,lineView, scrollView, textSetView])
        
        scrollView.addSubview(scrollContentView)
        scrollContentView.addSubviews([audoiSetView])
        
        scrollView.snp.makeConstraints { make in
            make.leading.equalToSuperview()
            make.top.equalToSuperview().inset(LCDevice.Nav_H + 62)
            make.width.equalTo(LCDevice.screenW)
            make.height.equalTo(LCDevice.screenH - LCDevice.Nav_H - 62)
        }
        scrollContentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }
        
        textButton.snp.makeConstraints { make in
            make.leading.equalToSuperview().inset(24)
            make.width.equalTo(80)
            make.height.equalTo(26)
            make.top.equalTo(navigation.snp.bottom).offset(12)
        }
        
        
        audoiButton.snp.makeConstraints { make in
            make.leading.equalTo(textButton.snp.trailing).offset(12)
            make.width.equalTo(100)
            make.height.equalTo(26)
            make.top.equalTo(navigation.snp.bottom).offset(12)
        }
        
        lineView.snp.makeConstraints { make in
            make.centerX.equalTo(textButton.snp.centerX)
            make.width.equalTo(69)
            make.height.equalTo(3)
            make.top.equalTo(navigation.snp.bottom).offset(42)
        }
        
        
        textSetView.snp.makeConstraints { make in
            make.leading.equalToSuperview()
            make.width.equalTo(LCDevice.screenW)
            make.height.equalTo(333)
            make.top.equalToSuperview().inset(LCDevice.Nav_H + 62)
        }
        
        audoiSetView.snp.makeConstraints { make in
            make.leading.equalToSuperview()
            make.width.equalTo(LCDevice.screenW)
            make.height.equalTo(800)
            make.top.equalToSuperview()
        }
        
        scrollContentView.snp.makeConstraints { make in
            make.bottom.equalTo(audoiSetView.snp.bottom).offset(16)
        }
    }
    
    func loadData() {
       
    }
    
    @objc func textAction() {
        self.textButton.isSelected = true
        self.audoiButton.isSelected = false
        
        self.textSetView.isHidden = false
        self.audoiSetView.isHidden = true
        
        self.lineView.snp.remakeConstraints { make in
            make.centerX.equalTo(textButton.snp.centerX)
            make.width.equalTo(69)
            make.height.equalTo(3)
            make.top.equalTo(navigation.snp.bottom).offset(42)
        }
    }
    
    @objc func aduioAction() {
        self.textButton.isSelected = false
        self.audoiButton.isSelected = true
        
        self.textSetView.isHidden = true
        self.audoiSetView.isHidden = false
        
        self.lineView.snp.remakeConstraints { make in
            make.centerX.equalTo(audoiButton.snp.centerX)
            make.width.equalTo(69)
            make.height.equalTo(3)
            make.top.equalTo(navigation.snp.bottom).offset(42)
        }
    }
    
    func business() {
        //
        NotificationCenter.default.addObserver(self, selector: #selector(keyboardWillShowNotice(sender:)), name: UIResponder.keyboardWillShowNotification, object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(keyboardWillHideNotice(sender:)), name: UIResponder.keyboardWillHideNotification, object: nil)
        
        downloadButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self else { return }
                self.didAction(type: .download)
            }.disposed(by: rx.disposeBag)
        
        uploadButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self else { return }
                self.didAction(type: .upload)
            }.disposed(by: rx.disposeBag)
       
    }
    
    // MARK: - notice
    @objc func keyboardWillShowNotice(sender:Notification) {
        self.scrollView.scrollToBottom(animated: true)
    }
    
    @objc func keyboardWillHideNotice(sender:Notification) {
        self.scrollView.scrollToTop(animated: true)
    }
    
    func showTimeView() {
        
        
//        let gaps = GiftSameGapModel.gaps()
//        gaps.forEach { g in
//            g.selected =  g.gap == gap
//        }
//        
//        timeView.updateView(infos: gaps)
//        showPopBot(view: timeView)
        
    }
        
}


extension AIConfigVC: GiftTimeViewProtocol {
    func didSelected(mode: GiftSameGapModel) {
        textSetView.config(model: AIReplyDataManager.shared.aiConfig)
        dismissAll()
    }
    
}

extension AIConfigVC: AIReplyDataManagerConfigDelegate {
    func didBackup_config() {
        textSetView.config(model: AIReplyDataManager.shared.aiConfig)
        audoiSetView.config(model: AIReplyDataManager.shared.aiConfig)
    }
}
