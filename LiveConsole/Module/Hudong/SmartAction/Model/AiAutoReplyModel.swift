//
//  AiAutoReplyModel.swift
//  LivePlus
//
//  Created by simon on 6.1.25.
//

import Foundation

/***
 * 语音直播间自动播放
 */

// 循环语音组（包含多个循环语音）
// MARK: - LoopVoiceGroupModel
class LoopVoiceGroupModel: NSObject, Codable {
    // 是否单item循环播放
    var isSingleLoop: Bool = false
    // 循环语音列表
    var loopVoiceList: [LoopVoiceModel]?
    // 循环语音音量
    var loopVoiceVolume: Float = 1.0
    //当前播放的循环语音位置
    var playingLoopVoiceModelIndex: Int = 0

    static func create() -> LoopVoiceGroupModel {
        let model = LoopVoiceGroupModel()
        model.loopVoiceList = []
        return model
    }
}

// MARK: - LoopVoiceModel // 循环语音（包含多个段落）
class LoopVoiceModel:NSObject, Codable {
    var id: String = UUID().uuidString //唯一id
    var title: String?
    // 段落列表
    var voiceParagraphList: [VoiceParagraphModel]?
    //绑定的场景组id
    var bindSceneGroupId: String?
    //当前播放的段落位置
    var playingVoiceParagraphModelIndex: Int = 0
    //当前播放的进度
    var curPlayPercent: Int = 0

    //返回毫秒
    func countMinTime() -> Int64 {
        var minTime: Int64 = 0
        voiceParagraphList?.forEach { paragraph in
            if paragraph.showed, let resources = paragraph.resources {
                minTime += resources.min(by: { $0.duration < $1.duration })?.duration ?? 0
            }
        }
        return minTime
    }

    func clone() -> LoopVoiceModel {
        let clone = LoopVoiceModel()
        clone.id = UUID().uuidString
        clone.voiceParagraphList = voiceParagraphList?.map { $0.clone() }
        clone.bindSceneGroupId = nil // 绑定关系不复制
        return clone
    }
}


/**
 * 语音关键词回复最外层model
 *
 */
// MARK: - AiKeywordVoiceReplyModel
class AiKeywordVoiceReplyModel:NSObject, Codable {
    var isOpen: Bool = false
    var timeInterval: Int64 = 60000 // 默认1分钟
    var aiKeywordVoiceReplyItemModels: [AiKeywordVoiceReplyItemModel]?

    static func create() -> AiKeywordVoiceReplyModel {
        let model = AiKeywordVoiceReplyModel()
        model.aiKeywordVoiceReplyItemModels = []
        return model
    }
}


/***
 * 语音关键词回复
 *
 */
// MARK: - AiKeywordVoiceReplyItemModel
class AiKeywordVoiceReplyItemModel: Codable {
    var id: String = String.random(ofLength: 12)
    var title: String?
    var minTime: Int = 0
    var maxTime: Int = 0
    // 交互类型（用户评论、系统互动）
    var interactionType: InteractionType = InteractionType.userChat
    // 系统互动类型
    var systemInteractionType: SystemInteractionType = SystemInteractionType.gift
    // 是否启用
    var enable: Bool = true
    
    var createTime: Int64 = Int64(Date().timeIntervalSince1970 * 1000)
    
    var updateTime: Int64 = Int64(Date().timeIntervalSince1970 * 1000)
    
    // 回复类型（音频、视频、特效等）
//    var replyType: DyAiConstants = .voice
    //礼物关键词
    var gifts: [DouyinGiftInfo]?
    //文案关键词
    var keywords: [String]?
    //回复内容 关注回复：语音， 礼物回复： 视频 特效、语音， 用户评论回复类型： 语音
    var replyItem: VoiceParagraphModel?
    // 语音回复当前触发的关键词
    var triggerKeyword: String?
    // 触发的抖音用户名
    var triggerDouyinUserName: String?// 
    

    init(id: String = String.random(ofLength: 8), title: String? = nil, minTime: Int = 0, maxTime: Int = 0, interactionType: InteractionType = .userChat, systemInteractionType: SystemInteractionType = .gift, enable: Bool = true, createTime: Int64 = Int64(Date().timeIntervalSince1970 * 1000), updateTime: Int64 = Int64(Date().timeIntervalSince1970 * 1000), gifts: [DouyinGiftInfo]? = nil, keywords: [String]? = nil, replyItem: VoiceParagraphModel? = nil, triggerKeyword: String? = nil, triggerDouyinUserName: String? = nil ) {
        self.id = id
        self.title = title
        self.minTime = minTime
        self.maxTime = maxTime
        self.interactionType = interactionType
        self.systemInteractionType = systemInteractionType
        self.enable = enable
        self.createTime = createTime
        self.updateTime = updateTime
        self.gifts = gifts
        self.keywords = keywords
        self.replyItem = replyItem
        self.triggerKeyword = triggerKeyword
        self.triggerDouyinUserName = triggerDouyinUserName
    }
    
    // 快速创建一个回复的卡片模型 这里的类型是默认
    static func create() -> AiKeywordVoiceReplyItemModel {
        let model = AiKeywordVoiceReplyItemModel()
        model.keywords = ["", "", "", "", ""]
        model.replyItem = VoiceParagraphModel.create()
        return model
    }
    
    // Check if the type is gift type
    func isGiftType() -> Bool {
        return (gifts?.count ?? 0) > 0
    }
    
    // //是不是关注类型
    func isAttType() -> Bool {
        return (gifts?.count ?? 0) <= 0 && (keywords?.count ?? 0) <= 0
    }
    
    // 检查两者是否存在一致的关键词
    func isKeywordSameMatch(other: AiKeywordVoiceReplyItemModel?) -> Bool {
        guard let thisKeywords = self.keywords, let otherKeywords = other?.keywords else {
            return false
        }
        for thisKeyword in thisKeywords {
            for otherKeyword in otherKeywords {
                if thisKeyword == otherKeyword {
                    return true
                }
            }
        }
        return false
    }
    
    func modifyUpdateTime() {
        self.updateTime = Int64(Date().timeIntervalSince1970 * 1000)
    }
    
    var itemType: AreaItemType {
        return self.replyItem?.itemType ?? .unknown
    }
    
    var itemCount: Int {
        return self.replyItem?.itemCount ?? 0
    }
    
}

// 语音段落
// MARK: - VoiceParagraphModel
class VoiceParagraphModel: NSObject, Codable {
    var id: String = String.random(ofLength: 8)//唯一id
    var title: String?
    var showed: Bool = true
    var sortType: VoiceReplySortType = VoiceReplySortType.sequence // 0 按顺序播放 1 按随机播放
    // 段落中的所有音频
    var resources: [ResFileModel]?
    //当前播放到的位置
    var playingMp3Index: Int? = -1
    //当前播放的进度
    var curPlayPercent: Int? = 0
    
    /// 音频的资源
    var audioResources: [ResFileModel] {
        if self.itemType == .musicLocal {
            return self.resources ?? []
        }
        return []
    }
    
    /// 文字的资源
    var textResources: [ResFileModel] {
        if self.itemType == .text {
            return self.resources ?? []
        }
        return []
    }
    
    func toggleSortType() {
        sortType = (sortType == VoiceReplySortType.sequence) ? VoiceReplySortType.random : VoiceReplySortType.sequence
    }

    init(id: String = String.random(ofLength: 8), title: String? = nil, showed: Bool = false, sortType: VoiceReplySortType = .sequence, resources: [ResFileModel]? = [], playingMp3Index: Int? = -1, curPlayPercent: Int? = 0) {
        self.id = id
        self.title = title
        self.showed = showed
        self.sortType = sortType
        self.resources = resources
        self.playingMp3Index = playingMp3Index
        self.curPlayPercent = curPlayPercent
    }
    
    func clone() -> VoiceParagraphModel {
        return VoiceParagraphModel(id: id, title: title, showed: showed, sortType: sortType, resources: resources, playingMp3Index: playingMp3Index, curPlayPercent: curPlayPercent)
    }
    
    static func create() -> VoiceParagraphModel {
        return VoiceParagraphModel()
    }
    
    // 当前模型中的回复内容是什么类型
    var itemType: AreaItemType {
        if let resources =  self.resources, let firt = resources.first {
            return firt.sourceType
        }
        return .unknown
    }
    
    var itemCount: Int {
        if let resources =  self.resources {
            return resources.count
        }
        return 0
    }
}


/***
 * 本地资源模型
 */
// MARK: - ResFileModel
class ResFileModel: NSObject, Codable {
    var id: String = String.random(ofLength: 8)
    var title: String?
    var duration: Int64 = 0 //毫秒
    var path: String?
    var cropImg: String? //缩图图
    var open: Bool = true
    var sourceType: AreaItemType = AreaItemType.musicLocal //资源类型

    var voiceFrom: VOICE_FROM?
    var width: CGFloat? = 0
    var height: CGFloat? = 0
    
    /// 回复文本
    var text: String?

    ///// 视频或者图片文件在相册中的路径地址
    var localIdentifier: String?
    var vipLevel: VipLevel?
    var voidespeed = "1.0"
    
    // MARK: - 新增的数据 不需要编码
    // 合成任务ID
    var taskId: String?
    // 是否有子任务
    var subtask: Bool?
    // 子任务ID
    var subPath: String?
    // AI 合成的音频的状态
    var audioState: AudioSynthesisState? = AudioSynthesisState.none
    
    
    var toAudioPlayUrl: URL {
        return URL(fileURLWithPath: AudioPathStyle.audioRecord.path.appendingPathComponent(self.path ?? ""))
    }
    
    func isMusic() -> Bool {
        return sourceType == AreaItemType.musicLocal
    }
    
    init(id: String = String.random(ofLength: 8), title: String? = nil, duration: Int64 = 0, path: String? , cropImg: String? = nil, open: Bool = true, sourceType: AreaItemType, voiceFrom: VOICE_FROM = .record, width: CGFloat? = 0, height: CGFloat? = 0, localIdentifier: String? = nil, vipLevel: VipLevel? = nil, voidespeed: String = "1.0", text: String? = nil) {
        self.id = id
        self.title = title
        self.duration = duration
        self.path = path
        self.cropImg = cropImg
        self.open = open
        self.sourceType = sourceType
        self.voiceFrom = voiceFrom
        self.width = width
        self.height = height
        self.localIdentifier = localIdentifier
        self.vipLevel = vipLevel
        self.voidespeed = voidespeed
        self.text = text
    }


    func toAreaItem() -> AreaItemModel {
        // title 是文件的名称， 根据名称生成路径
        var str = title ?? ""
        if self.sourceType == .musicLocal, let p = path  {
            str = p
        }
        let item =  AreaItemModel(name: str,
                                  type: sourceType,
                                  duration: Int(duration / 1000),
                                  voiced:  false,
                                  sourceId: id,
                                  text: self.text)
        item.voidespeed = self.voidespeed
        return item
    }
    
    var tabViewHeight: CGFloat {
        if let text = text {
            
            let attContent = LCTools.checkNickConten(text: text, color: .white)
            
            let size = attContent.boundingRect(with: CGSize(width: LCDevice.screenW - 140, height: 300), options: [.usesLineFragmentOrigin, .usesFontLeading], context: nil).size
            
            return max(size.height + 32, 66)
            
        }
        return 66
       
    }
   
}

// MARK: - Constants and Enums

// TTS合成的状态
enum AudioSynthesisState: Int, Codable {
    case none = 0 // 无
    case waitting = 1 // 等待中
    case success = 2 // 成功
    case fail = 3 // 失败了
}

enum VoiceReplySortType: Int, Codable {
     case sequence = 0
     case random = 1
}


enum PlayerLoopType: Int, Codable {
     case once = 0
}

enum DyAiConstants: Int, Codable {
     case voice = 1
     case alphaVide = 2
     case video = 3
}

enum VOICE_FROM: Int, Codable {
    case record = 0  // 录音
    case local = 1  // 本地
    case ai = 2 // ai合成
}



// MARK: - ResourceModel
struct ResourceModel: Codable {
    var playStatus: Int
    var path: String?
    var name: String?
    var duration: Int64
    init(playStatus: Int, path: String? = nil, name: String? = nil, duration: Int64) {
        self.playStatus = playStatus
        self.path = path
        self.name = name
        self.duration = duration
    }
}

// MARK: - AreaItemModel
struct AIAreaItemModel: Codable {
    var path: String
    var name: String
    var thumb: String
    var type: AreaItemType
    var videoPlayType: PlayerLoopType
    var duration: Int64
    var isSysSeek: Bool
    var voice: Bool
    var voidespeed: Float
    init(path: String, name: String, thumb: String, type: AreaItemType, videoPlayType: PlayerLoopType, duration: Int64, isSysSeek: Bool, voice: Bool, voidespeed: Float) {
        self.path = path
        self.name = name
        self.thumb = thumb
        self.type = type
        self.videoPlayType = videoPlayType
        self.duration = duration
        self.isSysSeek = isSysSeek
        self.voice = voice
        self.voidespeed = voidespeed
    }
}
