//
//  AIAudioResultModel.swift
//  LivePlus
//
//  Created by 郭炜 on 2025/1/16.
//

import UIKit

class AIAudioResultModel {
    
    var id: String = String.random(ofLength: 10)
    
    var title: String = "AI合成"
    var selected: Bool = true
    
    var duration: Int // 秒
    var pathName: String // 保存在文件夹下面的还未放到record文件夹下的音频
    var speed: Float // 1.0

    init(duration: Int, pathName: String, speed: Float) {
        self.duration = duration
        self.pathName = pathName
        self.speed = speed
    }
    
    var toAudioPlayUrl: URL {
        return URL(fileURLWithPath: AudioPathStyle.aiGeneratePreview.path.appendingPathComponent(self.pathName))
    }
}
