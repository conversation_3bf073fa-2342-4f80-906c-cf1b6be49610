//
//  AIReplyDataManager.swift
//  LivePlus
//
//  Created by simon on 6.1.25.
//

import Foundation

protocol AIReplyDataManagerDelegate: NSObjectProtocol {
    // 已经完成了智能互动的备份
    func didBackup_znhd()
}


protocol AIReplyDataManagerConfigDelegate: NSObjectProtocol {
    // 已经完成了智能互动的备份
    func didBackup_config()
}


protocol AIReplyDataManagerTTSDelegate: NSObjectProtocol {
    // 已经合成完了
    func didCombineAudio(taskId: String, finsh: Bool, filePath: String?)
}


class AIReplyDataManager: NSObject {
    
    weak var configDelegate: AIReplyDataManagerConfigDelegate?
    
    weak var delegate: AIReplyDataManagerDelegate?
    
    weak var ttsDelegate: AIReplyDataManagerTTSDelegate?
    
    static let shared = AIReplyDataManager()
    
    public var interactionObject: InteractionObject?
    
    public var configObject: AiConfigObject?
    
    // 智能互动的设置项
    public var aiConfig: AiConfigModel = AiConfigModel()
    
    // 智能互动的模型  用户评论和系统回复
    public var interactionModel: InteractionModel?
    
    // TTS的记录
    public var ttsRecords: TTS_Records = TTS_Records()
    
    // 缓存的音色信息
    public var spkIdCache: AudioSpkIdCache = AudioSpkIdCache()
    
    // 是否开启了试试朗读朗读用户昵称
    var ttsNickenable: Bool = false
    
    override init() {
        super.init()

        // 把本地保存的取出来
        loadDataBase()
        
        loadCacheSpkid()
        
        loadTTS_Record()
        
        NotificationCenter.default.addObserver(self, selector: #selector(userLogoutNoticeAction), name: LCKey.noti_logout, object: nil)
        
        NotificationCenter.default.addObserver(self, selector: #selector(userLoginNoticeAction), name: LCKey.noti_reLogin, object: nil)


    }
    
 
    // 更新所有的缓存数据 例如登录了
    func updateDataBase() {
        loadDataBase()
        
        loadCacheSpkid()
        
        loadTTS_Record()
    }
   

    
    // 创建一个智能互动的对象
    func creatInteractionModel() {
        if self.interactionModel == nil {
            self.interactionModel = InteractionModel()
        }
        
    }
    
    // 根据礼物查询是否有卡片
    func giftCard(giftid: String) -> AiKeywordVoiceReplyItemModel? {
        guard let sys = AIReplyDataManager.shared.interactionModel else { return nil }
        
        for (idx, item) in sys.cardList.enumerated() {
            
            if item.interactionType  == .systemInteraction,
               item.systemInteractionType == .gift,
               let _ = item.gifts?.first(where: {$0.id == giftid}) {
                return item
            }
        }
        
        return nil
    }
    
    // 根据任务ID找到
    func getCard(taskId: String) -> AiKeywordVoiceReplyItemModel? {
        guard let sys = AIReplyDataManager.shared.interactionModel else { return nil }
        
        for (idx, item) in sys.cardList.enumerated() {
            
            if let resources = item.replyItem?.resources {
                if let _ = resources.first(where: {$0.taskId == taskId}) {
                    return item
                }
            }
        }
        
        return nil
    }
    
    // 查找其他卡片中 是否有这个关键词
    func keywordExist(keyword: String, model: AiKeywordVoiceReplyItemModel) -> Bool {
        guard let sys = AIReplyDataManager.shared.interactionModel else { return false }
        
        for (idx, item) in sys.cardList.enumerated() {
            
            if item.id != model.id, let keys = item.keywords {
                if let _ = keys.first(where: {$0 == keyword}) {
                    return true
                }
            }
        }
        
        return false
    }


    func backupConfigModel(model: AiConfigModel) {
        self.aiConfig = model
        self.configDelegate?.didBackup_config()
    }
    
    func backupInteractionModel(model: InteractionModel) {

        self.interactionModel = model
        
        self.saveToDataBase()
        self.delegate?.didBackup_znhd()
        
    }

    func getTtsMp3Path() -> String {
        let file = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!.appendingPathComponent("AI合成")
        if !FileManager.default.fileExists(atPath: file.path) {
            try? FileManager.default.createDirectory(at: file, withIntermediateDirectories: true, attributes: nil)
        }
        return file.path + "/"
    }
}


// MARK: 新版合成接口 是异步返回
extension AIReplyDataManager {
    
    // taskId 是主任务的ID
    // 提交子任务
    func submitSubTask(text: String, taskId: String) {
        self.submitTTSVoiceAsyn(text: text, isSubtask: true, mainTaskid: taskId) { [weak self] mode in
            guard let self = self else { return }
        }
    }
    
    /***
     *   第一步：同步请求提交生成文本
     */
    func submitTTSVoiceAsyn(text: String, isSubtask: Bool = false, mainTaskid: String? = nil, completion: @escaping ((_ data: AsyncCloneTtsModel?) -> Void)){
        
        let req = TTSReqModel()
        req.text = text
        
        if let preAudioModel = spkIdCache.currentPreAudioModel {
            req.isPreTts = true
            req.spkId = preAudioModel.voiceType
        } else if let cloneAudioModel = spkIdCache.currentCloneAudioModel {
            req.isPreTts = false
            req.spkId = cloneAudioModel.spkId
        } else {
            completion(nil)
            HUD.showFail("暂未设置合成音色")
        }
        // 合成你们不扣费
        MiddleRequestNet.submitTTSVoiceAsyn(model: req, deduct: !isSubtask) {[weak self] data in
            guard let self = self else { return }
           //   返回一个任务ID给上面
            completion(data)
            
            if let taskId = data?.taskId {
                // 准备轮询 等7秒
                DispatchQueue.main.asyncAfter(deadline: .now() + 5.0) {
                    self.geTTSVoiceSynStatus(taskId: taskId, isSubtask: isSubtask, mainTaskid:mainTaskid)
                }
            }
        }
        
    }
    
    
    ///  第二步： 获取合成状态
    func geTTSVoiceSynStatus(taskId: String, isSubtask: Bool = false, mainTaskid: String? = nil) {
        
        /// 3、获取训练状态
        MiddleRequestNet.getTTSStatusStatus(taskId: taskId) { [weak self] (response, code)in
            guard let self = self else { return }
            
            if let response = response, code == 0  {
                // 合成成功了,去下载
                self.handleTTSResult(taskId: taskId, ttsModel: response, isSubtask:isSubtask, mainTaskid: mainTaskid)
                return
            }
            
            if code == 501 {
                DispatchQueue.global().asyncAfter(deadline: .now() + 10) {
                    self.geTTSVoiceSynStatus(taskId: taskId, isSubtask: isSubtask, mainTaskid: mainTaskid)
                }
                LCLog.d("正在合成中， 继续轮询")
            } else {
                self.combineAudioFail(taskId: taskId, isSubtask: isSubtask, mainTaskid: mainTaskid)
               
                // 失败了 这种情况应该是获取不到音频数据了
                self.ttsDelegate?.didCombineAudio(taskId: taskId, finsh: false, filePath: nil)
            }
            
        }
    }
    
    // 第三步：合成成功之后开始下载
    func handleTTSResult(taskId: String, ttsModel:AsyncTTSStatusModel, isSubtask: Bool = false, mainTaskid: String? = nil) {
        print("合成结果 audioName：\(String(describing: ttsModel.audioName))")
        
        guard let fileName = ttsModel.audioName else {
            DispatchQueue.main.async {
                self.updateTTSResult(taskId: taskId, finsh: false, filePath: nil, isSubtask: isSubtask, mainTaskid: mainTaskid)
            }
            return
        }
        
        DispatchQueue.global().async {
            DownloadFromBucket.download(fileName: fileName) { [weak self] localFilePath in
                guard let self = self else { return }
                guard let localFilePath = localFilePath else {
                    DispatchQueue.main.async {
                        self.updateTTSResult(taskId: taskId, finsh: false, filePath: nil,isSubtask: isSubtask, mainTaskid: mainTaskid, bucketName: fileName)
                    }
                    return
                }
                print("下载文件路径：\(localFilePath)")

                DispatchQueue.main.async {
                    self.updateTTSResult(taskId: taskId, finsh: true, filePath: localFilePath, isSubtask: isSubtask, mainTaskid: mainTaskid, bucketName: fileName)
                }
            }
        }
    }
    
    
    // 第四步：处理下载之后的音频
    // 如果智能互动主页被销毁了 任务完成之后可能无法更新数据， 因此，在次处理数据
    func updateTTSResult(taskId: String, finsh: Bool, filePath: String?, isSubtask: Bool = false, mainTaskid: String? = nil,bucketName: String? = nil) {
        // TODO:  调用接口删除桶里面的MP3
        self.deletAudioFile(fileName: bucketName)
        
        // 合成失败，要删除这个
        guard finsh, let filePath = filePath else {
            
            self.combineAudioFail(taskId: taskId, isSubtask: isSubtask, mainTaskid: mainTaskid)
            self.ttsDelegate?.didCombineAudio(taskId: taskId, finsh: false, filePath: filePath)
            // 刷新页面
            return
        }
        
        
        let fileUrl = URL(fileURLWithPath: filePath)
        
        AudioDurationHelper.getAudioDuration(from: fileUrl) { [weak self] result in
            guard let self = self else { return }
            var dur: TimeInterval = 0
            
            switch result {
            case .success(let duration):
                print("音频时长: \(duration)秒")
                dur = duration
            case .failure(let error):
                print("获取时长失败: \(error.localizedDescription)")
            }
            
             let resourceModel = ResFileModel(path: nil, sourceType: .musicLocal)
             resourceModel.duration = Int64(dur * 1000) // 毫秒
             resourceModel.path = fileUrl.lastPathComponent // 后缀
             resourceModel.taskId = taskId
             self.combineAudioFinsh(taskId: taskId,res: resourceModel, isSubtask: isSubtask, mainTaskid: mainTaskid)
            
            self.ttsDelegate?.didCombineAudio(taskId: taskId, finsh: finsh, filePath: filePath)
        }
        
    }
    
    // 更新UI
    func combineAudioFinsh(taskId: String, res: ResFileModel, isSubtask: Bool = false, mainTaskid: String? = nil) {
        // 子任务
        if isSubtask, let mainTaskid = mainTaskid {
            guard let replyItemModel = self.getCard(taskId: mainTaskid) else {
                return
            }
            
            if let audio = replyItemModel.replyItem?.resources?.first(where: {$0.taskId == mainTaskid}) {
                audio.subPath = res.path
                audio.subtask = true
            }
            return
        }
        
        guard let replyItemModel = self.getCard(taskId: taskId) else {
            return
        }
        
        if let audio = replyItemModel.replyItem?.resources?.first(where: {$0.taskId == res.taskId}) {
            audio.duration = res.duration
            audio.path = res.path
            audio.audioState = .success
        }
        
    }
    
    // 处理失败的情况
    func combineAudioFail(taskId: String, isSubtask: Bool, mainTaskid: String?) {
        
        var id = taskId
        if isSubtask, let mainTaskid = mainTaskid {
            id = mainTaskid
        }
        guard let replyItemModel = self.getCard(taskId: id) else {
            return
        }
        
        if let audio = replyItemModel.replyItem?.resources?.first(where: {$0.taskId == id}) {
            audio.audioState = .fail
        }
        
    }
    
    func deletAudioFile(fileName: String?) {
        guard let name = fileName else { return  }
        // 等待2秒
        DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
            DownloadFromBucket.delet(fileName: name) { [weak self] result in
                guard let self = self else { return }
            }
        }
        
    }
    
}

// MARK: - AiConfigModel 智能互动的设置
class AiConfigModel: NSObject, Codable {

    var speedRatio: String = "1.0"
    var openOutTtsFile: Bool = false // Export backup while exporting nickname
    var openInTtsFile: Bool = false // Import backup while exporting nickname
    var openAutoTts: Bool = true // Whether to enable nickname
    
    // 这个三个是语音回复的合成文本
    var keyTtsModel: AiAutoTtsModel = AiAutoTtsModel(text: AIAudioReplyType.keyword.synthesisContent)
    var goodTtsModel: AiAutoTtsModel = AiAutoTtsModel(text: AIAudioReplyType.gift.synthesisContent )
    var attTtsModel: AiAutoTtsModel = AiAutoTtsModel(text: AIAudioReplyType.follow.synthesisContent)
    
    // 这三个是文字的回复模板数据
    var followAtNameModel: AiAutoTtsModel = AiAutoTtsModel(text: AIAudioReplyType.follow.content, isOpen: true)
    
    var giftAtNameModel: AiAutoTtsModel = AiAutoTtsModel(text: AIAudioReplyType.gift.content, isOpen: true)
    
    var keywordAtNameModel: AiAutoTtsModel = AiAutoTtsModel(text: AIAudioReplyType.keyword.content, isOpen: true)
    
    init(speedRatio: String = "1.0",
         openOutTtsFile: Bool = false,
         openInTtsFile: Bool = false,
         openAutoTts: Bool = true,
         keyTtsModel: AiAutoTtsModel = AiAutoTtsModel(text: AIAudioReplyType.keyword.synthesisContent),
         goodTtsModel: AiAutoTtsModel = AiAutoTtsModel(text: AIAudioReplyType.gift.synthesisContent ),
         attTtsModel: AiAutoTtsModel = AiAutoTtsModel(text: AIAudioReplyType.follow.synthesisContent),
         followAtNameModel: AiAutoTtsModel = AiAutoTtsModel(text: AIAudioReplyType.follow.content, isOpen: true),
         giftAtNameModel: AiAutoTtsModel = AiAutoTtsModel(text: AIAudioReplyType.gift.content, isOpen: true),
         keywordAtNameModel: AiAutoTtsModel = AiAutoTtsModel(text: AIAudioReplyType.keyword.content, isOpen: true)
    ) {
        self.speedRatio = speedRatio
        self.openOutTtsFile = openOutTtsFile
        self.openInTtsFile = openInTtsFile
        self.openAutoTts = openAutoTts
        self.keyTtsModel = keyTtsModel
        self.goodTtsModel = goodTtsModel
        self.attTtsModel = attTtsModel
        self.followAtNameModel = followAtNameModel
        self.giftAtNameModel = giftAtNameModel
        self.keywordAtNameModel = keywordAtNameModel
    }
    
  
    
    
}


class GiftSameGapModel: NSObject, Codable {
    var gap: GiftSameGap
    var selected: Bool = false
    init(gap: GiftSameGap, selected: Bool = false) {
        self.gap = gap
        self.selected = selected
    }
    
    static func gaps() -> [GiftSameGapModel] {
        [GiftSameGapModel(gap: .one),
         GiftSameGapModel(gap: .two),
         GiftSameGapModel(gap: .three),
         GiftSameGapModel(gap: .four),
         GiftSameGapModel(gap: .five),
         GiftSameGapModel(gap: .six)]
    }
}

enum GiftSameGap: Int, Codable {
    case one = 30
    case two = 60
    case three = 90
    case four = 120
    case five = 150
    case six = 180
    
    var title: String {
        switch self {
        case .one:
            "30秒"
        case .two:
            "1分钟"
        case .three:
            "1分30秒"
        case .four:
            "2分钟"
        case .five:
            "2分30秒"
        case .six:
            "3分钟"
        }
    }
}

class AiAutoTtsModel: Codable {
    var text: String
    var isOpen: Bool = false

    init(text: String, isOpen: Bool = false ) {
        self.text = text
        self.isOpen = isOpen
    }
    
    
    var tabViewHeight: CGFloat {
            
        let attContent = LCTools.checkNickConten(text: text, color: .white)
        
        let size = attContent.boundingRect(with: CGSize(width: LCDevice.screenW - 80, height: 300), options: [.usesLineFragmentOrigin, .usesFontLeading], context: nil).size
        
        return max(size.height + 54, 75)
            
    }
}

class NickHechengModel: Codable {
    var text: String
    var count: Int

    init(text: String, count: Int  ) {
        self.text = text
        self.count = count
    }

}

enum ReplyMsgLevel: Int, Codable {
    case levelMsg = 2 // 关键词回复、关注回复 的等级是最低的
    case level3Normal = 3
    case level4 = 4
    case level7 = 7
    case level10 = 10
    
    var value: Int {
        switch self {
        case .levelMsg:
            return  0
        case .level3Normal:
            return 100
        case .level4:
            return 2000
        case .level7:
            return 9999
        case .level10:
            return Int.max
        }
    }
    
    var des: String {
        switch self {
        case .levelMsg:
            return "文案消息级别"
        case .level3Normal:
            return "100钻以下"
        case .level4:
            return "101钻-2000钻"
        case .level7:
            return "2001钻-9999钻"
        case .level10:
            return "10000钻以上"
        }
    }
    
}

enum ReplyMsgType: Int, Codable {
    case keyword = 0 // 关键词回复
    case good = 1 // 送礼回复
    case attention = 2 // 关注回复
}




enum AIAudioReplyType {
    case keyword // 关键词
    case gift // 送礼
    case follow // 关注
    
    var title: String {
        switch self {
        case .keyword:
            return "关键词回复的文字模版"
        case .gift:
            return "感谢送礼的文字模版"
        case .follow:
            return "感谢关注的文字模版"
        }
    }
    
    var synthesisTitle: String {
        switch self {
        case .keyword:
            return "关键词语音模版"
        case .gift:
            return "送礼语音模版"
        case .follow:
            return "关注语音模版"
        }
    }
    
    var synthesisContent:String {
        switch self {
        case .keyword:
            return "【用户1】你好"
        case .gift:
            return "感谢【用户1】和【用户2】，还有【用户3】，【用户4】和【用户5】"
        case .follow:
            return "感谢【用户1】和【用户2】，还有【用户3】，【用户4】和【用户5】"
        }
    }
    
    var content:String {
        switch self {
        case .keyword:
            return "@【用户1】你好"
        case .gift:
            return "@【用户1】和@【用户2】，还有@【用户3】"
        case .follow:
            return "@【用户1】和@【用户2】，还有@【用户3】"
        }
    }
    
    var synthesisSample: NSAttributedString {
        switch self {
        case .keyword:
            let text = "当触发关键词，需要回复用户时：\n需回复用户昵称为：时光雕刻师\n\n回复模版为：\n【用户1】你好\n\n那么，在语音互动回复前会自动加类似如下语音消息：\n时光雕刻师，你好"
            let paraph = NSMutableParagraphStyle()
            paraph.lineSpacing = 6
            let attributes = [NSAttributedString.Key.paragraphStyle: paraph, NSAttributedString.Key.foregroundColor: UIColor("#1E1F20"), NSAttributedString.Key.font: UIFont.systemFont(ofSize: 14, weight: .regular)]
            
            let attributedText = NSMutableAttributedString(string: text, attributes: attributes)
            
            attributedText.setAttributes([NSAttributedString.Key.paragraphStyle: paraph, NSAttributedString.Key.font: UIFont.systemFont(ofSize: 14, weight: .bold)], range: NSRange(location: 0, length: 15))
            
            attributedText.setAttributes([NSAttributedString.Key.paragraphStyle: paraph, NSAttributedString.Key.font: UIFont.systemFont(ofSize: 14, weight: .bold)], range: NSRange(location: 32, length: 6))
            
            attributedText.setAttributes([NSAttributedString.Key.paragraphStyle: paraph, NSAttributedString.Key.font: UIFont.systemFont(ofSize: 14, weight: .bold)], range: NSRange(location: 48, length: 24))
            
            
            return attributedText
        case .gift:
            let text = "当有用户送礼时：\n送礼用户昵称为：北极光之梦、量子猫咪、云端漫步、代码诗人、时光雕刻师\n\n回复模版为：\n感谢【用户1】和【用户2】，还有【用户3】，【用户4】和【用户5】\n\n那么，在语音互动回复前会自动加类似如下语音消息：\n感谢北极光之梦和量子猫咪，还有云端漫步，代码诗人和时光雕刻师"
            let paraph = NSMutableParagraphStyle()
            paraph.lineSpacing = 6
            let attributes = [NSAttributedString.Key.paragraphStyle: paraph, NSAttributedString.Key.foregroundColor: UIColor("#1E1F20"), NSAttributedString.Key.font: UIFont.systemFont(ofSize: 14, weight: .regular)]
            
            let attributedText = NSMutableAttributedString(string: text, attributes: attributes)
            
            attributedText.setAttributes([NSAttributedString.Key.paragraphStyle: paraph, NSAttributedString.Key.font: UIFont.systemFont(ofSize: 14, weight: .bold)], range: NSRange(location: 0, length: 8))
            
            attributedText.setAttributes([NSAttributedString.Key.paragraphStyle: paraph, NSAttributedString.Key.font: UIFont.systemFont(ofSize: 14, weight: .bold)], range: NSRange(location: 45, length: 6))
            
            attributedText.setAttributes([NSAttributedString.Key.paragraphStyle: paraph, NSAttributedString.Key.font: UIFont.systemFont(ofSize: 14, weight: .bold)], range: NSRange(location: 87, length: 24))
            
            return attributedText
        case .follow:
            let text = "当有用户关注时：\n关注用户昵称为：北极光之梦、量子猫咪、云端漫步、代码诗人、时光雕刻师\n\n回复模版为：\n感谢【用户1】和【用户2】，还有【用户3】，【用户4】和【用户5】\n\n那么，在语音互动回复前会自动加类似如下语音消息：\n感谢北极光之梦和量子猫咪，还有云端漫步，代码诗人和时光雕刻师"
            let paraph = NSMutableParagraphStyle()
            paraph.lineSpacing = 6
            let attributes = [NSAttributedString.Key.paragraphStyle: paraph, NSAttributedString.Key.foregroundColor: UIColor("#1E1F20"), NSAttributedString.Key.font: UIFont.systemFont(ofSize: 14, weight: .regular)]
            
            let attributedText = NSMutableAttributedString(string: text, attributes: attributes)
            
            attributedText.setAttributes([NSAttributedString.Key.paragraphStyle: paraph, NSAttributedString.Key.font: UIFont.systemFont(ofSize: 14, weight: .bold)], range: NSRange(location: 0, length: 8))
            
            attributedText.setAttributes([NSAttributedString.Key.paragraphStyle: paraph, NSAttributedString.Key.font: UIFont.systemFont(ofSize: 14, weight: .bold)], range: NSRange(location: 45, length: 6))
            
            attributedText.setAttributes([NSAttributedString.Key.paragraphStyle: paraph, NSAttributedString.Key.font: UIFont.systemFont(ofSize: 14, weight: .bold)], range: NSRange(location: 87, length: 24))
            
            return attributedText
        }
    }
    
   
    
    var sample: NSAttributedString {
        switch self {
        case .keyword:
            let text = "当触发关键词，需要回复用户时：\n需回复用户昵称为：北极光之梦\n\n回复模版为：\n@【用户1】你好\n\n那么，在文字互动回复前会自动加类似如下文字消息：\n@北极光之梦你好"
            let paraph = NSMutableParagraphStyle()
            paraph.lineSpacing = 6
            let attributes = [NSAttributedString.Key.paragraphStyle: paraph, NSAttributedString.Key.foregroundColor: UIColor("#1E1F20"), NSAttributedString.Key.font: UIFont.systemFont(ofSize: 14, weight: .regular)]
            
            let attributedText = NSMutableAttributedString(string: text, attributes: attributes)
            
            attributedText.setAttributes([NSAttributedString.Key.paragraphStyle: paraph, NSAttributedString.Key.font: UIFont.systemFont(ofSize: 14, weight: .bold)], range: NSRange(location: 0, length: 15))
            
            attributedText.setAttributes([NSAttributedString.Key.paragraphStyle: paraph, NSAttributedString.Key.font: UIFont.systemFont(ofSize: 14, weight: .bold)], range: NSRange(location: 32, length: 6))
            
            attributedText.setAttributes([NSAttributedString.Key.paragraphStyle: paraph, NSAttributedString.Key.font: UIFont.systemFont(ofSize: 14, weight: .bold)], range: NSRange(location: 49, length: 24))
            
            
            return attributedText
        case .gift:
            let text = "当有用户送礼时：\n送礼用户昵称为：北极光之梦、量子猫咪、云端漫步\n\n回复模版为：\n@【用户1】和@【用户2】，还有@【用户3】\n\n那么，在文字互动回复前会自动加类似如下文字消息：\n@北极光之梦和@量子猫咪，还有@云端漫步"
            let paraph = NSMutableParagraphStyle()
            paraph.lineSpacing = 6
            let attributes = [NSAttributedString.Key.paragraphStyle: paraph, NSAttributedString.Key.foregroundColor: UIColor("#1E1F20"), NSAttributedString.Key.font: UIFont.systemFont(ofSize: 14, weight: .regular)]
            
            let attributedText = NSMutableAttributedString(string: text, attributes: attributes)
            
            attributedText.setAttributes([NSAttributedString.Key.paragraphStyle: paraph, NSAttributedString.Key.font: UIFont.systemFont(ofSize: 14, weight: .bold)], range: NSRange(location: 0, length: 8))
            
            attributedText.setAttributes([NSAttributedString.Key.paragraphStyle: paraph, NSAttributedString.Key.font: UIFont.systemFont(ofSize: 14, weight: .bold)], range: NSRange(location: 34, length: 6))
            
            attributedText.setAttributes([NSAttributedString.Key.paragraphStyle: paraph, NSAttributedString.Key.font: UIFont.systemFont(ofSize: 14, weight: .bold)], range: NSRange(location: 65, length: 25))
            
            return attributedText
        case .follow:
            let text = "当有用户关注时：\n关注用户昵称为：北极光之梦、量子猫咪、云端漫步\n\n回复模版为：\n@【用户1】和@【用户2】，还有@【用户3】\n\n那么，在文字互动回复前会自动加类似如下文字消息：\n@北极光之梦和@量子猫咪，还有@云端漫步"
            let paraph = NSMutableParagraphStyle()
            paraph.lineSpacing = 6
            let attributes = [NSAttributedString.Key.paragraphStyle: paraph, NSAttributedString.Key.foregroundColor: UIColor("#1E1F20"), NSAttributedString.Key.font: UIFont.systemFont(ofSize: 14, weight: .regular)]
            
            let attributedText = NSMutableAttributedString(string: text, attributes: attributes)
            
            attributedText.setAttributes([NSAttributedString.Key.paragraphStyle: paraph, NSAttributedString.Key.font: UIFont.systemFont(ofSize: 14, weight: .bold)], range: NSRange(location: 0, length: 8))
            
            attributedText.setAttributes([NSAttributedString.Key.paragraphStyle: paraph, NSAttributedString.Key.font: UIFont.systemFont(ofSize: 14, weight: .bold)], range: NSRange(location: 34, length: 6))
            
            attributedText.setAttributes([NSAttributedString.Key.paragraphStyle: paraph, NSAttributedString.Key.font: UIFont.systemFont(ofSize: 14, weight: .bold)], range: NSRange(location: 65, length: 25))
            
            return attributedText
        }
    }
}
