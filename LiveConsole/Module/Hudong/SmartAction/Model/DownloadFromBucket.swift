//
//  DownloadFromBucket.swift
//  LivePlus
//
//  Created by 郭炜 on 2025/2/7.
//

import UIKit

// MARK: - 从Bucket中下载文件

let endpoint = "tos-cn-shanghai.volces.com";
let region = "cn-shanghai";
let accessKey = "AKLTNDlkOWJhODRlZDg3NDg0NDlkMTI5OTZmYWQ3YTI2NDE";
let accessSecret = "TTJNM1pUUXdPR0V6WXpFNE5EVTVOVGd5TkdReU4ySmtNVGxrTlRSbE5UVQ==";
let bucketName = "klzk-voice-bucket";  //对象存储的参数需要修改以下

class DownloadFromBucket {
    /// 下载文件
    /// 文件名 debug/3/6e59b316961235d2_completion.wav
    static func download(fileName: String, completion: @escaping ((String?) -> Void)) {
        let credential = TOSCredential(accessKey: accessKey, secretKey: accessSecret)
        let tosEndpoint = TOSEndpoint(urlString: endpoint, withRegion: region)
        let config = TOSClientConfiguration(endpoint: tosEndpoint,
                                            credential: credential)
        let client = TOSClient(configuration: config)
        let getObject = TOSGetObjectToFileInput()
        getObject.tosBucket = bucketName
        getObject.tosKey = fileName
        let audioName = fileName.lastPathComponent
        let localDownloadFilePath = AudioPathStyle.aiGeneratePreview.path.appendingPathComponent(audioName)
        getObject.tosFilePath = localDownloadFilePath
        let task = client.getObjectToFile(getObject)
        task.continueWith { result in
            if let error = result.error {
                print("下载文件：\(fileName) 失败")
                completion(nil)
            }
            let output = result.result
            print("下载文件结果：\(String(describing: output))")
            completion(localDownloadFilePath)
            return nil
        }
    }
    
    /// 上传文件
    static func upload(fileUrl: URL, completion: @escaping ((String?) -> Void)) {
        let credential = TOSCredential(accessKey: accessKey, secretKey: accessSecret)
        let tosEndpoint = TOSEndpoint(urlString: endpoint, withRegion: region)
        let config = TOSClientConfiguration(endpoint: tosEndpoint,
                                            credential: credential)
        let client = TOSClient(configuration: config)
        
        let putObject = TOSPutObjectFromFileInput()
        putObject.tosBucket = bucketName
        let fileName = fileUrl.lastPathComponent
        
        let uid: UInt = UserInfo.currentUser()?.userId ?? 0
        
        var fullUrl = fileName
        let isRelease = UserDefaults.standard.bool(forKey: LCKey.UD_ReleaseEnv)
        if isRelease {
            fullUrl = "online/\(uid)/copy_\(fileName)"
        } else {
            fullUrl = "debug/\(uid)/copy_\(fileName)"
        }
        
        putObject.tosKey = fullUrl
        putObject.tosMeta = ["usermeta-key-1": "usermeta-value-1", "usermeta-key-2": "usermeta-value-2"]
        putObject.tosFilePath = fileUrl.path
        let task = client.putObject(fromFile: putObject)
        task.continueWith { result in
            if let error = result.error {
                print("上传文件：\(fullUrl) 失败")
                completion(nil)
            }
            let output = result.result
            print("上传文件结果：\(String(describing: output))")
            completion(fullUrl)
            return nil
        }
    }
    
    
    /// 上传文件
    static func delet(fileName: String, completion: @escaping ((String?) -> Void)) {
        let credential = TOSCredential(accessKey: accessKey, secretKey: accessSecret)
        let tosEndpoint = TOSEndpoint(urlString: endpoint, withRegion: region)
        let config = TOSClientConfiguration(endpoint: tosEndpoint,
                                            credential: credential)
        let client = TOSClient(configuration: config)
        
        let putObject = TOSDeleteObjectInput()
        putObject.tosBucket = bucketName
        putObject.tosKey = fileName
        
        
        let task = client.deleteObject(putObject)
        task.continueWith { result in
            if let error = result.error {
                print("删除文件：\(fileName) 失败")
                completion(nil)
            }
            let output = result.result
            print("删除文件结果：\(String(describing: output))")
            completion(fileName)
            return nil
        }
    }
    
  

}

extension FileManager {
    class var documentPath: String {
        guard let documentPath = NSSearchPathForDirectoriesInDomains(.documentDirectory, .userDomainMask, true).last else {
            return ""
        }
        return documentPath
    }
    class var cachesPath: String {
        guard let cachesPath = NSSearchPathForDirectoriesInDomains(.cachesDirectory, .userDomainMask, true).last else {
            return ""
        }
        return cachesPath
    }
    class var tempPath: String {
        NSTemporaryDirectory()
    }
}
