//
//  DouyinGiftModel.swift
//  LivePlus
//
//  Created by simon on 9.1.25.
//

import Foundation


enum DouyinGiftSort {
    case none
    case ascending
    case descending
}

enum DouyinGiftFilter: Int64, Codable {
    case all = 0
    case less100 = 100
    case less2000 = 101
    case less9999 = 2001
    case more10000 = 10000
    case recommend = 99999
    
    
    var title: String {
        switch self {
        case .all:
            "全部"
        case .less100:
            "100钻以下"
        case .less2000:
            "101钻~2000钻"
        case .less9999:
            "2001钻~9999钻"
        case .more10000:
            "10000钻以上"
        case .recommend:
            "推荐"
        }
    }
}

class DouyinGiftModel: NSObject, Codable {
    var pages: Int
    var size: Int
    var total: Int
    var current: Int
    var optimizeCountSql: Bool
    var searchCount: Bool
    var records: [DouyinGiftModel_Row]
    
    func sortByDiamondCountAscending() {
        
        records.sort(by: {
            guard let first = $0.image?.diamondCount, let second = $1.image?.diamondCount  else {
                return false
            }
            let firstCount: Int64 = Int64(first) ?? 0
            let secondCount: Int64 = Int64(second) ?? 0
            
            return firstCount  < secondCount
        })
        
    }
    
    func sortByDiamondCountDescending() {
        records.sort(by: {
            guard let first = $0.image?.diamondCount, let second = $1.image?.diamondCount  else {
                return false
            }
            let firstCount: Int64 = Int64(first) ?? 0
            let secondCount: Int64 = Int64(second) ?? 0
            
            return firstCount  > secondCount
        })
        
    }
  
}

public class DouyinGiftModel_Row:NSObject, Codable {
    var id: Int
    var giftInfo: String
    var valid : Bool
    var image: DouyinGiftInfo? // 计算获得 接口不返回
    var enable: Bool? = true // 计算获得  接口不返回
    init(id: Int, giftInfo: String, valid: Bool, image: DouyinGiftInfo? = nil, enable: Bool? = true) {
        self.id = id
        self.giftInfo = giftInfo
        self.valid = valid
        self.image = image
        self.enable = enable
    }
}

class DouyinGiftInfo: NSObject, Codable {
    var image: DouyinGiftInfol_Image
    var name: String
    var diamondCount: String
    var id: String
    var type: String
    

    // 是否是低价值礼物， 100钻以下合并
    var lowValue: Bool {
        if let diamond = Int64(diamondCount), diamond >= 100 {
            return false
        }
        return true
    }
}

class DouyinGiftInfol_Image: NSObject, Codable{
    var width: String
    var avgColor: String
    var openWebUrl: String
    var urlList:[String]
    var isAnimated: Bool
    var imageType: String
    var uri: String
    var height: String
}


class GiftFilterModel: NSObject, Codable{
    var type: DouyinGiftFilter = .all
    
    var selected: Bool = false
    
    init(type: DouyinGiftFilter = .all, selected: Bool = false) {
        self.type = type
        self.selected = selected
    }
    
    static var allModels:[GiftFilterModel] = [GiftFilterModel(type: .recommend, selected: true),
                                              GiftFilterModel(type: .less100, selected: false),
                                              GiftFilterModel(type: .less2000, selected: false),
                                              GiftFilterModel(type: .less9999, selected: false),
                                              GiftFilterModel(type: .more10000, selected: false),
                                              GiftFilterModel(type: .all, selected: false)
    ]
    
}
