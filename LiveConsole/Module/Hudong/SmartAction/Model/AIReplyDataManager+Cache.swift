//
//  AIReplyDataManager+Cache.swift
//  LivePlus
//
//  Created by simon on 21.3.25.
//

import Foundation

extension AIReplyDataManager {
    
    //MARK: - 取出所有的音色缓存信息
    func loadCacheSpkid() {
        guard let userId = UserInfo.currentUser()?.userId  else { return  }
        
        // 上次保存的音色信息
        if let dict = getUD_data(key: LCKey.UD_AudioSpkIdCacheKey) {
            if  let spkstr = dict["\(userId)"], let spk: AudioSpkIdCache = JsonTool.string2Model(spkstr) {
                self.spkIdCache = spk
            }
        }

    }
    
    // MARK: -  取出所有的TTS缓存信息
    func loadTTS_Record() {
        guard let userId = UserInfo.currentUser()?.userId  else { return  }
        
        // 上次保存的音色信息
        if let dict = getUD_data(key: LCKey.UD_TTS_RecordsKey) {
            if  let spkstr = dict["\(userId)"], let record: TTS_Records = JsonTool.string2Model(spkstr) {
                self.ttsRecords = record
            }
        }
    }
    
    
    // MARK: - 智能互动+设置
    func loadDataBase() {
        
        guard let userId = UserInfo.currentUser()?.userId else { return }
        
        let curModels = DataBaseManager.queryObject(objectClass: InteractionObject(), filter: "userId == \(userId)")
        
        if let oldModel = curModels.first {
            if let m: InteractionModel = JsonTool.string2Model(oldModel.jsonString) {
                self.interactionModel = m
            }
            self.interactionObject = oldModel
        } else {
            self.interactionModel = InteractionModel()
        }
        
        let curModels1 = DataBaseManager.queryObject(objectClass: AiConfigObject(), filter: "userId == \(userId)")
        
        if let oldModel1 = curModels1.first {
            if let m: AiConfigModel = JsonTool.string2Model(oldModel1.jsonString) {
                self.aiConfig = m
            }
            self.configObject = oldModel1
        }
        
    }
    
    // MARK: - 智能互动+设置 数据保存到本地的数据库
    func saveToDataBase() {
        // 1. interactionModel 保存到本地
        guard let user = UserInfo.currentUser(), let userId = user.userId else { return }
        
        if let interactionModel = interactionModel {
            let js = JsonTool.model2String(interactionModel)
            
            if let history = self.interactionObject {
                DataBaseManager.updateObjectAttribute(object: history, attribute: ["jsonString": js])
            } else {
                let objc = InteractionObject(userId: Int(userId), jsonString: js)
                DataBaseManager.add(object: objc)
                
                self.interactionObject = objc
            }
            
        }
        
        // 2. aiConfig 保存到本地
        let cfgjs = JsonTool.model2String(aiConfig)
        
        if let history = self.configObject {
            DataBaseManager.updateObjectAttribute(object: history, attribute: ["jsonString": cfgjs])
        } else {
            let objc1 = AiConfigObject(userId: Int(userId), jsonString: cfgjs)
            DataBaseManager.add(object: objc1)
            self.configObject = objc1
        }
        
        // 3 保存TTS的记录
        if var dict = getUD_data(key: LCKey.UD_TTS_RecordsKey) {
            let json = JsonTool.model2String(ttsRecords)
            dict["\(userId)"] = json
            UserDefaults.standard.set(object: dict, forKey: LCKey.UD_TTS_RecordsKey)
        } else {
            let json = JsonTool.model2String(ttsRecords)
            var dict: [String: String] = [:]
            dict["\(userId)"] = json
            UserDefaults.standard.set(object: dict, forKey: LCKey.UD_TTS_RecordsKey)
        }
        
        // 4 保存音色的记录
        if  var dict = getUD_data(key: LCKey.UD_AudioSpkIdCacheKey) {
            let json = JsonTool.model2String(spkIdCache)
            dict["\(userId)"] = json
            UserDefaults.standard.set(object: dict, forKey: LCKey.UD_AudioSpkIdCacheKey)
        } else {
            let json = JsonTool.model2String(spkIdCache)
            var dict: [String: String] = [:]
            dict["\(userId)"] = json
            UserDefaults.standard.set(object: dict, forKey: LCKey.UD_AudioSpkIdCacheKey)
        }
    }
    
    // 查询是否有记录
    func  queryRecordExists(tts: TTS_RecordModel) -> TTS_RecordModel? {
        if let reocord = self.ttsRecords.records.first(where: {$0.spkid == tts.spkid && $0.text == tts.text && $0.speed == tts.speed}) {
            
            let path = AudioPathStyle.audioRecord.path.appendingPathComponent(reocord.audioPath)
            
            if !reocord.audioPath.isEmpty, FileManager.default.fileExists(atPath: path) {
                return reocord
            }
        }
        
        return nil
    }
    
    func getUD_data(key: String) -> [String: String]? {
        
        guard let userId = UserInfo.currentUser()?.userId  else { return  nil}
        
        if let data = UserDefaults.standard.object(forKey: key) as? Data {
            do {
                if let dict = try JSONSerialization.jsonObject(with: data, options: []) as? [String: String] {
                   return dict
                }
            } catch {
                print("JSONSerialization 转换出错: \(error)")
            }
        }
        return nil
    }
    
    
    // MARK: - Notification
    @objc func userLoginNoticeAction() {
        // 清空用户选的音色
        AIReplyDataManager.shared.spkIdCache.currentPreAudioModel = nil
        AIReplyDataManager.shared.spkIdCache.currentCloneAudioModel = nil
        updateDataBase()
    }
    
    @objc func userLogoutNoticeAction() {
        // 清空用户选的音色
        AIReplyDataManager.shared.spkIdCache.currentPreAudioModel = nil
        AIReplyDataManager.shared.spkIdCache.currentCloneAudioModel = nil
        updateDataBase()
    }
    
    // MARK: - 添加一个新卡片
    func addNewCardItem(replyItem:AiKeywordVoiceReplyItemModel) {
        //如果没有系统互动， 现在需要创建一个
        if let ms = AIReplyDataManager.shared.interactionModel {
            if !ms.cardList.contains(where: {$0.id == replyItem.id}) {
                ms.cardList.append(replyItem)
            }
        } else {
            AIReplyDataManager.shared.interactionModel = InteractionModel(cardList: [replyItem])
        }
        
    }
}
