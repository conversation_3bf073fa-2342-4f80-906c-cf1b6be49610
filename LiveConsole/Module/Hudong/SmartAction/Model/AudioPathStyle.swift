//
//  AudioPathStyle.swift
//  LivePlus
//
//  Created by 郭炜 on 2025/1/16.
//

import UIKit

// MARK: - 缓存音频的文件夹路径
let Documents = NSSearchPathForDirectoriesInDomains(.documentDirectory, .userDomainMask, true)[0]
enum AudioPathStyle {

    case audioRecord
    case aiGeneratePreview // AI合成的本地路径
    
    var path: String {
        var returnPath: String
        switch self {
        case .audioRecord:
            returnPath = Documents.appendingPathComponent("SmartAudioRecord")
        case .aiGeneratePreview:
            // 都用一个路径 好处理一点
            returnPath = Documents.appendingPathComponent("SmartAudioRecord")
        }
        do {
            if !FileManager.default.fileExists(atPath: returnPath) {
                try FileManager.default.createDirectory(atPath: returnPath, withIntermediateDirectories: true)
            }
        } catch {
            print("文件创建失败 \(error)")
        }
        return returnPath
    }
}
