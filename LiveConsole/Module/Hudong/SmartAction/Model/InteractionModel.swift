//
//  InteractionModel.swift
//  LivePlus
//
//  Created by simon on 6.1.25.
//

import Foundation
import Realm
import RealmSwift


struct RoomBackUpModel: Codable {
    // 直播间标题
    var title: String
    // 直播间ID
    var roomId: String
    // 微秒时间戳
    var time: String
    // 需要的最小用户权限
    var minLevel: Int
    // 用户id
    var userId: String
}

class AIBackupModel: NSObject, Codable {
     var configModel: AiConfigModel
     var dataSource: InteractionModel
    init(configModel: AiConfigModel, dataSource: InteractionModel) {
        self.configModel = configModel
        self.dataSource = dataSource
    }
}

class InteractionModel: NSObject,Codable {
    var interactionType: InteractionType = InteractionType.userChat // 互动类型
    var systemInteractionTypeList: [SystemInteractionType] = [] // 系统互动类型列表
    var sortType: SortType = SortType.createTime // 排序类型
    var orderType: OrderType = OrderType.aes // 排序顺序，升序或降序
    var previewType: PreviewType = PreviewType.all // 预览类型
    // 注意  只有 cardList 是保存的所有数据， 上面的是用于在交互上使用的 过滤的参数
    
    var cardList: [AiKeywordVoiceReplyItemModel] = []
    
    init(cardList: [AiKeywordVoiceReplyItemModel] = []) {
        self.cardList = cardList
    }
    
    // 是否可以选择全部的类型
    var isAllSystemInteraction:Bool {
        if systemInteractionTypeList.isEmpty {
            return true
        }
        if systemInteractionTypeList.contains(where: {$0 == .gift}) && systemInteractionTypeList.contains(where: {$0 == .follow }) {
            return true
        }
        return false
    }
    
    
}

enum InteractionType: Int, Codable {
    case userChat = 0 // 用户评论
    case systemInteraction = 1 // 系统互动
}


enum SystemInteractionType: Int, Codable {
    case gift = 0 // 赠送
    case follow = 1 // 关注

     func asList() -> [SystemInteractionType] {
         return [.gift, .follow]
    }
    
    var des: String {
        switch self {
        case .gift:
            return "收到指定礼物，触发语音或文字回复"
        case .follow:
            return "收到关注时，触发语音或文字回复"
        }
    }
    
    var title: String {
        switch self {
        case .gift:
            return "送礼回复"
        case .follow:
            return "关注回复"
        }
    }
    
    var imageName: String {
        switch self {
        case .gift:
            return "reply_gift"
        case .follow:
            return "reply_flow"
        }
    }
}

enum SortType: Int, Codable  {
    case createTime = 0 // 按创建时间
    case updateTime = 1 // 按修改时间
}

enum OrderType: Int, Codable {
    case aes = 0 // 升序
    case des = 1 // 降序
    
    var reversed: OrderType {
        switch self {
        case .aes:
            return .des
        case .des:
            return .aes
        }
    }
}

enum PreviewType: Int, Codable {
    case all = 0 // 全部
    case onlyEffective = 1 // 仅看生效的
    case onlyInvalid = 2 // 仅看停用的
}

enum GiftFilterType: Int, Codable {
    case diamond100 = 0 // 100钻以下
    case diamond101_2000 = 1 // 101钻~2000钻
    case diamond2001_9999 = 2 // 2001钻~9999钻
    case diamond10000 = 3 // 10000钻以上
}


// 保存到本地数据库
class InteractionObject: Object {

    @objc dynamic var userId: Int = 0
    
    @objc dynamic var jsonString: String = ""
    
    @objc dynamic var saveTime = ""

    convenience init(userId: Int, jsonString: String) {
        self.init()
        self.userId = userId
        self.jsonString = jsonString
        self.saveTime = LCTools.secondStamp()
    }
}


// 保存到本地数据库
class AiConfigObject: Object {

    @objc dynamic var userId: Int = 0
    
    @objc dynamic var jsonString: String = ""
    
    @objc dynamic var saveTime = ""

    convenience init(userId: Int, jsonString: String) {
        self.init()
        self.userId = userId
        self.jsonString = jsonString
        self.saveTime = LCTools.secondStamp()
    }
    
}

