//
//  AreaItemModel.swift
//  LiveConsole
//
//  Created by simon on 21.4.25.
//

import Foundation
import UIKit
import YQImageCompressor
import Photos

/// 图层--区域 内容类型
public enum AreaItemType: UInt, Codable {
   
    case  unknown = 0
    
    // 音乐 ()
    case  musicLocal = 9
    
    // 文本
    case  text = 10
}

/// 图层---区域 单个素材定义
class AreaItemModel: NSObject, NSCopying {
    /// 资源文件名
    public var name: String?

    // 如果回复的是文本的内容
    public var text: String?
    
    // 生成的是多条回复文本
    public var allTexts:[String]?
    
    public var voiced: Bool = false
        
    /// 视频时长(秒)
    public var duration = Int(0)
    
    
    // 选择模式下 当前的资源有没有被选中
    public var selected: Bool = false
    
    // 添加素材的时候
    public var  sourceId: String?
  
    public var  type: AreaItemType = .unknown
    
    // 为了兼容ResFileModel
    var toAudioPlayUrl: URL {
        return URL(fileURLWithPath: AudioPathStyle.audioRecord.path.appendingPathComponent(self.name ?? ""))
    }
    
    var voidespeed: String?
    
    // MARK: - init
    
    /// 区域素材初始化
    public init(name: String?,
                type: AreaItemType,
                duration: Int,
                voiced: Bool = false,
                sourceId: String?,
                text: String?) {
        self.name = name
        self.type = type
        self.duration = duration
        self.voiced = voiced
        self.text = text
        
        if let sourceId = sourceId {
            self.sourceId = sourceId
        } else {
            self.sourceId = LCTools.milliStamp() + String.random(ofLength: 8)
        }
    
    }
    
    func copy(with zone: NSZone? = nil) -> Any {
        let itemModel = AreaItemModel(name: name, type: self.type, duration: self.duration, sourceId:LCTools.milliStamp() + String.random(ofLength: 8) ,text: text)
        return itemModel
    }
    

    override init() {
        super.init()
    }
    
    // MARK: - pulic method
    
    /// 获取资源加载的本地完整路径
    public var path: String? {
        get {
            guard let name = name, !name.isEmpty else { return nil }
            return LCTools.getMaterialSourceLocalFullPathFromFolder(name)
        }
    }


    /// 获取当前视频的时长（如：00:12）
    public var durationString: String {
        let dur = self.duration
        switch dur {
        case 0..<60:
            return String(format: "00:%02d", dur)
        case 60..<3600:
            let m = dur / 60
            let s = dur % 60
            return String(format: "%02d:%02d", m, s)
        case 3600...:
            let h = dur / 3600
            let m = (dur % 3600) / 60
            let s = dur % 60
            return String(format: "%02d:%02d:%02d", h, m, s)
        default:
            return ""
        }
    }
    
    static public func == (lhs: AreaItemModel, rhs: AreaItemModel) -> Bool {
        return lhs.hashValue == rhs.hashValue && lhs.name == rhs.name
    }
}
