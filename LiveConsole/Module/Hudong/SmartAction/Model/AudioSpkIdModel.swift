//
//  AudioSpkIdModel.swift
//  LivePlus
//
//  Created by 郭炜 on 2025/1/16.
//

import UIKit

// MARK: - 预置音色、已克隆音色列表
class AudioSpkIdModel: Codable {
    /// 用户克隆的音色列表
    var userSpk: [AudioUserSpeakModel]
    /// 预设的音色列表
    var preSpk: [AudioPreSpeakModel]
    
    init(userSpk: [AudioUserSpeakModel], preSpk: [AudioPreSpeakModel]) {
        self.userSpk = userSpk
        self.preSpk = preSpk
    }
}

// MARK: - 克隆音色模型
class AudioUserSpeakModel: Codable {
    var spkId: String
    var spkName: String? = "未命名"
    var spkVersion: String? = "V0"
    var spkStatus: Int
    // 试听url
    var previewUrl: String?
    // 状态值 Completed = 完成
     // Training = 训练中
    var statusStr : String
  
    init(spkId: String, spkName: String? = nil, spkVersion: String? = nil, spkStatus: Int, previewUrl: String? = nil, statusStr: String) {
        self.spkId = spkId
        self.spkName = spkName
        self.spkVersion = spkVersion
        self.spkStatus = spkStatus
        self.previewUrl = previewUrl
        self.statusStr = statusStr
    }
}

// MARK: - 预置音色模型
class AudioPreSpeakModel: Codable {
    var voiceType: String
    var voiceName: String
    var voiceUrl: String
    
    init(voiceType: String, voiceName: String, voiceUrl: String) {
        self.voiceType = voiceType
        self.voiceName = voiceName
        self.voiceUrl = voiceUrl
    }
}

// MARK: - 预置模式音频生成结果
class AudioPresetCombineModel: Codable {
    /// 音频名：存放在bucket桶中的
    var audioName: String?
    /// 剩余可用积分
    var points: Int?
    /// 当前使用的字符
    var preLen: Int?
    
    init(audioName: String? = nil, points: Int? = nil, preLen: Int? = nil) {
        self.audioName = audioName
        self.points = points
        self.preLen = preLen
    }
}

// MARK: - 克隆模式音频生成结果
class AudioCloneCombineModel: Codable {
    /// 音频名：存放在bucket桶中的
    var audioName: String?
    /// 剩余可用积分
    var points: Int?
    /// 当前使用的字符
    var preLen: Int?
    
    init(audioName: String? = nil, points: Int? = nil, preLen: Int? = nil) {
        self.audioName = audioName
        self.points = points
        self.preLen = preLen
    }
}

// MARK: - 用户训练克隆音色结果
class AudioTrainResultModel: Codable {
    /// 创建时间
    var createTime: Int64?
    /// demo音频
    var demoAudio: String?
    /// SpeakerId
    var speakerId: String
    /// 状态
    var status: Int?
    /// 版本
    var version: String?
    // 训练任务ID
    var taskId: String?
//积分
    var  points: Int64
    // 消耗积分
    var neededPoints: Int64
}


class AsyncCloneTtsModel: Codable {
    /// 任务ID
    var taskId: String?
    /// 剩余可用字符
    var balanceWord: Int?
    
    init(taskId: String? = nil, balanceWord: Int? = nil) {
        self.taskId = taskId
        self.balanceWord = balanceWord
    }
}

class AsyncTTSStatusModel: Codable {
    /// 音频名：存放在bucket桶中的
    var audioName: String?
    /// 剩余可用字符
    var balanceWord: Int?
    /// 当前使用的字符
    var preLen: Int?
}


// MARK: - 用户选择的音色信息，做缓存
class AudioSpkIdCache: Codable {
    /// 当前选择的语速
    var currentSpeed: Float = 1.0
    /// 当前选择的预置音色
    var currentPreAudioModel: AudioPreSpeakModel? {
        didSet {
            if let currentPreAudioModel = currentPreAudioModel {
                self.currentCloneAudioModel = nil
            }
        }
    }
    
    /// 当前选择的克隆音色
    var currentCloneAudioModel: AudioUserSpeakModel? {
        didSet {
            if let currentCloneAudioModel = currentCloneAudioModel {
                self.currentPreAudioModel = nil
            }
        }
    }

    init(currentSpeed: Float = 1.0, currentPreAudioModel: AudioPreSpeakModel? = nil, currentCloneAudioModel: AudioUserSpeakModel? = nil) {
        self.currentSpeed = currentSpeed
        self.currentPreAudioModel = currentPreAudioModel
        self.currentCloneAudioModel = currentCloneAudioModel
    }
   
    // 是否有一个
    var hasSpeak: Bool {
        if let _ = currentPreAudioModel {
            return true
        }
        
        if let _ = currentCloneAudioModel {
            return true
        }
        return false
    }
}



// MARK: - 用户克隆音色的文本
class AudioCloneTextModel: Codable {
    var id: Int
    var content: String
}

class EmptyResponseModel: Codable {
    var msgCode: String?
}

// 记录每个TTS的相关信息：文本、音色ID、语速。 如果有相同的TTS任务， 不在执行TTS请求， 直接返回上次的音频

class TTS_Records: NSObject, Codable {
    var records: [TTS_RecordModel]
    init(records: [TTS_RecordModel] = []) {
        self.records = records
    }
}

class TTS_RecordModel: NSObject, Codable {
    var text: String // 合成文本
    var spkid: String // 音色ID
    var speed: Float // 语速
    var creatTime: String // 合成时间
    var audioPath: String // 合成的TTS保存在本地的路径
    var type: String //
    init(text: String, spkid: String, speed: Float, creatTime: String = LCTools.secondStamp(), audioPath: String, type: String) {
        self.text = text
        self.spkid = spkid
        self.speed = speed
        self.creatTime = creatTime
        self.audioPath = audioPath
        self.type = type
    }
}



class TTSReqModel:NSObject, Codable {
    var isPreTts: Bool
    var spkId: String
    var text: String
    var isUseForName: Bool // 是不是昵称回复
    init(isPreTts: Bool = false, spkId: String = "" , text: String = "" , isUseForName: Bool = false) {
        self.isPreTts = isPreTts
        self.spkId = spkId
        self.text = text
        self.isUseForName = isUseForName
    }
    
}
