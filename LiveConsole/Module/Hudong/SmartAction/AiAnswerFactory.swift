//
//  AiAnswerFactory.swift
//  LivePlus
//
//  Created by simon on 8.2.25.
//

// 直播间触发的队列管理器

import Foundation
import UIKit
import AVFoundation

/****
 * 智能互动响应管理
 */
class AiAnswerFactory: NSObject {
    
    static let shared = AiAnswerFactory()
    
    private let max_execute_durataion: Int = 5 * 60  //最大5分钟兜底
                
    // 最后执行的一个资源
    private var lastResItem: AreaItemModel?
    
    //key：Int从小到大 所有要执行的任务数据都在这里
    private var workQueue: [Int: [WorkMessage]] = [:]
        
    private var isWorking = false //服务是否在运行
    
    private var isExePlaying = false //任务是否在执行中
    
    private var lastGoodSize = 0 //最近执行的礼物的回复的次数
        
    // 执行任务的队列
    private let queue: XSDispatchQueue = XSDispatchQueue(name: "com.liveplus.aiQueue")
    
    // 任务执行时的超时计时器， 一个任务最多有多少时间可以执行， 例如，回复小心心的礼物 可以设置为30秒， 那么如果触发语音恢复了， 30秒到语音没播放完， 要主动停止
    public var outTimer: Timer?
    
    override init() {
        super.init()
        startWork()
    }
    
    // 启动任务
    func startWork() {
        LCLog.d("startWork---->开启工作")
        isWorking = true
    }
    
    func stopWork() {
        LCLog.d( "stopWork---->停止工作")
        isWorking = false
    }
   
    func toRelease() {
        
        self.queue.performSynchronously(true) { [weak self] in
            guard let self = self else { return }
            // 删除所有的任务数据
            self.workQueue.removeAll()
            
            self.lastResItem = nil
            
            self.isExePlaying = false
                    
            self.stopRenderAudio()
            
            self.lastGoodSize = 0
        }
        
        // 取消计时器
        releaseTimer()
                
    }
    
    
    // MARK: - 开始执行
    func sendMessage() {
        self.queue.performSynchronously(true) { [weak self] in
            guard let self = self else { return }
            self.handleMessage()
        }
    }

    
    // 处理消息
    func handleMessage() {
        if isExePlaying {
            LCLog.d("当前任务正在执行中。。。 请等待")
            return
        }
        isExePlaying = true
        
        executeNextMsg { [weak self] isContinue in
            guard let self = self else { return }
            
            self.isExePlaying = false
            
            // 继续执行下一个
            if isContinue {
                self.sendMessage()
            }
        }
    }
    
   
    private func postMessageForHuDong(_ itemMsg: WorkMessage) {
        //
        var list: [WorkMessage] = workQueue[itemMsg.level.rawValue] ?? []
                
        let isSkip = isLowLevel(itemMsg)
        
        // 是否可以把这个任务添加到队列
        var isAddMsg = false
        
        if (isSkip) {
            //规则：普通商品，队列中已经有的且未执行的，直接忽略新的 相同的礼物ID作为key
            // TODO: - 礼物ID作为判断依据
            if let oldItem = list.first(where: {$0.key == itemMsg.key}), itemMsg.type == .good {
                LCLog.d("postMessage->忽略任务--》\(itemMsg);规则：普通商品，队列中已经有的且未执行的，直接忽略新的")
            } else {
                isAddMsg = true
            }
        } else {
            isAddMsg = true
        }
        
        // 如果正在工作
        if (isWorking) {
            // 是否需要自动合成TTS
            var isToAutoTts = false
            
            if (isAddMsg) {
                // 如果要包含昵称，需要吧昵称加进去 然后 重新合成TTS
                // 注意：合成成功之后需要加入队列
                let isDeal = checkForAutoTts(itemMsg)
                isToAutoTts = isDeal
                if (!isDeal) {
                    LCLog.d( "postMessage->任务放入队列中")
                    list.append(itemMsg)
                    // 保存下来
                    workQueue[itemMsg.level.rawValue] = list
                }
            }
            
            LCLog.d("postMessage->isAddMsg=\(isAddMsg);isToAutoTts=\(isToAutoTts);当前任务\(itemMsg.level)--》\(list.count)个;" )

            // 如果没有任务，直接触发队列
            if (!hasExecuteMsg()) {
                sendMessage()
            }
        } else {
            LCLog.d( "服务未开启，任务丢弃")
        }
    }
    
    /***
     * 实时昵称tts
     */
    private func checkForAutoTts(_ itemMsg: WorkMessage) -> Bool {
        
        // 如果回复设置的不是音频，也不需要执行TTS
        if !itemMsg.isVoiceType() {
            return false
        }
        
        var itemTtsModel: AiAutoTtsModel?
        
        if (itemMsg.type == .good) {
            itemTtsModel = getAiConfigModel().goodTtsModel
        } else if (itemMsg.type == .attention) {
            itemTtsModel = getAiConfigModel().attTtsModel
        } else {
            itemTtsModel = getAiConfigModel().keyTtsModel
        }
        
        // 如果根据回复类型触发的模型没有开， 那么就不需要执行
        guard let itemTtsModel = itemTtsModel, itemTtsModel.isOpen else {
            return false
        }
       
        // 如果没有开实时朗读用户昵称 就不走下面的合成逻辑
        if !AIReplyDataManager.shared.ttsNickenable {
            // 如果有多个昵称  要播放第二条语音 subPath 是第二条语音的数据 合成为语音内容为：你们 开头
            if let triggerUserNames = itemMsg.triggerUserNames, triggerUserNames.count > 1, let subPath = itemMsg.subName  {
                itemMsg.items.first?.name = subPath
            }
            return false
        }
        
        // 开始合成 这里会返回多条
        hechengTTSAction(type:itemMsg.type, tts: itemTtsModel, item: itemMsg) { [weak self] (finsh, path, count) in
            guard let self = self else { return }
            self.addTTSWorkeMessage(itemMsg: itemMsg, finsh: finsh, path: path, count: count)
        }
        
        return true
       
    }
    
    // 合成之后把任务添加到队列中
    private func addTTSWorkeMessage(itemMsg: WorkMessage, finsh: Bool, path: String?, count: Int) {
        
        self.queue.performSynchronously(true) { [weak self] in
            guard let self = self else { return }
            //需要创建一个新的任务加到当前这个任务的前面
            // 1、 创建新的任务
           var list: [WorkMessage] = self.workQueue[itemMsg.level.rawValue] ?? []
           
           if finsh, let path = path {
               
               let res =  AreaItemModel(name: path,
                                         type: .musicLocal,
                                         duration: 1000,
                                         sourceId: "",
                                         text: nil )
               
               // 2、添加TTS之后的音频任务：
               let newItem = WorkMessage(key: itemMsg.key , level: itemMsg.level, type: itemMsg.type , item: res)
               newItem.isAutoTts = true
               newItem.triggerUserNames = itemMsg.triggerUserNames
               list.append(newItem)
           } else {
               LCLog.d("无法获取TTS的音频")
           }
            
            var audioName: String = itemMsg.items.first?.name ?? ""
            // 如果有多个昵称  要播放第二条语音 subPath 是第二条语音的数据 合成为语音内容为：你们 开头
            if count > 1, let subPath = itemMsg.subName  {
                audioName = subPath
            }
            
            let res =  AreaItemModel(name: audioName,
                                      type: .musicLocal,
                                      duration: itemMsg.items.first?.duration ?? 1000,
                                      sourceId: itemMsg.items.first?.sourceId,
                                      text: itemMsg.items.first?.text )
            res.voidespeed = itemMsg.items.first?.voidespeed
            let msg = WorkMessage(key: itemMsg.key , level: itemMsg.level, type: itemMsg.type , item: res)
            
            // 3 添加音频的任务
            list.append(msg)
            
           // 4 保存下来
           self.workQueue[itemMsg.level.rawValue] = list
           
           self.sendMessage()
        }
    }
    
    
    func getAiConfigModel() -> AiConfigModel {
        return AIReplyDataManager.shared.aiConfig
    }
    
    
    //是否正在执行任务
    private func hasExecuteMsg() -> Bool {
        return  isExePlaying
    }
    
    private func isLowLevel(_ item: WorkMessage) -> Bool {
        return item.level.rawValue <= ReplyMsgLevel.level3Normal.rawValue
    }
    
    // 真正执行任务的代码
    private func executeNextMsg(endBack: @escaping (Bool) -> Void) {
        if (!isWorking) {
            LCLog.d( "postMessage->executeNextMsg-->任务已停止")
            endBack(false)
            return
        }
        
        
        guard let next = getNextMsg() else {
            LCLog.d( "postMessage->executeNextMsg-->无任务")
            endBack(false)
            return
        }
        
        //4.一个任务的最大响应时长为1分钟（低价值礼物），高价值礼物不做此限制，给用户一个选择的时间段30秒-3分钟
        // 一个任务在 time 时间内主动停止
        
        let isLow = isLowLevel(next)
        
        var time = max_execute_durataion
        
        if (isLow){
            time = 60
        }
        
        startTimer(time)

        // 执行任务
        executeWorkImpl(next, 0) {  [weak self] ret in
            guard let self = self else { return }
            endBack(true)
        }
        
    }
    
    // 执行任务
    private func executeWorkImpl(_ msg: WorkMessage, _ index: Int, endBack: @escaping (Bool) -> Void) {
        //感谢礼物的权重要比关键词回复的高，如果已经回答了3个礼物感谢，抽一条关键词回答（如果说关键词的合成还未合成完，可以把之后的感谢礼物提前，在关键词合成完毕之后，再进行播放）
        if msg.isGoodType() {
            lastGoodSize =  lastGoodSize  + 1
        } else {
            lastGoodSize = 0
        }
        
        let item: AreaItemModel? = msg.items[safe: index]
        
        guard let item = item else {
            endBack(true)
            return
        }
        
        answerImpl(item) { [weak self] ret in
            guard let self = self else { return }
            var nextIndex = index + 1
            if (nextIndex < msg.items.count) {
                self.executeWorkImpl(msg, nextIndex, endBack: endBack)
            } else {
                // 执行完了
                endBack(true)
            }
        }
    }
    
    // 查找下一个任务
    private func getNextMsg() -> WorkMessage? {
        //根据基本取任务 根据等级找
        var list:[Int] = Array(workQueue.keys)
        // 排序 从大到小
        list = list.sorted(by: >)
        
        for (_, key) in list.enumerated() {
            
            //查询并删除
            if var typeList = workQueue[key], let item = typeList.first {
                
                if (item.isGoodType() && lastGoodSize >= 3) {
                    //处理:需求是3条送礼回复中，穿插一条关键词回复
                    
                    if var msgList = workQueue[ReplyMsgLevel.levelMsg.rawValue], let msgTem = msgList.first {
                        msgList.removeFirst()
                        workQueue[ReplyMsgLevel.levelMsg.rawValue] = msgList
                        return msgTem
                    }
                }
                
                typeList.removeFirst()
                workQueue[key] = typeList
                return item
            }
        }
        return nil
    }
    
    /***
     * 检查是否继续执行
     */
    private func isContinueExe(_ next: WorkMessage) -> Bool {
        return true
    }
    
     // MARK: - 执行具体的任务  播放音频 和 发送弹幕
    private func answerImpl(_ item: AreaItemModel, endBack: @escaping (Bool) -> Void) {
        
        lastResItem = item
        
        switch item.type {
        case .musicLocal:
            
            if (item.path == nil) {
                LCLog.d("文件不存在\(item.name ?? "")")
                endBack(false)
                return
            }
            toPlayVoice(item: item, endBack: endBack)
            
        case .text:
            // 使用文字 回复用户
            toSendTextMessage(item: item, endBack: endBack)
        default:
            break
        }
    }
    
}

// MARK: - 对外接口
extension AiAnswerFactory {
    /****
     * 放入一个智能互动的消息
     */
   public func addItemForHuDong(model: AiKeywordVoiceReplyItemModel) {
        LCLog.d("\n\n*******postMessage->准备放入数据;\(model)*********************************")
        
       guard  let list = model.replyItem?.resources, let resFile =  list.randomElement() else {
           LCLog.d( "无回复内容 不处理")
           return
       }
       
        // 回复的内容模型
        let resItem = resFile.toAreaItem()
        
        var level: ReplyMsgLevel = .levelMsg
        
        if !model.isGiftType() {
            level = .levelMsg
        } else {
            // 礼物数量
            var giftCount: Int = 0
            
            if let gift = model.gifts?.first, let count = Int(gift.diamondCount) {
                giftCount = count
            }
            
            if (giftCount > ReplyMsgLevel.level7.value) {
                level = .level10
            } else if (giftCount > ReplyMsgLevel.level4.value) {
                level = .level7
            } else if (giftCount > ReplyMsgLevel.level3Normal.value) {
                level = .level4
            } else {
                level = .level3Normal
            }
        }
        
        var type: ReplyMsgType = .good
        
        if model.isAttType() {
            type = .attention
        } else if model.isGiftType() {
            type = .good
        } else {
            type = .keyword
        }
        
        var id = model.id
        if let gift = model.gifts?.first {
            id =  gift.id
        }
       
        let msgItem = WorkMessage(key: id , level: level, type: type , item: resItem)
       
       // 目前只有礼物回复的时候 会合成2条音频数据
        msgItem.subName = resFile.subPath        
        
        if let triggerDouyinUserName = model.triggerDouyinUserName,
           let m: [String] = JsonTool.string2Model(triggerDouyinUserName) {
            
            msgItem.triggerUserNames =  m //["张三", "李四", "王五", "陈六", "抖音彦祖", "赵八"]
            
            // 如果有多个昵称  要播放第二条语音 subPath 是第二条语音的数据 合成为语音内容为：你们 开头
            
//            if resItem.type == .musicLocal, let subPath = resFile.subPath, m.count > 1  {
//                resItem.name = subPath
//            }
            
        }
       
       // 如果回复的资源为文本， 那么需要把 @用户昵称的设置拼接上
       if resItem.type == .text {
           self.appendText(item: msgItem, gift: model.gifts?.first)
       }
       
        // 计算出了一个 任务数据 加入任务池
       self.queue.performSynchronously(true) { [weak self] in
           guard let self = self else { return }
           self.postMessageForHuDong(msgItem)
       }
    }
    
}



// MARK: - 音频
extension AiAnswerFactory {
    
    private func stopRenderAudio() {
        DispatchQueue.main.async {
            AudioPlayManager.shared.stop()
        }
    }
    
    /***
     * 去播放语音回复
     */
    private func toPlayVoice(item: AreaItemModel, endBack: @escaping (Bool) -> Void) {
        
        DispatchQueue.main.async {
            AudioPlayManager.shared.stop()
            
            let _ = try? AudioPlayManager.shared.play(url: item.toAudioPlayUrl, rate: Float(item.voidespeed ?? "1.0") ?? 1.0)
            
            AudioPlayManager.shared.endBack = { [weak self] finsh in
                guard let self = self else { return }
                endBack(true)
            }
        }
    }
}

// MARK: - 文本的回复
extension AiAnswerFactory {
    
    private func toSendTextMessage(item: AreaItemModel, endBack: @escaping (Bool) -> Void) {
        if let allTexts = item.allTexts {
            
            for (index, msg) in allTexts.enumerated() {
                // 计算延迟时间：每个消息间隔 200 毫秒
                let delay = Double(index) * 0.2 // 0.2 秒
                DispatchQueue.global().asyncAfter(deadline: .now() + delay) { [weak self] in
                    guard let self = self else { return }
                    SendMessageManager.shared.sendMessage(msg: msg) { [weak self] rest in
                        guard let self = self else { return }
                        // 处理发送完成后的逻辑（若有）
                    }
                }
                if index + 1 == allTexts.count {
                    endBack(true)
                }
            }
        } else {
            if let msg = item.text {
                // 切换到全局队列去执行
                DispatchQueue.global().async { [weak self] in
                    guard let self = self else { return }
                    SendMessageManager.shared.sendMessage(msg: msg) {  [weak self] rest in
                        guard let self = self else { return }
                        endBack(true)
                    }
                }
            } else {
                endBack(true)
            }
        }
        
    }
}

/****
 * 任务模型
 */

class WorkMessage: NSObject, Comparable {
    var key: String = ""
    var level: ReplyMsgLevel = .level3Normal
    var type: ReplyMsgType = .keyword
    var createTime: TimeInterval = Date().timeIntervalSince1970
    var triggerUserNames: [String]?
    var items: [AreaItemModel]
    var isAutoTts: Bool = false // 是否TTS的任务
    
    var subName: String? // tts 的文件名 合成的文本是 你们开头的
    
    init(key: String = "", level: ReplyMsgLevel = .level3Normal, type: ReplyMsgType = .keyword, item: AreaItemModel) {
        self.key = key
        self.level = level
        self.type = type
        self.items = [item]
        self.createTime = Date().timeIntervalSince1970
    }
    
    func getResType() -> Int {
        return Int(items.first?.type.rawValue ?? 0)
    }
    
    func isVoiceType() -> Bool {
        return getResType() == AreaItemType.musicLocal.rawValue
    }
    
    
    static func < (lhs: WorkMessage, rhs: WorkMessage) -> Bool {
        return lhs.createTime < rhs.createTime
    }
    
    static func == (lhs: WorkMessage, rhs: WorkMessage) -> Bool {
        return lhs.createTime == rhs.createTime
    }
    
    func isGoodType() -> Bool {
        return type == ReplyMsgType.good
    }
}


extension AiAnswerFactory {
    /// 开启定时器
    public func startTimer(_ time: Int) {
        DispatchQueue.main.async {
            self.releaseTimer()
            self.outTimer = Timer.scheduledTimer(timeInterval: TimeInterval(time), target: self, selector: #selector(self.timerAction(sender:)), userInfo: nil, repeats: false)
        }
    }
    
    /// 释放定时器
    public func releaseTimer() {
        self.outTimer?.invalidate()
        self.outTimer = nil
    }
    
    /// 触发定时切换
    @objc public func timerAction(sender: Timer) {
        LCLog.d("触发了任务的超时逻辑， 此时要结束任务")
        stopRenderAudio()
        releaseTimer()
        sendMessage()
    }
}

