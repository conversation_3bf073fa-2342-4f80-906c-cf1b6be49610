//
//  AudioRecordManager.swift
//  LivePlus
//
//  Created by 郭炜 on 2025/1/14.
//

import UIKit

// MARK: - 录音管理类
class AudioRecordManager: NSObject {
    
    static let shared = AudioRecordManager()
    
    var audioRecorder: AVAudioRecorder?
    private var recordingSession: AVAudioSession?
    
    // 录音状态
    enum RecordingState {
        case notStarted
        case recording
        case paused
    }
    
    // 当前录音状态
    private(set) var currentState: RecordingState = .notStarted
    
    // 录音文件URL
    private(set) var currentRecordingURL: URL?
    
    // 录音配置
    private let settings: [String: Any] = [
        AVFormatIDKey: Int(kAudioFormatLinearPCM),  // AAC 格式足够了
        AVSampleRateKey: 16000.0,                   // 16kHz 对语音足够
        AVNumberOfChannelsKey: 1,                   // 单声道
        AVEncoderAudioQualityKey: AVAudioQuality.medium.rawValue,  // 中等质量即可
        AVEncoderBitRateKey: 32000                 // 32kbps 对语音够用
    ]
    
    override private init() {
        super.init()
        setupAudioSession()
    }
    
    private func setupAudioSession() {
        recordingSession = AVAudioSession.sharedInstance()
        do {
            try recordingSession?.setCategory(.playAndRecord, mode: .default, options: [.defaultToSpeaker, .allowBluetooth])
            try recordingSession?.setActive(true)
        } catch {
            print("Failed to set up recording session: \(error.localizedDescription)")
        }
    }
    
    // MARK: - Public Methods
    
    /// 开始录音
    /// - Parameter completion: 完成回调，返回是否成功和错误信息
    func startRecording(completion: @escaping (Bool, Error?) -> Void) {
        GCDServices.async {
            self.setupAudioSession()
            
            // 如果正在录音，就恢复录音
            if self.currentState == .paused {
                self.resumeRecording()
                completion(true, nil)
                return
            }
            
            // 检查麦克风权限
            self.recordingSession?.requestRecordPermission { [weak self] allowed in
                guard let self = self else { return }
                
                if allowed {
                    self.initializeNewRecording(completion: completion)
                } else {
                    let error = NSError(domain: "AudioRecordManager",
                                      code: -1,
                                      userInfo: [NSLocalizedDescriptionKey: "麦克风权限被拒绝"])
                    completion(false, error)
                }
            }
        }
    }
    
    /// 暂停录音
    func pauseRecording() {
        guard currentState == .recording, audioRecorder?.isRecording == true else { return }
        audioRecorder?.pause()
        currentState = .paused
    }
    
    /// 恢复录音
    func resumeRecording() {
        guard currentState == .paused else { return }
        audioRecorder?.record()
        currentState = .recording
    }
    
    /// 停止录音
    /// - Returns: 返回录音文件的URL
    @discardableResult
    func stopRecording() -> URL? {
        guard currentState != .notStarted else { return nil }
        
        audioRecorder?.stop()
        currentState = .notStarted
        
        return currentRecordingURL
    }
    
    /// 获取当前录音时长
    var currentRecordingTime: TimeInterval {
        return audioRecorder?.currentTime ?? 0
    }
    
    // MARK: - Private Methods
    
    private func initializeNewRecording(completion: @escaping (Bool, Error?) -> Void) {
        // 创建录音文件URL
        currentRecordingURL = URL(fileURLWithPath: AudioPathStyle.audioRecord.path.appendingPathComponent("\(Date.currentMilliTimeStamp).wav"))
        
        guard let fileURL = currentRecordingURL else {
            completion(false, NSError(domain: "AudioRecordManager",
                                    code: -2,
                                    userInfo: [NSLocalizedDescriptionKey: "无法创建录音文件"]))
            return
        }
        
        do {
            audioRecorder = try AVAudioRecorder(url: fileURL, settings: settings)
            // 开启音频测量功能
            audioRecorder?.isMeteringEnabled = true
            audioRecorder?.delegate = self
            audioRecorder?.prepareToRecord()
            audioRecorder?.record()
            currentState = .recording
            completion(true, nil)
        } catch {
            completion(false, error)
        }
    }
    
    /// 删除当前录音文件
    func deleteCurrentRecording() {
        if let url = currentRecordingURL {
            try? FileManager.default.removeItem(at: url)
            currentRecordingURL = nil
        }
    }
}

// MARK: - AVAudioRecorderDelegate
extension AudioRecordManager: AVAudioRecorderDelegate {
    func audioRecorderDidFinishRecording(_ recorder: AVAudioRecorder, successfully flag: Bool) {
        if !flag {
            deleteCurrentRecording()
        }
    }
    
    func audioRecorderEncodeErrorDidOccur(_ recorder: AVAudioRecorder, error: Error?) {
        if let error = error {
            print("Recording encode error: \(error.localizedDescription)")
        }
    }
}

extension Date {
    
    /// 获取当前时间戳（毫秒）
    static var currentMilliTimeStamp: Int64 {
        return Int64(Date().timeIntervalSince1970 * 1000)
    }
}

extension TimeInterval {
    var toUIString: String {
        let hrs = Int(self) / 3600
        let mins = (Int(self)-hrs*3600) / 60
        let secs = Int(self) % 60
        if hrs > 0 {
            return String(format: "%02d:%02d:%02d", hrs, mins, secs)
        } else {
            return String(format: "%02d:%02d", mins, secs)
        }
    }
    
    var toUIHourString: String {
        let hrs = Int(self) / 3600
        let mins = (Int(self)-hrs*3600) / 60
        let secs = Int(self) % 60
        return String(format: "%02d:%02d:%02d", hrs, mins, secs)
    }
}
