//
//  AudioPlayManager.swift
//  LivePlus
//
//  Created by 郭炜 on 2025/1/14.
//

import UIKit
import AVFoundation

// MARK: - Error
enum AudioError: Error {
    case invalidData
    case playbackFailed
    case alreadyPlaying
    case noActivePlayer
    
    var localizedDescription: String {
        switch self {
        case .invalidData:
            return "Invalid audio data"
        case .playbackFailed:
            return "Failed to play audio"
        case .alreadyPlaying:
            return "Audio is already playing"
        case .noActivePlayer:
            return "No active audio player"
        }
    }
}

// MARK: - Protocols
protocol AudioPlayManagerDelegate: AnyObject {
    func audioPlayManager(_ manager: AudioPlayManager, didFinishPlaying successfully: Bool)
    func audioPlayManager(_ manager: AudioPlayManager, didUpdateProgress progress: Float)
    func audioPlayManager(_ manager: AudioPlayManager, didUpdateCurrentTime currentTime: TimeInterval)
    func audioPlayManager(_ manager: AudioPlayManager, didEncounterError error: Error)
}

typealias AudioPlayEndBack = (Bool) -> Void

// MARK: - Manager
class AudioPlayManager: NSObject {
    
   
    
    // MARK: - Properties
    static let shared = AudioPlayManager()
    
    weak var delegate: AudioPlayManagerDelegate?
    private var audioPlayer: AVAudioPlayer?
    private var progressTimer: Timer?
    public private(set) var isPlaying: Bool = false
    
    public var endBack: AudioPlayEndBack?
    
    // MARK: - Initialization
    private override init() {
        super.init()
        setupAudioSession()
    }
    
    // MARK: - Setup
    private func setupAudioSession() {
        do {
            try AVAudioSession.sharedInstance().setCategory(.playback)
            try AVAudioSession.sharedInstance().setActive(true)
        } catch {
            delegate?.audioPlayManager(self, didEncounterError: error)
            endBack?(false)
        }
    }
    
    // MARK: - Public Methods
    @discardableResult
    func play(url: URL, rate: Float = 1.0) throws -> TimeInterval {
        // 检查是否已经在播放
        if isPlaying {
            self.stop()
        }
        
        
        do {
            // 首先尝试直接播放
            do {
                audioPlayer = try AVAudioPlayer(contentsOf: url)
            } catch {
                // 如果直接播放失败，读取数据并尝试不同格式
                let audioData = try Data(contentsOf: url)
                
                // 尝试不同的格式提示
                let fileExtension = url.pathExtension.lowercased()
                let alternativeType = fileExtension == "mp3" ? "wav" : "mp3"
                
                do {
                    // 首先尝试与扩展名相同的格式
                    audioPlayer = try AVAudioPlayer(data: audioData, fileTypeHint: fileExtension)
                } catch {
                    // 如果失败，尝试备选格式
                    audioPlayer = try AVAudioPlayer(data: audioData, fileTypeHint: alternativeType)
                }
            }
            
            audioPlayer?.delegate = self
            audioPlayer?.enableRate = true
            audioPlayer?.rate = rate
            
            // 准备播放
            guard audioPlayer?.prepareToPlay() == true else {
                throw AudioError.playbackFailed
            }
            
            // 开始播放
            guard audioPlayer?.play() == true else {
                throw AudioError.playbackFailed
            }
            
            isPlaying = true
            startProgressTimer()
            
            return audioPlayer?.duration ?? 0
            
        } catch {
            delegate?.audioPlayManager(self, didEncounterError: error)
            endBack?(false)
            throw AudioError.playbackFailed
        }
    }
    
    func pause() {
        audioPlayer?.pause()
        isPlaying = false
        stopProgressTimer()
    }
    
    func resume() throws {
        guard let player = audioPlayer else {
            throw AudioError.noActivePlayer
        }
        
        player.play()
        isPlaying = true
        startProgressTimer()
    }
    
    func stop() {
        audioPlayer?.stop()
        audioPlayer = nil
        isPlaying = false
        stopProgressTimer()
    }
    
    func seek(to time: TimeInterval) {
        audioPlayer?.currentTime = time
    }
    
    // MARK: - Progress Tracking
    private func startProgressTimer() {
        stopProgressTimer()
        
        // 确保在主线程创建和运行 Timer
        DispatchQueue.main.async { [weak self] in
            self?.progressTimer = Timer.scheduledTimer(
                withTimeInterval: 0.1,
                repeats: true
            ) { [weak self] _ in
                self?.updateProgress()
            }
            
            // 将 Timer 添加到当前运行循环
            RunLoop.current.add(self?.progressTimer ?? Timer(), forMode: .common)
            
            // 立即触发第一次更新
            self?.progressTimer?.fire()
        }
    }

    private func updateProgress() {
        guard let player = audioPlayer,
              player.duration > 0 else { return }
        
        // 计算进度并通知代理
        let progress = Float(player.currentTime / player.duration)
        
        // 确保在主线程调用代理方法
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            self.delegate?.audioPlayManager(self, didUpdateProgress: progress)
            self.delegate?.audioPlayManager(self, didUpdateCurrentTime: player.currentTime)
        }
    }

    private func stopProgressTimer() {
        // 确保在主线程停止 Timer
        DispatchQueue.main.async { [weak self] in
            self?.progressTimer?.invalidate()
            self?.progressTimer = nil
        }
    }
    
    /// 重置播放器到起始位置
    func resetToBeginning() {
        seek(to: 0)
        stopProgressTimer()
        delegate?.audioPlayManager(self, didUpdateProgress: 0)
    }
    
    // MARK: - Getters
    var currentTime: TimeInterval {
        return audioPlayer?.currentTime ?? 0
    }
    
    var duration: TimeInterval {
        return audioPlayer?.duration ?? 0
    }
    
    var volume: Float {
        get { return audioPlayer?.volume ?? 1.0 }
        set { audioPlayer?.volume = newValue }
    }
}

// MARK: - AVAudioPlayerDelegate
extension AudioPlayManager: AVAudioPlayerDelegate {
    func audioPlayerDidFinishPlaying(_ player: AVAudioPlayer, successfully flag: Bool) {
        isPlaying = false
        stopProgressTimer()
        resetToBeginning()
        delegate?.audioPlayManager(self, didFinishPlaying: flag)
        endBack?(true)
    }
    
    func audioPlayerDecodeErrorDidOccur(_ player: AVAudioPlayer, error: Error?) {
        isPlaying = false
        stopProgressTimer()
        if let error = error {
            delegate?.audioPlayManager(self, didEncounterError: error)
        }
        endBack?(true)
    }
}
