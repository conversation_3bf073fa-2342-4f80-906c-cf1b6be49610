//
//  RecordCountDownLabel.swift
//  LivePlus
//
//  Created by 郭炜 on 2025/1/14.
//

import UIKit

// MARK: - 录制倒计时
class RecordCountDownLabel: UILabel {

    var _isRecording: Bool = false
    var timer: Timer?
    var recordCount = 0
    
    deinit {
        timer?.invalidate()
        timer = nil
    }
    
    convenience init() {
        self.init(frame: .zero)
    }
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        self.makeUI()
    }
    
    required init?(coder aCoder:NSCoder) {
        super.init(coder: aCoder)
    }
    
    func makeUI() {
        self.textColor = .white
        self.backgroundColor = .clear
        self.text = "00:00"
        self.textAlignment = .center
        self.font = UIFont.monospacedDigitSystemFont(ofSize: 17, weight: .medium) // 使用等宽字体
    }

    var isPausing: Bool = false
    
    public var isRecording: Bool {
        get {
            return _isRecording
        }
        set {
            if(_isRecording != newValue) {
                _isRecording = newValue
                
                if let timer = self.timer {
                    timer.invalidate()
                    self.timer = nil
                }
                
                if _isRecording {
                    self.text = "00:00"
                    recordCount = 0
                    self.timer = Timer.scheduledTimer(timeInterval: 1.0, target: self, selector: #selector(onTimerEvent(timer:)), userInfo: nil, repeats: true)
                }
                else {
                    self.text = "00:00"
                }
            }
        }
    }
    
    @objc func onTimerEvent(timer: Timer) {
        if isPausing { return }
        recordCount += 1
        let hrs = Int(recordCount) / 3600
        let mins = (Int(recordCount)-hrs*3600) / 60
        let secs = Int(recordCount) % 60
        if hrs > 0 {
            self.text = String(format: "%02d:%02d:%02d", hrs, mins, secs)
        } else {
            self.text = String(format: "%02d:%02d", mins, secs)
        }
    }

}
