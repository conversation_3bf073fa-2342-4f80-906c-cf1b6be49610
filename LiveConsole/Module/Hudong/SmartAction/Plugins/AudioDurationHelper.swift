//
//  AudioDurationHelper.swift
//  LivePlus
//
//  Created by 郭炜 on 2025/1/14.
//

import UIKit
import AVFoundation

class AudioDurationHelper {
    
    /// 获取音频时长（异步）
    /// - Parameters:
    ///   - url: 音频文件URL
    ///   - completion: 完成回调，返回时长（秒）或错误
    static func getAudioDuration(from url: URL, completion: @escaping (Result<TimeInterval, Error>) -> Void) {
        // 创建 AVAsset
        let asset = AVAsset(url: url)
        
        // 加载 duration
        let durationKey = "duration"
        asset.loadValuesAsynchronously(forKeys: [durationKey]) {
            // 检查加载状态
            var error: NSError?
            let status = asset.statusOfValue(forKey: durationKey, error: &error)
            
            // 切换到主线程回调
            DispatchQueue.main.async {
                switch status {
                case .loaded:
                    // 获取时长（CMTime转为秒）
                    let duration = CMTimeGetSeconds(asset.duration)
                    if duration.isNaN {
                        completion(.failure(NSError(domain: "AudioDuration",
                                                 code: -1,
                                                 userInfo: [NSLocalizedDescriptionKey: "Invalid duration"])))
                    } else {
                        completion(.success(duration))
                    }
                    
                case .failed:
                    completion(.failure(error ?? NSError(domain: "AudioDuration",
                                                       code: -1,
                                                       userInfo: [NSLocalizedDescriptionKey: "Failed to load duration"])))
                    
                case .cancelled:
                    completion(.failure(NSError(domain: "AudioDuration",
                                             code: -2,
                                             userInfo: [NSLocalizedDescriptionKey: "Operation cancelled"])))
                    
                default:
                    completion(.failure(NSError(domain: "AudioDuration",
                                             code: -3,
                                             userInfo: [NSLocalizedDescriptionKey: "Unknown error"])))
                }
            }
        }
    }
    
    /// 格式化时长为字符串 (例如: "01:23")
    static func formatDuration(_ duration: TimeInterval) -> String {
        let minutes = Int(duration) / 60
        let seconds = Int(duration) % 60
        return String(format: "%02d:%02d", minutes, seconds)
    }
}
