//
//  SmartAlertView.swift
//  LivePlus
//
//  Created by 郭炜 on 2025/1/13.
//

import UIKit

class SmartAlertView: UIView {
    
    // MARK: - Properties
    private let containerView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        view.layer.cornerRadius = 16
        view.clipsToBounds = true
        return view
    }()
    
    private let stackView: UIStackView = {
        let stack = UIStackView()
        stack.axis = .vertical
        stack.spacing = 28
        stack.alignment = .center
        return stack
    }()
    
    private let titleLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 20, weight: .medium)
        label.textAlignment = .center
        label.textColor = UIColor("#1E1F20")
        return label
    }()
    
    private let contentLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 15)
        label.textColor = UIColor("#4D4E52")
        label.numberOfLines = 0
        label.textAlignment = .left
        return label
    }()
    
    private let confirmButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitle("知道了", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.backgroundColor = UIColor("#AC63F9")
        button.titleLabel?.font = .systemFont(ofSize: 14)
        button.applyGradient()
        button.cornerRadius = 20
        return button
    }()
    
    // MARK: - Init
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }
    
    // MARK: - Setup
    private func setupUI() {
        backgroundColor = UIColor.black.withAlphaComponent(0.4)
        
        addSubview(containerView)
        containerView.addSubview(stackView)
        
        stackView.addArrangedSubview(titleLabel)
        stackView.addArrangedSubview(contentLabel)
        stackView.addArrangedSubview(confirmButton)
        
        setupConstraints()
        
        confirmButton.addTarget(self, action: #selector(dismissAlert), for: .touchUpInside)
    }
    
    private func setupConstraints() {
        containerView.translatesAutoresizingMaskIntoConstraints = false
        stackView.translatesAutoresizingMaskIntoConstraints = false
        confirmButton.translatesAutoresizingMaskIntoConstraints = false
        
        NSLayoutConstraint.activate([
            containerView.centerXAnchor.constraint(equalTo: centerXAnchor),
            containerView.centerYAnchor.constraint(equalTo: centerYAnchor),
            containerView.widthAnchor.constraint(equalToConstant: 320),
            
            stackView.topAnchor.constraint(equalTo: containerView.topAnchor, constant: 30),
            stackView.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 25),
            stackView.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -25),
            stackView.bottomAnchor.constraint(equalTo: containerView.bottomAnchor, constant: -34),
            
            confirmButton.widthAnchor.constraint(equalToConstant: 204),
            confirmButton.heightAnchor.constraint(equalToConstant: 40)
        ])
    }
    
    // MARK: - Public Methods
    func show(title: String, content: String, in view: UIView) {
        titleLabel.text = title
        contentLabel.text = content
        
        view.addSubview(self)
        self.frame = view.bounds
        
        // 添加显示动画
        containerView.transform = CGAffineTransform(scaleX: 0.1, y: 0.1)
        UIView.animate(withDuration: 0.3, delay: 0, options: .curveEaseOut) {
            self.containerView.transform = .identity
        }
    }
    
    @objc private func dismissAlert() {
        UIView.animate(withDuration: 0.2, animations: {
            self.alpha = 0
        }) { _ in
            self.removeFromSuperview()
        }
    }
}
