//
//  AVPlayManager.swift
//  LiveConsole
//
//  Created by simon on 19.5.25.
//

import Foundation
import AVFoundation


protocol AVPlayManagerDelegate: AnyObject {
    func avPlayManager(_ manager: AVPlayManager, didFinishPlaying successfully: Bool)
}

// MARK: - Manager
class AVPlayManager: NSObject {
    
    // MARK: - Properties
    static let shared = AVPlayManager()
    
    weak var delegate: AVPlayManagerDelegate?
   
    lazy var player: AVPlayer = {
        let player = AVPlayer()
        player.automaticallyWaitsToMinimizeStalling = true
        return player
    }()
    
    var playItem: AVPlayerItem?
    
    public private(set) var isPlaying: Bool = false
        
    // MARK: - Initialization
    private override init() {
        super.init()
    }

    
    // MARK: - Public Methods
    
    func videoPlayer(url: URL) {
        
         let asset = AVURLAsset(url: url)

        registerNotice()
        
        playItem = AVPlayerItem(asset: asset)
        player.replaceCurrentItem(with: playItem)

        do {
            let session = AVAudioSession.sharedInstance()
            try session.setCategory(.playback)
            try session.setActive(true, options: [])
        } catch {
            print("session playback failed \(error)")
        }
        player.play()
    }
    
    ///  注册通知
    private func registerNotice() {
        NotificationCenter.default.addObserver(self, selector: #selector(handleVideoPlayToEnd(sender:)), name: NSNotification.Name.AVPlayerItemDidPlayToEndTime, object: self.player.currentItem)
        NotificationCenter.default.addObserver(self, selector: #selector(handleAppWillResignActive(sender:)), name: UIApplication.willResignActiveNotification, object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(handleAppDidBecomeActive(sender:)), name: UIApplication.didBecomeActiveNotification, object: nil)
    }
    
    /// 视频播放完成
    @objc private func handleVideoPlayToEnd(sender: Notification) {
        self.delegate?.avPlayManager(self, didFinishPlaying: true)
    }
    
    /// app切后台
    @objc private func handleAppWillResignActive(sender: Notification) {
        LCLog.d("app 切到后台")
       pause()
    }
    
    /// app返回前台
    @objc private func handleAppDidBecomeActive(sender: Notification) {
        LCLog.d("app 回到前台")
        // 不需要在播放了
//        resume()
    }
    
    func pause() {
        self.player.pause()
        isPlaying = false
    }
    
    func resume()  {
        player.play()
        isPlaying = true
    }
    
    func stop() {
        player.pause()
        isPlaying = false
    }
}
