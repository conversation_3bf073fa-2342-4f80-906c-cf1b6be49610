//
//  UIView+Extension.swift
//  LivePlus
//
//  Created by 郭炜 on 2025/1/13.
//

import UIKit

/// 渐变色视图
class GradientView: UIView {
    override class var layerClass: AnyClass {
        return CAGradientLayer.self
    }
}

extension UIView {
    enum CustomGradientDirection {
        case leftToRight         // 从左到右
        case rightToLeft         // 从右到左
        case topToBottom        // 从上到下
        case bottomToTop        // 从下到上
        case topLeftToBottomRight    // 左上到右下
        case bottomRightToTopLeft    // 右下到左上
        
        var startPoint: CGPoint {
            switch self {
            case .leftToRight:
                return CGPoint(x: 0, y: 0.1)
            case .rightToLeft:
                return CGPoint(x: 1, y: 0.5)
            case .topToBottom:
                return CGPoint(x: 0.5, y: 0)
            case .bottomToTop:
                return CGPoint(x: 0.5, y: 1)
            case .topLeftToBottomRight:
                return CGPoint(x: 0, y: 0)
            case .bottomRightToTopLeft:
                return CGPoint(x: 1, y: 1)
            }
        }
        
        var endPoint: CGPoint {
            switch self {
            case .leftToRight:
                return CGPoint(x: 1, y: 0.9)
            case .rightToLeft:
                return CGPoint(x: 0, y: 0.5)
            case .topToBottom:
                return CGPoint(x: 0.5, y: 1)
            case .bottomToTop:
                return CGPoint(x: 0.5, y: 0)
            case .topLeftToBottomRight:
                return CGPoint(x: 1, y: 1)
            case .bottomRightToTopLeft:
                return CGPoint(x: 0, y: 0)
            }
        }
    }
    
    func applyGradient(colors: [UIColor] = [UIColor("#6974F2"), UIColor("#AC63F9")],
                       direction: CustomGradientDirection = .leftToRight,
                       locations: [NSNumber]? = [0.0, 1.0]) {
        let gradientView = GradientView()
        gradientView.isUserInteractionEnabled = false
        guard let gradientLayer = gradientView.layer as? CAGradientLayer else { return }
        
        // 移除旧的渐变层
        if let oldGradientView = self.subviews.first(where: { ($0.layer as? CAGradientLayer) != nil }) {
            oldGradientView.removeFromSuperview()
        }
        self.insertSubview(gradientView, at: 0)
        gradientView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        gradientLayer.colors = colors.map { $0.cgColor }
        gradientLayer.locations = locations
        gradientLayer.startPoint = direction.startPoint
        gradientLayer.endPoint = direction.endPoint
    }
}
