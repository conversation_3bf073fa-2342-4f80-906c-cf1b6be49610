//
//  AudioP.swift
//  IRecord
//
//  Created by 郭炜 on 2024/11/13.
//

import UIKit

import AVFoundation

enum AudioPermissionStatus {
    case authorized          // 已授权
    case denied             // 已拒绝
    case notDetermined      // 未确定（未请求过）
    case restricted         // 受限制（比如家长控制）
}

// MARK: - 录音权限检查
class AudioPermissionManager {
    
    static let shared = AudioPermissionManager()
    
    private init() {}
    
    /// 检查当前麦克风权限状态
    var currentPermissionStatus: AudioPermissionStatus {
        switch AVAudioSession.sharedInstance().recordPermission {
        case .granted:
            return .authorized
        case .denied:
            return .denied
        case .undetermined:
            return .notDetermined
        @unknown default:
            return .restricted
        }
    }
    
    /// 检查是否有录音权限
    var isAuthorized: Bool {
        return currentPermissionStatus == .authorized
    }
    
    /// 检查麦克风权限（同步方法）
    func checkPermission() -> Bool {
        return AVAudioSession.sharedInstance().recordPermission == .granted
    }
    
    /// 请求麦克风权限（异步方法）
    /// - Parameter completion: 完成回调，返回是否获得授权
    func requestPermission(completion: @escaping (Bool) -> Void) {
        AVAudioSession.sharedInstance().requestRecordPermission { granted in
            DispatchQueue.main.async {
                completion(granted)
            }
        }
    }
    
    /// 请求麦克风权限（异步方法，返回详细状态）
    /// - Parameter completion: 完成回调，返回权限状态
    func requestPermissionWithStatus(completion: @escaping (AudioPermissionStatus) -> Void) {
        AVAudioSession.sharedInstance().requestRecordPermission { [weak self] _ in
            guard let self = self else { return }
            DispatchQueue.main.async {
                completion(self.currentPermissionStatus)
            }
        }
    }
    
    /// 跳转到系统设置页面
    func openSettings() {
        guard let settingsUrl = URL(string: UIApplication.openSettingsURLString) else {
            return
        }
        
        if UIApplication.shared.canOpenURL(settingsUrl) {
            UIApplication.shared.open(settingsUrl)
        }
    }
    
    /// 检查并请求权限，如果被拒绝则显示提示
    /// - Parameter completion: 完成回调，返回是否获得授权
    func checkAndRequestPermission(completion: @escaping (Bool) -> Void) {
        switch currentPermissionStatus {
        case .authorized:
            completion(true)
            
        case .notDetermined:
            requestPermission(completion: completion)
            
        case .denied, .restricted:
            completion(false)
        }
    }
    
    /// 显示权限提示弹窗
    public func showPermissionAlert() {
        let alert = UIAlertController(
            title: "未开启麦克风权限",
            message: "点击'去设置'，开启麦克风权限",
            preferredStyle: .alert
        )
        
        alert.addAction(UIAlertAction(title: "取消", style: .cancel))
        alert.addAction(UIAlertAction(title: "去设置", style: .default) { [weak self] _ in
            self?.openSettings()
        })
        
        // 获取当前最顶层的视图控制器来显示警告框
        AppDelegate.curDisplayVC().present(alert, animated: true)
    }
}
