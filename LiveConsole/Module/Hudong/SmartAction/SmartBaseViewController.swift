//
//  SmartBaseViewController.swift
//  LivePlus
//
//  Created by 郭炜 on 2025/1/13.
//

import UIKit

class SmartBaseViewController: UIViewController {
    
    override func viewWillAppear(_ animated: <PERSON><PERSON>) {
        super.viewWillAppear(animated)
        self.navigationController?.navigationBar.isHidden = true
    }
    
    override func viewDidDisappear(_ animated: Bool) {
        super.viewDidDisappear(animated)
        self.navigationController?.navigationBar.isHidden = true
    }
    
    deinit {
        print("\(type(of: self)) deinited")
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        
        view.backgroundColor = UIColor("#111111")

        makeUI()
        bindViewModel()
        business()
    }
    
    // 需要子类实现的方法
    func makeUI() { }

    func business() { }

    func bindViewModel() { }
    
}
