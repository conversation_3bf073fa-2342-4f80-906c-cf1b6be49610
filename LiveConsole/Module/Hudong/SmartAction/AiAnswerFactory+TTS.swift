//
//  AiAnswerFactory+TTS.swift
//  LivePlus
//
//  Created by simon on 20.3.25.
//

import Foundation

// MARK: - 合成
extension AiAnswerFactory {
    
    func hechengTTSAction(type: ReplyMsgType, tts: AiAutoTtsModel, item: WorkMessage, endBack: @escaping (Bool, String?, Int) -> Void) {
        // 用户要合成的文案如：感谢【用户昵称A】和【用户昵称B】，还有【用户昵称C】，【用户昵称D】和【用户昵称E】
        var text = tts.text
        
        switch type {
        case .keyword:
            var names: String = item.triggerUserNames?.first ?? ""
           
            names = String(names.prefix(8))
                   
            text = text.replacingOccurrences(of: "【用户1】", with: names)
            // 只需要合成一次
            hechengTTSAction(text: text, tts: tts, item: item, endBack: endBack)
            
        case .good, .attention:
            // 感谢【用户昵称A】和【用户昵称B】，还有【用户昵称C】，【用户昵称D】和【用户昵称E】
            
            if let triggerUserNames = item.triggerUserNames {
                
                var groupTexts:[NickHechengModel] = []
                
                // 分组 5 个一组
                let groupNames = triggerUserNames.chunked(into: 5)
                groupNames.forEach { groupName in
                    let groupText = self.formatText(template: text, names: groupName, prefix: true)
                    groupTexts.append(NickHechengModel(text: groupText, count: groupName.count))
                }
               
                // 有几组数据就要合成几次
                for (idx, groupText) in groupTexts.enumerated() {
                    hechengTTSAction(text: groupText.text, tts: tts, item: item, count: groupText.count, endBack: endBack)
                }
                
            }
            
        }
    }
    
    
    /// 合成语音
    func hechengTTSAction(text:String,  tts: AiAutoTtsModel, item: WorkMessage, count: Int = 0, endBack: @escaping (Bool, String?,Int) -> Void) {
       
        
        // 1、如果是预置语音合成
        if let preAudioModel = AIReplyDataManager.shared.spkIdCache.currentPreAudioModel {
            
            MiddleRequestNet.combinePresetAudio(voiceType: preAudioModel.voiceType,
                                                text: text) { [weak self] response in
                guard let self = self, let response = response else {
                    DispatchQueue.main.async {
                        endBack(false, nil, count)
                    }
                    return
                }
                print("合成结果：\(response)")
                
                guard let fileName = response.audioName else {
                    DispatchQueue.main.async {
                        endBack(false, nil, count)
                    }
                    return
                }
                DispatchQueue.global().async {
                    DownloadFromBucket.download(fileName: fileName) { [weak self] localFilePath in
                        guard let self = self, let localFilePath = localFilePath else {
                            DispatchQueue.main.async {
                                endBack(false, nil, count)
                            }
                            return
                        }
                        print("下载文件路径：\(localFilePath)")
                        self.deletAudioFile(fileName: fileName)
                        DispatchQueue.main.async {
                            endBack(true, localFilePath.lastPathComponent, count)
                        }
                    }
                }
            }
        }
        
        // 2、如果是克隆音色合成
        else if let cloneAudioModel = AIReplyDataManager.shared.spkIdCache.currentCloneAudioModel {
           
            MiddleRequestNet.combineCloneAudio(spkId: cloneAudioModel.spkId,
                                               text: text) { [weak self] response in
                guard let self = self, let response = response else {
                    DispatchQueue.main.async {
                        endBack(false, nil, count)
                    }
                    return
                }
                print("合成结果：\(response)")
                
                guard let fileName = response.audioName else {
                    DispatchQueue.main.async {
                        HUD.showFail("请稍后重试")
                        endBack(false, nil, count)
                    }
                    return
                }
                DispatchQueue.global().async {
                    DownloadFromBucket.download(fileName: fileName) { [weak self] localFilePath in
                        guard let self = self, let localFilePath = localFilePath else {
                            DispatchQueue.main.async {
                                endBack(false, nil, count)
                            }
                            return
                        }
                        print("下载文件路径：\(localFilePath)")
                        self.deletAudioFile(fileName: fileName)
                       
                        DispatchQueue.main.async {
                            endBack(true, localFilePath.lastPathComponent, count)
                        }
                    }
                }
            }
        } 
        else {
            LCLog.d("未设置音色， 无法合成")
            DispatchQueue.main.async {
                endBack(false, nil, count)
            }
        }
        
    }
    
    
    func deletAudioFile(fileName: String?) {
        guard let name = fileName else { return  }
        // 等待2秒
        DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
            DownloadFromBucket.delet(fileName: name) { [weak self] result in
                guard let self = self else { return }
            }
        }
        
    }
    
}


// MARK: - 拼接回复的文本
extension AiAnswerFactory {
    
    func appendText(item: WorkMessage, gift: DouyinGiftInfo?)  {
        
        var itemTtsModel: AiAutoTtsModel?
        
        if (item.type == .good) {
            itemTtsModel = getAiConfigModel().giftAtNameModel
        } else if (item.type == .attention) {
            itemTtsModel = getAiConfigModel().followAtNameModel
        } else {
            itemTtsModel = getAiConfigModel().keywordAtNameModel
        }
        
        guard let tts = itemTtsModel else { return }
        
        guard let resou = item.items.first else { return }
        
        // 用户要合成的文案如：@【用户1】和@【用户2】，还有@【用户3】
        var text = tts.text
        
        var orgText: String = resou.text ?? ""
        
        switch item.type {
        case .keyword:
            var names: String = item.triggerUserNames?.first ?? ""
           
            // 拼接成新的
            text = text.replacingOccurrences(of: "【用户1】", with: names)
            //
            resou.text =  text + "  " +  orgText
           
        case .attention:
            // 感谢 @【用户1】和@【用户2】，还有@【用户3】
            
            if let triggerUserNames = item.triggerUserNames {
                
                var groupTexts:[String] = []
                
                // 分组 3 个一组
                let groupNames = triggerUserNames.chunked(into: 3)
                groupNames.forEach { groupName in
                    let groupText = self.formatText(template: text, names: groupName)
                    groupTexts.append(groupText)
                }
                
                var msgs: [String] = []
               
                // 有几组数据就要合成几次
                for (_, groupText) in groupTexts.enumerated() {
                    msgs.append(groupText +  "  " + orgText)
                }
                resou.allTexts = msgs
            }
        case .good:
            
            // 原始数据 这个数据可能包含礼物的变量： 感谢您送的【礼物名称】，这里需要替换成具体的礼物
            if let  gift = gift {
                orgText = orgText.replacingOccurrences(of: "【礼物名称】", with: gift.name)
            }
            
            if let triggerUserNames = item.triggerUserNames {
                
                var groupTexts:[String] = []
                
                // 分组 3 个一组
                let groupNames = triggerUserNames.chunked(into: 3)
                groupNames.forEach { groupName in
                    let groupText = self.formatText(template: text, names: groupName)
                    groupTexts.append(groupText)
                }
                
                var msgs: [String] = []
               
                // 有几组数据就要合成几次
                for (_, groupText) in groupTexts.enumerated() {
                    msgs.append(groupText +  "  " + orgText)
                }
                resou.allTexts = msgs
            }
            
        }
    }
}

extension AiAnswerFactory {
    
    func formatText(template: String, names: [String], prefix: Bool = false) -> String {
        
        var newNames:[String] = []
        
        names.forEach { n in
            if prefix {
                newNames.append(String(n.prefix(8)))
            } else {
                newNames.append(n)
            }
        }
        
        
        // 拆分文本为占位符和非占位符组件
        let pattern = "(【用户.*?】)"
        let regex = try! NSRegularExpression(pattern: pattern)
        let nsString = template as NSString
        var components:[String] = []
        var lastIndex = 0
        
        // 步骤1：分割模板为交替的文本和占位符
        regex.enumerateMatches(in: template, options: [], range: NSRange(location: 0, length: template.utf16.count)) { match, _, _ in
            guard let match = match else { return }
            
            // 添加占位符前的普通文本
            let beforeRange = NSRange(location: lastIndex, length: match.range.location - lastIndex)
            if beforeRange.length > 0 {
                components.append(nsString.substring(with: beforeRange))
            }
            
            // 添加占位符
            components.append(nsString.substring(with: match.range))
            lastIndex = match.range.upperBound
        }
        
        // 添加最后一段普通文本
        if lastIndex < nsString.length {
            components.append(nsString.substring(from: lastIndex))
        }

        // 步骤2：替换占位符并按数组长度截断
        var result = ""
        var replacementCount = 0
        let maxReplacements = min(newNames.count, components.filter { $0.range(of: pattern, options: .regularExpression) != nil }.count)
        
        for component in components {
            guard replacementCount < maxReplacements else { break }
            
            if component.range(of: pattern, options: .regularExpression) != nil {
                // 替换占位符
                result += newNames[replacementCount]
                replacementCount += 1
            } else {
                // 保留普通文本
                result += component
            }
        }
        
        return result
    }


}


extension Array {
    func chunked(into size: Int) -> [[Element]] {
        return stride(from: 0, to: self.count, by: size).map {
            Array(self[$0..<Swift.min($0 + size, self.count)])
        }
    }
}
