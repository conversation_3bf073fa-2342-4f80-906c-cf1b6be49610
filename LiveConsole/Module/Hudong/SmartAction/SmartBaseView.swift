//
//  SmartBaseView.swift
//  LivePlus
//
//  Created by 郭炜 on 2025/1/13.
//

import UIKit

class SmartBaseView: UIView {

    override init(frame: CGRect) {
        super.init(frame: frame)
        self.backgroundColor = UIColor("#111111")
        
        makeUI()
        business()
    }
    
    deinit {
        print("\(type(of: self)) deinited")
    }
    
    convenience init() {
        self.init(frame: .zero)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func makeUI() {
        clipsToBounds = true
    }
    
    func business() { }

}
