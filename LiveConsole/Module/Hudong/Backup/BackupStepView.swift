//
//  BackupStepView.swift
//  LivePlus
//
//  Created by ZYZ-HF-BJB-081 on 2024/3/28.
//


import Foundation
import SnapKit

class StepView: UIView {
    lazy var coverView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(named: "backup_step1_n")
        imageView.contentMode = .scaleAspectFit
        imageView.backgroundColor = .clear
        return imageView
    }()
    
    lazy var titleLab: UILabel = {
        let label = UILabel()
        label.textColor = .white
        label.font = UIFont.systemFont(ofSize: 12, weight: .regular)
        return label
    }()
    
    lazy var desTitle: UILabel = {
        let label = UILabel()
        label.textColor = UIColor("#4D4E52")
        label.font = UIFont.systemFont(ofSize: 12, weight: .regular)
        return label
    }()
    
    lazy var tipImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(named: "backup_step2_n")
        imageView.contentMode = .scaleAspectFit
        imageView.backgroundColor = .clear
        return imageView
    }()
    
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        
        makeUI()
    }
    
    convenience init() {
        self.init(frame: .zero)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    
    
    func makeUI() {
        addSubviews([coverView, desTitle, tipImageView])

        coverView.addSubview(titleLab)
        coverView.snp.makeConstraints { make in
            make.leading.equalToSuperview()
            make.top.equalToSuperview()
            make.width.height.equalTo(20)
        }
        
        titleLab.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.centerX.equalToSuperview()
            make.height.equalTo(20)
        }
        
        desTitle.snp.makeConstraints { make in
            make.leading.equalToSuperview().inset(24)
            make.top.equalToSuperview()
            make.height.equalTo(20)
        }
        
        tipImageView.snp.makeConstraints { make in
            make.trailing.equalToSuperview().inset(6)
            make.centerY.equalToSuperview()
            make.width.equalTo(22)
            make.height.equalTo(8)
        }
        
    }
    
    public func setSelected(selected: Bool) {
        coverView.image = selected ?  UIImage(named: "backup_step1_s") : UIImage(named: "backup_step1_n")
        tipImageView.image = selected ?  UIImage(named: "backup_step2_s") : UIImage(named: "backup_step2_n")
        desTitle.textColor = selected ?  UIColor("#4D4E52") : UIColor("#A6A6A6")
    }
    
}



/// 导出状态
enum BackupStepType {
    case one
    case two
    case three
}


class BackupStepView: UIView {
    
    // 素材数量
    var step: BackupStepType = .one
    
    
    lazy var step1: StepView = {
        let imageView = StepView()
        imageView.titleLab.text = "1"
        imageView.desTitle.text = "名称确认"
        return imageView
    }()
    
    lazy var step2: StepView = {
        let imageView = StepView()
        imageView.titleLab.text = "2"
        imageView.desTitle.text = "打包备份"
        return imageView
    }()
    
   
    lazy var step3: StepView = {
        let imageView = StepView()
        imageView.titleLab.text = "3"
        imageView.desTitle.text = "保存文件"
        imageView.tipImageView.isHidden = true
        return imageView
    }()
    
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        
        makeUI()
    }
    
    convenience init() {
        self.init(frame: .zero)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    
    
    func makeUI() {
        addSubviews([step1, step2, step3])

        step1.snp.makeConstraints { make in
            make.leading.equalToSuperview()
            make.height.equalTo(20)
            make.width.equalTo(108)
        }
        
        step2.snp.makeConstraints { make in
            make.leading.equalTo(step1.snp.trailing)
            make.height.equalTo(20)
            make.width.equalTo(108)
        }
        
        step3.snp.makeConstraints { make in
            make.leading.equalTo(step2.snp.trailing)
            make.height.equalTo(20)
            make.width.equalTo(108)
        }
    }
    
    

}

// MARK: - 业务逻辑
extension BackupStepView {
    // 绑定直播间数据
    func bind(to step: BackupStepType) {
        self.step = step
        switch step {
        case .one:
            self.step1.setSelected(selected: true)
            self.step2.setSelected(selected: false)
            self.step3.setSelected(selected: false)
        case .two:
            self.step1.setSelected(selected: true)
            self.step2.setSelected(selected: true)
            self.step3.setSelected(selected: false)
        case .three:
            self.step1.setSelected(selected: true)
            self.step2.setSelected(selected: true)
            self.step3.setSelected(selected: true)
        }
       
       
    }
    
}


// MARK: - BackupProgressView
class BackupProgressView: UIView {
    
    lazy var sendedTimeLabel: UILabel = {
        let label = UILabel()
        label.textColor = UIColor("#4D4E52")
        label.font = LCDevice.DIN_Font_PF_M(13)
        label.text = "已导出19分钟"
        return label
    }()
    
    lazy var expectedTimeLabel: UILabel = {
        let label = UILabel()
        label.textColor = UIColor("#6863F7")
        label.font = LCDevice.DIN_Font_PF_R(13)
        label.text = "预计还需分钟"
        label.textAlignment = .right
        return label
    }()
    
    lazy var progressView: CustomProgressBar = {
        let view = CustomProgressBar(frame: .zero)
        return view
    }()
    
    lazy var tipLabel: UILabel = {
        let label = UILabel()
        label.text = "导出速度与文件大小有关，请耐心等待，导出期间请保持电量充足、手机常亮，不要退出软件"
        label.font = UIFont.systemFont(ofSize: 12, weight: .regular)
        label.numberOfLines = 0
        label.textColor = UIColor("#4D4E52")
        return label
    }()
    
    
    var timer: DispatchSourceTimer?
    var progressSecond: Int64 = 0
    // 上次的时间
    var lastProgressSecond: Int64 = 0
    var fileSize: Int64 = 1
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        
        makeUI()
        business()
    }
    
    convenience init() {
        self.init(frame: .zero)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func makeUI() {
        addSubviews([sendedTimeLabel, expectedTimeLabel, progressView, tipLabel])
        
        sendedTimeLabel.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.leading.equalToSuperview().inset(16)
        }
        expectedTimeLabel.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.trailing.equalToSuperview().inset(16)
        }
        progressView.snp.makeConstraints { make in
            make.top.equalTo(sendedTimeLabel.snp.bottom).offset(10)
            make.leading.trailing.equalToSuperview().inset(16)
            make.height.equalTo(18)
        }
        tipLabel.snp.makeConstraints { make in
            make.top.equalTo(progressView.snp.bottom).offset(12)
            make.leading.trailing.equalToSuperview().inset(16)
        }
        
        progressView.cornerRadius = 9
    }
    
    func business() {
        
    }
    
    public func clear() {
        self.sendedTimeLabel.text = "已导入"
        expectedTimeLabel.text = "预计还需"
        progressView.progress = 0.0
    }
    
    public func progressView(progress: CGFloat, fileSize: Int64, real: Bool = true) {
        self.fileSize = fileSize
        self.lastProgressSecond = progressSecond
        if progressView.progress <= progress {
            progressView.progress = progress
        }
        if !real {
            return
        }
        let lasttime = CGFloat(fileSize) * (1.0 - progress) / CGFloat(LCSingleton.zipSpeed)
        self.expectedTimeLabel.text = "预计还需\(Int64(lasttime).getFormatPlayTime())"
        LCLog.d("真实进度：\(progress)")
    }
    
    func startTimer() {
        releaseTimer()
        progressSecond = 0
        self.timer = LCTools.dispatchTimer(timeInterval: 1, repeatCount: Int.max) { [weak self] _, _ in
            guard let self = self else { return }
            self.progressSecond = self.progressSecond + 1
            DispatchQueue.main.async {
                self.sendedTimeLabel.text = "已打包\(self.progressSecond.getFormatPlayTime())"
                // 主动更新一个假的进度 这时不能更新剩余时间
                if self.progressSecond - self.lastProgressSecond >= 3 {
                    var progress = self.progressView.progress
                    self.progressView(progress: min(progress + 0.01, 0.99), fileSize: self.fileSize, real: false)
                    LCLog.d("触发了假的进度：\(min(progress + 0.01, 0.99))")
                }
            }
        }
    }
    
    func releaseTimer() {
        self.timer?.cancel()
        self.timer = nil
    }
    
    deinit {
        releaseTimer()
    }
}


class CustomProgressBar: UIView {
    private let gradientLayer = CAGradientLayer()
    private let progressView = UIView()
    private let progressLabel = UILabel()
    
    var progress: CGFloat = 0.0 {
        didSet {
            updateProgress()
        }
    }
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        
        setupGradient()
        setupProgressView()
        setupProgressLabel()
        backgroundColor = UIColor("#DEDDE2")
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        
        setupProgressView()
        setupGradient()
        setupProgressLabel()
        
        backgroundColor = UIColor("#DEDDE2")
    }
    
    private func setupGradient() {
        gradientLayer.colors = [UIColor("#AB64F9").cgColor, UIColor("#6C74F3").cgColor]
        gradientLayer.startPoint = CGPoint(x: 0.0, y: 0.5)
        gradientLayer.endPoint = CGPoint(x: 1.0, y: 0.5)
        gradientLayer.frame = progressView.bounds
        progressView.layer.addSublayer(gradientLayer)
    }
    
    private func setupProgressView() {
        addSubview(progressView)
    }
    
    private func setupProgressLabel() {
        progressLabel.textAlignment = .center
        progressLabel.textColor = .white
        progressLabel.font = LCDevice.DIN_Font_PF_R(12)
        progressLabel.backgroundColor = .clear
        addSubview(progressLabel)
    }
    
    override func layoutSubviews() {
        super.layoutSubviews()
        
        progressView.frame = CGRect(x: 0, y: 0, width: bounds.width * progress, height: bounds.height)
        gradientLayer.frame = progressView.bounds
        progressLabel.frame = bounds
    }
    
    private func updateProgress() {
        progressView.frame = CGRect(x: 0, y: 0, width: bounds.width * progress, height: bounds.height)
        progressLabel.text = "\(Int(progress * 100))%"
        gradientLayer.frame = progressView.bounds
    }
}
