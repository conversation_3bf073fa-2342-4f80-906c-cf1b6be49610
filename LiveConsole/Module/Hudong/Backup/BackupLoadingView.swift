//
//  BackupLoadingView.swift
//  LivePlus
//
//  Created by 郭炜 on 2023/5/9.
//

import UIKit

class BackupLoadingView: UIView {
    
    lazy var shape1: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor("#D952EE")
        view.cornerRadius = 6
        return view
    }()
    
    lazy var shape2: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor("#613AF9")
        view.cornerRadius = 6
        return view
    }()
    
    lazy var shape3: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor("#411133")
        view.cornerRadius = 6
        return view
    }()

    override init(frame: CGRect) {
        super.init(frame: frame)
        
        makeUI()
        business()
    }
    
    convenience init() {
        self.init(frame: .zero)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func makeUI() {
        addSubviews([shape1, shape2, shape3])
        
        shape2.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.leading.equalToSuperview().inset((LCDevice.screenW - 154)/2.0)
            make.width.height.equalTo(12)
        }
        shape1.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.leading.equalToSuperview().inset((LCDevice.screenW - 154)/2.0)
            make.width.height.equalTo(12)
        }
        shape3.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.leading.equalToSuperview().inset((LCDevice.screenW - 154)/2.0)
            make.width.height.equalTo(12)
        }
    }
    
    func business() {
//        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
//            self.beginAnimation()
//        }
    }

}

extension BackupLoadingView {
    func beginAnimation() {
        let positionAnimation = CAKeyframeAnimation(keyPath: "position.x")
        positionAnimation.values = [-5, 0, 10, 40, 70, 80, 75]
        positionAnimation.keyTimes = [0, NSNumber(value: 5/90.0), NSNumber(value: 15/90.0), NSNumber(value: 45/90.0), NSNumber(value: 75/90.0), NSNumber(value: 85/90.0), 1]
        positionAnimation.isAdditive = true

        let scaleAnimation = CAKeyframeAnimation(keyPath: "transform.scale")
        scaleAnimation.values = [0.7, 0.9, 1, 0.9, 0.7]
        scaleAnimation.keyTimes = [0, NSNumber(value: 15/90.0), NSNumber(value: 45/90.0), NSNumber(value: 75/90.0), 1]

        let alphaAnimation = CAKeyframeAnimation(keyPath: "opacity")
        alphaAnimation.values = [0, 1, 1, 1, 0]
        alphaAnimation.keyTimes = [0, NSNumber(value: 1/6.0), NSNumber(value: 3/6.0), NSNumber(value: 5/6.0), 1]

        let group = CAAnimationGroup()
        group.animations = [positionAnimation, scaleAnimation, alphaAnimation]
        group.timingFunction = CAMediaTimingFunction(name: .easeInEaseOut)
        group.repeatCount = .infinity
        group.duration = 1.3
        
        self.shape1.layer.add(group, forKey: "basic1")
        group.timeOffset = 0.43
        self.shape2.layer.add(group, forKey: "basic2")
        group.timeOffset = 0.86
        self.shape3.layer.add(group, forKey: "basic3")
    }
}
