//
//  HudongAlertView.swift
//  LivePlus
//
//  Created by simon on 7.2.25.
//

import Foundation
import UIKit

class HudongAlertView: UIView {
    
    let factoryBackHeight: CGFloat = 311
    let factoryBackWidth: CGFloat = 252
    
    private lazy var contentView: UIImageView = {
        let view = UIImageView()
        view.backgroundColor = .clear
        view.image = UIImage(named: "anim_alertbg")
        view.isUserInteractionEnabled = true
        return view
    }()
    
    /// 渐变层
    private var gradientLayer: CAGradientLayer = {
        let leftColor = UIColor("#6974F2")
        let rightColor = UIColor("#AC63F9")
        let gradientColors = [leftColor.cgColor, rightColor.cgColor]
        let gradientLocations: [NSNumber] = [0.0, 1.0]
        let gradientLayer = CAGradientLayer()
        gradientLayer.startPoint = CGPoint(x: 0, y: 0)
        gradientLayer.endPoint = CGPoint(x: 1, y: 0)
        gradientLayer.colors = gradientColors
        gradientLayer.locations = gradientLocations
        return gradientLayer
    }()
    
    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.textColor = UIColor("#333333")
        label.font = LCDevice.DIN_Font_PF_M(18)
        label.text = "温馨提示"
        return label
    }()
    
    private lazy var imageView: UIImageView = {
        let vi = UIImageView()
        vi.backgroundColor = UIColor.clear
        vi.image = UIImage(named: "anim_tip")
        return vi
    }()
    
    private lazy var messageLabel: UILabel = {
        let label = UILabel()
        
        let paragraphStyle = NSMutableParagraphStyle()
        paragraphStyle.lineSpacing = 6
        let attribute = NSMutableAttributedString(string: "智能互动需另一部手机打开 快瓴中控台，在", attributes: [.foregroundColor: UIColor("#4D4E52"), .font: UIFont.systemFont(ofSize: 16, weight: .regular), NSAttributedString.Key.paragraphStyle: paragraphStyle])
        
        attribute.append(NSMutableAttributedString(string: "我的>行业版>智能互动", attributes: [.foregroundColor: UIColor("#6763F6"), .font: UIFont.systemFont(ofSize: 16, weight: .medium), NSAttributedString.Key.paragraphStyle: paragraphStyle]))
        
        attribute.append(NSMutableAttributedString(string: "与本机连接成功并开启后，本机才可开启使用", attributes: [.foregroundColor: UIColor("#4D4E52"), .font: UIFont.systemFont(ofSize: 16, weight: .regular), NSAttributedString.Key.paragraphStyle: paragraphStyle]))
        
        label.textAlignment = .left
        label.attributedText = attribute
        label.numberOfLines = 0
        return label
    }()
    
    private lazy var sureButton: UIButton = {
        let button = UIButton { [weak self] in
            guard let self = self else { return }
            self.dismiss()
            if let callback = self.sureAction {
                callback()
            }
            
        }
        button.setTitle("连接", for: .normal)
        button.cornerRadius = 14
        button.titleLabel?.font = UIFont.systemFont(ofSize: 14, weight: .regular)
        button.setTitleColor(.white, for: .normal)
        button.cornerRadius = 20
        return button
    }()
    
    lazy var checkButton: UIButton = {
        let button = UIButton()
        button.addTarget(self, action: #selector(closeButtonAction), for: .touchUpInside)
        button.setImage(UIImage(named: "scene_check_unsel"), for: .normal)
        button.setImage(UIImage(named: "scene_check_sel"), for: .selected)
        button.imageView?.contentMode = .scaleAspectFit
        button.setTitle(" 不再提示", for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 12, weight: .regular)
        button.setTitleColor(UIColor("#A5A7AC"), for: .normal)
        return button
    }()
    
    private lazy var cancelButton: UIButton = {
        let button = UIButton { [weak self] in
            guard let self = self else { return }
            self.dismiss()
        }
        button.titleLabel?.font = UIFont.systemFont(ofSize: 14, weight: .regular)
        button.setTitleColor(UIColor("#19191A"), for: .normal)
        button.setTitle("取消", for: .normal)
        button.cornerRadius = 20
        button.borderWidth = 1
        button.borderColor = UIColor("#6974F2")
        return button
    }()
    
    private var sureAction: VIPAction?
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        makeUI()
    }
    
    convenience init(sureAction: VIPAction?) {
        self.init(frame: CGRect(x: 0, y: 0, width: LCDevice.screenW, height: LCDevice.screenH))
        self.sureAction = sureAction
        makeUI()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    @objc func closeButtonAction() {
        self.checkButton.isSelected = !self.checkButton.isSelected
    }
    
    func makeUI() {
        backgroundColor = UIColor(white: 0.0, alpha: 0.5)
        addSubviews([contentView])
        contentView.addSubviews([titleLabel, imageView, sureButton, messageLabel, cancelButton, checkButton])
        
        contentView.snp.makeConstraints { make in
            make.width.equalTo(factoryBackWidth)
            make.height.equalTo(factoryBackHeight)
            make.center.equalToSuperview()
        }
        
        imageView.snp.makeConstraints { make in
            make.leading.equalToSuperview().inset(50)
            make.top.equalToSuperview().inset(25)
            make.height.equalTo(28)
            make.width.equalTo(28)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(26)
            make.leading.equalToSuperview().inset(79)
            make.height.equalTo(25)
        }
        
        messageLabel.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview().inset(23)
            make.top.equalTo(titleLabel.snp.bottom).offset(12)
            make.height.equalTo(110)
        }
        
        sureButton.snp.makeConstraints { make in
            make.top.equalTo(messageLabel.snp.bottom).offset(14)
            make.trailing.equalToSuperview().inset(23)
            make.height.equalTo(40)
            make.width.equalTo(95)
        }
       
         cancelButton.snp.makeConstraints { make in
             make.top.equalTo(messageLabel.snp.bottom).offset(14)
             make.leading.equalToSuperview().inset(23)
             make.height.equalTo(40)
             make.width.equalTo(95)
        }
        
        checkButton.snp.makeConstraints { make in
            make.top.equalTo(sureButton.snp.bottom).offset(16)
            make.leading.trailing.equalToSuperview().inset(56)
            make.height.equalTo(28)
        }
        
        sureButton.layer.insertSublayer(gradientLayer, at: 0)
        gradientLayer.frame = CGRect(x: 0, y: 0, width: 95, height: 40)
        
    }
    
    func dismiss() {
        
//        UserDefaults.standard.setValue(checkButton.isSelected, forKey: SKey.UD_HudongAlert)
        
        UIView.animate(withDuration: 0.2) {
            self.alpha = 0
        } completion: { _ in
            self.removeFromSuperview()
        }
    }
}

extension HudongAlertView {
    public static func show(sureAction: VIPAction?) {
        guard let appDel = UIApplication.shared.delegate as? AppDelegate else { return }
        let showingView = HudongAlertView(sureAction: sureAction )
        showingView.alpha = 0.0
        appDel.wd.addSubview(showingView)
        UIView.animate(withDuration: 0.2) {
            showingView.alpha = 1.0
        }
    }
}

// 智能互动的导入提示
class HudongImportAlertView: UIView {
    
    let factoryBackHeight: CGFloat = 221
    let factoryBackWidth: CGFloat = 320
    
    private lazy var contentView: UIImageView = {
        let view = UIImageView()
        view.backgroundColor = .white
        view.isUserInteractionEnabled = true
        view.cornerRadius = 12
        return view
    }()
    
    /// 渐变层
    private var gradientLayer: CAGradientLayer = {
        let leftColor = UIColor("#6974F2")
        let rightColor = UIColor("#AC63F9")
        let gradientColors = [leftColor.cgColor, rightColor.cgColor]
        let gradientLocations: [NSNumber] = [0.0, 1.0]
        let gradientLayer = CAGradientLayer()
        gradientLayer.startPoint = CGPoint(x: 0, y: 0)
        gradientLayer.endPoint = CGPoint(x: 1, y: 0)
        gradientLayer.colors = gradientColors
        gradientLayer.locations = gradientLocations
        return gradientLayer
    }()
    
    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.textColor = UIColor("#333333")
        label.font = LCDevice.DIN_Font_PF_M(18)
        label.text = "导入后现有的数据会被覆盖，您确定导入吗？"
        label.numberOfLines = 0
        return label
    }()
    
    
    private lazy var messageLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 14, weight: .regular)
        label.textColor = UIColor("#4D4E52")
        label.textAlignment = .left
        label.text = "如不想被覆盖，请先备份后再导入"
        label.numberOfLines = 0
        return label
    }()
    
    private lazy var sureButton: UIButton = {
        let button = UIButton { [weak self] in
            guard let self = self else { return }
            self.dismiss()
            if let callback = self.sureAction {
                callback()
            }
            
        }
        button.setTitle("确定", for: .normal)
        button.cornerRadius = 14
        button.titleLabel?.font = UIFont.systemFont(ofSize: 14, weight: .regular)
        button.setTitleColor(.white, for: .normal)
        button.cornerRadius = 20
        return button
    }()
    
   
    
    private lazy var cancelButton: UIButton = {
        let button = UIButton { [weak self] in
            guard let self = self else { return }
            self.dismiss()
        }
        button.titleLabel?.font = UIFont.systemFont(ofSize: 14, weight: .regular)
        button.setTitleColor(UIColor("#19191A"), for: .normal)
        button.setTitle("取消", for: .normal)
        button.cornerRadius = 20
        button.borderWidth = 1
        button.borderColor = UIColor("#6974F2")
        return button
    }()
    
    private var sureAction: VIPAction?
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        makeUI()
    }
    
    convenience init(sureAction: VIPAction?) {
        self.init(frame: CGRect(x: 0, y: 0, width: LCDevice.screenW, height: LCDevice.screenH))
        self.sureAction = sureAction
        makeUI()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    
    func makeUI() {
        backgroundColor = UIColor(white: 0.0, alpha: 0.5)
        addSubviews([contentView])
        contentView.addSubviews([titleLabel, sureButton, messageLabel, cancelButton])
        
        contentView.snp.makeConstraints { make in
            make.width.equalTo(factoryBackWidth)
            make.height.equalTo(factoryBackHeight)
            make.center.equalToSuperview()
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(26)
            make.leading.trailing.equalToSuperview().inset(30)
//            make.height.equalTo(55)
        }
        
        messageLabel.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview().inset(30)
            make.top.equalTo(titleLabel.snp.bottom).offset(12)
        }
        
        sureButton.snp.makeConstraints { make in
            make.bottom.equalToSuperview().inset(30)
            make.trailing.equalToSuperview().inset(45)
            make.height.equalTo(40)
            make.width.equalTo(95)
        }
       
         cancelButton.snp.makeConstraints { make in
             make.bottom.equalToSuperview().inset(30)
             make.leading.equalToSuperview().inset(45)
             make.height.equalTo(40)
             make.width.equalTo(95)
        }
        
        
        sureButton.layer.insertSublayer(gradientLayer, at: 0)
        gradientLayer.frame = CGRect(x: 0, y: 0, width: 95, height: 40)
        
    }
    
    func dismiss() {
        
        UIView.animate(withDuration: 0.2) {
            self.alpha = 0
        } completion: { _ in
            self.removeFromSuperview()
        }
    }
}

extension HudongImportAlertView {
    public static func show(sureAction: VIPAction?) {
        guard let appDel = UIApplication.shared.delegate as? AppDelegate else { return }
        let showingView = HudongImportAlertView(sureAction: sureAction )
        showingView.alpha = 0.0
        appDel.wd.addSubview(showingView)
        UIView.animate(withDuration: 0.2) {
            showingView.alpha = 1.0
        }
    }
}
