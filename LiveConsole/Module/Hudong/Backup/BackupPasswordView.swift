//
//  BackupPasswordView.swift
//  LivePlus
//
//  Created by simon on 12.5.23.
//

import Foundation

class BackupPasswordView: UIView {
    
    var sureBtnActionBlock:((_ string:String) -> Void)?
    
    var closeBtnActionBlock: (() -> Void)?
    
    lazy var okButton: UIButton = {
        let button = UIButton()
        button.setTitle("确定", for: .normal)
        button.setTitleColor(UIColor.white, for: .normal)
        button.backgroundColor = UIColor("#6863F7")
        button.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .regular)
        button.cornerRadius = 22
        return button
    }()
    
    lazy var ipInputView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor("#F5F5F8")
        view.cornerRadius = 22
        return view
    }()
    
    lazy var closeButton: UIButton = {
        let button = UIButton()
        button.setImage(UIImage(named: "icon_close"), for: .normal)
        button.zl_enlargeValidTouchArea(inset: 30)
        return button
    }()
    
    /// 区域名称
    private lazy var bgview: UIView = {
        let label = UIView()
        label.backgroundColor = .white
        label.cornerRadius = 12
        return label
    }()
    
    private lazy var mimaView: UIImageView = {
        let v = UIImageView()
        v.image = UIImage(named: "backup_密码")
        return v
    }()
    
    private lazy var expireLab: UILabel = {
        let label = UILabel()
        label.text = "请输入直播间打开密码"
        label.font = LCDevice.DIN_Font_PF_M(18)
        label.textColor = UIColor("#4D4E52")
        label.textAlignment = .center
        return label
    }()
    
    private lazy var desLab: UILabel = {
        let label = UILabel()
        label.text = "如何查看密码？可前往我的直播间-- 我的备份--找到分享的直播间密码。"
        label.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        label.textColor = UIColor("#4D4E52")
        label.numberOfLines = 0
        return label
    }()
    
   public lazy var textField: ParserTextField = {
        let textField = ParserTextField()
        textField.placeholder = "请输入4位数密码"
        textField.borderStyle = .none
        textField.font = LCDevice.DIN_Font_PF_M(15)
        textField.delegate = self
        textField.pattern = "^[a-zA-Z0-9]{1,4}$"
        return textField
    }()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupBaseView()
        
        okButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self else { return }
                self.textField.resignFirstResponder()
                guard let str = self.textField.text else {
                    HUD.showFail("请输入密码")
                    return
                }
                self.sureBtnActionBlock?(str)
            }.disposed(by: rx.disposeBag)
        
        closeButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self else { return }
                self.closeBtnActionBlock?()
                self.removeFromSuperview()
            }.disposed(by: rx.disposeBag)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupBaseView() {
        self.backgroundColor = UIColor.black.alpha(value: 0.5)

        addSubview(bgview)
        bgview.cornerRadius = 12
        
        bgview.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview().offset(-100)
            make.width.equalTo(333)
            make.height.equalTo(327)
        }
        
        bgview.addSubviews([okButton, expireLab,ipInputView,mimaView, closeButton, desLab])
        ipInputView.addSubview(textField)
        
        closeButton.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(21)
            make.trailing.equalToSuperview().inset(15)
            make.width.height.equalTo(24)
        }
        
        mimaView.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(21)
            make.centerX.equalToSuperview()
            make.width.height.equalTo(36)
        }
        
        expireLab.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(64)
            make.centerX.equalToSuperview()
            make.width.equalTo(200)
            make.height.equalTo(25)
        }
        
        ipInputView.snp.makeConstraints { make in
            make.top.equalTo(expireLab.snp.bottom).offset(36)
            make.centerX.equalToSuperview()
            make.width.equalTo(200)
            make.height.equalTo(44)
        }
        
        desLab.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.width.equalTo(280)
            make.bottom.equalToSuperview().inset(16)
        }
        
        textField.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.centerX.equalToSuperview()
            make.width.equalTo(160)
            make.height.equalTo(44)
        }
        
        okButton.snp.makeConstraints { make in
            make.bottom.equalTo(desLab.snp.top).offset(-16)
            make.centerX.equalToSuperview()
            make.width.equalTo(280)
            make.height.equalTo(44)
        }
        
        
    }
}


extension BackupPasswordView: UITextFieldDelegate {
    func textFieldDidEndEditing(_ textField: UITextField) {
        
    }
}



class ImportProgressView: UIView {
    
    var closeBtnActionBlock: (() -> Void)?
    
    lazy var sendedTimeLabel: UILabel = {
        let label = UILabel()
        label.textColor = UIColor.black
        label.font = LCDevice.DIN_Font_PF_M(13)
        label.text = "已导出19分钟"
        return label
    }()
    
    lazy var expectedTimeLabel: UILabel = {
        let label = UILabel()
        label.textColor = UIColor("#6863F7")
        label.font = LCDevice.DIN_Font_PF_M(13)
        label.text = "预计还需分钟"
        label.textAlignment = .right
        return label
    }()
    
    private lazy var nameLab: UILabel = {
        let label = UILabel()
        label.text = "导入中，请耐心等待..."
        label.font = LCDevice.DIN_Font_PF_M(16)
        label.textColor = .black
        label.textAlignment = .center
        return label
    }()
    
    
    lazy var progressView: CustomProgressBar = {
        let view = CustomProgressBar(frame: .zero)
        return view
    }()
    
    lazy var tipLabel: UILabel = {
        let label = UILabel()
        label.text = "导入速度与素材大小有关，请耐心等待，导入期间保持电量充足、手机常亮，不要退出 快瓴中控台APP软件"
        label.font = LCDevice.DIN_Font_PF_M(13)
        label.numberOfLines = 0
        label.textColor = .black
        return label
    }()
    
    lazy var startView: UIView = {
        let view = UIView()
        return view
    }()
    
    lazy var successView: UIView = {
        let view = UIView()
        return view
    }()
    
    lazy var successButton: UIButton = {
        let button = UIButton()
        button.setImage(UIImage(named: "backup_success"), for: .normal)
        button.setTitle("导如成功", for: .normal)
        button.titleLabel?.font = LCDevice.DIN_Font_PF_R(16)
        button.setTitleColor(.black, for: .normal)
        button.imagePosition(style: .left, spacing: 7)
        button.isUserInteractionEnabled = false
        return button
    }()
    
    lazy var gotoLocalDocumentButton: UIButton = {
        let button = UIButton(frame: CGRect(x: 0, y: 0, width: LCDevice.screenW - 98, height: 44))
        button.setTitle("我知道了", for: .normal)
        button.titleLabel?.font = LCDevice.DIN_Font_PF_M(16)
        button.setTitleColor(.white, for: .normal)
        button.imagePosition(style: .left, spacing: 7)
        button.cornerRadius = 22
        return button
    }()
    
    
    lazy var successTitle: UILabel = {
        let label = UILabel()
        label.textColor = UIColor("#797B7D")
        label.font = LCDevice.DIN_Font_PF_M(13)
        label.text = "本次导入共花费"
        label.textAlignment = .center
        return label
    }()
    
    
//    lazy var closeButton: UIButton = {
//        let button = UIButton()
//        button.setImage(UIImage(named: "icon_close"), for: .normal)
//        button.zl_enlargeValidTouchArea(inset: 30)
//        return button
//    }()
    
    /// 区域名称
    private lazy var bgview: UIView = {
        let label = UIView()
        label.backgroundColor = .white
        label.cornerRadius = 12
        return label
    }()
    
    /// 渐变层
    lazy var gradientLayer2: CAGradientLayer = {
        let leftColor = UIColor("#6974F2")
        let rightColor = UIColor("#AC63F9")
        let gradientColors = [leftColor.cgColor, rightColor.cgColor]
        let gradientLocations: [NSNumber] = [0.0, 1.0]
        let gradientLayer = CAGradientLayer()
        gradientLayer.startPoint = CGPoint(x: 0, y: 0)
        gradientLayer.endPoint = CGPoint(x: 1, y: 0)
        gradientLayer.colors = gradientColors
        gradientLayer.locations = gradientLocations
        return gradientLayer
    }()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        
        makeUI()
        business()

        gotoLocalDocumentButton.layer.insertSublayer(gradientLayer2, at: 0)
        gradientLayer2.frame = gotoLocalDocumentButton.bounds
    }
    
    var timer: DispatchSourceTimer?
    var progressSecond: Int64 = 0
    // 上次的时间
    var lastProgressSecond: Int64 = 0
    var fileSize: Int64 = 1
    
    convenience init() {
        self.init(frame: .zero)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func makeUI() {
        
        self.backgroundColor = UIColor.black.alpha(value: 0.5)

        addSubview(bgview)
        bgview.cornerRadius = 12
        
        bgview.snp.makeConstraints { make in
            make.centerY.equalToSuperview().offset(-100)
            make.leading.trailing.equalToSuperview().inset(25)
            make.height.equalTo(180)
        }
        
        bgview.addSubviews([startView,successView])
        
        startView.addSubviews([nameLab, sendedTimeLabel, expectedTimeLabel, progressView, tipLabel])
        
        successView.addSubviews([successButton, successTitle, gotoLocalDocumentButton])
        
        startView.isHidden = false
        startView.snp.makeConstraints { make in
            make.leading.top.trailing.bottom.equalToSuperview()
        }
        
        nameLab.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(16)
            make.leading.trailing.equalToSuperview().inset(16)
        }
        
//        closeButton.snp.makeConstraints { make in
//            make.top.equalToSuperview().inset(12)
//            make.trailing.equalToSuperview().inset(15)
//            make.width.height.equalTo(24)
//        }
        
        sendedTimeLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(60)
            make.leading.equalToSuperview().inset(16)
        }
        expectedTimeLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(60)
            make.trailing.equalToSuperview().inset(16)
        }
        progressView.snp.makeConstraints { make in
            make.top.equalTo(sendedTimeLabel.snp.bottom).offset(10)
            make.leading.trailing.equalToSuperview().inset(16)
            make.height.equalTo(18)
        }
        tipLabel.snp.makeConstraints { make in
            make.top.equalTo(progressView.snp.bottom).offset(12)
            make.leading.trailing.equalToSuperview().inset(16)
        }
        
        successView.isHidden = true
        successView.snp.makeConstraints { make in
            make.leading.top.trailing.bottom.equalToSuperview()
        }
        
        successButton.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(16)
            make.height.equalTo(32)
            make.centerX.equalToSuperview()
            make.width.equalTo(100)
        }
        
        successTitle.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(68)
            make.leading.trailing.equalToSuperview().inset(16)
            make.centerX.equalToSuperview()
            make.height.equalTo(16)
        }
        
        gotoLocalDocumentButton.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview().inset(24)
            make.height.equalTo(44)
            make.bottom.equalToSuperview().inset(24)
        }
        
        progressView.cornerRadius = 9
    }
    
    func business() {
        gotoLocalDocumentButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self else { return }
                self.removeFromSuperview()
                self.closeBtnActionBlock?()
            }.disposed(by: rx.disposeBag)
    }
    
    // 显示成功的画面
    func showSuccessView() {
        self.startView.isHidden = true
        self.successView.isHidden = false
        self.successButton.setTitle("导入成功", for: .normal)
        let attributes1 = [NSAttributedString.Key.font: LCDevice.DIN_Font_PF_M(13),
                          NSAttributedString.Key.foregroundColor: UIColor("#ABABAB")]
        let attribeString1 = NSAttributedString(string: "本次导入共花费 ", attributes: attributes1)
        
        let attributes2 = [NSAttributedString.Key.font: LCDevice.DIN_Font_PF_M(13),
                          NSAttributedString.Key.foregroundColor: UIColor("#6863F7")]
        let attribeString2 = NSAttributedString(string: "\(progressSecond.getFormatPlayTime())", attributes: attributes2)
        let attString = NSMutableAttributedString()
        attString.append(attribeString1)
        attString.append(attribeString2)
        self.successTitle.attributedText =  attString
        self.releaseTimer()
    }
    
    public func clear() {
        self.sendedTimeLabel.text = "已导入"
        expectedTimeLabel.text = "预计还需"
        progressView.progress = 0.0
    }
    
    public func importProgressView(progress: CGFloat, fileSize: Int64, real: Bool = true) {
        self.fileSize = fileSize
        self.lastProgressSecond = progressSecond
        self.startView.isHidden = false
        self.successView.isHidden = true
        if progressView.progress <= progress {
            progressView.progress = progress
        }
        if !real {
            return
        }
        let lasttime =  Int64(CGFloat(fileSize) * (1.0 - progress)) / Int64(LCSingleton.unzipSpeed)
        expectedTimeLabel.text = "预计还需\(lasttime.getFormatPlayTime())"
        LCLog.d("真实进度：\(progress)")
    }
    
    func startTimer() {
        releaseTimer()
        progressSecond = 0
        self.timer = LCTools.dispatchTimer(timeInterval: 1, repeatCount: Int.max) { [weak self] _, _ in
            guard let self = self else { return }
            self.progressSecond = self.progressSecond + 1
            DispatchQueue.main.async {
                self.sendedTimeLabel.text = "已导入\(self.progressSecond.getFormatPlayTime())"
                // 主动更新一个假的进度 这时不能更新剩余时间
                if self.progressSecond - self.lastProgressSecond >= 3 {
                    var progress = self.progressView.progress
                    self.importProgressView(progress: min(progress + 0.01, 0.99), fileSize: self.fileSize, real: false)
                    LCLog.d("触发了假的进度：\(min(progress + 0.01, 0.99))")
                }
            }
        }
    }
    
    func releaseTimer() {
        self.timer?.cancel()
        self.timer = nil
    }
    
    deinit {
        releaseTimer()
    }
    

}

class BackupFilePasswordView: UIView {
    
    var sureBtnActionBlock:((_ string:String) -> Void)?
    
    var closeBtnActionBlock: (() -> Void)?
    
    lazy var okButton: UIButton = {
        let button = UIButton()
        button.setTitle("确定", for: .normal)
        button.setTitleColor(UIColor.white, for: .normal)
        button.backgroundColor = UIColor("#6863F7")
        button.titleLabel?.font = UIFont.systemFont(ofSize: 12, weight: .medium)
        button.cornerRadius = 22
        return button
    }()
    
    lazy var ipInputView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor("#F5F5F8")
        view.cornerRadius = 22
        return view
    }()
    
    lazy var closeButton: UIButton = {
        let button = UIButton()
        button.setImage(UIImage(named: "icon_close"), for: .normal)
        button.zl_enlargeValidTouchArea(inset: 30)
        return button
    }()
    
    /// 区域名称
    private lazy var bgview: UIView = {
        let label = UIView()
        label.backgroundColor = .white
        label.cornerRadius = 12
        return label
    }()
    
    private lazy var mimaView: UIImageView = {
        let v = UIImageView()
        v.image = UIImage(named: "backup_密码")
        return v
    }()
    
    private lazy var expireLab: UILabel = {
        let label = UILabel()
        label.text = "请输入直播间打开密码"
        label.font = LCDevice.DIN_Font_PF_M(18)
        label.textColor = UIColor("#4D4E52")
        label.textAlignment = .center
        return label
    }()
    
    private lazy var desLab: UILabel = {
        let label = UILabel()
        label.text = "如何查看密码？可前往我的直播间-- 我的备份--找到分享的直播间密码。"
        label.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        label.textColor = UIColor("#4D4E52")
        label.numberOfLines = 0
        return label
    }()
    
   public lazy var textField: UITextField = {
        let textField = UITextField()
        textField.placeholder = "请联系分享人获取密码"
        textField.borderStyle = .none
        textField.font = LCDevice.DIN_Font_PF_M(15)
        textField.delegate = self
        return textField
    }()
    
    lazy var fileLabel: UILabel = {
        let label = UILabel()
        label.textColor = UIColor("#797B7D")
        label.font = LCDevice.DIN_Font_PF_R(14)
        label.numberOfLines = 0
        return label
    }()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupBaseView()
        
        okButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self else { return }
                self.textField.resignFirstResponder()
                guard let str = self.textField.text else {
                    HUD.showFail("请输入密码")
                    return
                }
                self.sureBtnActionBlock?(str)
            }.disposed(by: rx.disposeBag)
        
        closeButton.rx.tap
            .subscribe { [weak self] _ in
                guard let self = self else { return }
                self.closeBtnActionBlock?()
                self.removeFromSuperview()
            }.disposed(by: rx.disposeBag)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupBaseView() {
        self.backgroundColor = UIColor.black.alpha(value: 0.5)

        addSubview(bgview)
        bgview.cornerRadius = 12
        
        bgview.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview().offset(-100)
            make.width.equalTo(333)
            make.height.equalTo(390)
        }
        
        bgview.addSubviews([okButton, expireLab,ipInputView,mimaView, closeButton, fileLabel, desLab])
        ipInputView.addSubview(textField)
        
        closeButton.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(21)
            make.trailing.equalToSuperview().inset(15)
            make.width.height.equalTo(24)
        }
        
        mimaView.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(21)
            make.centerX.equalToSuperview()
            make.width.height.equalTo(36)
        }
        
        expireLab.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(64)
            make.centerX.equalToSuperview()
            make.width.equalTo(200)
            make.height.equalTo(25)
        }
        fileLabel.snp.makeConstraints { make in
            make.top.equalTo(expireLab.snp.bottom).offset(10)
            make.leading.trailing.equalToSuperview().inset(26)
        }
        
        ipInputView.snp.makeConstraints { make in
            make.top.equalTo(fileLabel.snp.bottom).offset(36)
            make.centerX.equalToSuperview()
            make.leading.trailing.equalToSuperview().inset(26)
            make.height.equalTo(44)
        }
        
        textField.snp.makeConstraints { make in
            make.top.bottom.equalToSuperview()
            make.leading.trailing.equalTo(16)
        }
        
        desLab.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview().inset(26)
            make.bottom.equalToSuperview().inset(16)
        }
        
        okButton.snp.makeConstraints { make in
            make.bottom.equalTo(desLab.snp.top).offset(-16)
            make.centerX.equalToSuperview()
            make.leading.trailing.equalToSuperview().inset(26)
            make.height.equalTo(44)
        }
    }
}


extension BackupFilePasswordView: UITextFieldDelegate {
    func textFieldDidEndEditing(_ textField: UITextField) {
        
    }
}

// 备份保存的提示UI
class BackupSaveTipView: UIView {
 
    lazy var tipLabel: UILabel = {
        let label = UILabel()
        label.text = "点击右上角\"存储\"才能完成保存"
        label.font = LCDevice.DIN_Font_PF_R(14)
        label.numberOfLines = 0
        label.textColor = .white
        return label
    }()
    
    
    lazy var timeLabel: UILabel = {
        let label = UILabel()
        label.text = "3s"
        label.font = LCDevice.DIN_Font_PF_M(14)
        label.textColor = UIColor("#C0BEFF")
        label.textAlignment = .right
        return label
    }()

    /// 区域名称
    private lazy var bgview: UIView = {
        let label = UIView()
        label.backgroundColor = .clear
        label.cornerRadius = 6
        return label
    }()
    
  
    override init(frame: CGRect) {
        super.init(frame: frame)
        makeUI()
    }
    
    var timer: DispatchSourceTimer?
    var progressSecond: Int64 = 3
   
    
    convenience init() {
        self.init(frame: .zero)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func makeUI() {
        
        self.backgroundColor = UIColor.clear

        addSubview(bgview)
        bgview.addSubviews([tipLabel, timeLabel])
        bgview.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
       
        tipLabel.snp.makeConstraints { make in
            make.leading.equalToSuperview().inset(20)
            make.centerY.equalToSuperview()
        }
        
        timeLabel.snp.makeConstraints { make in
            make.trailing.equalToSuperview().inset(20)
            make.centerY.equalToSuperview()
            make.width.equalTo(22)
        }

    }
   
    
    func startTimer() {
        releaseTimer()
        progressSecond = 4
        self.timer = LCTools.dispatchTimer(timeInterval: 1, repeatCount: 3) { [weak self] _, _ in
            guard let self = self else { return }
            self.progressSecond = self.progressSecond - 1
            
            DispatchQueue.main.async {
               
                // 主动更新一个假的进度 这时不能更新剩余时间
                if self.progressSecond > 0 {
                     self.timeLabel.text = "\(self.progressSecond)s"
                }
            }
        }
    }
    
    func releaseTimer() {
        self.timer?.cancel()
        self.timer = nil
    }
    
    deinit {
        releaseTimer()
    }
    
    func dismiss() {
        self.removeFromSuperview()
    }

}
