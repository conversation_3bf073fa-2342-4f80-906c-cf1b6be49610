<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="20037" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" launchScreen="YES" useTraitCollections="YES" colorMatched="YES" initialViewController="01J-lp-oVM">
    <device id="retina5_9" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="20020"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--View Controller-->
        <scene sceneID="EHf-IW-A2E">
            <objects>
                <viewController id="01J-lp-oVM" sceneMemberID="viewController">
                    <layoutGuides>
                        <viewControllerLayoutGuide type="top" id="Awi-Ne-I4b"/>
                        <viewControllerLayoutGuide type="bottom" id="myk-jV-Dhn"/>
                    </layoutGuides>
                    <view key="view" contentMode="scaleToFill" id="Ze5-6b-2t3">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="812"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <imageView userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="splash_zh" translatesAutoresizingMaskIntoConstraints="NO" id="u03-ax-yPN">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="812"/>
                            </imageView>
                        </subviews>
                        <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <constraints>
                            <constraint firstItem="u03-ax-yPN" firstAttribute="top" secondItem="Ze5-6b-2t3" secondAttribute="top" id="6Vu-O1-g3T"/>
                            <constraint firstAttribute="bottom" secondItem="u03-ax-yPN" secondAttribute="bottom" id="Qq0-I5-ftj"/>
                            <constraint firstItem="u03-ax-yPN" firstAttribute="leading" secondItem="Ze5-6b-2t3" secondAttribute="leading" id="cP0-c5-aDa"/>
                            <constraint firstAttribute="trailing" secondItem="u03-ax-yPN" secondAttribute="trailing" id="g4d-14-oR4"/>
                        </constraints>
                    </view>
                    <nil key="simulatedStatusBarMetrics"/>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="iYj-Kq-Ea1" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="52" y="374.6305418719212"/>
        </scene>
    </scenes>
    <resources>
        <image name="splash_zh" width="521" height="926"/>
    </resources>
</document>
