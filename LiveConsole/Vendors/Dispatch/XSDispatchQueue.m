//
//  IFVDispatchQueue.m
//  CoolVideoSDK
//
//  Created by admin on 2020/4/22.
//  Copyright © 2020 admin. All rights reserved.
//

#import "XSDispatchQueue.h"

@implementation XSDispatchQueue

+ (instancetype)sharedInstance {
    static XSDispatchQueue *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[XSDispatchQueue alloc] init];
    });
    return instance;
}

- (instancetype)init {
    return [self initWithName:NSStringFromClass(XSDispatchQueue.class) attr:NULL];
}

- (instancetype)initWithName:(NSString *)name {
    return [self initWithName:name attr:NULL];
}

- (instancetype)initWithName:(NSString *)name attr:(dispatch_queue_attr_t _Nullable)attr {
    self = [super init];
    if (self) {
        _queue = dispatch_queue_create(name.UTF8String, attr);
        IsOnSocketQueueOrTargetQueueKey = &IsOnSocketQueueOrTargetQueueKey;
        void *nonNullUnusedPointer = (__bridge void *)self;
        dispatch_queue_set_specific(_queue, IsOnSocketQueueOrTargetQueueKey, nonNullUnusedPointer, NULL);
    }
    return self;
}

- (void)performSynchronously:(BOOL)synchronously task:(dispatch_block_t)task  {
    if (dispatch_get_specific(IsOnSocketQueueOrTargetQueueKey)) {
        task();
        return;
    }
    if (synchronously) {
        dispatch_sync(_queue, task);
    } else {
        dispatch_async(_queue, task);
    }
}

@end
