//
//  DispatchGroup.m
//  CoolVideoSDK
//
//  Created by admin on 2020/4/22.
//  Copyright © 2020 admin. All rights reserved.
//

#import "XSDispatchGroup.h"

NSString *const XSDispatchGroupQueueName = @"XSDispatchGroupQueueName";

@implementation XSDispatchGroup

+ (instancetype)sharedInstance {
    static XSDispatchGroup *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[XSDispatchGroup alloc] initWithName:XSDispatchGroupQueueName];
    });
    return instance;
}

- (instancetype)init {
    return [self initWithName:XSDispatchGroupQueueName];
}

- (instancetype)initWithName:(NSString *)name {
    self = [super init];
    if (self) {
        _queue = [[XSDispatchQueue alloc] initWithName:name attr:DISPATCH_QUEUE_CONCURRENT];
        _group = dispatch_group_create();
    }
    return self;
}

- (void)beginATask {
    dispatch_group_enter(self.group);
}

- (void)endATask {
    dispatch_group_leave(self.group);
}

- (void)perform:(ifv_dispatch_group_tasks)tasks completion:(dispatch_block_t)completion {
    
    [self.queue performSynchronously:NO task:^{

        // 在group正在运行时，传入新的任务后，注册新的完成block。
        // 那么，当group把所有任务执行完后，所有的block都会同时被调用。
        if (self.isRunning) {
            NSLog(@"%s you are performing new tasks to a already running group.", __func__);
        }

        dispatch_block_t block = ^{
            self->_running = NO;

            !completion ? : completion();
        };

        self->_running = YES;

        // 载入传入的任务
        tasks(self);

        // 注册
        dispatch_group_notify(self.group,
                              self.completionQueue ? self.completionQueue : dispatch_get_main_queue(),
                              block);
    }];
}

@end
