//
//  XSDispatchQueue.h
//  CoolVideoSDK
//
//  Created by admin on 2020/4/22.
//  Copyright © 2020 admin. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN


/// This class is a wrapper around a dispatch queue that provides a more secure API for dispatching tasks.
@interface XSDispatchQueue : NSObject
{
@private
    /// a private pointer for tagging the queue.
    void *IsOnSocketQueueOrTargetQueueKey;
}

@property (nonatomic, strong) dispatch_queue_t queue;

/// use the shared instance
+ (instancetype)sharedInstance;

/// init a new instance with a specific queue name.
- (instancetype)initWithName:(NSString *)name;

- (instancetype)initWithName:(NSString *)name attr:(dispatch_queue_attr_t _Nullable)attr;

/// dispatch tasks
- (void)performSynchronously:(BOOL)synchronously task:(dispatch_block_t)task;


@end

NS_ASSUME_NONNULL_END
