//
//  DispatchGroup.h
//  CoolVideoSDK
//
//  Created by admin on 2020/4/22.
//  Copyright © 2020 admin. All rights reserved.
//

#import <Foundation/Foundation.h>

#import "XSDispatchQueue.h"

NS_ASSUME_NONNULL_BEGIN

@class XSDispatchQueue, XSDispatchGroup;

typedef void(^ifv_dispatch_group_tasks)(XSDispatchGroup *group);

/// this class is a wrapper of a dispatch group object.
@interface XSDispatchGroup : NSObject

@property (nonatomic, strong) XSDispatchQueue *queue;

/// you can specific the completion queue. default is dispatch-get-main-queue.
@property (nonatomic, strong, nullable) dispatch_queue_t completionQueue;

@property (nonatomic, strong) dispatch_group_t group;

@property (nonatomic, assign, readonly, getter=isRunning) BOOL running;

/// use the shared instance. the same queue and the same group.
+ (instancetype)sharedInstance;

/// init a new group and a new queue with specific name.
- (instancetype)initWithName:(NSString *)name;

/// a task is put into the group.
/// calls to this function must be balanced with `endATask`
- (void)beginATask;

/// a task is completed.
/// calls to this function must be balanced with `beginATask`
- (void)endATask;

- (void)perform:(ifv_dispatch_group_tasks)tasks completion:(nullable dispatch_block_t)completion;

@end

NS_ASSUME_NONNULL_END
