//
// Copyright 2012 Square Inc.
// Portions Copyright (c) iflytek, Inc.
//
// All rights reserved.
//
// This source code is licensed under the BSD-style license found in the
// LICENSE file in the root directory of this source tree. An additional grant
// of patent rights can be found in the PATENTS file in the same directory.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface NSRunLoop (IFVWebSocket)

/**
 Default run loop that will be used to schedule all instances of `IFVWebSocket`.

 @return An instance of `NSRunLoop`.
 */
+ (NSRunLoop *)IFV_networkRunLoop;

@end

NS_ASSUME_NONNULL_END
