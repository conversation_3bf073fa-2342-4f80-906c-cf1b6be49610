//
// Copyright (c) iflytek, Inc.
// All rights reserved.
//
// This source code is licensed under the BSD-style license found in the
// LICENSE file in the root directory of this source tree. An additional grant
// of patent rights can be found in the PATENTS file in the same directory.
//

#import "IFVError.h"

#import "IFVWebSocket.h"

NS_ASSUME_NONNULL_BEGIN

NSError *IFVErrorWithDomainCodeDescription(NSString *domain, NSInteger code, NSString *description)
{
    return [NSError errorWithDomain:domain code:code userInfo:@{ NSLocalizedDescriptionKey: description }];
}

NSError *IFVErrorWithCodeDescription(NSInteger code, NSString *description)
{
    return IFVErrorWithDomainCodeDescription(IFVWebSocketErrorDomain, code, description);
}

NSError *IFVErrorWithCodeDescriptionUnderlyingError(NSInteger code, NSString *description, NSError *underlyingError)
{
    return [NSError errorWithDomain:IFVWebSocketErrorDomain
                               code:code
                           userInfo:@{ NSLocalizedDescriptionKey: description,
                                       NSUnderlyingErrorKey: underlyingError }];
}

NSError *IFVHTTPErrorWithCodeDescription(NSInteger httpCode, NSInteger errorCode, NSString *description)
{
    return [NSError errorWithDomain:IFVWebSocketErrorDomain
                               code:errorCode
                           userInfo:@{ NSLocalizedDescriptionKey: description,
                                       IFVHTTPResponseErrorKey: @(httpCode) }];
}

NS_ASSUME_NONNULL_END
