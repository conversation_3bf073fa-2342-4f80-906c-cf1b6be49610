//
// Copyright (c) iflytek, Inc.
// All rights reserved.
//
// This source code is licensed under the BSD-style license found in the
// LICENSE file in the root directory of this source tree. An additional grant
// of patent rights can be found in the PATENTS file in the same directory.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

typedef __attribute__((capability("mutex"))) pthread_mutex_t *IFVMutex;

extern IFVMutex IFVMutexInitRecursive(void);
extern void IFVMutexDestroy(IFVMutex mutex);

extern void IFVMutexLock(IFVMutex mutex) __attribute__((acquire_capability(mutex)));
extern void IFVMutexUnlock(IFVMutex mutex) __attribute__((release_capability(mutex)));

NS_ASSUME_NONNULL_END
