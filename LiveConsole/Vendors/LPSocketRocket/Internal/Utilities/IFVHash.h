//
// Copyright (c) iflytek, Inc.
// All rights reserved.
//
// This source code is licensed under the BSD-style license found in the
// LICENSE file in the root directory of this source tree. An additional grant
// of patent rights can be found in the PATENTS file in the same directory.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

extern NSData *IFVSHA1HashFromString(NSString *string);
extern NSData *IFVSHA1HashFromBytes(const char *bytes, size_t length);

extern NSString *IFVBase64EncodedStringFromData(NSData *data);

NS_ASSUME_NONNULL_END
