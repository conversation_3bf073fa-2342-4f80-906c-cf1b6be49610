//
// Copyright (c) iflytek, Inc.
// All rights reserved.
//
// This source code is licensed under the BSD-style license found in the
// LICENSE file in the root directory of this source tree. An additional grant
// of patent rights can be found in the PATENTS file in the same directory.
//

#import <Foundation/Foundation.h>

#import "IFVWebSocket.h"

NS_ASSUME_NONNULL_BEGIN

#if OBJC_BOOL_IS_BOOL

struct IFVDelegateAvailableMethods {
    BOOL didReceiveMessage : 1;
    BOOL didReceiveMessageWithString : 1;
    <PERSON>O<PERSON> didReceiveMessageWithData : 1;
    BOOL didOpen : 1;
    <PERSON><PERSON><PERSON> didFailWithError : 1;
    BOOL didCloseWithCode : 1;
    BOOL didReceivePing : 1;
    BOOL didReceivePong : 1;
    BO<PERSON> shouldConvertTextFrameToString : 1;
};

#else

struct IFVDelegateAvailableMethods {
    BOOL didReceiveMessage;
    BOOL didReceiveMessageWithString;
    BOOL didReceiveMessageWithData;
    BOOL didOpen;
    BOOL didFailWithError;
    BOOL didCloseWithCode;
    BOOL didReceivePing;
    BOOL didReceivePong;
    BOOL shouldConvertTextFrameToString;
};

#endif

typedef struct IFVDelegateAvailableMethods IFVDelegateAvailableMethods;

typedef void(^IFVDelegateBlock)(id<IFVWebSocketDelegate> _Nullable delegate, IFVDelegateAvailableMethods availableMethods);

@interface IFVDelegateController : NSObject

@property (nonatomic, weak) id<IFVWebSocketDelegate> delegate;
@property (atomic, readonly) IFVDelegateAvailableMethods availableDelegateMethods;

@property (nullable, nonatomic, strong) dispatch_queue_t dispatchQueue;
@property (nullable, nonatomic, strong) NSOperationQueue *operationQueue;

///--------------------------------------
#pragma mark - Perform
///--------------------------------------

- (void)performDelegateBlock:(IFVDelegateBlock)block;
- (void)performDelegateQueueBlock:(dispatch_block_t)block;

@end

NS_ASSUME_NONNULL_END
