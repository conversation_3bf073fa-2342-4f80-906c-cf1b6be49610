//
// Copyright 2012 Square Inc.
// Portions Copyright (c) iflytek, Inc.
//
// All rights reserved.
//
// This source code is licensed under the BSD-style license found in the
// LICENSE file in the root directory of this source tree. An additional grant
// of patent rights can be found in the PATENTS file in the same directory.
//

#import "NSRunLoop+IFVWebSocket.h"
#import "NSRunLoop+IFVWebSocketPrivate.h"

#import "IFVRunLoopThread.h"

// Required for object file to always be linked.
void import_NSRunLoop_IFVWebSocket() { }

@implementation NSRunLoop (IFVWebSocket)

+ (NSRunLoop *)IFV_networkRunLoop
{
    return [IFVRunLoopThread sharedThread].runLoop;
}

@end
