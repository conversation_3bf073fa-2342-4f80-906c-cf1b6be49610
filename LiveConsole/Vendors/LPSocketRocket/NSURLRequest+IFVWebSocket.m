//
// Copyright 2012 Square Inc.
// Portions Copyright (c) iflytek, Inc.
//
// All rights reserved.
//
// This source code is licensed under the BSD-style license found in the
// LICENSE file in the root directory of this source tree. An additional grant
// of patent rights can be found in the PATENTS file in the same directory.
//

#import "NSURLRequest+IFVWebSocket.h"
#import "NSURLRequest+IFVWebSocketPrivate.h"

// Required for object file to always be linked.
void import_NSURLRequest_IFVWebSocket() { }

NS_ASSUME_NONNULL_BEGIN

static NSString *const IFVSSLPinnnedCertificateLCKey = @"SocketRocket_SSLPinnedCertificates";

@implementation NSURLRequest (IFVWebSocket)

- (nullable NSArray *)IFV_SSLPinnedCertificates
{
    return nil;
}

@end

@implementation NSMutableURLRequest (IFVWebSocket)

- (void)setIFV_SSLPinnedCertificates:(nullable NSArray *)IFV_SSLPinnedCertificates
{
    [NSException raise:NSInvalidArgumentException
                format:@"Using pinned certificates is neither secure nor supported in SocketRocket, "
                        "and leads to security issues. Please use a proper, trust chain validated certificate."];
}

@end

NS_ASSUME_NONNULL_END
