//
// Copyright 2012 Square Inc.
// Portions Copyright (c) iflytek, Inc.
//
// All rights reserved.
//
// This source code is licensed under the BSD-style license found in the
// LICENSE file in the root directory of this source tree. An additional grant
// of patent rights can be found in the PATENTS file in the same directory.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface NSURLRequest (IFVWebSocket)

/**
 An array of pinned `SecCertificateRef` SSL certificates that `IFVWebSocket` will use for validation.
 */
@property (nullable, nonatomic, copy, readonly) NSArray *IFV_SSLPinnedCertificates
    DEPRECATED_MSG_ATTRIBUTE("Using pinned certificates is neither secure nor supported in SocketRocket, "
                             "and leads to security issues. Please use a proper, trust chain validated certificate.");

@end

@interface NSMutableURLRequest (IFVWebSocket)

/**
 An array of pinned `SecCertificateRef` SSL certificates that `IFVWebSocket` will use for validation.
 */
@property (nullable, nonatomic, copy) NSArray *IFV_SSLPinnedCertificates
    DEPRECATED_MSG_ATTRIBUTE("Using pinned certificates is neither secure nor supported in SocketRocket, "
                             "and leads to security issues. Please use a proper, trust chain validated certificate.");

@end

NS_ASSUME_NONNULL_END
