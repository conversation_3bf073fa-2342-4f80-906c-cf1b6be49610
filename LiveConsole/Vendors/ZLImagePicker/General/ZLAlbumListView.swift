//
//  ZLAlbumListController.swift
//  ZLPhotoBrowser
//
//  Created by long on 2020/8/18.
//
//  Copyright (c) 2020 Long Zhang <<EMAIL>>
//
//  Permission is hereby granted, free of charge, to any person obtaining a copy
//  of this software and associated documentation files (the "Software"), to deal
//  in the Software without restriction, including without limitation the rights
//  to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
//  copies of the Software, and to permit persons to whom the Software is
//  furnished to do so, subject to the following conditions:
//
//  The above copyright notice and this permission notice shall be included in
//  all copies or substantial portions of the Software.
//
//  THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
//  IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
//  FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
//  AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
//  LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
//  OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
//  THE SOFTWARE.

import UIKit
import Photos

protocol ZLAlbumListViewProtocol: NSObjectProtocol {
    // 选中了一个相册
    func didSelectedAlbum(album:ZLAlbumListModel)
    
}

class ZLAlbumListView: UIView {

    weak var delegate: ZLAlbumListViewProtocol?
    
    private lazy var collectionView: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        layout.minimumLineSpacing = 9
        layout.minimumInteritemSpacing = 9
        layout.scrollDirection = .vertical
        layout.itemSize = CGSize(width: (LCDevice.screenW - 42) / 3.0, height: 148)
        let collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        collectionView.backgroundColor = UIColor.clear
        collectionView.register(cellWithClass: ZLAlbumCell.self)
        collectionView.delegate = self
        collectionView.dataSource = self
        collectionView.showsVerticalScrollIndicator = true
        collectionView.contentInset = UIEdgeInsets(top: 10, left: 10, bottom: 10, right: 10)
        collectionView.cornerRadius = 4
        return collectionView
    }()
    
    var arrDataSource: [ZLAlbumListModel] = []
    
    var shouldReloadAlbumList = true
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        self.backgroundColor = UIColor.black //UIColor("#292931")
        self.addSubview(self.collectionView)
        collectionView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.bottom.equalToSuperview().offset(-184)
        }
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    public func show(allowSelectImage: Bool, allowSelectVideo: Bool, parentId: Int) {
        LCDevice.isCanVisitPhotoLibrary {(isOk) in
            guard isOk else {
                HUD.showFail("无法访问系统相册，前往设置打开相册权限")
                return
            }
            self.setupUI(allowSelectImage: allowSelectImage, allowSelectVideo: allowSelectVideo, parentId: parentId)
            PHPhotoLibrary.shared().register(self)
        }
    }
    
    func setupUI(allowSelectImage: Bool, allowSelectVideo: Bool, parentId: Int) {
      
        guard self.shouldReloadAlbumList else {
            // 只要刷新数据源就ok
            var tmps: [ZLAlbumListModel] = self.buildCollectAlbum(list: self.arrDataSource, allowSelectImage: allowSelectImage, allowSelectVideo: allowSelectVideo, parentId: parentId)
            self.arrDataSource.removeAll()
            self.arrDataSource.append(contentsOf: tmps)
            self.collectionView.reloadData()
            return
        }
        
        DispatchQueue.global().async {
            ZLPhotoManager.getPhotoAlbumList(ascending: ZLPhotoConfiguration.default().sortAscending, allowSelectImage: allowSelectImage, allowSelectVideo: allowSelectVideo) { [weak self] (albumList) in
                guard let self = self else { return }
                self.arrDataSource.removeAll()
                self.arrDataSource.append(contentsOf: self.buildCollectAlbum(list: albumList,  allowSelectImage: allowSelectImage, allowSelectVideo: allowSelectVideo, parentId: parentId) )
                
                self.shouldReloadAlbumList = false
                DispatchQueue.main.async {
                    self.collectionView.reloadData()
                }
            }
        }
    }

    // 构建收藏的一个组的数据
    func buildCollectAlbum(list: [ZLAlbumListModel], allowSelectImage: Bool, allowSelectVideo: Bool,  parentId: Int) -> [ZLAlbumListModel] {
        
        var tmp: [ZLAlbumListModel] = []
        var models: [ZLPhotoModel] = []
        // 给相册对象添加资源数据
//        list.forEach { album in
//            if !album.custom {
//                tmp.append(album)
//                album.refetchAllPhotos(allowSelectImage: allowSelectImage, allowSelectVideo: allowSelectVideo)
//                album.models.forEach { photoModel in
//                    if LPMaterialHelper.isCollectAlbum(localIdentifier: photoModel.asset.localIdentifier, categoryParentId: parentId), !models.contains(where: {$0 == photoModel}) {
//                        models.append(photoModel)
//                    }
//                }
//            }
//        }
      
        let collection = ZLAlbumListModel(title: "快瓴中控台收藏", result: PHAsset.fetchAssets(with: PHFetchOptions()), collection: PHAssetCollection(), option: PHFetchOptions(), isCameraRoll: false, custom: true)
        collection.models.removeAll()
        collection.models.append(contentsOf: models)
        
        if tmp.count >= 1 {
            tmp.insert(collection, at: 1)
        } else {
            tmp.append(collection)
        }
        
        return tmp
    }
    
}

extension ZLAlbumListView: UICollectionViewDelegate, UICollectionViewDataSource {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return arrDataSource.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let item = collectionView.dequeueReusableCell(withClass: ZLAlbumCell.self, for: indexPath)

        item.configureCell(model: self.arrDataSource[indexPath.row], style: .externalAlbumList)
        return item
    }
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        if let delegate = delegate {
            if let source = self.arrDataSource[safe: indexPath.row] {
                delegate.didSelectedAlbum(album: source)
                self.isHidden = true
            }
        }
    }
}

extension ZLAlbumListView: PHPhotoLibraryChangeObserver {
    
    func photoLibraryDidChange(_ changeInstance: PHChange) {
        self.shouldReloadAlbumList = true
    }
    
}
