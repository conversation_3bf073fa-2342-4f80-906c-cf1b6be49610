//
//  ZLAlbumListCell.swift
//  ZLPhotoBrowser
//
//  Created by long on 2020/8/19.
//
//  Copyright (c) 2020 Long Zhang <<EMAIL>>
//
//  Permission is hereby granted, free of charge, to any person obtaining a copy
//  of this software and associated documentation files (the "Software"), to deal
//  in the Software without restriction, including without limitation the rights
//  to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
//  copies of the Software, and to permit persons to whom the Software is
//  furnished to do so, subject to the following conditions:
//
//  The above copyright notice and this permission notice shall be included in
//  all copies or substantial portions of the Software.
//
//  THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
//  IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
//  FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
//  AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
//  LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
//  OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
//  THE SOFTWARE.

import UIKit

class ZLAlbumCell: UICollectionViewCell {

    var coverImageView: UIImageView!
    
    var titleLabel: UILabel!
    
    var countLabel: UILabel!
    
//    var selectBtn: UIButton!
    
    var imageIdentifier: String?
    
    var model: ZLAlbumListModel!
    
    var style: ZLPhotoBrowserStyle = .embedAlbumList
    
    override func awakeFromNib() {
        super.awakeFromNib()
        // Initialization code
    }
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        self.setupUI()
    }
    

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func setupUI() {
        self.contentView.backgroundColor = UIColor("#292931")
        self.contentView.layer.masksToBounds = true
        self.contentView.layer.cornerRadius = 8
        
        self.coverImageView = UIImageView()
        self.coverImageView.contentMode = .scaleAspectFill
        self.coverImageView.clipsToBounds = true
        self.coverImageView.layer.masksToBounds = true
        self.coverImageView.layer.cornerRadius = 8
        self.contentView.addSubview(self.coverImageView)
        
        self.coverImageView.snp.makeConstraints { make in
            make.left.right.top.equalToSuperview()
            make.height.equalTo(108)
        }
        
        self.titleLabel = UILabel()
        self.titleLabel.font = getFont(12)
        self.titleLabel.textColor = .white
        self.titleLabel.lineBreakMode = .byTruncatingTail
        self.contentView.addSubview(self.titleLabel)
        
        self.titleLabel.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(8)
            make.height.equalTo(18)
            make.bottom.equalToSuperview().offset(-22)
        }
        
        self.countLabel = UILabel()
        self.countLabel.font = getFont(12)
        self.countLabel.textColor = .white
        self.contentView.addSubview(self.countLabel)
        
        self.countLabel.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(8)
            make.height.equalTo(15)
            make.bottom.equalToSuperview().offset(-4)
        }
        
//        self.selectBtn = UIButton(type: .custom)
//        self.selectBtn.isUserInteractionEnabled = false
//        self.selectBtn.isHidden = true
//        self.selectBtn.setImage(getImage("zl_albumSelect"), for: .selected)
//        self.contentView.addSubview(self.selectBtn)
//        self.selectBtn.snp.makeConstraints { make in
//            make.right.equalToSuperview().offset(8)
//            make.top.equalToSuperview().offset(8)
//            make.height.width.equalTo(20)
//        }
    }
    
    func configureCell(model: ZLAlbumListModel, style: ZLPhotoBrowserStyle) {
        self.model = model
        self.style = style
        self.titleLabel.text = self.model.title
        if model.custom {
            self.countLabel.text = "(" + String(self.model.models.count) + ")"
            self.coverImageView.image = UIImage(named: "相册收藏")
        } else {
            self.countLabel.text = "(" + String(self.model.count) + ")"
            self.imageIdentifier = self.model.headImageAsset?.localIdentifier
            if let asset = self.model.headImageAsset {
                let w = self.bounds.height * 2.5
                ZLPhotoManager.fetchImage(for: asset, size: CGSize(width: w, height: w)) { [weak self] (image, _) in
                    if self?.imageIdentifier == self?.model.headImageAsset?.localIdentifier {
                        self?.coverImageView.image = image ?? getImage("zl_defaultphoto")
                    }
                }
            }
        }
    }

}
