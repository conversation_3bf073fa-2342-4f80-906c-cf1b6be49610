"previewCamera" = "Camera";
"previewCameraRecord" = "Record";
"previewAlbum" = "Album";
"cancel" = "Cancel";

"originalPhoto" = "Full Image";
"done" = "Done";
"ok" = "OK";
"editFinish" = "Done";

"back" = "Back";
"edit" = "Edit";
"revert" = "Undo";

"photo" = "Photos";
"preview" = "Preview";

"noPhotoTips" = "No Photo";
"notAllowMixSelect" = "Unable to select video";

"loading" = "loading, waiting please";
"hudLoading" = "waiting...";

"exceededMaxSelectCount" = "Max count for selection: %ld";
"longerThanMaxVideoDuration" = "Unable to select video with a duration longer than %lds";
"shorterThanMaxVideoDuration" = "Unable to select video with a duration shorter than %lds";
"exceededMaxVideoSelectCount" = "Max count for video selection: %ld";
"lessThanMinVideoSelectCount" = "Min count for video selection: %ld";

"noCameraAuthority" = "Please allow %@ to access your device's camera in \"Settings\"->\"Privacy\"->\"Camera\"";
"noPhotoLibratyAuthority" = "Please allow %@ to access your album in \"Settings\"->\"Privacy\"->\"Photos\"";
"noMicrophoneAuthority" = "Please allow %@ to access your device's microphone in \"Settings\"->\"Privacy\"->\"Microphone\"";
"cameraUnavailable" = "Camera is unavailable";

"iCloudVideoLoadFaild" = "Unable to sync from iCloud";
"imageLoadFailed" = "loading failed";

"save" = "Save";
"saveImageError" = "Failed to save the image";
"saveVideoError" = "Failed to save the video";
"timeout" = "Request timed out";

"customCameraTips" = "Tap to take photo and hold to record video";
"customCameraTakePhotoTips" = "Tap to take photo";
"customCameraRecordVideoTips" = "Hold to record video";
"minRecordTimeTips" = "Record at least %lds";

"cameraRoll" = "Recents";
"panoramas" = "Panoramas";
"videos" = "Videos";
"favorites" = "Favorites";
"timelapses" = "Time-Lapse";
"recentlyAdded" = "Recently Added";
"bursts" = "Bursts";
"slomoVideos" = "Slo-mo";
"selfPortraits" = "Selfies";
"screenshots" = "Screenshots";
"depthEffect" = "Portrait";
"livePhotos" = "Live Photos";
"animated" = "Animated";
"myPhotoStream" = "My Photo Stream";

"noTitleAlbumListPlaceholder" = "All Photos";
"unableToAccessAllPhotos" = "Unable to access all photos, go to settings";
"textStickerRemoveTips" = "Drag here to remove";
