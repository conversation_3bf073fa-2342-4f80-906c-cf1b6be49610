//
//  CYCropCornerView.h
//  CYImageCrop
//
//  Created by <PERSON> on 16/6/9.
//  Copyright © 2016年 <PERSON>. All rights reserved.
//

#import <UIKit/UIKit.h>

typedef NS_ENUM(NSInteger, CYCropCornerPosition) {
    CYCropCornerPositionLeftTop,
    CYCropCornerPositionRightTop,
    CYCropCornerPositionLeftBottom,
    CYCropCornerPositionRightBottom,
    
    CYCropCornerPositionLeftCenter,
    CYCropCornerPositionTopCenter,
    CYCropCornerPositionRightCenter,
    CYCropCornerPositionBottomCenter,
};

@interface CYCropCornerView : UIView

- (instancetype)initWithPosition:(CYCropCornerPosition)position;

@end
