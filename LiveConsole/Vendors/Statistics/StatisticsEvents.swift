//
//  StatisticsEvents.swift
//  LivePlus
//
//  Created by 郭炜 on 2021/3/25.
//

import Foundation

let statisticBaseName = "FT"

/*
 1、功能模块/功能编号 见EventModuleType
 2、操作定义/子编号   见EventIDXXX
 3、自定义标签以及标签值
 */
enum EventModuleType {
    /// 启动APP
    case ENTER_APP(EventIDEnterAppType)
    /// 协议提示
    case PROTOCOL_REMAIND(EventIDProtocolRemaindType)
    /// 权限请求
    case PREMISSION_REQUEST(EventIDPremissionType)
    /// 登录
    case LOGIN_ACTION(EventIDLoginType)
    /// 我的直播间/首页
    case VIDEO_PRE_PAGE(EventIDVideoPreType)
    /// 直播间
    case VIDEO_MAIN_PAGE(EventIDVideoRoomType)
    /// 推流
    case VIDEO_PUSH_STREAM(EventIDPushStreamType)
    /// 直播中
    case VIDEO_LIVING(EventIDLivingType)
    /// 我的
    case MINE(EventIDMineType)
    
    func curretRawValue() -> String {
        switch self {
        case .ENTER_APP: return "01"
        case .PROTOCOL_REMAIND: return "02"
        case .PREMISSION_REQUEST: return "03"
        case .LOGIN_ACTION: return "04"
        case .VIDEO_PRE_PAGE: return "05"
        case .VIDEO_MAIN_PAGE: return "06"
        case .VIDEO_PUSH_STREAM: return "07"
        case .VIDEO_LIVING: return "08"
        case .MINE: return "09"
        }
    }
}

// MARK: - 通用的自定义标签及标签值
enum EventIDCommonAction: String {
    case zero   =     "0"
    case one    =     "1"
    case two    =     "2"
    case three  =     "3"
    case four   =     "4"
    case five   =     "5"
    case six   =     "6"
}

// MARK: - 启动APP
enum EventIDEnterAppType {
    /// 用户打开APP来源
    case enterApp
    
    // MARK: - 不单独处理每个“自定义标签以及标签值了，无意义 直接使用EventIDCommonAction”
//    enum DetailType_enterApp: String {
//        /// 桌面图标点击
//        case clickDeskTop    = "0"
//        /// 常驻消息推送点击（暂时不统计）
//        case clickPush       = "1"
//    }
    
    func currentRawValue() -> String {
        switch self {
        case .enterApp: return "001"
        }
    }
}

// MARK: - 协议提示
enum EventIDProtocolRemaindType {
    /// 出协议提示弹窗后，用户点击行为
    case clickProtocol
    /// 查看《用户使用协议》
    case seeProtocol
    /// 查看《隐私政策》
    case seePrivacy

    func currentRawValue() -> String {
        switch self {
        case .clickProtocol: return "001"
        case .seeProtocol: return "002"
        case .seePrivacy: return "003"
        }
    }
    // MARK: - 不单独处理每个“自定义标签以及标签值了，无意义 直接使用EventIDCommonAction”
//    /// 用户点击弹窗里的选项
//    enum DetailType_clickProtocol: String {
//        /// 0、不同意
//        case unAgreen = "0"
//        /// 1、同意并继续
//        case agreen = "1"
//    }
//
//    /// 用户点击弹窗内的《用户使用协议》超链接
//    enum DetailType_seeProtocol {} // 打点文档无内容
//
//    /// 用户点击弹窗内的《隐私政策》超链接
//    enum DetailType_seePrivacy {} // 打点文档无内容
    
}

// MARK: - 权限请求
enum EventIDPremissionType {
    /// 请求麦克风权限
    case micPremiss
    /// 请求摄像头权限
    case cameraPremiss
    /// 请求相册权限
    case albumPremiss
    /// 请求网络权限
    case networkPremiss
    
    func currentRawValue() -> String {
        switch self {
        case .micPremiss: return "001"
        case .cameraPremiss: return "002"
        case .albumPremiss: return "003"
        case .networkPremiss: return "004"
        }
    }
}

// MARK: - 登录
enum EventIDLoginType {
    /// 点击验证码
    case clickVerify
    /// 点击登录按钮
    case clickLogin
    /// 点击微信icon
    case clickWechat
    /// 微信授权
    case wechatAuth
    /// 登录成功
    case loginSuccess
    
    func currentRawValue() -> String {
        switch self {
        case .clickVerify: return "001"
        case .clickLogin: return "002"
        case .clickWechat: return "003"
        case .wechatAuth: return "004"
        case .loginSuccess: return "005"
        }
    }
}

// MARK: - 我的直播间/首页
enum EventIDVideoPreType {
    /// 界面展示
    case jmzs
    /// 直播间多选
    case zbjdx
    /// 删除确认
    case scqr
    /// 重命名
    case cmm
    /// 新手指导
    case xszd
    
    func currentRawValue() -> String {
        switch self {
        case .jmzs: return "001"
        case .zbjdx: return "002"
        case .scqr: return "003"
        case .cmm: return "004"
        case .xszd: return "005"
        }
    }
}

// MARK: - 直播间
enum EventIDVideoRoomType {
    /// 创建直播间
    case cjzbj
    /// 镜头翻转
    case jtfz
    /// 点击画面
    case djhm
    /// 选择画面形状
    case xzhmxz
    /// 修改画面形状
    case xghmxz
    /// 点击美化
    case djmh
    /// 预览
    case yl
    /// 虚拟背景
    case xnbj
    /// 虚拟背景-自定义
    case xnbj_zdy
    /// 选择自定义背景
    case xzzdybj
    /// 虚拟背景--选择系统背景
    case xnbj_xzxtbj
    /// 虚拟背景--绿幕
    case xnbj_lm
    /// 虚拟背景--绿幕编辑
    case xnbj_lmbj
    /// 虚拟背景-高级编辑-背景颜色
    case xnbj_gjbj_bjys
    /// 虚拟背景-高级编辑-细节调整
    case xnbj_gjbj_xjtz
    /// 人像形状--自定义
    case rxxz_zdy
    /// 预置贴纸-自定义
    case yztz_zdy
    /// 预置贴纸-系统分类
    case yztz_xtfl
    /// 预置贴纸-放大缩小等
    case yztz_fdsx
    /// 预置贴纸-修改文字
    case yztz_xgwz
    /// 实时道具-商品素材
    case ssdj_spsc
    /// 实时道具-选中商品素材
    case ssdj_xzspsc
    /// 实时道具-商品素材编辑
    case ssdj_spscbj
    /// 实时道具-动态道具
    case ssdj_dtdj
    
    func currentRawValue() -> String {
        switch self {
        case .cjzbj: return "001"
        case .jtfz: return "002"
        case .djhm: return "003"
        case .xzhmxz: return "004"
        case .xghmxz: return "005"
        case .djmh: return "006"
        case .yl: return "007"
        case .xnbj: return "008"
        case .xnbj_zdy: return "009"
        case .xzzdybj: return "010"
        case .xnbj_xzxtbj: return "011"
        case .xnbj_lm: return "012"
        case .xnbj_lmbj: return "013"
        case .xnbj_gjbj_bjys: return "014"
        case .xnbj_gjbj_xjtz: return "015"
        case .rxxz_zdy: return "016"
        case .yztz_zdy: return "017"
        case .yztz_xtfl: return "018"
        case .yztz_fdsx: return "019"
        case .yztz_xgwz: return "020"
        case .ssdj_spsc: return "021"
        case .ssdj_xzspsc: return "022"
        case .ssdj_spscbj: return "023"
        case .ssdj_dtdj: return "024"
        }
    }
}

// MARK: - 推流
enum EventIDPushStreamType {
    /// 点击教程视频
    case djjcsp
    /// 点击暂存
    case djzc
    /// 点击开始直播
    case djkszb
    /// 分辨率
    case fbl
    
    func currentRawValue() -> String {
        switch self {
        case .djjcsp: return "001"
        case .djzc: return "002"
        case .djkszb: return "003"
        case .fbl: return "004"
        }
    }
}

// MARK: - 直播中
enum EventIDLivingType {
    /// 点击结束直播
    case djjszb
    /// 直播间保存
    case zbjbc
    
    func currentRawValue() -> String {
        switch self {
        case .djjszb: return "001"
        case .zbjbc: return "002"
        }
    }
}

// MARK: - 我的
enum EventIDMineType {
    /// 反馈建议
    case fkjy
    /// 设置
    case sz
    /// 绑定手机号
    case bdsjh
    /// 绑定微信
    case bdwx
    /// 版本更新
    case bbgx
    /// 退出登录
    case tcdl
    
    func currentRawValue() -> String {
        switch self {
        case .fkjy: return "001"
        case .sz: return "002"
        case .bdsjh: return "003"
        case .bdwx: return "004"
        case .bbgx: return "005"
        case .tcdl: return "006"
        }
    }
}
