//
//  LPStatistics.swift
//  LivePlus
//
//  Created by 郭炜 on 2021/3/25.
//

import Foundation

struct LPStatistics {
    
    static func logEvent(_ moduleType: EventModuleType, eventAction: EventIDCommonAction?) {
        
        var eventString =
                statisticBaseName +                 // "FT"
                moduleType.curretRawValue() +       // "FT01"
                getCurrentEventMsg(moduleType)      // "FT01001"
//            +
//                "_" +                               // "FT01001_"
//                eventAction.rawValue                // "FT01001_1"
        
        if let eAction = eventAction {
//            eventString = eventString + "_" + eAction.rawValue
            // 调用友盟打点
            MobClick.event(eventString,label: eAction.rawValue)
            print("打点数据为：\(eventString) ---- \(eAction.rawValue)")
        }else{
            // 调用友盟打点
            MobClick.event(eventString)
            print("打点数据为：\(eventString)")
        }
//        print("打点数据为：\(eventString)")
//
//        // 调用友盟打点
//        MobClick.event(eventString)
    }
    
    
    
    
}

extension LPStatistics {
    static func getCurrentEventMsg(_ moduleType: EventModuleType) -> String {
        switch moduleType {
        case .ENTER_APP(let enterType):
            return enterType.currentRawValue() // "FT01001"
        case .LOGIN_ACTION(let loginType):
            return loginType.currentRawValue()
        case .PROTOCOL_REMAIND(let proType):
            return proType.currentRawValue()
        case .PREMISSION_REQUEST(let preType):
            return preType.currentRawValue()
        case .VIDEO_PRE_PAGE(let preType):
            return preType.currentRawValue()
        case .VIDEO_MAIN_PAGE(let mainType):
            return mainType.currentRawValue()
        case .VIDEO_PUSH_STREAM(let pushType):
            return pushType.currentRawValue()
        case .VIDEO_LIVING(let linvingType):
            return linvingType.currentRawValue()
        case .MINE(let mineType):
            return mineType.currentRawValue()
        }
    }
}
