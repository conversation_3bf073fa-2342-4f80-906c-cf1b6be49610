//
//  BaseTabBarViewController.swift
//  MockBuyin
//
//  Created by simon on 4.12.24.
//

import Foundation
import QMUIKit

class BaseTabBarViewController: UITabBarController {
    
    // 商品讲解
    lazy var jiangjieController:BaseNavigationController = {
        let v = BaseNavigationController(rootViewController: BuyinVC())
        return v
    }()
    
    // 智能互动
    lazy var hudongController:BaseNavigationController = {
        let v = BaseNavigationController(rootViewController: SmartActionVC())
        return v
    }()
    
    
    lazy var myViewController:BaseNavigationController = {
        let v = BaseNavigationController(rootViewController: MineVC())
        return v
    }()
    
    //用户协议 弹窗
    lazy var userPolicyAlert: PolicyAlert = {
        return PolicyAlert(frame: UIScreen.main.bounds)
    }()
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupTabBar()
        
        // 把“用户协议”弹窗加上
        let agreePolicyDone = UserDefaults.standard.bool(forKey: LCKey.UD_PolicyAlertDone)
        if agreePolicyDone {
            // 已同意，不再显示
            MiddleRequestNet.getVersionApiInfo()
            MiddleRequestNet.getConfigApiData()
            MiddleRequestNet.requestLimit()
        } else {
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                // 还未同意，需要显示
//                self?.userPolicyAlert.show()
                self.hudongController.visibleViewController?.view.addSubview(self.userPolicyAlert)
            }
        }
    }
    
    
    private func setupTabBar() {
        // 创建视图控制器
        self.delegate = self
        // 设置标签栏项
        jiangjieController.tabBarItem = createTabBarItem(title: "商品讲解", normalImageName: "icon_tab_首页_nor", selectedImageName: "icon_tab_首页_sel")  // 商品标签
        
        // 设置标签栏项
        hudongController.tabBarItem = createTabBarItem(title: "智能互动", normalImageName: "icon_tab_互动_nor", selectedImageName: "icon_tab_互动_sel")  // 商品标签
        
        myViewController.tabBarItem = createTabBarItem(title: "我的", normalImageName: "tab_mine_nor", selectedImageName: "tab_mine_sel")  // 后台标签
        
        // 设置视图控制器数组
        self.viewControllers = [hudongController, jiangjieController, myViewController]
        
        // 自定义标签栏样式
        self.tabBar.tintColor = UIColor.white
        self.tabBar.unselectedItemTintColor = UIColor("#797B7D")
        let img = UIImage.qmui_image(with: UIColor("#282828"), size: CGSize(width: 1, height: 1), cornerRadius: 0)
        
        self.tabBar.shadowImage = img
        
        
        self.hidesBottomBarWhenPushed = true
        self.tabBar.backgroundColor = UIColor("#111111")
        self.tabBar.backgroundImage = UIImage(color: UIColor("#111111"))
    }
    
    func createTabBarItem(title: String, normalImageName: String, selectedImageName: String) -> UITabBarItem {
        let imageNormal = UIImage(named: normalImageName)?.withRenderingMode(.alwaysOriginal)
        let imageSelected = UIImage(named: selectedImageName)?.withRenderingMode(.alwaysOriginal)
        
        let tabBarItem = UITabBarItem(title: title, image: imageNormal, selectedImage: imageSelected)
        
        tabBarItem.setTitleTextAttributes([NSAttributedString.Key.foregroundColor: UIColor.white.withAlphaComponent(0.6),
                                            NSAttributedString.Key.font: UIFont.systemFont(ofSize: 13)], for: .normal)
        
        tabBarItem.setTitleTextAttributes([NSAttributedString.Key.foregroundColor: UIColor.white,
                                            NSAttributedString.Key.font: UIFont.systemFont(ofSize: 13)], for: .selected)
        
        return tabBarItem
    }
}

extension  BaseTabBarViewController: UITabBarControllerDelegate {
    func tabBarController(_ tabBarController: UITabBarController, didSelect viewController: UIViewController) {
//        if viewController == jiangjieController || viewController == hudongController  {
//            self.tabBar.backgroundColor =  UIColor("#111111")
//            self.tabBar.backgroundImage = UIImage(color: UIColor("#111111"))
//        } else {
//            self.tabBar.backgroundColor = .white
//            self.tabBar.backgroundImage = UIImage(color: UIColor.white)
//        }
    }
    
    func tabBarController(_ tabBarController: UITabBarController, shouldSelect viewController: UIViewController) -> Bool {
       let agreePolicyDone = UserDefaults.standard.bool(forKey: LCKey.UD_PolicyAlertDone)
        if agreePolicyDone == false {
            return false
        }
        return true
    }
}
