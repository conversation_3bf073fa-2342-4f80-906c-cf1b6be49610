//
//  BaseNavigationController.swift
//  LiveConsole
//
//  Created by simon on 18.12.24.
//

import Foundation
import UIKit
//import QMUIKit

class BaseNavigationController: UINavigationController {

    override func viewDidLoad() {
        super.viewDidLoad()
        // 自定义导航控制器的外观和行为
        setupNavigationBar()
        self.delegate  = self
        self.view.backgroundColor = UIColor("#111111")
    }
    
    private func setupNavigationBar() {
        // 设置导航栏的背景颜色
        
    }
    
}


extension BaseNavigationController {

    override func pushViewController(_ viewController: UIViewController, animated: Bool) {
    
        if viewControllers.count != 0 {
            viewController.hidesBottomBarWhenPushed = true
            tabBarController?.tabBar.isHidden = true
        }
        super.pushViewController(viewController, animated: animated)
    }
    
    override func popToRootViewController(animated: Bo<PERSON>) -> [UIViewController]? {
        if #available(iOS 14.0, *) {
            for vc in viewControllers {
                vc.hidesBottomBarWhenPushed = false
                tabBarController?.tabBar.isHidden = false
            }
        }
        return super.popToRootViewController(animated: animated)
        
    }
    
}

extension BaseNavigationController: UINavigationControllerDelegate {
    
}
