//
//  SRouteMediator.swift
//  LivePlus
//
//  Created by iclick on 2020/12/23.
//

import RxSwift

struct RouterBag {
    var cls: AnyClass
    var params: [String: Any]?
}

public protocol Routable {
    /**
     类的初始化方法
     - params 传参字典,路由过来参数都是String:String,所以有些取id的页面需要把String的id转Int
     */
    static func initRouteParams(params: [String: Any]?) -> UIViewController
}

class Router {

    static let disposeBag = DisposeBag()
    
    static func handleScheme(_ scheme: String) {
        
        let encoded = scheme.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed)
        if let url = URL.init(string: encoded!) {

            let lastPath = SchemeLastPath.fromURL(url)              // 比如 userpage
            let category = SchemeMiddlePath.fromURL(url)            // 比如 main
            let strPram = Router.getParameterFromUrl(encoded!)        // 比如 ["user_id":"1002"]

            switch lastPath {
                
            case .browser:
                if category == .main {
                    Router.open(RouterBag(cls: WebVC.self, params: strPram))
                    
                } else if category == .system, let outUrl = strPram["url"] {
                    Router.openSafari(outUrl)
                }
            case .market:
                Router.openAppStore()
            default: break
            }

        }
    }
    
    static func openSafari(_ url: String) {
        if let url = URL(string: url) {
            if #available(iOS 10, *) {
                UIApplication.shared.open(url, options: [:], completionHandler: nil)
            } else {
                UIApplication.shared.openURL(url)
            }
        }
    }
    
    /// push模式打开新vc
    static func open(_ path: RouterBag, pushVC: Bool = true, animated: Bool = true) {
        if let cls = path.cls as? Routable.Type {
            let curTopVC = AppDelegate.curDisplayVC()
            if pushVC {
                let targetVC = cls.initRouteParams(params: path.params)
                curTopVC.navigationController?.pushViewController(targetVC, animated: animated)
            }
        }
    }
    
    static func openRecordHelp() {
        
    }
    
    /// 保存用户信息 打开系统浏览器
    static func openUserInfo() {
        guard let userModel = UserInfo.currentUser(), let token = userModel.token else {  return }
        var urlStr =  "https://dev-klzkweb.quickleading.com/userCollectionEmail.html?token="
        if let config = ConfigModel.currentUserConfig(), let  user_send_email = config.staticResource.userCollectionSendEmail {
            urlStr = user_send_email
        }
        
        urlStr = "\(urlStr)\(token)"
        let webCtrl = WebVC.init(title: "个人信息导出", url: urlStr, withProgressLine: true, needStartNewWeb: false)
        let vc = AppDelegate.curDisplayVC()
        vc.navigationController?.pushViewController(webCtrl, animated: true)
    }
    
    /// 打开积分消耗规则
    static func openPointsRulesAgreement() {
        var urlStr =  "https://docs.quickleading.com/docs/klzk/com/pointsRulesAgreement.html"
        if let config = ConfigModel.currentUserConfig(), let  user_send_email = config.staticResource.pointsRulesAgreement {
            urlStr = user_send_email
        }
        
        let webCtrl = WebVC.init(title: "积分消耗规则", url: urlStr, withProgressLine: true, needStartNewWeb: false)
        let vc = AppDelegate.curDisplayVC()
        vc.navigationController?.pushViewController(webCtrl, animated: true)
    }
    
    
    // 首页的直播课兜底地址
    static func openHomeLiving(urlStr: String) {
        let webCtrl = WebVC.init(title: "特约讲师", url: urlStr, withProgressLine: true, needStartNewWeb: true)
        let vc = AppDelegate.curDisplayVC()
        vc.navigationController?.pushViewController(webCtrl, animated: true)
    }
    
   
    
    static func willOpenMemberDetail() {
        if let config = ConfigModel.currentUserConfig(), let urlStr = config.staticResource.vipcenter_detail_h5, !urlStr.isEmpty {
            openMemberDetail(urlStr: urlStr)
            return
        }
    }
    
    static func openMemberDetail(urlStr: String) {
        var urlStr = ""
        if let config = ConfigModel.currentUserConfig() {
            urlStr = config.staticResource.vipcenter_detail_h5 ?? ""
        }
        
        if urlStr.isEmpty {
            return
        }
        let webCtrl = WebVC.init(title: "会员介绍", url: urlStr, withProgressLine: true, needStartNewWeb: true)
        
        var cachePolicy: URLRequest.CachePolicy = .reloadIgnoringLocalCacheData
        if ConfigModel.webCache(key: LCKey.Config_H5_Vip_Equity) {
            cachePolicy = .returnCacheDataElseLoad
        }
        
        if let configModel = ConfigModel.currentUserConfig() {
            let arr = configModel.dynamicResource
            if let ite = arr.first(where: {$0.key == LCKey.Config_H5_Vip_Equity}) {
                webCtrl.cacheKey = LCKey.Config_H5_Vip_Equity
                webCtrl.cacheVersion = ite.action
                webCtrl.cachePolicy = cachePolicy
            }
            
        }
        
        let vc = AppDelegate.curDisplayVC()
        vc.navigationController?.pushViewController(webCtrl, animated: true)
    }
    
    /// 保存用户信息 打开系统浏览器
    static func openPrivacyDown() {
        
        var urlStr =  "https://docs.quickleading.com/docs/klzk/com/privacyAgreement.pdf"
        if let config = ConfigModel.currentUserConfig(), let  privacy_ios_down = config.staticResource.privacyAgreementPdf {
            urlStr = privacy_ios_down
        }
        
        if #available(iOS 10, *) {
            UIApplication.shared.open(URL.init(string: urlStr)!, options: [:], completionHandler: nil)
        } else {
            UIApplication.shared.openURL(URL.init(string: urlStr)!)
        }
    }
        
    /// 跳转appstore主页
    static func openAppStore() {
        guard let url = URL.init(string: LCDevice.AppStoreUrl),
              UIApplication.shared.canOpenURL(url) else {
            DispatchQueue.main.async {
                HUD.showFail("App Store跳转失败")
            }
            return
        }
       
        if #available(iOS 10, *) {
            UIApplication.shared.open(URL.init(string: LCDevice.AppStoreUrl)!, options: [:], completionHandler: nil)
        } else {
            UIApplication.shared.openURL(URL.init(string: LCDevice.AppStoreUrl)!)
        }
    }
    
    /// 跳转appstore
    static func openAppStoreForId(storeId: String) {
        let storeUrl = "itms-apps://itunes.apple.com/app/" + storeId
        guard let url = URL.init(string: storeUrl),
              UIApplication.shared.canOpenURL(url) else {
            DispatchQueue.main.async {
                HUD.showFail("App Store跳转失败")
            }
            return
        }
       
        if #available(iOS 10, *) {
            UIApplication.shared.open(URL.init(string: storeUrl)!, options: [:], completionHandler: nil)
        } else {
            UIApplication.shared.openURL(URL.init(string: storeUrl)!)
        }
    }
    
    /// 跳转到快提词
    static func openKuaiTiCi() {
        func openKuaiTiciAppStore() {
            let kuaiticiID = "1557211448"
            openAppStoreForId(storeId: kuaiticiID)
        }
        
        if let url = URL(string: "fastword5CD12938://"),
           UIApplication.shared.canOpenURL(url) {
            UIApplication.shared.open(url, options: [:], completionHandler: nil)
        } else {
            openKuaiTiciAppStore()
        }
    }
    
    /// 跳转到系统设置
    static func openSystemSetter() {
        guard let url = URL(string: UIApplication.openSettingsURLString) else { return }
        if #available(iOS 10.0, *) {
            UIApplication.shared.open(url, options: [:], completionHandler: nil)
        } else {
            UIApplication.shared.openURL(url)
        }
    }

    /// 跳转appstore评分页
    static func openAppStoreStar() {
        guard let url = URL.init(string: LCDevice.AppstoreStarURL),
              UIApplication.shared.canOpenURL(url) else {
            DispatchQueue.main.async {
                HUD.showFail("App Store跳转失败")
            }
            return
        }
        if #available(iOS 10, *) {
            UIApplication.shared.open(URL.init(string: LCDevice.AppstoreStarURL)!, options: [:], completionHandler: nil)
        } else {
            UIApplication.shared.openURL(URL.init(string: LCDevice.AppstoreStarURL)!)
        }
    }
    
    /// 用户政策
    static func openUserAgreement() {
        var urlStr = "https://minio.optimix.cn/ckzs/agreement/agreement.html"
        if let config = ConfigModel.currentUserConfig() {
            urlStr = config.staticResource.userAgreement
        }
        let webCtrl = WebVC.init(title: "用户服务协议", url: urlStr, withProgressLine: true, needStartNewWeb: false)
        let vc = AppDelegate.curDisplayVC()
        vc.navigationController?.pushViewController(webCtrl, animated: true)
    }
    
    static func openWebView(title: String, urlStr: String, startNewWeb: Bool = false) {
        let webCtrl = WebVC.init(title: title, url: urlStr, withProgressLine: true, needStartNewWeb: startNewWeb)
        let vc = AppDelegate.curDisplayVC()
        vc.navigationController?.pushViewController(webCtrl, animated: true)
    }
    
    /// 快瓴中控台会员协议
    static func openMembershipAgreement() {
        var urlStr = "https://docs.quickleading.com/docs/klzk/com/memberAgreement.html?timestamp=1748585558"
        if let config = ConfigModel.currentUserConfig(), let url = config.staticResource.memberAgreement {
            urlStr = url
        }
        let webCtrl = WebVC.init(title: "会员协议", url: urlStr, withProgressLine: true, needStartNewWeb: false)
        let vc = AppDelegate.curDisplayVC()
        vc.navigationController?.pushViewController(webCtrl, animated: true)
    }
    
    
    /// 隐私协议
    static func openPrivacyAgreement() {
        var urlStr = "https://minio.optimix.cn/ckzs/privacy/privacy_ios.html"
        if let config = ConfigModel.currentUserConfig() {
            urlStr = config.staticResource.privacyAgreement
        }
        let webCtrl = WebVC.init(title: "隐私政策", url: urlStr, withProgressLine: true, needStartNewWeb: false, showDownloadBtn: true)
        let vc = AppDelegate.curDisplayVC()
        vc.navigationController?.pushViewController(webCtrl, animated: true)
    }
    
    ///
    static func openSDKList() {
        var urlStr = "https://minio.optimix.cn/ckzs/sdklist/sdklist_ios.html"
        if let config = ConfigModel.currentUserConfig(), let lsit = config.staticResource.sdkList {
            urlStr = lsit
        }
        
        let webCtrl = WebVC.init(title: "第三方SDK列表", url: urlStr, withProgressLine: true, needStartNewWeb: false, showDownloadBtn: false)
        let vc = AppDelegate.curDisplayVC()
        vc.navigationController?.pushViewController(webCtrl, animated: true)
    }
    
    /// 个人信息收集列表
    static func openInformationList() {
        var urlStr = "https://dev-klzkweb.quickleading.com/userCollection.html?token="
        if let config = ConfigModel.currentUserConfig(), let lsit = config.staticResource.userCollectionList {
            urlStr = lsit
        }
        guard let userModel = UserInfo.currentUser(), let token = userModel.token else {
            return
        }
        urlStr = "\(urlStr)\(token)"
        let webCtrl = WebVC.init(title: "个人信息收集清单", url: urlStr, withProgressLine: true, needStartNewWeb: false, showDownloadBtn: false)
        let vc = AppDelegate.curDisplayVC()
        vc.navigationController?.pushViewController(webCtrl, animated: true)
    }

    

    
   
    
    static func getParameterFromUrl(_ urlString: String) -> [String : String] {
        var res = [String: String]()
        let url = URL.init(string: urlString)!
        if let array = url.query?.split(separator: "&") {
            for par in array {
                let kv = par.split(separator: "=", maxSplits: 1) // 只匹配第一个“=”
                res[String(kv[0])] = String(kv[1])
            }
        }
        return res
    }
    
    static func openPromotion() {
        
        #if DEBUG
        var urlStr = "https://zhibotest.optaim.com/agent/?userid="

        #else
        var urlStr = "https://zhibojiajia.optimix.cn/agent/?userid="

        #endif
                
        if let uid = UserInfo.currentUser()?.userId {
            urlStr = "\(urlStr)\(uid)"
        }
        
        let webCtrl = WebVC.init(title: "推广人申请", url: urlStr, withProgressLine: true, needStartNewWeb: true)
        let vc = AppDelegate.curDisplayVC()
        vc.navigationController?.pushViewController(webCtrl, animated: true)
    }
    
    /// 克隆协议
    static func openAudioCloneAgreement() {
        
        guard let resource = ConfigModel.currentUserConfig()?.dynamicResource.first(where: { $0.key == "key_copyvoice_guifan" }) else { return }
        
        let urlStr = resource.url
        let webCtrl = WebVC.init(title: "《“快瓴中控台“AI功能使用规范》", url: urlStr, withProgressLine: true, needStartNewWeb: false)
        let vc = AppDelegate.curDisplayVC()
        vc.navigationController?.pushViewController(webCtrl, animated: true)
    }
    
    
    
}

enum SchemeMiddlePath: String {

    case main = "main"
    case system = "system"

    static func fromURL(_ url: URL) -> SchemeMiddlePath {
        var middlePath = ""
        if url.pathComponents.count >= 2 {
            middlePath = url.pathComponents[url.pathComponents.count-2]
        }
        if let some = SchemeMiddlePath.init(rawValue: middlePath) {
            return some
        }
        return .main
    }
}


enum SchemeLastPath: String {
    case none = "none"
    
    case browser = "browser"
    case market = "market"
//    case home = "home"
//    case login = "login"
//    case phone_bind = "phone_bind"
//    case feedback = "feedback"
    
    static func fromURL(_ url: URL) -> SchemeLastPath {
        if let some = SchemeLastPath.init(rawValue: url.lastPathComponent) {
            return some
        }
        return .none
    }
}
