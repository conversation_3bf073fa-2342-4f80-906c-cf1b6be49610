//
//  BaseVC.swift
//  LivePlus
//
//  Created by iclick on 2020/11/30.
//

import UIKit
import RxSwift

/// 导航栏 左侧按钮tag
let kNaviLeftBtnTag = 111

/// 导航栏 右侧按钮tag
let kNaviRightBtnTag = 222

/// 导航栏 标题tag
let kNaviTitleLabTag = 10086

@objcMembers class BaseVC: UIViewController {
    
    public let disposeBag = DisposeBag()

    //重写无参数初始化方法，自动调用xib文件
    override init(nibName nibNameOrNil: String?, bundle nibBundleOrNil: Bundle?) {
        super.init(nibName: nibNameOrNil, bundle: nibBundleOrNil)
    }
    required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
    }
    
    // MARK: - 默认不支持旋转，需要的需重写
    override var shouldAutorotate: Bool {
        return false
    }
    
    override var preferredInterfaceOrientationForPresentation: UIInterfaceOrientation {
        return .portrait
    }
    
    override var supportedInterfaceOrientations: UIInterfaceOrientationMask{
        return .portrait
    }
    
    override var preferredStatusBarStyle: UIStatusBarStyle {
        return .lightContent
    }
    
    override var prefersHomeIndicatorAutoHidden: Bool
    {
        return true
    }
    
    // MARK: - View lifeCycle
    override func viewDidLoad() {
        view.backgroundColor = UIColor.white
        fd_prefersNavigationBarHidden = true
        
        edgesForExtendedLayout = []
        automaticallyAdjustsScrollViewInsets = false
        
//        NotificationCenter.default.addObserver(self,selector:#selector(reLoginAction),name:LCKey.noti_reLogin,object:nil)
    }
    
    // MARK: - Public methods
    
    /// 跳转新页面
    func push(to ctrl:UIViewController, animated:Bool = true) {
        self.navigationController?.pushViewController(ctrl, animated: animated)
    }
    
    func persent(to ctrl: UIViewController) {
        self.present(ctrl, animated: false, completion: nil)
    }
    
    /// 不带过渡  返回导航栏背景View
    func getNavViewWithItem(titleStr: String?,
                            leftImageName: String?,
                            rightImageName: String?,
                            hasBotLine: Bool = false,
                            color: UIColor = .white,
                            showClose: Bool = false,
                            rightName: String? = nil) -> UIView {
        let view = getNavBarView(titleStr: titleStr, color: color)
        
        if leftImageName != nil {
            let leftItem = UIButton(frame: CGRect(x: 14, y: 22, width: 33, height: 33))
            leftItem.bottom = LCDevice.Nav_H - 2
            leftItem.addTarget(self, action: #selector(self.leftButtonAction), for: .touchUpInside)
            leftItem.tag = kNaviLeftBtnTag
            leftItem.setImage(UIImage.init(named: leftImageName!), for: .normal)
            leftItem.titleLabel?.font = LCDevice.DIN_Font_PF_M(14)
            view.addSubview(leftItem)
        }
//        private lazy var closeButton: UIButton = {
//            let button = UIButton()
//            button.setImage(UIImage(named: "横竖屏_icon_close"), for: .normal)
//            return button
//        }()
        if showClose {
            let closeButton = UIButton(frame: CGRect(x: 61, y: 22, width: 33, height: 33))
            closeButton.bottom = LCDevice.Nav_H - 2
            closeButton.addTarget(self, action: #selector(closeButtonAction), for: .touchUpInside)
            closeButton.setImage(UIImage(named: "横竖屏_icon_close"), for: .normal)
            view.addSubview(closeButton)
        }
        if rightImageName != nil {
            let rightItem = UIButton(frame: CGRect(x: LCDevice.screenW - 42, y: 22, width: 40, height: 40))
            
            rightItem.addTarget(self, action: #selector(self.rightButtonAction), for: .touchUpInside)
            rightItem.tag = kNaviRightBtnTag
            rightItem.setImage(UIImage.init(named: rightImageName!), for: .normal)
            rightItem.titleLabel?.font = LCDevice.DIN_Font_PF_M(14)
            if let rightName = rightName {
                rightItem.setTitle(rightName, for: .normal)
                rightItem.frame = CGRect(x: LCDevice.screenW - 102, y: 22, width: 100, height: 34)
                rightItem.imagePosition(style: .right, spacing: 4)
            }
            rightItem.bottom = LCDevice.Nav_H - 2
            rightItem.zl_enlargeValidTouchArea(inset: 10)
            view.addSubview(rightItem)
        }
//        if hasBotLine{
//            let line = UIView.getLineView()
//            line.bottom = view.height
//            view.addSubview(line)
//        }
        return view
    }
    
    /// 返回导航栏背景View，需要点击返回的方法重写leftButtonAction和rightButtonAction
    func getNavBarView(titleStr: String?,
                       hasBotLine: Bool = false,
                       color: UIColor = .white) -> UIView {
        let BarView = UIView(frame: CGRect(x: 0, y: 0, width: LCDevice.screenW, height: LCDevice.Nav_H))
        BarView.backgroundColor = color
        let titleLabel = UILabel(frame: CGRect(x: 25, y: 32, width: LCDevice.screenW - 50, height: 40))
        titleLabel.bottom = LCDevice.Nav_H + 3
        titleLabel.text = titleStr
        titleLabel.tag = kNaviTitleLabTag
        if color == UIColor.white {
            titleLabel.textColor = SColor.baseText
        } else {
            titleLabel.textColor = .white
        }
        titleLabel.font = LCDevice.DIN_Font_PF_M(18)
        titleLabel.textAlignment = .center
        BarView.addSubview(titleLabel)
//        if hasBotLine{
//            let line = UIView.getLineView()
//            line.bottom = BarView.height
//            BarView.addSubview(line)
//        }
        return BarView
    }
    
    /// 子类如果左键按钮不是返回，就重写这个方法
    @objc func leftButtonAction() {
        if let _ = navigationController?.popViewController(animated: true) {
            
        } else {
            dismiss(animated: false, completion: nil)
        }
    }
    
    @objc func closeButtonAction() {}
    
    @objc func rightButtonAction() {
        LCLog.d("baseVC rightAction")
    }
    
    /// Alert提示框，系统风格的
    func alertSingleAction(title: String?,
                           msg: String,
                           handler: ((UIAlertAction) -> Void)? = nil)  {
        let alert = UIAlertController.init(title: title, message: msg, preferredStyle: .alert)
        let ok = UIAlertAction.init(title: "我知道了", style: .cancel, handler: handler)
        alert.addAction(ok)
        present(alert, animated: true, completion: nil)
    }
    
    /// 关闭键盘
    func keyBoardDismss() {
        self.view.endEditing(true)
    }

}
