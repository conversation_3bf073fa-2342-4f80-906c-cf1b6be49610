//
//  CookieRequestWebView.swift
//  MockAnchor
//
//  Created by 郭炜 on 2024/11/20.
//

import UIKit
import WebKit


protocol CookieRequestWebViewDelegate: AnyObject {
    func webViewDidSuccess(cookies: [HTTPCookie], extraInfo: [String: Any])
    func webViewDidFail(error: Error)
    func webViewMockUrls(request: DYRequestData)
    func userContentController(key: String)
}

// 自定义错误类型
enum CookieError: Error {
    case invalidURL
    case noCookieFound
    case invalidCookie
}

class CookieRequestWebView: UIView {
    
    // MARK: - Properties
    weak var delegate: CookieRequestWebViewDelegate?
    
    lazy var closeButton: UIButton = {
        let button = UIButton()
        button.setImage(UIImage(named: "icon_close_black"), for: .normal)
        button.backgroundColor = .clear
        button.addTarget(self, action: #selector(closeAction), for: .touchUpInside)
        return button
    }()
    
    // 挡住web弹窗的右上角的关闭按钮
    lazy var closeButton1: UIButton  = {
        let button = UIButton()
        button.setImage(UIImage(named: "icon_close_black"), for: .normal)
        button.backgroundColor = .white
        button.addTarget(self, action: #selector(closeAction), for: .touchUpInside)
        button.cornerRadius = 6
        return button
    }()
    
    private var webView: WKWebView!
    
    lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.text = "请使用抖音APP\n扫码登录"
        label.textColor = UIColor("#1E1F20")
        label.font = UIFont.systemFont(ofSize: 20, weight: .medium)
        label.numberOfLines = 0
        label.textAlignment = .center
        return label
    }()
    
    lazy var desLabel: UILabel = {
        let label = UILabel()
        
        let paraph = NSMutableParagraphStyle()
        paraph.lineSpacing = 6
        let attributes = [NSAttributedString.Key.paragraphStyle: paraph, NSAttributedString.Key.foregroundColor: UIColor("#868686"), NSAttributedString.Key.font: UIFont.systemFont(ofSize: 14, weight: .regular)]
        
        let attributedText = NSMutableAttributedString(string: "打开抖音App\n点击首页右上角 ", attributes: attributes)
        
        // 图片
        let imageAttachment = NSTextAttachment()
        imageAttachment.image = UIImage(named: "搜索")
        imageAttachment.bounds = CGRect(x: 0, y: -3, width: 12, height: 12)

        let imageString = NSAttributedString(attachment: imageAttachment)
        attributedText.append(imageString)
        
        let attributedText1 = NSMutableAttributedString(string: "，点击扫一扫 ", attributes: attributes)
        attributedText.append(attributedText1)
        
        // 图片
        let imageAttachment1 = NSTextAttachment()
        imageAttachment1.image = UIImage(named: "扫一扫 1")
        imageAttachment1.bounds = CGRect(x: 0, y: -3, width: 12, height: 12)

        let imageString1 = NSAttributedString(attachment: imageAttachment1)
        attributedText.append(imageString1)
        
        label.attributedText = attributedText
        label.textAlignment = .center
        label.numberOfLines = 0
        return label
    }()
    
    lazy var qrbgView: UIView  = {
        let v = UIView()
        v.backgroundColor = .white
        v.layer.cornerRadius = 12
        v.layer.masksToBounds = true
        return v
    }()
    
    lazy var retryView: UIView  = {
        let v = UIView()
        v.backgroundColor = .black.alpha(value: 0.7)
        v.isHidden = true
        return v
    }()
    
    lazy var retryButton: UIButton = {
        let button = UIButton()
        button.setImage(UIImage(named: "重试"), for: .normal)
        button.backgroundColor = .clear
        button.addTarget(self, action: #selector(retryAction), for: .touchUpInside)
        return button
    }()
    
    lazy var retryLabel: UILabel = {
        let label = UILabel()
        label.text = "刷新二维码"
        label.textColor = .white
        label.font = UIFont.systemFont(ofSize: 16, weight: .regular)
        return label
    }()
    
    lazy var qrImageView: UIImageView  = {
        let imageView = UIImageView()
        return imageView
    }()
    
    
    lazy var bgView: UIView  = {
        let v = UIView()
        v.backgroundColor = .clear
        v.layer.cornerRadius = 12
        v.layer.masksToBounds = true
        return v
    }()
    
    lazy var touchView: UIView  = {
        let v = UIView()
        v.backgroundColor = .clear
//        let tap = UITapGestureRecognizer(target: self, action: #selector(tapAction))
//        v.addGestureRecognizer(tap)
        return v
    }()
    
    var qrtimer: Timer?
    
    var timer: Timer?
    
    // MARK: - Initialization
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        self.backgroundColor = UIColor.black.alpha(value: 0.5)
        
        setupWebView()
        loadCookiesIntoWebView()

    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Lifecycle
    
    private func setupWebView() {
        self.addSubviews([touchView ,bgView, qrbgView])
        
        touchView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        bgView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview()
            make.leading.trailing.equalToSuperview().inset(30)
            make.height.equalTo(500)
        }
        
        let config = WKWebViewConfiguration()
        webView = WKWebView(frame: CGRect(x: -800, y: -100, width: 1000, height: 500), configuration: config)
        webView.backgroundColor = .white
        webView.navigationDelegate = self
        webView.autoresizingMask = [.flexibleWidth, .flexibleHeight]
        bgView.addSubview(webView)

        webView.addSubview(closeButton1)
        
        closeButton1.snp.makeConstraints { make in
            make.trailing.equalToSuperview().inset(8)
            make.top.equalToSuperview().inset(10)
            make.width.height.equalTo(50)
        }
        
        qrbgView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview()
            make.leading.trailing.equalToSuperview().inset(30)
            make.height.equalTo(400)
        }
        
        qrbgView.addSubviews([titleLabel, desLabel, qrImageView, retryView, closeButton])
        
        qrImageView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview()
            make.height.width.equalTo(200)
        }
        
        closeButton.snp.makeConstraints { make in
            make.top.trailing.equalToSuperview().inset(6)
            make.width.height.equalTo(32)
        }
        
        
        titleLabel.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalToSuperview().inset(24)
            make.width.equalTo(200)
        }
        
        desLabel.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.bottom.equalToSuperview().inset(24)
            make.width.equalTo(220)
            make.height.equalTo(60)
        }
        
        retryView.addSubviews([retryButton, retryLabel])
        
        retryView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview()
            make.height.width.equalTo(200)
        }
        
        retryButton.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview()
            make.width.height.equalTo(44)
        }
        
        retryLabel.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.bottom.equalToSuperview().inset(28)
        }
    }
    
    @objc func tapAction() {
        self.isHidden = true
    }
    
    @objc func closeAction() {
        self.isHidden = true
    }
    
    private func loadCookiesIntoWebView() {
        let cookies = CookieInfo.shared.getAllCookies() // 从持久化存储中加载 cookies
        let cookieStore = webView.configuration.websiteDataStore.httpCookieStore
        
        for cookie in cookies {
            cookieStore.setCookie(cookie)
        }
    }
    
    public func loadTargetPage(url: String) {
        releaseTimer()
        releaseQrtimer()
        
        webView.isHidden = true
        qrbgView.isHidden = false
        self.retryView.isHidden = true
        self.qrImageView.image = nil
        if let url = URL(string: url) {
            let request = URLRequest(url: url)
            webView.load(request)
            
        } else {
            // 处理无效 URL 错误
            print("Invalid URL")
        }
    }

    
    deinit {
        // 确保在视图控制器释放时停止定时器
        releaseTimer()
        releaseQrtimer()
    }
    
    func resetWebViewFrame() {
        webView.frame = CGRect(x: 0, y: 50, width: LCDevice.screenW - 60, height: 400)
    }
    
    func offsetWebViewFrame() {
        webView.frame = CGRect(x: -800, y: -100, width: 1000, height: 500)
    }
    
}

// MARK: - WKNavigationDelegate
extension CookieRequestWebView: WKNavigationDelegate {
    
    func webView(_ webView: WKWebView, didFinish navigation: WKNavigation!) {
        webView.configuration.websiteDataStore.httpCookieStore.getAllCookies { [weak self] cookies in
            guard let self = self else { return }
            print("cookies is : \(cookies)")
            // 检查是否存在 sessionid cookie
            guard cookies.contains(where: { $0.name == "sessionid_ss" }) else {
                LCLog.d("未找到 sessionid cookie，不执行回调")
                self.start()
                return
            }
            
            // 保存 cookie 到 CookieInfo
            CookieInfo.shared.saveCookies(cookies)
            
            // 回调成功 用户自己关闭
            delegate?.webViewDidSuccess(cookies: cookies, extraInfo: [:])
            
            releaseTimer()
            releaseQrtimer()
        }
       
    }
    
    func webView(_ webView: WKWebView, didFail navigation: WKNavigation!, withError error: Error) {
        delegate?.webViewDidFail(error: error)
    }
    
    func webView(_ webView: WKWebView, didFailProvisionalNavigation navigation: WKNavigation!, withError error: Error) {
        delegate?.webViewDidFail(error: error)
    }
    
    
     func webView(_ webView: WKWebView, decidePolicyFor navigationAction: WKNavigationAction, decisionHandler: @escaping (WKNavigationActionPolicy) -> Void) {
         LCLog.d("web重定向加载的url:\(navigationAction.request.url)")
         decisionHandler(.allow)
     }
     
}

// MARK: - 扫码登录的业务
extension CookieRequestWebView {

    func start() {
        DispatchQueue.main.asyncAfter(deadline: .now() + 1) {[weak self ] in
            self?.getqrUrl()
            self?.startListeningForPopup()
        }
    }
    
    // 获取二维码
    func getqrUrl(){
        
        webView.evaluateJavaScript(JSSingleton.shared.get_qr_img) { [weak self ](result, error) in
            if let error = error {
                print("Error getting image src: \(error.localizedDescription)")
            } else if let src = result as? String {
                print("Image src: \(src)")
                // 这里可以处理获取到的 src，例如保存或使用
                self?.creatQRImage(urlbase64: src)
            } else {
                print("No image found or src is nil.")
            }
        }
    }
    
    
    func creatQRImage(urlbase64: String) {

        // 去掉前缀
        let base64String = urlbase64.replacingOccurrences(of: "data:image/jpeg;base64,", with: "")

        // 解码 Base64 字符串
        if let imageData = Data(base64Encoded: base64String, options: .ignoreUnknownCharacters) {
            // 创建 UIImage
            if let image = UIImage(data: imageData) {
                // 这里可以使用生成的 UIImage
                self.qrbgView.isHidden = false
                self.qrImageView.image = image
            } else {
                print("生成 UIImage 失败")
            }
        } else {
            print("解码 Base64 字符串失败")
        }
        // 这时候把webview的frema 恢复正常， 因为弹窗需要
        resetWebViewFrame()
        
        // 开始计时二维码
        startQrTimer()
        
        self.retryView.isHidden = true
    }
    
    
    func startQrTimer() {
        releaseQrtimer()
        self.qrtimer = Timer.scheduledTimer(timeInterval: 30.0, target: self, selector: #selector(qrimageTimeout), userInfo: nil, repeats: false)
    }
    
    func releaseQrtimer() {
        self.qrtimer?.invalidate()
        self.qrtimer = nil
    }
    
    @objc func qrimageTimeout() {
        self.retryView.isHidden = false
    }
    
    @objc func retryAction() {
        self.loadTargetPage(url: "https://anchor.douyin.com/anchor/dashboard")
    }
    
    // 开始监听弹窗
    func startListeningForPopup() {
        releaseTimer()
        self.timer = Timer.scheduledTimer(timeInterval: 1.0, target: self, selector: #selector(checkPopup), userInfo: nil, repeats: true)
    }
    
    // 检查弹窗是否出现
    @objc func checkPopup() {
        LCLog.d("监听弹窗")
        webView.evaluateJavaScript(JSSingleton.shared.second_verify) { (result, error) in
            if let error = error {
                print("Error checking popup: \(error.localizedDescription)")
            } else if let isVisible = result as? Bool, isVisible {
                print("弹窗已弹出！")
                self.popupDidAppear()
            }
        }
    }
    
    // 弹窗出现时的回调
    func popupDidAppear() {
        self.webView.isHidden = false
        self.qrbgView.isHidden = true
        // 停止监听
        releaseTimer()
        // 处理弹窗出现的逻辑
        print("处理弹窗出现的逻辑")
        // 这里可以添加你需要执行的代
        
    }
    
    func releaseTimer() {
        self.timer?.invalidate()
        self.timer = nil
    }
}
