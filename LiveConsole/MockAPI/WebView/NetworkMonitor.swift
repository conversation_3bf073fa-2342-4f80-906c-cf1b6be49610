//
//  NetworkMonitor.swift
//  MockBuyin
//
//  Created by simon on 20.11.24.
//

import Foundation

// NetworkMonitor.swift
import WebKit

// 业务数据
enum DYRequestWorkType: Int, Codable {
    case promotion_v2 = 0 // 直播商品列表

    case bind = 2 // 上架
    case combination = 3 // 搭配
    case combination_on = 4 // 搭配上架
    case promotion_v2_all = 5
    
    case promotion_v2_refresh  = 6
}

// 返回给业务层的包装数据
struct DYRequestData: Codable {
    var data: RequestData
    var workType:DYRequestWorkType
}


// 请求数据模型
struct RequestData: Codable {
    let id: String
    let timestamp: Date
    let method: String
    let url: String
    let requestHeaders: [String: String]
    let requestBody: String?
    var statusCode: Int?
    var isExpanded: Bool = false
    
    // 用于过滤和分类的计算属性
    var requestType: RequestType {
        switch method.uppercased() {
        case "GET": return .get
        case "POST": return .post
        default: return .other
        }
    }
}

enum RequestType: String {
    case get = "GET"
    case post = "POST"
    case other = "OTHER"
    case all = "ALL"
}

class NetworkMonitor: NSObject {
    static let shared = NetworkMonitor()
    
    private let responseHandler: String = "responseHandler"
    private let requestHandler: String = "requestHandler"
    private let getMessage: String = "getMessage"
    
    // 回调闭包
    var onResponseCaptured: ((RequestData) -> Void)?
    var onRequestCaptured: ((RequestData) -> Void)?
    var onMessageReceived: (([String: Any]) -> Void)?
   
    // 配置 WKWebView
    func configureWebView(_ webView: WKWebView) {
        let script = WKUserScript(
            source: NetworkMonitor_injectedScript,
            injectionTime: .atDocumentStart,
            forMainFrameOnly: false
        )
        
        webView.configuration.userContentController.addUserScript(script)
        webView.configuration.userContentController.add(self, name: requestHandler)
        webView.configuration.userContentController.add(self, name: responseHandler)
        webView.configuration.userContentController.add(self, name: getMessage)
    }
}

// WKScriptMessageHandler 实现
extension NetworkMonitor: WKScriptMessageHandler {
    func userContentController(_ userContentController: WKUserContentController, didReceive message: WKScriptMessage) {
//        print("name: \(message.name)")
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return}
            if message.name == getMessage {
                if  let keys = message.body as? [String: Any]  {
                    self.onMessageReceived?(keys)
                }
            } else if message.name == requestHandler {
                if let dict = message.body as? [String: Any],  let requestId = dict["id"] as? String  {
                    self.handleRequest(dict, requestId:requestId )
                }
            } else if message.name == responseHandler {
                if let dict = message.body as? [String: Any], let requestId = dict["id"] as? String  {
                    self.handleResponse(dict, requestId:requestId )
                }
            }
        }
    }
    
    private func handleRequest(_ dict: [String: Any], requestId: String) {
        let request = RequestData(
            id: requestId,
            timestamp: Date(),
            method: dict["method"] as? String ?? "Unknown",
            url: dict["url"] as? String ?? "",
            requestHeaders: dict["headers"] as? [String: String] ?? [:],
            requestBody: dict["body"] as? String
        )
//        Slog.d("请求地址：\(request.url)")
        
        onRequestCaptured?(request)
    }
    
    private func handleResponse(_ dict: [String: Any], requestId: String) {
        let request = RequestData(
            id: requestId,
            timestamp: Date(),
            method: dict["method"] as? String ?? "Unknown",
            url: dict["url"] as? String ?? "",
            requestHeaders: dict["headers"] as? [String: String] ?? [:],
            requestBody: dict["body"] as? String
        )
        
        onResponseCaptured?(request)
    }
}
