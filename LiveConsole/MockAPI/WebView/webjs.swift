//
//  webjs.swift
//  LivePlus
//
//  Created by simon on 16.10.24.
//

// 接口请求监听
public let NetworkMonitor_js = """
    (function() {
        // 拦截 fetch 请求
        let originalFetch = window.fetch;
        window.fetch = async function() {
            let request = arguments[0];
            let init = arguments[1] || {};
            
            let requestId = Date.now().toString();
            
            // 获取完整的请求头信息
            let headers = {};
            
            // 1. 如果第一个参数是 Request 对象，获取其头信息
            if (request instanceof Request) {
                request.headers.forEach((value, key) => {
                    headers[key] = value;
                });
                
                // 克隆请求以获取更多信息
                request = request.clone();
            }
            
            // 2. 合并 init 中的头信息
            if (init.headers) {
                let initHeaders = new Headers(init.headers);
                initHeaders.forEach((value, key) => {
                    headers[key] = value;
                });
            }
            
            // 3. 添加默认和自动生成的头信息
            headers['user-agent'] = navigator.userAgent;
            headers['accept'] = headers['Accept'] || '*/*';
            headers['accept-language'] = navigator.language;
            headers['origin'] = window.location.origin;
            headers['referer'] = window.location.href;
            
            let requestInfo = {
                id: requestId,
                url: typeof request === 'string' ? request : request.url,
                method: (init.method || (request instanceof Request ? request.method : 'GET')),
                headers: headers,
                body: init.body || null
            };
            
            window.webkit.messageHandlers.requestHandler.postMessage(requestInfo);
            
            try {
                let response = await originalFetch.apply(this, arguments);
                let responseClone = response.clone();
                
                // 获取响应头
                let responseHeaders = {};
                response.headers.forEach((value, key) => {
                    responseHeaders[key] = value;
                });
                
                let responseInfo = {
                    id: requestId,
                    status: response.status,
                    headers: responseHeaders,
                    body: await responseClone.text(),
                    url: typeof request === 'string' ? request : request.url
                };
                
                window.webkit.messageHandlers.responseHandler.postMessage(responseInfo);
                return response;
            } catch (error) {
                let errorInfo = {
                    id: requestId,
                    error: error.toString()
                };
                window.webkit.messageHandlers.responseHandler.postMessage(errorInfo);
                throw error;
            }
        };
        
        // 拦截 XMLHttpRequest
        let XHR = XMLHttpRequest.prototype;
        let originalOpen = XHR.open;
        let originalSend = XHR.send;
        let originalSetRequestHeader = XHR.setRequestHeader;
        
        XHR.open = function() {
            this._requestId = Date.now().toString();
            this._method = arguments[0];
            this._url = arguments[1];
            this._headers = {
                // 添加默认头信息
                'user-agent': navigator.userAgent,
                'accept': '*/*',
                'accept-language': navigator.language,
                'origin': window.location.origin,
                'referer': window.location.href
            };
            return originalOpen.apply(this, arguments);
        };
        
        XHR.setRequestHeader = function(header, value) {
            this._headers[header] = value;
            return originalSetRequestHeader.apply(this, arguments);
        };
        
        XHR.send = function() {
            let xhr = this;
            let body = arguments[0];
            
            // 在发送前获取所有可用的请求头
            let allHeaders = {...xhr._headers};
            
            // 尝试获取额外的请求头
            try {
                let headerLines = xhr.getAllResponseHeaders().split('\\r\\n');
                headerLines.forEach(line => {
                    if (line) {
                        let [key, value] = line.split(': ');
                        if (key && value) {
                            allHeaders[key] = value;
                        }
                    }
                });
            } catch (e) {
                // 忽略错误
            }
            
            let requestInfo = {
                id: xhr._requestId,
                url: xhr._url,
                method: xhr._method,
                headers: allHeaders,
                body: body || null
            };
            
            window.webkit.messageHandlers.requestHandler.postMessage(requestInfo);
            
            xhr.addEventListener('load', function() {
                let responseHeaders = {};
                let headerString = xhr.getAllResponseHeaders();
                let headerLines = headerString.split('\\r\\n');
                
                headerLines.forEach(line => {
                    if (line) {
                        let [key, value] = line.split(': ');
                        if (key && value) {
                            responseHeaders[key] = value;
                        }
                    }
                });
                
                let responseInfo = {
                    id: xhr._requestId,
                    status: xhr.status,
                    headers: responseHeaders,
                    body: xhr.responseText,
                    url: xhr._url
                };
                
                window.webkit.messageHandlers.responseHandler.postMessage(responseInfo);
            });
            
            return originalSend.apply(this, arguments);
        };
    })();
    """

let second_verify_js = """
(function() {
    var popup = document.getElementById('uc-second-verify');
    return popup ? true : false;
})();
"""

let getQR_image_js = """
       (function() {
           var imgElement = document.querySelector('img.nmV5s');
           return imgElement ? imgElement.src : null;
       })();
       """

let get_Buyin_QR_image_js = """
       (function() {
           var imgElement = document.querySelector('img.qr-image');
           return imgElement ? imgElement.src : null;
       })();
       """


