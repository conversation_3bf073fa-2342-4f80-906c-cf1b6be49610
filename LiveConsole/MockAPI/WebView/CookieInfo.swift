//
//  CookieInfo.swift
//  MockAnchor
//
//  Created by 郭炜 on 2024/11/20.
//

import Foundation
import WebKit

class CookieInfo {
    static let shared = CookieInfo()
    
    private init() {
        // 初始化时从本地加载 cookies
        loadCookiesFromStorage()
    }
    
    private var request: RequestData?

    
    private var cookies: [HTTPCookie] = []
    private let cookieStorageKey = "SavedCookies"
    
    // MARK: - 持久化存储相关方法
    
    private func loadCookiesFromStorage() {
        guard let cookieDictArray = UserDefaults.standard.array(forKey: cookieStorageKey) as? [[String: Any]] else {
            return
        }
        
        cookies = cookieDictArray.compactMap { dict -> HTTPCookie? in
            // 将 String 类型的 key 转换为 HTTPCookiePropertyKey
            let properties = dict.reduce(into: [HTTPCookiePropertyKey: Any]()) { result, pair in
                let key = HTTPCookiePropertyKey(pair.key)
                result[key] = pair.value
            }
            return HTTPCookie(properties: properties)
        }
        
        // 同步到 HTTPCookieStorage
        cookies.forEach { cookie in
            HTTPCookieStorage.shared.setCookie(cookie)
        }
    }
    
    private func saveCookiesToStorage() {
        let cookieDictArray = cookies.compactMap { cookie -> [String: Any]? in
            // 将 HTTPCookiePropertyKey 转换为 String
            guard let properties = cookie.properties else { return nil }
            return Dictionary(uniqueKeysWithValues: properties.map { key, value in
                (key.rawValue, value)
            })
        }
        UserDefaults.standard.set(cookieDictArray, forKey: cookieStorageKey)
        UserDefaults.standard.synchronize()
    }
    
    // MARK: - Public Methods
    
    func saveRequest(_ request: RequestData) {
        self.request = request
    }
    
    func getRequestHeader() -> RequestData? {
        return self.request
    }
    
    
    func saveCookies(_ cookies: [HTTPCookie]) {
        self.cookies = cookies
        
        // 保存到 HTTPCookieStorage
        cookies.forEach { cookie in
            HTTPCookieStorage.shared.setCookie(cookie)
        }
        
        // 持久化存储
        saveCookiesToStorage()
    }
    
    /// 获取AID，这玩意是啥我也不知道 但是有些接口请求里面有
    /// 可能是抖音主播版的AppId  477650
    func getAid() -> String {
        func extractFirstValue(from cookie: HTTPCookie, withName name: String) -> String? {
            guard cookie.name == name else { return nil }
            
            let valueComponents = cookie.value.split(separator: ",")
            return valueComponents.first.map { String($0) }
        }
        if let cookies = HTTPCookieStorage.shared.cookies {
            for cookie in cookies {
                if let firstValue = extractFirstValue(from: cookie, withName: "gfkadpd") {
                    return firstValue
                }
            }
        }
        return "477650"
    }
    
    func getAllCookies() -> [HTTPCookie] {
        return cookies
    }
    
    func getCookie(named name: String) -> HTTPCookie? {
        return cookies.first { $0.name == name }
    }
    
    func clearCookies() {
        cookies.removeAll()
        
        // 清除 HTTPCookieStorage
        if let cookies = HTTPCookieStorage.shared.cookies {
            cookies.forEach { cookie in
                HTTPCookieStorage.shared.deleteCookie(cookie)
            }
        }
        
        // 清除持久化存储
        UserDefaults.standard.removeObject(forKey: cookieStorageKey)
        UserDefaults.standard.synchronize()
    }
    
    func apply(to request: URLRequest) -> URLRequest {
        var mutableRequest = request
        
        // 构建 cookie 字符串
        let cookieHeaders = HTTPCookie.requestHeaderFields(with: cookies)
        
        // 将 cookie 添加到请求头
        if let cookieString = cookieHeaders["Cookie"] {
            mutableRequest.setValue(cookieString, forHTTPHeaderField: "Cookie")
        }
        
        return mutableRequest
    }
    
    // MARK: - Cookie 验证
    
    func isValid() -> Bool {
        // 检查是否存在必要的 cookie（比如 sessionid）
        guard let sessionCookie = getCookie(named: "sessionid") else {
            return false
        }
        
//        // 检查 cookie 是否过期
//        if let expiresDate = sessionCookie.expiresDate,
//           expiresDate < Date() {
//            clearCookies() // 如果过期就清除
//            return false
//        }
//        
        return true
    }
}



class BuyinCookieInfo {
    static let shared = BuyinCookieInfo()
    
    private init() {
        // 初始化时从本地加载 cookies
        loadCookiesFromStorage()
    }
    
    private var request: RequestData?

    
    private var cookies: [HTTPCookie] = []
    private let cookieStorageKey = "buyinSavedCookies"
    
    // MARK: - 持久化存储相关方法
    
    private func loadCookiesFromStorage() {
        guard let cookieDictArray = UserDefaults.standard.array(forKey: cookieStorageKey) as? [[String: Any]] else {
            return
        }
        
        cookies = cookieDictArray.compactMap { dict -> HTTPCookie? in
            // 将 String 类型的 key 转换为 HTTPCookiePropertyKey
            let properties = dict.reduce(into: [HTTPCookiePropertyKey: Any]()) { result, pair in
                let key = HTTPCookiePropertyKey(pair.key)
                result[key] = pair.value
            }
            return HTTPCookie(properties: properties)
        }
        
        // 同步到 HTTPCookieStorage
        cookies.forEach { cookie in
            HTTPCookieStorage.shared.setCookie(cookie)
        }
    }
    
    private func saveCookiesToStorage() {
        let cookieDictArray = cookies.compactMap { cookie -> [String: Any]? in
            // 将 HTTPCookiePropertyKey 转换为 String
            guard let properties = cookie.properties else { return nil }
            return Dictionary(uniqueKeysWithValues: properties.map { key, value in
                (key.rawValue, value)
            })
        }
        UserDefaults.standard.set(cookieDictArray, forKey: cookieStorageKey)
        UserDefaults.standard.synchronize()
    }
    
    // MARK: - Public Methods
    
    func saveRequest(_ request: RequestData) {
        self.request = request
    }
    
    func getRequestHeader() -> RequestData? {
        return self.request
    }
    
    
    func saveCookies(_ cookies: [HTTPCookie]) {
        self.cookies = cookies
        
        // 保存到 HTTPCookieStorage
        cookies.forEach { cookie in
            HTTPCookieStorage.shared.setCookie(cookie)
        }
        
        // 持久化存储
        saveCookiesToStorage()
    }
    
    
    
    func getAllCookies() -> [HTTPCookie] {
        return cookies
    }
    
    func getCookie(named name: String) -> HTTPCookie? {
        return cookies.first { $0.name == name }
    }
    
    func clearCookies() {
        cookies.removeAll()
        
        // 清除 HTTPCookieStorage
        if let cookies = HTTPCookieStorage.shared.cookies {
            cookies.forEach { cookie in
                HTTPCookieStorage.shared.deleteCookie(cookie)
            }
        }
        
        // 清除持久化存储
        UserDefaults.standard.removeObject(forKey: cookieStorageKey)
        UserDefaults.standard.synchronize()
    }
    
    func apply(to request: URLRequest) -> URLRequest {
        var mutableRequest = request
        
        // 构建 cookie 字符串
        let cookieHeaders = HTTPCookie.requestHeaderFields(with: cookies)
        
        // 将 cookie 添加到请求头
        if let cookieString = cookieHeaders["Cookie"] {
            mutableRequest.setValue(cookieString, forHTTPHeaderField: "Cookie")
        }
        
        return mutableRequest
    }
    
 
}
