//
//  JSSingleton.swift
//  LivePlus
//
//  Created by simon on 21.10.24.
//

import Foundation

let web_ua = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36"

public let js_type_url_ua = "js_type_url_ua"

public let js_type_url_NetworkMonitor = "js_type_url_NetworkMonitor"

public let js_type_second_verify = "js_type_second_verify"

public let js_type_get_qr_img = "js_type_get_qr_img"

// 新增巨量百应

public let js_type_get_buyin_qr_img = "js_type_get_buyin_qr_img"

public let js_type_get_login = "js_type_get_login"

public let js_type_get_goods = "js_type_get_goods"

public let js_type_jiangjie = "handle_jiangjie"

public let js_type_find = "js_type_find"

public let js_type_find_click = "js_type_find_click"

public let js_type_jiangjie_no = "handle_jiangjie_no"

public let js_type_zhongkong = "js_type_zhongkong"

public let js_type_clickIKnow = "js_type_clickIKnow"

public let js_type_tiyan = "js_type_tiyan"
// 注入的JS函数
private let js_type_code = "js_type_code"

// 直播商品的操作  下架、主推、讲解等
public let js_type_clickPromotion = "js_type_clickPromotion"


class JSSingleton: NSObject {
    @objc static let shared = JSSingleton()
    
    var apiConfigs: String?
    
    // 下发的
    var jsConfigs:[JSConfigModel]?
    
    override init() {
        super.init()
        getJSCode()
    }
    
    
    func getJSConfig() {
        let configs = JSConfigModel.defineJSCode()
        self.jsConfigs = configs
        
//        MiddleRequestNet.getJSConfig { [weak self]  enc in
//            guard let self = self else { return }
//            if let encStr = enc.encStr {
//                self.encryptJS(str: encStr)
//
//            }
//        }
    }
    
    func encryptJS(str: String) {
        
        if let dstr = EncryptedConfigManager.shared.decryptConfig(encryptedString: str),
           let data = Data(base64Encoded: dstr),
            let decodedString = String(data: data, encoding:.utf8) {
            if let ms:[JSConfigModel] = JsonTool.string2Model(decodedString) {
                self.jsConfigs =  ms
                LCLog.d("JS配置获取成功")
            }
        }
    }
    
    func getApiConfig() {
        
        MiddleRequestNet.getApiConfig { [weak self]  enc in
            guard let self = self, let enc = enc else { return }

            if let encStr = enc.encStr {
                self.apiConfigs = EncryptedConfigManager.shared.decryptConfig(encryptedString: encStr)
                APIConfigurationManager.shared.loadConfiguration()
            }
        }
    }
    
    //
    func getJSCode() {
        
        getApiConfig()
        getJSConfig()
    }
    
      
    
    var userAgent: String? {
        guard let js = jsConfigs else{
            return nil
        }
        if let j = js.first(where: {$0.key == js_type_url_ua}) {
            if let data = Data(base64Encoded: j.value) {
                if let decodedString = String(data: data, encoding:.utf8) {
                    return decodedString
                }
            }
        }
        return nil
    }
    
    var networkMonitor: String {
        guard let js = jsConfigs else{
            return NetworkMonitor_js
        }
        if let j = js.first(where: {$0.key == js_type_url_NetworkMonitor}) {
            return j.value
        }
        return NetworkMonitor_js
    }
    
    
    var get_qr_img: String {
        guard let js = jsConfigs else{
            return getQR_image_js
        }
        if let j = js.first(where: {$0.key == js_type_get_qr_img}) {
            return j.value
        }
        return getQR_image_js
    }
    
    var second_verify: String {
        guard let js = jsConfigs else{
            return second_verify_js
        }
        if let j = js.first(where: {$0.key == js_type_second_verify}) {
            return j.value
        }
        return second_verify_js
    }
}

// MARK: - 巨量百应
extension JSSingleton  {
    
    var clickPromotion: String? {
        guard let js = jsConfigs else{
            return nil
        }
        if let j = js.first(where: {$0.key == js_type_clickPromotion}) {
            return j.value
        }
        return nil
    }
    
    var get_buyin_qr_img: String {
        guard let js = jsConfigs else{
            return get_Buyin_QR_image_js
        }
        if let j = js.first(where: {$0.key == js_type_get_buyin_qr_img}) {
            return j.value
        }
        return get_Buyin_QR_image_js
    }
    
    
    var jsCode: String? {
        guard let js = jsConfigs else{
            return JavascriptBridgeJS
        }
        if let j = js.first(where: {$0.key == js_type_code}) {
            if let data = Data(base64Encoded: j.value) {
                if let decodedString = String(data: data, encoding:.utf8) {
                    return decodedString
                }
            }
        }
        return JavascriptBridgeJS
    }
    
    var findUserName: String? {
        guard let js = jsConfigs else{
            return nil
        }
        if let j = js.first(where: {$0.key == js_type_get_login}) {
            return j.value
        }
        return nil
    }
    
    var zhongkong: String? {
        guard let js = jsConfigs else{
            return nil
        }
        if let j = js.first(where: {$0.key == js_type_zhongkong}) {
            return j.value
        }
        return nil
    }
    
    
    var getGoods: String? {
        guard let js = jsConfigs else{
            return nil
        }
        if let j = js.first(where: {$0.key == js_type_get_goods}) {
            return j.value
        }
        return nil
    }
    
    var clickGood: String? {
        guard let js = jsConfigs else{
            return nil
        }
        if let j = js.first(where: {$0.key == js_type_find_click}) {
            return j.value
        }
        return nil
    }
    
    var findElement: String? {
        guard let js = jsConfigs else{
            return nil
        }
        if let j = js.first(where: {$0.key == js_type_find}) {
            return j.value
        }
        return nil
    }
    
    var jiangjie: String? {
        guard let js = jsConfigs else{
            return nil
        }
        if let j = js.first(where: {$0.key == js_type_jiangjie}) {
            return j.value
        }
        return nil
    }
    
    var iKnow: String? {
        guard let js = jsConfigs else{
            return nil
        }
        if let j = js.first(where: {$0.key == js_type_clickIKnow}) {
            return j.value
        }
        return nil
    }
    
    var tiyan: String? {
        guard let js = jsConfigs else{
            return nil
        }
        if let j = js.first(where: {$0.key == js_type_tiyan}) {
            return j.value
        }
        return nil
    }
}


// JS配置模型
class JSConfigModel: NSObject, Codable {
    // JS key
    var key: String = ""
    // JS 的函数
    var value: String = ""
    init(key: String = "", value: String = "") {
        self.key = key
        self.value = value
    }
    
    static func convertDictionaryToM(dictionary: [String: Any]) -> JSConfigModel {
        let good = JSConfigModel()
        good.key = dictionary["key"] as? String ?? ""
        good.value = dictionary["value"] as? String ?? ""
        return good
    }
    
    // 生成默认值
    static func defineJSCode() -> [JSConfigModel] {
        
        var js = JavascriptBridgeJS
        //查找元素并点击的JS函数名
        if let data = JavascriptBridgeJS.data(using:.utf8) {
            js = data.base64EncodedString()
        }
        
        let jsmodels =  [
            //ua
            JSConfigModel(key: js_type_url_ua, value: web_ua),
            
            //网络监听的JS函数
            JSConfigModel(key: js_type_url_NetworkMonitor, value: NetworkMonitor_js),
            
            // 登录二维码函数
            JSConfigModel(key: js_type_get_qr_img, value: getQR_image_js),
            
            //验证弹窗函数
            JSConfigModel(key: js_type_second_verify, value: second_verify_js),
            
            //查询登录的JS函数名
            JSConfigModel(key: js_type_get_login, value: "findUserName"),
            
            //查询商品的JS函数名
            JSConfigModel(key: js_type_get_goods, value: "getGoodsData"),
            
            //查询讲解的JS函数名
            JSConfigModel(key: js_type_jiangjie, value: "findAndClickGood"),
            
            //取消讲解的JS函数名
            JSConfigModel(key: js_type_jiangjie_no, value: "findAndClickGood"),
            
            //查找商品的JS函数名
            JSConfigModel(key: js_type_find, value: "findElement"),
            
            //查找元素并点击的JS函数名
            JSConfigModel(key: js_type_find_click, value: "findElementAndClick"),
            
            //查找元素并点击的JS函数名
            JSConfigModel(key: js_type_zhongkong, value: "toZhongkongtaiFromRoot"),
            
            //我知道了
            JSConfigModel(key: js_type_clickIKnow, value: "clickIKnow"),
            
            //体验弹窗
            JSConfigModel(key: js_type_tiyan, value: "clicktiyan"),
            
            //查找元素并点击的JS函数名
            JSConfigModel(key: js_type_code, value: js),
            
            // 直播间商品点击
            JSConfigModel(key: js_type_clickPromotion, value: "clickPromotion"),
            
            // 百应后台登录二维码函数
            JSConfigModel(key: js_type_get_buyin_qr_img, value: get_Buyin_QR_image_js),
            
        ]
        
        return jsmodels
    }
}



struct ConfigEncModel: Codable {
    let encStr: String?
}


// 抖音商品模型
class DyGoodModel: NSObject {
    var title: String = ""
    var source: String = ""
    var type : Int = 0
    init(title: String = "", source: String = "", type: Int = 0) {
        self.title = title
        self.source = source
        self.type = type
    }
    
    static func convertDictionaryToPerson(dictionary: [String: Any]) -> DyGoodModel {
        let good = DyGoodModel()
        good.title = dictionary["title"] as? String ?? ""
        good.source = dictionary["source"] as? String ?? ""
        good.type = dictionary["type"] as? Int ?? 0
        return good
    }
}
