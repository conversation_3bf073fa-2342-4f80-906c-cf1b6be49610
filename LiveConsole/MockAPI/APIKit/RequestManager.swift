//
//  RequstManager.swift
//  MockAnchor
//
//  Created by 郭炜 on 2024/12/3.
//

import UIKit

enum HttpMethod: String {
    case GET  = "GET"
    case POST = "POST"
}

// MARK: - 封装一个通用的数据请求管理类
struct RequestManager {
    
    static let shared = RequestManager()
}

// MARK: - 外部接口
extension RequestManager {
    
    /// 请求接口
    /// - Parameter style: API类型
    /// - Returns: codable类型数据 接口的返回数据模型
    @available(iOS 13.0.0, *)
    public func requestApi<T: Codable>(style: APIStyle) async throws -> T {
        let request = self.buildRequest(apiUrl: style.apiUrl,
                                        params: style.params,
                                        httpMethod: style.httpMethod)
        // 发送请求
        let (data, _) = try await URLSession.shared.data(for: request)
//        print("---- API is \(style) ----")

        // 解析响应
        let decoder = JSONDecoder()
        let response = try decoder.decode(T.self, from: data)
//        print("---- response is \n \(response) ----")
        return response
    }
    
}

// MARK: - 构建请求
extension RequestManager {
    
    
    /// 构建请求
    /// - Parameters:
    ///   - apiUrl: 接口请求地址
    ///   - params: 参数列表：拼接在URL后面的
    ///   - httpMethod: 请求类型 GET/POST
    /// - Returns: 构建的url请求
    private func buildRequest(apiUrl: String, params: [String: String], httpMethod: HttpMethod) -> URLRequest {
        // 构建 URL
        var components = URLComponents(string: apiUrl)!
        
        // 设置请求参数
        components.queryItems = params.map { URLQueryItem(name: $0.key, value: $0.value) }

        // 构建请求
        var request = URLRequest(url: components.url!)
        request.httpMethod = httpMethod.rawValue
        
        // 设置 headers 默认值
        request.setValue("anchor.douyin.com", forHTTPHeaderField: "Host")
//        request.setValue("\"Google Chrome\";v=\"129\", \"Not=A?Brand\";v=\"8\", \"Chromium\";v=\"129\"", forHTTPHeaderField: "sec-ch-ua")
//        request.setValue("\"macOS\"", forHTTPHeaderField: "sec-ch-ua-platform")
        request.setValue("?0", forHTTPHeaderField: "sec-ch-ua-mobile")
        request.setValue("1116", forHTTPHeaderField: "x-sub-web-id")
        request.setValue("Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", forHTTPHeaderField: "user-agent")
        request.setValue("application/json, text/plain, */*", forHTTPHeaderField: "accept")
        request.setValue("000100000001c458efb4f94320cbfc1be2b86f9b9f9fd72e1428cfb4823f4b55b93f0aac47aa1809a034d6c7da65", forHTTPHeaderField: "x-secsdk-csrf-token")
        request.setValue("4", forHTTPHeaderField: "x-tenant-id")
        request.setValue("3000", forHTTPHeaderField: "x-appid")
        request.setValue("same-origin", forHTTPHeaderField: "sec-fetch-site")
        request.setValue("cors", forHTTPHeaderField: "sec-fetch-mode")
        request.setValue("empty", forHTTPHeaderField: "sec-fetch-dest")
        request.setValue("https://anchor.douyin.com/anchor/dashboard?from=default", forHTTPHeaderField: "referer")
        request.setValue("zh-CN,zh;q=0.9", forHTTPHeaderField: "accept-language")
        request.setValue("u=1, i", forHTTPHeaderField: "priority")
        
        // 从 CookieInfo 获取 header
        if let requestData = CookieInfo.shared.getRequestHeader() {
            
            for (key, value) in requestData.requestHeaders {
                request.setValue(value, forHTTPHeaderField: key)
            }
            
        }
        
        // 从 CookieInfo 获取 cookies
        let cookies = CookieInfo.shared.getAllCookies()
        let cookieHeader = HTTPCookie.requestHeaderFields(with: cookies)["Cookie"]
        request.setValue(cookieHeader, forHTTPHeaderField: "Cookie")
        
        return request
    }
}
