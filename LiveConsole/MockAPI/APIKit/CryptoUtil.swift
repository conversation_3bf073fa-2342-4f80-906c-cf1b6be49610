//
//  CryptoUtil.swift
//  liveplusexplore
//
//  Created by 郭炜 on 2024/12/23.
//

import Foundation
import CommonCrypto

class CryptoUtil {
    // 加密密钥 (32字节/256位)
    private static let key = "abcdefghijklmopqrstuvwxyz"
    // 初始化向量 (16字节/128位)
    private static let iv = "idf_123456789"
    
    /// 加密字符串
    static func encrypt(_ string: String) -> String? {
        guard let data = string.data(using: .utf8) else { return nil }
        return encrypt(data)?.base64EncodedString()
    }
    
    /// 解密字符串
    static func decrypt(_ base64String: String) -> String? {
        guard let data = Data(base64Encoded: base64String) else { return nil }
        guard let decryptedData = decrypt(data) else { return nil }
        return String(data: decryptedData, encoding: .utf8)
    }
    
    /// 加密数据
    static func encrypt(_ data: Data) -> Data? {
        return performCipher(data: data, operation: CCOperation(kCCEncrypt))
    }
    
    /// 解密数据
    static func decrypt(_ data: Data) -> Data? {
        return performCipher(data: data, operation: CCOperation(kCCDecrypt))
    }
    
    /// 执行加密/解密操作
    private static func performCipher(data: Data, operation: CCOperation) -> Data? {
        guard let keyData = key.data(using: .utf8),
              let ivData = iv.data(using: .utf8) else { return nil }
        
        let cryptLength = size_t(data.count + kCCBlockSizeAES128)
        var cryptData = Data(count: cryptLength)
        
        let keyLength = size_t(kCCKeySizeAES256)
        let options = CCOptions(kCCOptionPKCS7Padding)
        var numBytesEncrypted: size_t = 0
        
        let cryptStatus = cryptData.withUnsafeMutableBytes { cryptBytes in
            data.withUnsafeBytes { dataBytes in
                ivData.withUnsafeBytes { ivBytes in
                    keyData.withUnsafeBytes { keyBytes in
                        CCCrypt(operation,
                                CCAlgorithm(kCCAlgorithmAES),
                                options,
                                keyBytes.baseAddress, keyLength,
                                ivBytes.baseAddress,
                                dataBytes.baseAddress, data.count,
                                cryptBytes.baseAddress, cryptLength,
                                &numBytesEncrypted)
                    }
                }
            }
        }
        
        if cryptStatus == kCCSuccess {
            cryptData.removeSubrange(numBytesEncrypted..<cryptData.count)
            return cryptData
        }
        
        return nil
    }
}

class EncryptedConfigManager {
    static let shared = EncryptedConfigManager()
    
    private init() {}
    
    /// 加密配置文件
    func encryptConfig(jsonString: String) -> String? {
        return CryptoUtil.encrypt(jsonString)
    }
    
    /// 解密配置文件
    func decryptConfig(encryptedString: String) -> String? {
        return CryptoUtil.decrypt(encryptedString)
    }
    
    /// 保存加密配置到文件
    func saveEncryptedConfig(_ jsonString: String, to filename: String) -> Bool {
        guard let encrypted = encryptConfig(jsonString: jsonString) else { return false }
        
        let paths = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)
        guard let documentsDirectory = paths.first else { return false }
        
        let fileURL = documentsDirectory.appendingPathComponent(filename)
        
        do {
            try encrypted.write(to: fileURL, atomically: true, encoding: .utf8)
            return true
        } catch {
            print("保存加密配置失败: \(error)")
            return false
        }
    }
    
    /// 从文件加载加密配置
    func loadEncryptedConfig(from filename: String) -> String? {
        guard let fileURL = Bundle.main.url(forResource: filename, withExtension: nil) else { return nil }
        
        do {
            let encrypted = try String(contentsOf: fileURL, encoding: .utf8)
            return decryptConfig(encryptedString: encrypted)
        } catch {
            print("加载加密配置失败: \(error)")
            return nil
        }
    }
    
    /// 从文件加载加密配置
    func loadConfig(from filename: String) -> String? {
        guard let fileURL = Bundle.main.url(forResource: filename, withExtension: nil) else { return nil }
        
        do {
            let encrypted = try String(contentsOf: fileURL, encoding: .utf8)
            return encrypted
        } catch {
            print("加载加密配置失败: \(error)")
            return nil
        }
    }
}
