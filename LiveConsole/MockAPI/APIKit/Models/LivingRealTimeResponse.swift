//
//  LivingRealTimeResponse.swift
//  MockAnchor
//
//  Created by 郭炜 on 2024/12/4.
//

import UIKit

/**
 {"data":{"data":"{\"code\":0,\"componentID\":\"cgrni33c77u7lluskn1g\",\"data\":{\"series\":[{\"commentUcnt\":\"2\",\"consumeUcnt\":\"0\",\"earnScore\":\"0\",\"followUcnt\":\"0\",\"likeCnt\":\"0\",\"liveDuration\":\"3168\",\"onlineUcnt\":\"1\",\"watchUcnt\":\"22\"}],\"extra\":{\"interval\":\"10000\"}},\"meta\":\"{\\\"field\\\":[{\\\"key\\\":\\\"onlineUcnt\\\",\\\"title\\\":\\\"在线人数\\\",\\\"_showType\\\":\\\"number\\\"},{\\\"key\\\":\\\"consumeUcnt\\\",\\\"title\\\":\\\"送礼人数\\\",\\\"_showType\\\":\\\"number\\\"},{\\\"key\\\":\\\"earnScore\\\",\\\"title\\\":\\\"收获音浪\\\",\\\"_showType\\\":\\\"number\\\"},{\\\"key\\\":\\\"followUcnt\\\",\\\"title\\\":\\\"新增粉丝\\\",\\\"_showType\\\":\\\"number\\\"},{\\\"key\\\":\\\"likeCnt\\\",\\\"title\\\":\\\"点赞次数\\\",\\\"_showType\\\":\\\"number\\\"},{\\\"key\\\":\\\"commentUcnt\\\",\\\"title\\\":\\\"评论人数\\\",\\\"_showType\\\":\\\"number\\\"}],\\\"packages\\\":[{\\\"group\\\":[{\\\"title\\\":\\\"在线人数\\\",\\\"keys\\\":[\\\"onlineUcnt\\\"]},{\\\"title\\\":\\\"送礼人数\\\",\\\"keys\\\":[\\\"consumeUcnt\\\"]},{\\\"title\\\":\\\"收获音浪\\\",\\\"keys\\\":[\\\"earnScore\\\"]},{\\\"title\\\":\\\"新增粉丝\\\",\\\"keys\\\":[\\\"followUcnt\\\"]},{\\\"title\\\":\\\"点赞次数\\\",\\\"keys\\\":[\\\"likeCnt\\\"]},{\\\"title\\\":\\\"评论人数\\\",\\\"keys\\\":[\\\"commentUcnt\\\"]}]}],\\\"componentID\\\":\\\"meta_cgrnkr3c77u7lluskn20\\\"}\"}"},"extra":{"now":1733194958948},"status_code":0}
 */

// MARK: - 直播间实时数据
struct LivingRealTimeResponse: Codable {
    let data: RealTimeDataWrapper
    let extra: RealTimeExtra
    let statusCode: Int
    
    enum CodingKeys: String, CodingKey {
        case data
        case extra
        case statusCode = "status_code"
    }
}

// MARK: - Data包装层
struct RealTimeDataWrapper: Codable {
    let data: String // JSON字符串
    
    // 解析内部JSON字符串的方法
    func parseInnerData() -> RealTimeInnerDataWrapper? {
        guard let jsonData = data.data(using: .utf8) else { return nil }
        return try? JSONDecoder().decode(RealTimeInnerDataWrapper.self, from: jsonData)
    }
}

// MARK: - 内部数据结构
struct RealTimeInnerDataWrapper: Codable {
    let code: Int
    let componentID: String
    let data: RealTimeData
    let meta: String // 元数据JSON字符串
}

// MARK: - 实时数据
struct RealTimeData: Codable {
    let series: [RealTimeStats]
    let extra: RealTimeDataExtra
}

// MARK: - 数据统计
struct RealTimeStats: Codable {
    let commentUcnt: String
    let consumeUcnt: String
    let earnScore: String
    let followUcnt: String
    let likeCnt: String
    let liveDuration: String
    let onlineUcnt: String
    let watchUcnt: String
}

// MARK: - 数据额外信息
struct RealTimeDataExtra: Codable {
    let interval: String
}

// MARK: - Extra
struct RealTimeExtra: Codable {
    let now: Int64
}

// MARK: - 使用示例
extension LivingRealTimeResponse {
    /// 获取格式化后的实时数据
    var realTimeStats: [RealTimeStats]? {
        data.parseInnerData()?.data.series
    }
    
    /// 获取刷新间隔（毫秒）
    var refreshInterval: Int? {
        guard let intervalStr = data.parseInnerData()?.data.extra.interval,
              let interval = Int(intervalStr) else {
            return nil
        }
        return interval
    }
}
