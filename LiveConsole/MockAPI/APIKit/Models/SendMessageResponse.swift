//
//  SendMessageResponse.swift
//  MockAnchor
//
//  Created by 郭炜 on 2024/12/5.
//

import UIKit

// MARK: - 数据模型
struct SendMessageResponse: Codable {
    let statusCode: Int?
    let data: SendMessageData?
    let extra: SendMessageExtra?
    
    enum CodingKeys: String, CodingKey {
        case statusCode = "status_code"
        case data
        case extra
    }
}

struct SendMessageExtra: Codable {
    let now: Int64?
}

struct SendMessageData: Codable {
    let id: Int?
    let msgId: Int64?
    let msgIdStr: String?
    let content: String?
    let user: SendMessageUser?
    let showIdentity: Int?
    let identityLabel: ImageInfo?
    let fullscreenTextColor: String?
    
    enum CodingKeys: String, CodingKey {
        case id
        case msgId = "msg_id"
        case msgIdStr = "msg_id_str"
        case content
        case user
        case showIdentity = "show_identity"
        case identityLabel = "identity_label"
        case fullscreenTextColor = "fullscreen_text_color"
    }
}

struct SendMessageUser: Codable {
    let id: Int64?
    let shortId: Int64?
    let nickname: String?
    let gender: Int?
    let signature: String?
    let level: Int?
    let avatarThumb: ImageInfo?
    let avatarMedium: ImageInfo?
    let avatarLarge: ImageInfo?
    let verified: Bool?
    let followInfo: FollowInfo?
    let secUid: String?
    
    enum CodingKeys: String, CodingKey {
        case id
        case shortId = "short_id"
        case nickname
        case gender
        case signature
        case level
        case avatarThumb = "avatar_thumb"
        case avatarMedium = "avatar_medium"
        case avatarLarge = "avatar_large"
        case verified
        case followInfo = "follow_info"
        case secUid = "sec_uid"
    }
}

struct ImageInfo: Codable {
    let urlList: [String]?
    let uri: String?
    let height: Int?
    let width: Int?
    let imageType: Int?
    let isAnimated: Bool?
    
    enum CodingKeys: String, CodingKey {
        case urlList = "url_list"
        case uri
        case height
        case width
        case imageType = "image_type"
        case isAnimated = "is_animated"
    }
}

struct FollowInfo: Codable {
    let followingCount: Int?
    let followerCount: Int?
    let followStatus: Int?
    let pushStatus: Int?
    
    enum CodingKeys: String, CodingKey {
        case followingCount = "following_count"
        case followerCount = "follower_count"
        case followStatus = "follow_status"
        case pushStatus = "push_status"
    }
}

