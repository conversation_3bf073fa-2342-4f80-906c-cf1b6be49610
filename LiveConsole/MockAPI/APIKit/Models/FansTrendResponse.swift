//
//  FansTrendResponse.swift
//  MockAnchor
//
//  Created by 郭炜 on 2024/12/3.
//

import UIKit

/**
 {
     "data": {
         "data": "{\"code\":0,\"componentID\":\"cgtus5rc77u4bor46kgg\",\"data\":{\"series\":[{\"date\":\"2024-11-26\",\"followAnchorUcnt\":\"0\",\"newFollowFansUcnt\":\"4\",\"unfollowFansUcnt\":\"0\"},{\"date\":\"2024-11-27\",\"followAnchorUcnt\":\"0\",\"newFollowFansUcnt\":\"5\",\"unfollowFansUcnt\":\"1\"},{\"date\":\"2024-11-28\",\"followAnchorUcnt\":\"0\",\"newFollowFansUcnt\":\"1\",\"unfollowFansUcnt\":\"0\"},{\"date\":\"2024-11-29\",\"followAnchorUcnt\":\"0\",\"newFollowFansUcnt\":\"-1\",\"unfollowFansUcnt\":\"1\"},{\"date\":\"2024-11-30\",\"followAnchorUcnt\":\"0\",\"newFollowFansUcnt\":\"-1\",\"unfollowFansUcnt\":\"1\"},{\"date\":\"2024-12-01\",\"followAnchorUcnt\":\"0\",\"newFollowFansUcnt\":\"-1\",\"unfollowFansUcnt\":\"2\"},{\"date\":\"2024-12-02\",\"followAnchorUcnt\":\"0\",\"newFollowFansUcnt\":\"1\",\"unfollowFansUcnt\":\"1\"}]},\"meta\":\"{\\\"field\\\":[{\\\"key\\\":\\\"date\\\",\\\"title\\\":\\\"日期\\\",\\\"_showType\\\":\\\"number\\\",\\\"unit\\\":\\\"\\\"},{\\\"key\\\":\\\"followAnchorUcnt\\\",\\\"title\\\":\\\"直播新增粉丝\\\",\\\"_showType\\\":\\\"number\\\",\\\"unit\\\":\\\"\\\"},{\\\"key\\\":\\\"newFollowFansUcnt\\\",\\\"title\\\":\\\"粉丝净增量\\\",\\\"_showType\\\":\\\"number\\\",\\\"unit\\\":\\\"\\\"},{\\\"key\\\":\\\"unfollowFansUcnt\\\",\\\"title\\\":\\\"取关粉丝\\\",\\\"_showType\\\":\\\"number\\\",\\\"unit\\\":\\\"\\\"}],\\\"packages\\\":[{\\\"title\\\":\\\"粉丝净增量\\\",\\\"keys\\\":[\\\"newFollowFansUcnt\\\"]},{\\\"title\\\":\\\"取关粉丝\\\",\\\"keys\\\":[\\\"unfollowFansUcnt\\\"]},{\\\"title\\\":\\\"直播新增粉丝\\\",\\\"keys\\\":[\\\"followAnchorUcnt\\\"]}],\\\"categoryAxis\\\":[\\\"date\\\"],\\\"valueAxis\\\":[\\\"\\\"],\\\"componentID\\\":\\\"meta_cgtut23c77ucrn6s7i30\\\"}\"}"
     },
     "extra": {
         "now": 1733194998103
     },
     "status_code": 0
 }
 */

// MARK: - 粉丝增长趋势
struct FansTrendResponse: Codable {
    let data: FansTrendWrapperData
    let extra: FansTrendExtra
    let statusCode: Int
    
    enum CodingKeys: String, CodingKey {
        case data
        case extra
        case statusCode = "status_code"
    }
}

// MARK: - 包装数据
struct FansTrendWrapperData: Codable {
    let data: String  // JSON字符串
    
    // 解析内部数据
    func parseInnerData() -> FansTrendInnerResponse? {
        guard let jsonData = data.data(using: .utf8) else { return nil }
        return try? JSONDecoder().decode(FansTrendInnerResponse.self, from: jsonData)
    }
}

// MARK: - 额外信息
struct FansTrendExtra: Codable {
    let now: Int64
}

// MARK: - 内部响应
struct FansTrendInnerResponse: Codable {
    let code: Int
    let componentID: String
    let data: FansTrendInnerData
    let meta: String
    let subCode: Int
}

// MARK: - 内部数据
struct FansTrendInnerData: Codable {
    let series: [FansTrendSeries]
}

// MARK: - 系列数据
struct FansTrendSeries: Codable {
    let date: String              // 日期
    let followAnchorUcnt: String  // 直播新增粉丝数
    let newFollowFansUcnt: String // 粉丝净增量
    let unfollowFansUcnt: String  // 取关粉丝数
    
    // 便利计算属性
    var followAnchorCount: Int {
        return Int(followAnchorUcnt) ?? 0
    }
    
    var newFollowFansCount: Int {
        return Int(newFollowFansUcnt) ?? 0
    }
    
    var unfollowFansCount: Int {
        return Int(unfollowFansUcnt) ?? 0
    }
    
    // 日期转换便利属性
    var dateObject: Date? {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        return formatter.date(from: date)
    }
}
