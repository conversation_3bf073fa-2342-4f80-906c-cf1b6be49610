//
//  LivingEntranceResponse.swift
//  MockAnchor
//
//  Created by 郭炜 on 2024/12/4.
//

import UIKit

/**
 {"data":{"data":"{\"code\":0,\"componentID\":\"cgrpm9bc77u9eokb3i80\",\"data\":{\"series\":[{\"name\":\"直播推荐\",\"value\":\"58.33\",\"watchValue\":\"14\"},{\"name\":\"其他\",\"value\":\"33.33\",\"watchValue\":\"8\"},{\"name\":\"个人主页\",\"value\":\"4.17\",\"watchValue\":\"1\"},{\"name\":\"关注\",\"value\":\"4.17\",\"watchValue\":\"1\"}],\"extra\":{\"interval\":\"30000\"}},\"meta\":\"{\\\"field\\\":[{\\\"key\\\":\\\"name\\\",\\\"title\\\":\\\"流量类型\\\",\\\"_showType\\\":\\\"string\\\",\\\"unit\\\":\\\"\\\"},{\\\"key\\\":\\\"ratio\\\",\\\"title\\\":\\\"占比\\\",\\\"_showType\\\":\\\"number\\\",\\\"unit\\\":\\\"%\\\"},{\\\"key\\\":\\\"value\\\",\\\"title\\\":\\\"人数\\\",\\\"_showType\\\":\\\"number\\\",\\\"unit\\\":\\\"\\\"}],\\\"valueAxis\\\":[[\\\"value\\\",\\\"ratio\\\"]],\\\"categoryAxis\\\":[\\\"name\\\"],\\\"packages\\\":[{}],\\\"componentID\\\":\\\"meta_cgrpmrjc77ucrn6s7ha0\\\"}\"}"},"extra":{"now":1733194958925},"status_code":0}
 */

// MARK: - 直播间观众来源
// MARK: - 直播间观众来源
struct LivingEntranceResponse: Codable {
    let data: LivingEntranceDataWrapper
    let extra: LivingEntranceExtra
    let statusCode: Int
    
    enum CodingKeys: String, CodingKey {
        case data
        case extra
        case statusCode = "status_code"
    }
}

// MARK: - Data包装层
struct LivingEntranceDataWrapper: Codable {
    let data: String // JSON字符串
    
    // 解析内部JSON字符串的方法
    func parseInnerData() -> LivingEntranceInnerDataWrapper? {
        guard let jsonData = data.data(using: .utf8) else { return nil }
        return try? JSONDecoder().decode(LivingEntranceInnerDataWrapper.self, from: jsonData)
    }
}

// MARK: - 内部数据结构
struct LivingEntranceInnerDataWrapper: Codable {
    let code: Int
    let componentID: String
    let data: LivingEntranceData
    let meta: String // 元数据JSON字符串
    
    enum CodingKeys: String, CodingKey {
        case code
        case componentID = "componentID"
        case data
        case meta
    }
}

// MARK: - 观众来源数据
struct LivingEntranceData: Codable {
    let series: [LivingEntranceStats]
    let extra: LivingEntranceDataExtra
}

// MARK: - 数据统计
struct LivingEntranceStats: Codable {
    let name: String      // 流量类型
    let value: String     // 占比
    let watchValue: String // 人数
}

// MARK: - 数据额外信息
struct LivingEntranceDataExtra: Codable {
    let interval: String  // 间隔时间
}

// MARK: - Extra
struct LivingEntranceExtra: Codable {
    let now: Int64
}

// MARK: - 使用示例
extension LivingEntranceResponse {
    /// 获取格式化后的来源数据
    var entranceStats: [LivingEntranceStats]? {
        data.parseInnerData()?.data.series
    }
    
    /// 获取刷新间隔（毫秒）
    var refreshInterval: Int? {
        guard let intervalStr = data.parseInnerData()?.data.extra.interval,
              let interval = Int(intervalStr) else {
            return nil
        }
        return interval
    }
}
