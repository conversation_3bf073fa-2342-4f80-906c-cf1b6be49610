//
//  AnchorInfoModel.swift
//  MockAnchor
//
//  Created by 郭炜 on 2024/12/3.
//

import UIKit

/// 主播信息模型
/**
 {
     "data": {
         "user": {
             "avatar": "https://p3-sign.douyinpic.com/aweme/100x100/aweme-avatar/tos-cn-i-0813c001_oMwBME8PpInjtuAAUiAAbzDfLfQ179pBEASe5E.jpeg?lk3s=93de098e\u0026x-expires=**********\u0026x-signature=R4M6uF5jX7HJZ0ZNF4iERPG3gCk%3D\u0026from=**********\u0026s=profile\u0026se=false\u0026sc=avatar\u0026l=20241203110230CF6BB540EA751602EE39",
             "nickname": "橙心好物",
             "mystery_man": false,
             "sec_uid": "",
             "anchor_id": "",
             "room_id": "",
             "gender": 0
         },
         "is_living": true,
         "total_favorited": 2163,
         "follower_count": 1764,
         "following_count": 177,
         "signature": "❤️爱生活，爱好物\n❤️每天好物分享，视频同款好物下方橱窗\n👇👇👇下方橱窗直接购买哦"
     },
     "extra": {
         "now": 1733194950910
     },
     "status_code": 0
 }
 */
struct AnchorInfoResponse: Codable {
    let data: AnchorInfoDataModel?
    let extra: AnchorInfoExtraModel?
    let statusCode: Int
    let message: String?
    enum CodingKeys: String, CodingKey {
        case data
        case extra
        case statusCode = "status_code"
        case message
    }
}

struct AnchorInfoDataModel: Codable {
    let user: AnchorInfoUserModel?
    let isLiving: Bool?
    let totalFavorited: Int?
    let followerCount: Int?
    let followingCount: Int?
    let signature: String?
    
    enum CodingKeys: String, CodingKey {
        case user
        case isLiving = "is_living"
        case totalFavorited = "total_favorited"
        case followerCount = "follower_count"
        case followingCount = "following_count"
        case signature
    }
}

struct AnchorInfoUserModel: Codable {
    let avatar: String
    let nickname: String
    let mysteryMan: Bool
    let secUid: String
    let anchorId: String
    let roomId: String
    let gender: Int
    
    enum CodingKeys: String, CodingKey {
        case avatar
        case nickname
        case mysteryMan = "mystery_man"
        case secUid = "sec_uid"
        case anchorId = "anchor_id"
        case roomId = "room_id"
        case gender
    }
}

struct AnchorInfoExtraModel: Codable {
    let now: Int64
}
