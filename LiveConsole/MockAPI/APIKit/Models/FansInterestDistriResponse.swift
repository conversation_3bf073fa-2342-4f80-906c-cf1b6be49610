//
//  FansInterestDistriResponse.swift
//  MockAnchor
//
//  Created by 郭炜 on 2024/12/3.
//

import UIKit

/**
 {
     "data": {
         "data": "{\"code\":0,\"componentID\":\"cgtv2sbc77u9eokb3iu0\",\"data\":{\"series\":[]},\"meta\":\"{\\\"field\\\":[{\\\"key\\\":\\\"name\\\",\\\"title\\\":\\\"name\\\",\\\"_showType\\\":\\\"number\\\",\\\"unit\\\":\\\"%\\\"},{\\\"key\\\":\\\"value\\\",\\\"title\\\":\\\"兴趣分布\\\",\\\"_showType\\\":\\\"number\\\",\\\"unit\\\":\\\"%\\\"}],\\\"categoryAxis\\\":[\\\"name\\\"],\\\"valueAxis\\\":[[\\\"value\\\"]],\\\"packages\\\":[{}],\\\"componentID\\\":\\\"meta_cgtv3ojc77u5o35v65d0\\\"}\",\"subCode\":0}"
     },
     "extra": {
         "now": 1733194998085
     },
     "status_code": 0
 }
 */

// MARK: - 粉丝兴趣分布范围
struct FansInterestDistriResponse: Codable {
    let data: FansInterestDistriWrapperData
    let extra: FansInterestDistriExtra
    let statusCode: Int
    
    enum CodingKeys: String, CodingKey {
        case data
        case extra
        case statusCode = "status_code"
    }
}

// MARK: - 包装数据
struct FansInterestDistriWrapperData: Codable {
    let data: String  // JSON字符串
    
    // 解析内部数据
    func parseInnerData() -> FansInterestDistriInnerResponse? {
        guard let jsonData = data.data(using: .utf8) else { return nil }
        return try? JSONDecoder().decode(FansInterestDistriInnerResponse.self, from: jsonData)
    }
}

// MARK: - 额外信息
struct FansInterestDistriExtra: Codable {
    let now: Int64
}

// MARK: - 内部响应
struct FansInterestDistriInnerResponse: Codable {
    let code: Int?
    let componentID: String?
    let data: FansInterestDistriInnerData?
    let meta: String?
    let subCode: Int?
}

// MARK: - 内部数据
struct FansInterestDistriInnerData: Codable {
    let series: [FansInterestDistriSeries]?
}

// MARK: - 系列数据
struct FansInterestDistriSeries: Codable {
    let name: String?    // 兴趣类别名称
    let value: String?   // 百分比值
    
    // 转换为数值的便利方法
    var percentage: Double {
        return Double(value ?? "0") ?? 0
    }
}
