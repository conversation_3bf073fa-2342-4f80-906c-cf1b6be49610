//
//  HistoryDetailResponse.swift
//  MockAnchor
//
//  Created by 郭炜 on 2024/12/5.
//

import UIKit

/**
 {"data":{"scene":"0","anchor":{"id_str":"3389124946438090","avatar":"https://p3.douyinpic.com/aweme/100x100/aweme-avatar/tos-cn-i-0813c001_oMwBME8PpInjtuAAUiAAbzDfLfQ179pBEASe5E.jpeg?from=**********","nickname":"橙心好物"},"audio_room_bg_image":null,"offline_room_background":{"background_url":{"open_web_url":"","content":null,"is_animated":false,"flex_setting_list":[],"text_setting_list":[],"url_list":[],"width":"0","avg_color":"#CCB1A3","image_type":0,"uri":"","height":"0"},"start_time":"0","end_time":"0","scene":"0","bg_type":0},"is_customize_audio_room":false,"room_id":"7439539639687334683","check_status":0,"room_status":3,"room_start_time":"**********","play_info":[{"size":"","flv_url":"https://lf6-record-tos.bytefcdn.com/obj/fcdnlarge4-fcdn-dy/7511243480476995527/push-rtmp-hs-f5.douyincdn.com_stage_stream-**********61682602_1732152847_1732164373_record.m3u8?psm=webcast.content_player.api\u0026vcodec=bytevc1","resolution":"","hls_url":"https://lf6-record-tos.bytefcdn.com/obj/fcdnlarge4-fcdn-dy/7511243480476995527/push-rtmp-hs-f5.douyincdn.com_stage_stream-**********61682602_1732152847_1732164373_record.m3u8?psm=webcast.content_player.api\u0026vcodec=bytevc1"}]},"extra":{"now":1732178660177},"status_code":0,"message":"success"}%
 */

struct HistoryDetailResponse: Codable {
    let statusCode: Int?
    let message: String?
    let data: HistoryRoomData?
    let extra: HistoryDetailExtra?
    
    enum CodingKeys: String, CodingKey {
        case statusCode = "status_code"
        case message
        case data
        case extra
    }
}

struct HistoryRoomData: Codable {
    let offlineRoomBackground: HistoryDetailOfflineRoomBackground?
    let roomId: String?
    let scene: String?
    let checkStatus: Int?
    let anchor: HistoryAnchor?
    let audioRoomBgImage: String?
    let playInfo: [HistoryDetailPlayInfo]?
    let roomStatus: Int?
    let roomStartTime: String?
    let isCustomizeAudioRoom: Bool?
    
    enum CodingKeys: String, CodingKey {
        case offlineRoomBackground = "offline_room_background"
        case roomId = "room_id"
        case scene
        case checkStatus = "check_status"
        case anchor
        case audioRoomBgImage = "audio_room_bg_image"
        case playInfo = "play_info"
        case roomStatus = "room_status"
        case roomStartTime = "room_start_time"
        case isCustomizeAudioRoom = "is_customize_audio_room"
    }
}

struct HistoryAnchor: Codable {
    let nickname: String
    let idStr: String
    let avatar: String
    
    enum CodingKeys: String, CodingKey {
        case nickname
        case idStr = "id_str"
        case avatar
    }
}

struct HistoryDetailOfflineRoomBackground: Codable {
    let bgType: Int?
    let backgroundUrl: BackgroundUrl?
    let startTime: String?
    let endTime: String?
    let scene: String?
    
    enum CodingKeys: String, CodingKey {
        case bgType = "bg_type"
        case backgroundUrl = "background_url"
        case startTime = "start_time"
        case endTime = "end_time"
        case scene
    }
}

struct BackgroundUrl: Codable {
    let imageType: Int?
    let openWebUrl: String?
    let isAnimated: Bool?
    let textSettingList: [String]?
    let width: String?
    let avgColor: String?
    let height: String?
    let content: String?
    let flexSettingList: [String]?
    let urlList: [String]?
    let uri: String?
    
    enum CodingKeys: String, CodingKey {
        case imageType = "image_type"
        case openWebUrl = "open_web_url"
        case isAnimated = "is_animated"
        case textSettingList = "text_setting_list"
        case width
        case avgColor = "avg_color"
        case height
        case content
        case flexSettingList = "flex_setting_list"
        case urlList = "url_list"
        case uri
    }
}

struct HistoryDetailAnchor: Codable {
    let idStr: String?
    let avatar: String?
    let nickname: String?
    
    enum CodingKeys: String, CodingKey {
        case idStr = "id_str"
        case avatar
        case nickname
    }
}

struct HistoryDetailPlayInfo: Codable {
    let size: String?
    let flvUrl: String?
    let resolution: String?
    let hlsUrl: String?
    
    enum CodingKeys: String, CodingKey {
        case size
        case flvUrl = "flv_url"
        case resolution
        case hlsUrl = "hls_url"
    }
}

struct HistoryDetailExtra: Codable {
    let now: Int64?
}
