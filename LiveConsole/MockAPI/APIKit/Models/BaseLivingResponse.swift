//
//  BaseLivingData.swift
//  MockAnchor
//
//  Created by 郭炜 on 2024/12/3.
//

import UIKit

/**
 {
     "data": {
         "data": "{\"code\":0,\"componentID\":\"cgrr66bc77u5o35v64u0\",\"data\":{\"series\":[{\"clientUnfollowAnchorUcnt1d\":\"0\",\"earnScore\":\"2\",\"fansClubJoinUcnt1d\":\"1\",\"liveCnt\":\"6\",\"liveCommentUcnt1dDirect\":\"4\",\"liveConsumeUcnt1dDirect\":\"1\",\"liveDuration\":\"342.23\",\"liveFansCommentUcnt1dDirect\":\"1\",\"liveFansConsumeUcnt1dDirect\":\"1\",\"liveFansServerLikeCnt1dDirect\":\"29\",\"liveFansServerWatchUcnt1dDirect\":\"11\",\"liveFansWatchDuration1dPavgDirect\":\"11.12\",\"liveFollowAnchorUcnt1dDirect\":\"-\",\"liveServerLikeCnt1dDirect\":\"29\",\"liveServerWatchDuration1dPavgDirect\":\"0.67\",\"liveServerWatchUcnt1dDirect\":\"234\",\"starGuardEarnScore\":\"-\",\"starGuardUcnt\":\"-\"}]},\"meta\":\"{\\\"field\\\":[{\\\"key\\\":\\\"liveCnt\\\",\\\"title\\\":\\\"开播场次\\\",\\\"_showType\\\":\\\"number\\\",\\\"unit\\\":\\\"\\\",\\\"tip\\\":\\\"如直播时间跨天的时候，当前对2天分别计算有1场直播，总计会记录为2场\\\"},{\\\"key\\\":\\\"liveDuration\\\",\\\"title\\\":\\\"开播时长\\\",\\\"_showType\\\":\\\"number\\\",\\\"unit\\\":\\\"分钟\\\"},{\\\"key\\\":\\\"earnScore\\\",\\\"title\\\":\\\"收获音浪\\\",\\\"_showType\\\":\\\"number\\\",\\\"unit\\\":\\\"\\\",\\\"tip\\\":\\\"直播收到的音浪数，如果直播中含聊天室连线收入、传送门奖励、付费连线、互动玩法，主播收入比例会有所差异，详情请在主播账单中查看\\\"},{\\\"key\\\":\\\"fansEarnScore\\\",\\\"title\\\":\\\"粉丝\\\",\\\"_showType\\\":\\\"number\\\",\\\"unit\\\":\\\"\\\"},{\\\"key\\\":\\\"liveConsumeUcnt1dDirect\\\",\\\"title\\\":\\\" 送礼人数\\\",\\\"_showType\\\":\\\"number\\\",\\\"unit\\\":\\\"\\\",\\\"tip\\\":\\\"直播观众中有送礼行为的总人数\\\"},{\\\"key\\\":\\\"liveFansConsumeUcnt1dDirect\\\",\\\"title\\\":\\\"粉丝\\\",\\\"_showType\\\":\\\"number\\\",\\\"unit\\\":\\\"\\\"},{\\\"key\\\":\\\"liveServerWatchUcnt1dDirect\\\",\\\"title\\\":\\\"观众人数\\\",\\\"_showType\\\":\\\"number\\\",\\\"unit\\\":\\\"\\\"},{\\\"key\\\":\\\"liveFansServerWatchUcnt1dDirect\\\",\\\"title\\\":\\\"粉丝\\\",\\\"_showType\\\":\\\"number\\\",\\\"unit\\\":\\\"\\\"},{\\\"key\\\":\\\"liveServerWatchDuration1dPavgDirect\\\",\\\"title\\\":\\\"人均观看时长\\\",\\\"_showType\\\":\\\"number\\\",\\\"unit\\\":\\\"分钟\\\",\\\"tip\\\":\\\"直播观众平均观看直播的时长，该统计不支持今日实时统计\\\"},{\\\"key\\\":\\\"liveFansWatchDuration1dPavgDirect\\\",\\\"title\\\":\\\"粉丝\\\",\\\"_showType\\\":\\\"number\\\",\\\"unit\\\":\\\"分钟\\\"},{\\\"key\\\":\\\"liveCommentUcnt1dDirect\\\",\\\"title\\\":\\\"评论人数\\\",\\\"_showType\\\":\\\"number\\\",\\\"unit\\\":\\\"\\\",\\\"tip\\\":\\\"直播观众中有评论行为的总人数\\\"},{\\\"key\\\":\\\"liveFansCommentUcnt1dDirect\\\",\\\"title\\\":\\\"粉丝\\\",\\\"_showType\\\":\\\"number\\\",\\\"unit\\\":\\\"\\\"},{\\\"key\\\":\\\"liveServerLikeCnt1dDirect\\\",\\\"title\\\":\\\"点赞数\\\",\\\"_showType\\\":\\\"number\\\",\\\"unit\\\":\\\"\\\"},{\\\"key\\\":\\\"liveFansServerLikeCnt1dDirect\\\",\\\"title\\\":\\\"粉丝\\\",\\\"_showType\\\":\\\"number\\\",\\\"unit\\\":\\\"\\\"},{\\\"key\\\":\\\"liveFollowAnchorUcnt1dDirect\\\",\\\"title\\\":\\\"新增粉丝\\\",\\\"_showType\\\":\\\"number\\\",\\\"unit\\\":\\\"\\\",\\\"tip\\\":\\\"通过直播关注的粉丝数\\\"},{\\\"key\\\":\\\"clientUnfollowAnchorUcnt1d\\\",\\\"title\\\":\\\"取关\\\",\\\"_showType\\\":\\\"number\\\",\\\"unit\\\":\\\"\\\"},{\\\"key\\\":\\\"fansClubJoinUcnt1d\\\",\\\"title\\\":\\\"加粉丝团\\\",\\\"_showType\\\":\\\"number\\\",\\\"unit\\\":\\\"\\\"},{\\\"key\\\":\\\"starGuardEarnScore\\\",\\\"title\\\":\\\"星守护音浪\\\",\\\"_showType\\\":\\\"number\\\",\\\"unit\\\":\\\"\\\",\\\"tip\\\":\\\"星守护音浪分摊至每天入账，如用户开通1个月星守护，则分摊到31天入账\\\"}],\\\"packages\\\":[{\\\"group\\\":[{\\\"title\\\":\\\"收获音浪\\\",\\\"keys\\\":[\\\"earnScore\\\",\\\"fansEarnScore\\\",\\\"\\\"]},{\\\"title\\\":\\\"送礼人数\\\",\\\"keys\\\":[\\\"liveConsumeUcnt1dDirect\\\",\\\"liveFansConsumeUcnt1dDirect\\\",\\\"\\\"]},{\\\"title\\\":\\\"观众人数\\\",\\\"keys\\\":[\\\"liveServerWatchUcnt1dDirect\\\",\\\"liveFansServerWatchUcnt1dDirect\\\",\\\"\\\"]},{\\\"title\\\":\\\"人均观看时长\\\",\\\"keys\\\":[\\\"liveServerWatchDuration1dPavgDirect\\\",\\\"liveFansWatchDuration1dPavgDirect\\\",\\\"\\\"]},{\\\"title\\\":\\\"新增粉丝\\\",\\\"keys\\\":[\\\"liveFollowAnchorUcnt1dDirect\\\",\\\"clientUnfollowAnchorUcnt1d\\\",\\\"\\\"]}]},{\\\"group\\\":[{\\\"title\\\":\\\"新增粉丝团\\\",\\\"keys\\\":[\\\"fansClubJoinUcnt1d\\\",\\\"\\\",\\\"\\\"]},{\\\"title\\\":\\\"评论人数\\\",\\\"keys\\\":[\\\"liveCommentUcnt1dDirect\\\",\\\"liveFansCommentUcnt1dDirect\\\",\\\"\\\"]},{\\\"title\\\":\\\"点赞数\\\",\\\"keys\\\":[\\\"liveServerLikeCnt1dDirect\\\",\\\"liveFansServerLikeCnt1dDirect\\\",\\\"\\\"]},{\\\"title\\\":\\\"开播场次\\\",\\\"keys\\\":[\\\"liveCnt\\\",\\\"\\\",\\\"\\\"]},{\\\"title\\\":\\\"开播时长\\\",\\\"keys\\\":[\\\"liveDuration\\\",\\\"\\\",\\\"\\\"]}]},{\\\"group\\\":[{\\\"title\\\":\\\"星守护音浪\\\",\\\"keys\\\":[\\\"starGuardEarnScore\\\",\\\"\\\",\\\"\\\"]}]}],\\\"componentID\\\":\\\"meta_cgrr72bc77u5o35v64ug\\\"}\"}"
     },
     "extra": {
         "now": 1733194995711
     },
     "status_code": 0
 }
 */

// MARK: - 基础的数据总览
struct BaseLivingResponse: Codable {
    let data: BaseLivingDataWrapper
    let extra: BaseLivingExtra
    let statusCode: Int
    
    enum CodingKeys: String, CodingKey {
        case data
        case extra
        case statusCode = "status_code"
    }
}

// MARK: - Data包装层
struct BaseLivingDataWrapper: Codable {
    let data: String // 这是一个JSON字符串
    
    // 解析内部JSON字符串的方法
    func parseInnerData() -> BaseLivingInnerDataWrapper? {
        guard let jsonData = data.data(using: .utf8) else { return nil }
        return try? JSONDecoder().decode(BaseLivingInnerDataWrapper.self, from: jsonData)
    }
}

// MARK: - 内部数据结构
struct BaseLivingInnerDataWrapper: Codable {
    let code: Int
    let componentID: String
    let data: BaseLivingLiveData
    let meta: String // 元数据也是JSON字符串
    
    enum CodingKeys: String, CodingKey {
        case code
        case componentID = "componentID"
        case data
        case meta
    }
}

// MARK: - 直播数据
struct BaseLivingLiveData: Codable {
    let series: [BaseLivingLiveStats]
}

// MARK: - 直播统计数据
struct BaseLivingLiveStats: Codable {
    let clientUnfollowAnchorUcnt1d: String
    let earnScore: String
    let fansClubJoinUcnt1d: String
    let liveCnt: String
    let liveCommentUcnt1dDirect: String
    let liveConsumeUcnt1dDirect: String
    let liveDuration: String
    let liveFansCommentUcnt1dDirect: String
    let liveFansConsumeUcnt1dDirect: String
    let liveFansServerLikeCnt1dDirect: String
    let liveFansServerWatchUcnt1dDirect: String
    let liveFansWatchDuration1dPavgDirect: String?
    let liveFollowAnchorUcnt1dDirect: String
    let liveServerLikeCnt1dDirect: String
    let liveServerWatchDuration1dPavgDirect: String?
    let liveServerWatchUcnt1dDirect: String
    let starGuardEarnScore: String?
    let starGuardUcnt: String?
}

// MARK: - Extra
struct BaseLivingExtra: Codable {
    let now: Int64
}
