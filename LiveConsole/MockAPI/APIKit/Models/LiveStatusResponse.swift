//
//  LiveStatusResponse.swift
//  liveplusexplore
//
//  Created by 郭炜 on 2024/12/18.
//

import UIKit

struct LiveStatusResponse: Codable {
    let statusCode: Int
    let message: String
    let extra: Extra?
    let data: LiveStatusData?
    
    enum CodingKeys: String, CodingKey {
        case statusCode = "status_code"
        case message
        case extra
        case data
    }
}

struct Extra: Codable {
    let now: Int64
}

struct LiveStatusData: Codable {
    let linkAvatarUrl: String
    let linkStartTime: String
    let linkStatus: Int
    let linkTitle: String
    let role: Int
    let roomAvatarUrl: String
    let roomId: String
    let roomStatus: Int
    let roomTitle: String
    let startTime: String
    let subWebIdRole: String
    let userLinkId: String
    
    var roomIdValue: Int64 {
        return Int64(roomId) ?? 0
    }
    
    enum CodingKeys: String, CodingKey {
        case linkAvatarUrl = "link_avatar_url"
        case linkStartTime = "link_start_time"
        case linkStatus = "link_status"
        case linkTitle = "link_title"
        case role
        case roomAvatarUrl = "room_avatar_url"
        case roomId = "room_id"
        case roomStatus = "room_status"
        case roomTitle = "room_title"
        case startTime = "start_time"
        case subWebIdRole = "sub_web_id_role"
        case userLinkId = "user_link_id"
    }
}

