//
//  FansAnalysisResponse.swift
//  MockAnchor
//
//  Created by 郭炜 on 2024/12/3.
//

import UIKit

/**
 {
     "data": {
         "data": "{\"code\":0,\"componentID\":\"\",\"data\":{\"series\":[{\"avgTop1\":\"50岁以上\",\"avgTop2\":\"41-50岁\",\"genderTop1\":\"男性\"}]},\"meta\":\"\",\"subCode\":0}"
     },
     "extra": {
         "now": 1733194950942
     },
     "status_code": 0
 }
 */
// MARK: - 粉丝分析概览
struct FansAnalysisResponse: Codable {
    let data: FansAnalysisWrapperData
    let extra: FansAnalysisExtra
    let statusCode: Int
    
    enum CodingKeys: String, CodingKey {
        case data
        case extra
        case statusCode = "status_code"
    }
}

// MARK: - 包装数据
struct FansAnalysisWrapperData: Codable {
    let data: String  // JSON字符串
    
    // 解析内部数据
    func parseInnerData() -> FansAnalysisInnerResponse? {
        guard let jsonData = data.data(using: .utf8) else { return nil }
        return try? JSONDecoder().decode(FansAnalysisInnerResponse.self, from: jsonData)
    }
}

// MARK: - 额外信息
struct FansAnalysisExtra: Codable {
    let now: Int64
}

// MARK: - 内部响应
struct FansAnalysisInnerResponse: Codable {
    let code: Int
    let componentID: String
    let data: FansAnalysisInnerData
    let meta: String
    let subCode: Int
    
    enum CodingKeys: String, CodingKey {
        case code
        case componentID = "componentID"
        case data
        case meta
        case subCode
    }
}

// MARK: - 内部数据
struct FansAnalysisInnerData: Codable {
    let series: [FansAnalysisSeries]
}

// MARK: - 系列数据
struct FansAnalysisSeries: Codable {
    let avgTop1: String      // 年龄段TOP1
    let avgTop2: String      // 年龄段TOP2
    let genderTop1: String   // 性别TOP1
}
