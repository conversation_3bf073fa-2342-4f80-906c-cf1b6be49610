//
//  HistoryKeyFragmentResponse.swift
//  MockAnchor
//
//  Created by 郭炜 on 2024/12/5.
//

import UIKit

/**
 {
     "data": {
         "data": "{\"code\":0,\"componentID\":\"\",\"data\":{\"series\":[{\"coverUrl\":\"https://lf-anchor-web.douyin.com/obj/webcast-anchor-web/key_fragment/stream-116312468574699960/20241203095500.image\",\"entranceTop1Name\":\"其他\",\"entranceTop1Ratio\":\"100.00\",\"rank\":\"2\",\"rankType\":\"watchTop\",\"showSort\":\"2\",\"timeMinute\":\"2024-12-03 09:55:00\",\"timeMinuteRange\":\"09:55:00 - 09:56:00\",\"watchUcnt\":\"2\",\"watchUcntFansRatio\":\"50.00\"},{\"coverUrl\":\"https://lf-anchor-web.douyin.com/obj/webcast-anchor-web/key_fragment/stream-116312468574699960/20241203100000.image\",\"entranceTop1Name\":\"直播推荐\",\"entranceTop1Ratio\":\"100.00\",\"rank\":\"3\",\"rankType\":\"watchTop\",\"showSort\":\"3\",\"timeMinute\":\"2024-12-03 10:00:00\",\"timeMinuteRange\":\"10:00:00 - 10:01:00\",\"watchUcnt\":\"2\",\"watchUcntFansRatio\":\"0.00\"},{\"coverUrl\":\"https://lf-anchor-web.douyin.com/obj/webcast-anchor-web/key_fragment/stream-116312468574699960/20241203100100.image\",\"entranceTop1Name\":\"直播推荐\",\"entranceTop1Ratio\":\"100.00\",\"rank\":\"1\",\"rankType\":\"watchTop\",\"showSort\":\"1\",\"timeMinute\":\"2024-12-03 10:01:00\",\"timeMinuteRange\":\"10:01:00 - 10:02:00\",\"watchUcnt\":\"3\",\"watchUcntFansRatio\":\"0.00\"}],\"extra\":{\"reportInfoStatus\":\"block\"}},\"meta\":\"\",\"subCode\":0}"
     },
     "extra": {
         "now": 1733194969927
     },
     "status_code": 0
 }
 */

// MARK: - 关键片段分析
struct HistoryKeyFragmentResponse: Codable {
    let data: KeyFragmentDataWrapper
    let extra: KeyFragmentExtra
    let statusCode: Int
    
    enum CodingKeys: String, CodingKey {
        case data
        case extra
        case statusCode = "status_code"
    }
}

// MARK: - Data包装层
struct KeyFragmentDataWrapper: Codable {
    let data: String // JSON字符串
    
    // 解析内部JSON字符串的方法
    func parseInnerData() -> KeyFragmentInnerData? {
        guard let jsonData = data.data(using: .utf8) else { return nil }
        return try? JSONDecoder().decode(KeyFragmentInnerData.self, from: jsonData)
    }
}

// MARK: - 内部数据结构
struct KeyFragmentInnerData: Codable {
    let code: Int
    let componentID: String
    let data: KeyFragmentContent
    let meta: String
    let subCode: Int
    
    enum CodingKeys: String, CodingKey {
        case code
        case componentID = "componentID"
        case data
        case meta
        case subCode = "subCode"
    }
}

// MARK: - 数据内容
struct KeyFragmentContent: Codable {
    let series: [KeyFragment]
    let extra: KeyFragmentContentExtra?
}

// MARK: - 关键片段
struct KeyFragment: Codable {
    // 封面信息
    let coverUrl: String?
    
    // 入口信息
    let entranceTop1Name: String?
    let entranceTop1Ratio: String?
    
    // 排名信息
    let rank: String?
    let rankType: String?
    let showSort: String?
    
    // 时间信息
    let timeMinute: String?
    let timeMinuteRange: String?
    
    // 观看数据
    let watchUcnt: String?
    let watchUcntFansRatio: String?
}

// MARK: - 内容额外信息
struct KeyFragmentContentExtra: Codable {
    let reportInfoStatus: String?
}

// MARK: - Extra
struct KeyFragmentExtra: Codable {
    let now: Int64?
}
