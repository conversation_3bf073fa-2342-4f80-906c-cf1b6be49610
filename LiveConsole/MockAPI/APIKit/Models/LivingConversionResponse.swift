//
//  LivingConversionResponse.swift
//  MockAnchor
//
//  Created by 郭炜 on 2024/12/5.
//

import Foundation

/**
 {"data":{"data":"{\"code\":0,\"componentID\":\"cgrq4tjc77u5o35v64r0\",\"data\":{\"series\":[{\"consumeRate\":\"0.00\",\"consumeRate7d\":\"0.00\",\"consumeUcnt\":\"0\",\"enterRate\":\"45.83\",\"enterRate7d\":\"50.00\",\"showUcnt\":\"48\",\"watchUcnt\":\"22\"}],\"extra\":{\"interval\":\"30000\"}},\"meta\":\"{\\\"field\\\":[{\\\"key\\\":\\\"showUcnt\\\",\\\"title\\\":\\\"曝光展现\\\",\\\"_showType\\\":\\\"number\\\",\\\"unit\\\":\\\"人\\\"},{\\\"key\\\":\\\"showCnt\\\",\\\"title\\\":\\\"曝光次数\\\",\\\"_showType\\\":\\\"number\\\",\\\"unit\\\":\\\"人\\\"},{\\\"key\\\":\\\"watchUcnt\\\",\\\"title\\\":\\\"进直播间\\\",\\\"_showType\\\":\\\"number\\\",\\\"unit\\\":\\\"人\\\"},{\\\"key\\\":\\\"watchCnt\\\",\\\"title\\\":\\\"进入次数\\\",\\\"_showType\\\":\\\"number\\\",\\\"unit\\\":\\\"人\\\"},{\\\"key\\\":\\\"consumeUcnt\\\",\\\"title\\\":\\\"打赏送礼\\\",\\\"_showType\\\":\\\"number\\\",\\\"unit\\\":\\\"人\\\"},{\\\"key\\\":\\\"enterRate\\\",\\\"title\\\":\\\"进直播间率\\\",\\\"_showType\\\":\\\"number\\\",\\\"unit\\\":\\\"%\\\"},{\\\"key\\\":\\\"consumeRate\\\",\\\"title\\\":\\\"付费转化率\\\",\\\"_showType\\\":\\\"number\\\",\\\"unit\\\":\\\"%\\\"},{\\\"key\\\":\\\"enterRate7d\\\",\\\"title\\\":\\\"近7日中位数\\\",\\\"_showType\\\":\\\"number\\\",\\\"type\\\":\\\"trend\\\",\\\"unit\\\":\\\"%\\\"},{\\\"key\\\":\\\"consumeRate7d\\\",\\\"title\\\":\\\"近7日中位数\\\",\\\"_showType\\\":\\\"number\\\",\\\"type\\\":\\\"trend\\\",\\\"unit\\\":\\\"%\\\"}],\\\"packages\\\":[{\\\"title\\\":\\\"曝光展现\\\",\\\"keys\\\":[\\\"showUcnt\\\",\\\"enterRate\\\",\\\"enterRate7d\\\"]},{\\\"title\\\":\\\"进直播间\\\",\\\"keys\\\":[\\\"watchUcnt\\\",\\\"consumeRate\\\",\\\"consumeRate7d\\\"]},{\\\"title\\\":\\\"打赏送礼\\\",\\\"keys\\\":[\\\"consumeUcnt\\\",\\\"\\\",\\\"\\\"]}],\\\"componentID\\\":\\\"meta_cgru7ajc77ucrn6s7hig\\\"}\"}"},"extra":{"now":1733194958904},"status_code":0}
 */

// MARK: - 直播间观众转化响应
struct LivingConversionResponse: Codable {
    let data: LivingConversionDataWrapper
    let extra: LivingConversionExtra
    let statusCode: Int
    
    enum CodingKeys: String, CodingKey {
        case data
        case extra
        case statusCode = "status_code"
    }
}

// MARK: - Data包装层
struct LivingConversionDataWrapper: Codable {
    let data: String // JSON字符串
    
    // 解析内部JSON字符串的方法
    func parseInnerData() -> LivingConversionInnerDataWrapper? {
        guard let jsonData = data.data(using: .utf8) else { return nil }
        return try? JSONDecoder().decode(LivingConversionInnerDataWrapper.self, from: jsonData)
    }
}

// MARK: - 内部数据结构
struct LivingConversionInnerDataWrapper: Codable {
    let code: Int
    let componentID: String
    let data: LivingConversionData
    let meta: String // 元数据JSON字符串
    
    enum CodingKeys: String, CodingKey {
        case code
        case componentID = "componentID"
        case data
        case meta
    }
}

// MARK: - 转化数据
struct LivingConversionData: Codable {
    let series: [LivingConversionStats]
    let extra: LivingConversionDataExtra
}

// MARK: - 转化统计数据
struct LivingConversionStats: Codable {
    let consumeRate: String    // 付费转化率
    let consumeRate7d: String  // 近7日付费转化率中位数
    let consumeUcnt: String    // 打赏送礼人数
    let enterRate: String      // 进直播间率
    let enterRate7d: String    // 近7日进直播间率中位数
    let showUcnt: String       // 曝光展现人数
    let watchUcnt: String      // 进直播间人数
}

// MARK: - 数据额外信息
struct LivingConversionDataExtra: Codable {
    let interval: String // 数据刷新间隔
}

// MARK: - Extra
struct LivingConversionExtra: Codable {
    let now: Int64 // 当前时间戳
}
