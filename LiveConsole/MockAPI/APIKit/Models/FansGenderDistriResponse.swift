//
//  FansGenderDistriResponse.swift
//  MockAnchor
//
//  Created by 郭炜 on 2024/12/3.
//

import UIKit

/**
 {
     "data": {
         "data": "{\"code\":0,\"componentID\":\"cgtuv9bc77ucrn6s7i3g\",\"data\":{\"series\":[{\"name\":\"男性\",\"value\":\"79.40\"},{\"name\":\"女性\",\"value\":\"20.60\"}]},\"meta\":\"{\\\"field\\\":[{\\\"key\\\":\\\"name\\\",\\\"title\\\":\\\"name\\\",\\\"_showType\\\":\\\"number\\\",\\\"unit\\\":\\\"\\\"},{\\\"key\\\":\\\"value\\\",\\\"title\\\":\\\"value\\\",\\\"_showType\\\":\\\"number\\\",\\\"unit\\\":\\\"%\\\"}],\\\"valueAxis\\\":[[\\\"value\\\",\\\"value\\\"]],\\\"categoryAxis\\\":[\\\"name\\\"],\\\"packages\\\":[{}],\\\"componentID\\\":\\\"meta_cgtuvubc77u7lluskn50\\\"}\",\"subCode\":0}"
     },
     "extra": {
         "now": 1733194997971
     },
     "status_code": 0
 }
 */

// MARK: - 粉丝性别分布范围
struct FansGenderDistriResponse: Codable {
    let data: FansGenderDistriWrapperData
    let extra: FansGenderDistriExtra
    let statusCode: Int
    
    enum CodingKeys: String, CodingKey {
        case data
        case extra
        case statusCode = "status_code"
    }
}

// MARK: - 包装数据
struct FansGenderDistriWrapperData: Codable {
    let data: String  // JSON字符串
    
    // 解析内部数据
    func parseInnerData() -> FansGenderDistriInnerResponse? {
        guard let jsonData = data.data(using: .utf8) else { return nil }
        return try? JSONDecoder().decode(FansGenderDistriInnerResponse.self, from: jsonData)
    }
}

// MARK: - 额外信息
struct FansGenderDistriExtra: Codable {
    let now: Int64
}

// MARK: - 内部响应
struct FansGenderDistriInnerResponse: Codable {
    let code: Int
    let componentID: String
    let data: FansGenderDistriInnerData
    let meta: String
    let subCode: Int
}

// MARK: - 内部数据
struct FansGenderDistriInnerData: Codable {
    let series: [FansGenderDistriSeries]
}

// MARK: - 系列数据
struct FansGenderDistriSeries: Codable {
    let name: String    // 性别（"男性" 或 "女性"）
    let value: String   // 百分比值（如："79.40"）
    
    // 转换为数值的便利方法
    var percentage: Double {
        return Double(value) ?? 0
    }
    
    // 性别类型便利属性
    var gender: Gender {
        return name == "男性" ? .male : .female
    }
    
    enum Gender {
        case male
        case female
    }
}
