//
//  HistoryNewFansConvResponse.swift
//  MockAnchor
//
//  Created by 郭炜 on 2024/12/5.
//

import UIKit

/**
 {
     "data": {
         "data": "{\"code\":0,\"componentID\":\"cgv3vm3c77u5o35v6680\",\"data\":{\"series\":[{\"enterRoomRatio\":\"26.5625\",\"enterRoomRatio7d\":\"51.61290322580645\",\"enterRoomUV\":\"17\",\"followRatio\":\"0\",\"followRatio7d\":\"0\",\"followUV\":\"0\",\"showUV\":\"64\"}]},\"meta\":\"{\\\"field\\\":[{\\\"key\\\":\\\"showUV\\\",\\\"title\\\":\\\"曝光展现人数\\\",\\\"_showType\\\":\\\"number\\\",\\\"unit\\\":\\\"\\\"},{\\\"key\\\":\\\"enterRoomUV\\\",\\\"title\\\":\\\"进直播间人数\\\",\\\"_showType\\\":\\\"number\\\",\\\"unit\\\":\\\"\\\"},{\\\"key\\\":\\\"followUV\\\",\\\"title\\\":\\\"新增粉丝数\\\",\\\"_showType\\\":\\\"number\\\",\\\"unit\\\":\\\"\\\"},{\\\"key\\\":\\\"enterRoomRatio\\\",\\\"title\\\":\\\"进直播间转化率\\\",\\\"_showType\\\":\\\"number\\\",\\\"unit\\\":\\\"%\\\"},{\\\"key\\\":\\\"followRatio\\\",\\\"title\\\":\\\"新增粉丝转化率\\\",\\\"_showType\\\":\\\"number\\\",\\\"unit\\\":\\\"%\\\"},{\\\"key\\\":\\\"enterRoomRatio7d\\\",\\\"title\\\":\\\"近7日中位数\\\",\\\"_showType\\\":\\\"number\\\",\\\"unit\\\":\\\"%\\\",\\\"type\\\":\\\"trend\\\"},{\\\"key\\\":\\\"followRatio7d\\\",\\\"title\\\":\\\"近7日中位数\\\",\\\"_showType\\\":\\\"number\\\",\\\"unit\\\":\\\"%\\\",\\\"type\\\":\\\"trend\\\"}],\\\"packages\\\":[{\\\"title\\\":\\\"曝光展现\\\",\\\"keys\\\":[\\\"showUV\\\",\\\"enterRoomRatio\\\",\\\"enterRoomRatio7d\\\"]},{\\\"title\\\":\\\"进直播间\\\",\\\"keys\\\":[\\\"enterRoomUV\\\",\\\"followRatio\\\",\\\"followRatio7d\\\"]},{\\\"title\\\":\\\"新增粉丝\\\",\\\"keys\\\":[\\\"followUV\\\",\\\"\\\",\\\"\\\"]}],\\\"componentID\\\":\\\"meta_cgv407jc77ucrn6s7j4g\\\"}\"}"
     },
     "extra": {
         "now": 1733384228962
     },
     "status_code": 0
 }
 */

// MARK: - 新增粉丝转化
struct HistoryNewFansConvResponse: Codable {
    let statusCode: Int?
    let data: HistoryNewFansData?
    let extra: HistoryNewFansExtra?
    
    enum CodingKeys: String, CodingKey {
        case statusCode = "status_code"
        case data
        case extra
    }
}

// MARK: - Data
struct HistoryNewFansData: Codable {
    let data: String?
    
    var parsedData: HistoryNewFansInnerData? {
        guard let data = data,
              let jsonData = data.data(using: .utf8) else { return nil }
        return try? JSONDecoder().decode(HistoryNewFansInnerData.self, from: jsonData)
    }
}

// MARK: - InnerData
struct HistoryNewFansInnerData: Codable {
    let code: Int?
    let componentID: String?
    let data: HistoryNewFansSeriesData?
    let meta: String?
    
    var parsedMeta: HistoryNewFansMeta? {
        guard let meta = meta,
              let jsonData = meta.data(using: .utf8) else { return nil }
        return try? JSONDecoder().decode(HistoryNewFansMeta.self, from: jsonData)
    }
    
    enum CodingKeys: String, CodingKey {
        case code
        case componentID = "componentID"
        case data
        case meta
    }
}

// MARK: - SeriesData
struct HistoryNewFansSeriesData: Codable {
    let series: [HistoryNewFansSeries]?
}

// MARK: - Series
struct HistoryNewFansSeries: Codable {
    let enterRoomRatio: String?
    let enterRoomRatio7d: String?
    let enterRoomUV: String?
    let followRatio: String?
    let followRatio7d: String?
    let followUV: String?
    let showUV: String?
}

// MARK: - Meta
struct HistoryNewFansMeta: Codable {
    let field: [HistoryNewFansField]?
    let packages: [HistoryNewFansPackage]?
    let componentID: String?
}

// MARK: - Field
struct HistoryNewFansField: Codable {
    let key: String?
    let title: String?
    let showType: String?
    let unit: String?
    let type: String?
    
    enum CodingKeys: String, CodingKey {
        case key
        case title
        case showType = "_showType"
        case unit
        case type
    }
}

// MARK: - Package
struct HistoryNewFansPackage: Codable {
    let title: String?
    let keys: [String]?
}

// MARK: - Extra
struct HistoryNewFansExtra: Codable {
    let now: Int64?
}
