//
//  FansAgeDistriResponse.swift
//  MockAnchor
//
//  Created by 郭炜 on 2024/12/3.
//

import UIKit

/**
 {
     "data": {
         "data": "{\"code\":0,\"componentID\":\"cgtv1m3c77ucrn6s7i40\",\"data\":{\"series\":[{\"name\":\"18-23岁\",\"value\":\"0.58\"},{\"name\":\"24-30岁\",\"value\":\"2.57\"},{\"name\":\"31-40岁\",\"value\":\"12.70\"},{\"name\":\"41-50岁\",\"value\":\"27.80\"},{\"name\":\"50岁以上\",\"value\":\"56.35\"}]},\"meta\":\"{\\\"field\\\":[{\\\"key\\\":\\\"name\\\",\\\"title\\\":\\\"name\\\",\\\"_showType\\\":\\\"number\\\",\\\"unit\\\":\\\"\\\"},{\\\"key\\\":\\\"value\\\",\\\"title\\\":\\\"value\\\",\\\"_showType\\\":\\\"number\\\",\\\"unit\\\":\\\"%\\\"}],\\\"packages\\\":[{}],\\\"valueAxis\\\":[[\\\"value\\\",\\\"value\\\"]],\\\"categoryAxis\\\":[\\\"name\\\"],\\\"componentID\\\":\\\"meta_cgtv2abc77u7lluskn60\\\"}\",\"subCode\":0}"
     },
     "extra": {
         "now": 1733194951174
     },
     "status_code": 0
 }
 */
// MARK: - 粉丝年龄分布范围
struct FansAgeDistriResponse: Codable {
    let data: FansAgeDistriWrapperData
    let extra: FansAgeDistriExtra
    let statusCode: Int
    
    enum CodingKeys: String, CodingKey {
        case data
        case extra
        case statusCode = "status_code"
    }
}

// MARK: - 包装数据
struct FansAgeDistriWrapperData: Codable {
    let data: String  // JSON字符串
    
    // 解析内部数据
    func parseInnerData() -> FansAgeDistriInnerResponse? {
        guard let jsonData = data.data(using: .utf8) else { return nil }
        return try? JSONDecoder().decode(FansAgeDistriInnerResponse.self, from: jsonData)
    }
}

// MARK: - 额外信息
struct FansAgeDistriExtra: Codable {
    let now: Int64
}

// MARK: - 内部响应
struct FansAgeDistriInnerResponse: Codable {
    let code: Int
    let componentID: String
    let data: FansAgeDistriInnerData
    let meta: String
    let subCode: Int
}

// MARK: - 内部数据
struct FansAgeDistriInnerData: Codable {
    let series: [FansAgeDistriSeries]
}

// MARK: - 系列数据
struct FansAgeDistriSeries: Codable {
    let name: String    // 年龄段（如："18-23岁"）
    let value: String   // 百分比值（如："0.58"）
    
    // 转换为数值的便利方法
    var percentage: Double {
        return Double(value) ?? 0
    }
}
