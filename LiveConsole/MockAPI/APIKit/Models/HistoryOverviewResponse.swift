//
//  HistoryOverviewResponse.swift
//  MockAnchor
//
//  Created by 郭炜 on 2024/12/5.
//

import UIKit

/**
 {
     "data": {
         "data": "{\"code\":0,\"componentID\":\"cgue8drc77u9eokb3j10\",\"data\":{\"series\":[{\"avgWatchDuration\":\"0.89\",\"avgWatchDurationCompare7dDiff\":\"0.78\",\"commentUcnt\":\"1\",\"commentUcntCompare7dDiff\":\"1\",\"consumeUcnt\":\"0\",\"consumeUcntCompare7dDiff\":\"0\",\"duration\":\"12分钟29秒\",\"earnScore\":\"0\",\"earnScoreCompare7dDiff\":\"-2\",\"endTime\":\"2024-12-03 10:06:45\",\"fansAvgWatchDuration\":\"11.38\",\"fansCommentUcnt\":\"1\",\"fansConsumeUcnt\":\"0\",\"fansLikeCnt\":\"0\",\"fansWatchUcnt\":\"1\",\"followUcnt\":\"0\",\"followUcntCompare7dDiff\":\"0\",\"likeCnt\":\"0\",\"likeCntCompare7dDiff\":\"0\",\"liveStatus\":\"4\",\"lnkSuitScore\":\"0\",\"starGuardEarnScore\":\"0\",\"starGuardEarnScoreCompare7dDiff\":\"0\",\"starGuardUcnt\":\"0\",\"starGuardUcntCompare7dDiff\":\"0\",\"startTime\":\"2024-12-03 09:54:16\",\"subscribeIncome\":\"0\",\"title\":\"橙心好物正在直播\",\"unfollowUcnt\":\"0\",\"watchUcnt\":\"13\",\"watchUcntCompare7dDiff\":\"-13\"}]},\"meta\":\"{\\\"field\\\":[{\\\"key\\\":\\\"earnScore\\\",\\\"title\\\":\\\"收获音浪\\\",\\\"_showType\\\":\\\"number\\\",\\\"unit\\\":\\\"\\\",\\\"tip\\\":\\\"直播收到的音浪数，如果直播中含聊天室连线收入、传送门奖励、付费连线、互动玩法，主播收入比例会有所差异，详情请在主播账单中查看\\\"},{\\\"key\\\":\\\"consumeUcnt\\\",\\\"title\\\":\\\"送礼人数\\\",\\\"_showType\\\":\\\"number\\\",\\\"unit\\\":\\\"\\\",\\\"tip\\\":\\\"直播观众中有送礼行为的总人数\\\"},{\\\"key\\\":\\\"fansConsumeUcnt\\\",\\\"title\\\":\\\"粉丝\\\",\\\"_showType\\\":\\\"number\\\",\\\"unit\\\":\\\"\\\"},{\\\"key\\\":\\\"watchUcnt\\\",\\\"title\\\":\\\"观看人数\\\",\\\"_showType\\\":\\\"number\\\",\\\"unit\\\":\\\"\\\",\\\"tip\\\":\\\"观看过直播的总人数\\\"},{\\\"key\\\":\\\"fansWatchUcnt\\\",\\\"title\\\":\\\"粉丝\\\",\\\"_showType\\\":\\\"number\\\",\\\"unit\\\":\\\"\\\"},{\\\"key\\\":\\\"avgWatchDuration\\\",\\\"title\\\":\\\"人均观看时长\\\",\\\"_showType\\\":\\\"number\\\",\\\"unit\\\":\\\"分钟\\\",\\\"tip\\\":\\\"直播观众平均观看直播的时长\\\"},{\\\"key\\\":\\\"fansAvgWatchDuration\\\",\\\"title\\\":\\\"粉丝\\\",\\\"_showType\\\":\\\"number\\\",\\\"unit\\\":\\\"分钟\\\"},{\\\"key\\\":\\\"commentUcnt\\\",\\\"title\\\":\\\"评论人数\\\",\\\"_showType\\\":\\\"number\\\",\\\"unit\\\":\\\"\\\"},{\\\"key\\\":\\\"fansCommentUcnt\\\",\\\"title\\\":\\\"粉丝\\\",\\\"_showType\\\":\\\"number\\\",\\\"unit\\\":\\\"\\\"},{\\\"key\\\":\\\"likeCnt\\\",\\\"title\\\":\\\"点赞人数\\\",\\\"_showType\\\":\\\"number\\\",\\\"unit\\\":\\\"\\\"},{\\\"key\\\":\\\"fansLikeCnt\\\",\\\"title\\\":\\\"粉丝\\\",\\\"_showType\\\":\\\"number\\\",\\\"unit\\\":\\\"\\\"},{\\\"key\\\":\\\"followUcnt\\\",\\\"title\\\":\\\"新增粉丝\\\",\\\"_showType\\\":\\\"number\\\",\\\"unit\\\":\\\"\\\",\\\"tip\\\":\\\"通过直播关注的粉丝数\\\"},{\\\"key\\\":\\\"unfollowUcnt\\\",\\\"title\\\":\\\"取关人数\\\",\\\"_showType\\\":\\\"number\\\",\\\"unit\\\":\\\"\\\"},{\\\"key\\\":\\\"coverUri\\\",\\\"title\\\":\\\"封面URI\\\",\\\"_showType\\\":\\\"number\\\",\\\"unit\\\":\\\"\\\"},{\\\"key\\\":\\\"title\\\",\\\"title\\\":\\\"标题\\\",\\\"_showType\\\":\\\"number\\\",\\\"unit\\\":\\\"\\\"},{\\\"key\\\":\\\"startTime\\\",\\\"title\\\":\\\"开始时间\\\",\\\"_showType\\\":\\\"number\\\",\\\"unit\\\":\\\"\\\"},{\\\"key\\\":\\\"endTime\\\",\\\"title\\\":\\\"结束时间\\\",\\\"_showType\\\":\\\"number\\\",\\\"unit\\\":\\\"\\\"},{\\\"key\\\":\\\"duration\\\",\\\"title\\\":\\\"指标时长\\\",\\\"_showType\\\":\\\"number\\\",\\\"unit\\\":\\\"分钟\\\"}],\\\"packages\\\":[{\\\"group\\\":[{\\\"title\\\":\\\"收获音浪\\\",\\\"keys\\\":[\\\"earnScore\\\"]},{\\\"title\\\":\\\"送礼人数\\\",\\\"keys\\\":[\\\"consumeUcnt\\\",\\\"fansConsumeUcnt\\\"]},{\\\"title\\\":\\\"观看人数\\\",\\\"keys\\\":[\\\"watchUcnt\\\",\\\"fansWatchUcnt\\\"]},{\\\"title\\\":\\\"人均观看时长\\\",\\\"keys\\\":[\\\"avgWatchDuration\\\",\\\"fansAvgWatchDuration\\\"]}]}],\\\"componentID\\\":\\\"meta_cgue92bc77u5o35v65fg\\\"}\"}"
     },
     "extra": {
         "now": 1733194969921
     },
     "status_code": 0
 }
 */

// MARK: - 直播详情数据分析总览
struct HistoryOverviewResponse: Codable {
    let data: HistoryOverviewDataWrapper
    let extra: HistoryOverviewExtra
    let statusCode: Int
    
    enum CodingKeys: String, CodingKey {
        case data
        case extra
        case statusCode = "status_code"
    }
}

// MARK: - Data包装层
struct HistoryOverviewDataWrapper: Codable {
    let data: String // JSON字符串
    
    // 解析内部JSON字符串的方法
    func parseInnerData() -> HistoryOverviewInnerData? {
        guard let jsonData = data.data(using: .utf8) else { return nil }
        return try? JSONDecoder().decode(HistoryOverviewInnerData.self, from: jsonData)
    }
}

// MARK: - 内部数据结构
struct HistoryOverviewInnerData: Codable {
    let code: Int
    let componentID: String
    let data: HistoryOverviewContent
    let meta: String // 元数据JSON字符串
    
    enum CodingKeys: String, CodingKey {
        case code
        case componentID = "componentID"
        case data
        case meta
    }
}

// MARK: - 数据内容
struct HistoryOverviewContent: Codable {
    let series: [HistoryOverviewStats]
}

// MARK: - 统计数据
struct HistoryOverviewStats: Codable {
    // 基础信息
    let title: String?
    let startTime: String?
    let endTime: String?
    let duration: String?
    let liveStatus: String?
    
    // 观众数据
    let watchUcnt: String?
    let watchUcntCompare7dDiff: String?
    let avgWatchDuration: String?
    let avgWatchDurationCompare7dDiff: String?
    
    // 互动数据
    let commentUcnt: String?
    let commentUcntCompare7dDiff: String?
    let likeCnt: String?
    let likeCntCompare7dDiff: String?
    
    // 收益数据
    let earnScore: String?
    let earnScoreCompare7dDiff: String?
    let consumeUcnt: String?
    let consumeUcntCompare7dDiff: String?
    
    // 粉丝数据
    let followUcnt: String?
    let followUcntCompare7dDiff: String?
    let unfollowUcnt: String?
    
    // 粉丝互动数据
    let fansWatchUcnt: String?
    let fansAvgWatchDuration: String?
    let fansCommentUcnt: String?
    let fansConsumeUcnt: String?
    let fansLikeCnt: String?
    
    // 守护相关
    let starGuardUcnt: String?
    let starGuardUcntCompare7dDiff: String?
    let starGuardEarnScore: String?
    let starGuardEarnScoreCompare7dDiff: String?
    
    // 其他数据
    let subscribeIncome: String?
    let lnkSuitScore: String?
}

// MARK: - Extra
struct HistoryOverviewExtra: Codable {
    let now: Int64
}
