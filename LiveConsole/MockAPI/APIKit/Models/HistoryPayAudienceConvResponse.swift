//
//  HistoryPayAudienceConvResponse.swift
//  MockAnchor
//
//  Created by 郭炜 on 2024/12/5.
//

import UIKit

/**
 {
     "data": {
         "data": "{\"code\":0,\"componentID\":\"cgv3uqbc77u9eokb3jt0\",\"data\":{\"series\":[{\"enterRoomRatio\":\"26.5625\",\"enterRoomRatio7d\":\"51.61290322580645\",\"enterRoomUV\":\"17\",\"payRatio\":\"5.88235294117647\",\"payRatio7d\":\"0\",\"payUV\":\"1\",\"showUV\":\"64\"}]},\"meta\":\"{\\\"field\\\":[{\\\"key\\\":\\\"showUV\\\",\\\"title\\\":\\\"曝光展现人数\\\",\\\"_showType\\\":\\\"number\\\",\\\"unit\\\":\\\"\\\"},{\\\"key\\\":\\\"enterRoomUV\\\",\\\"title\\\":\\\"进直播间人数\\\",\\\"_showType\\\":\\\"number\\\",\\\"unit\\\":\\\"\\\"},{\\\"key\\\":\\\"payUV\\\",\\\"title\\\":\\\"打赏送礼人数\\\",\\\"_showType\\\":\\\"number\\\",\\\"unit\\\":\\\"\\\"},{\\\"key\\\":\\\"enterRoomRatio\\\",\\\"title\\\":\\\"进直播间转化率\\\",\\\"_showType\\\":\\\"number\\\",\\\"unit\\\":\\\"%\\\"},{\\\"key\\\":\\\"payRatio\\\",\\\"title\\\":\\\"打赏送礼转化率\\\",\\\"_showType\\\":\\\"number\\\",\\\"unit\\\":\\\"%\\\"},{\\\"key\\\":\\\"enterRoomRatio7d\\\",\\\"title\\\":\\\"近7日中位数\\\",\\\"_showType\\\":\\\"number\\\",\\\"unit\\\":\\\"%\\\",\\\"type\\\":\\\"trend\\\"},{\\\"key\\\":\\\"payRatio7d\\\",\\\"title\\\":\\\"近7日中位数\\\",\\\"_showType\\\":\\\"number\\\",\\\"unit\\\":\\\"%\\\",\\\"type\\\":\\\"trend\\\"}],\\\"packages\\\":[{\\\"title\\\":\\\"曝光展现\\\",\\\"keys\\\":[\\\"showUV\\\",\\\"enterRoomRatio\\\",\\\"enterRoomRatio7d\\\"]},{\\\"title\\\":\\\"进直播间\\\",\\\"keys\\\":[\\\"enterRoomUV\\\",\\\"payRatio\\\",\\\"payRatio7d\\\"]},{\\\"title\\\":\\\"打赏送礼\\\",\\\"keys\\\":[\\\"payUV\\\",\\\"\\\",\\\"\\\"]}],\\\"componentID\\\":\\\"meta_cgv3vjjc77ucrn6s7j3g\\\"}\"}"
     },
     "extra": {
         "now": 1733384228992
     },
     "status_code": 0
 }
 */

// MARK: - 观看付费转化
struct HistoryPayAudienceConvResponse: Codable {
    let statusCode: Int?
    let data: HistoryPayAudienceData?
    let extra: HistoryPayAudienceExtra?
    
    enum CodingKeys: String, CodingKey {
        case statusCode = "status_code"
        case data
        case extra
    }
}

// MARK: - Data
struct HistoryPayAudienceData: Codable {
    let data: String?
    
    var parsedData: HistoryPayAudienceInnerData? {
        guard let data = data,
              let jsonData = data.data(using: .utf8) else { return nil }
        return try? JSONDecoder().decode(HistoryPayAudienceInnerData.self, from: jsonData)
    }
}

// MARK: - InnerData
struct HistoryPayAudienceInnerData: Codable {
    let code: Int?
    let componentID: String?
    let data: HistoryPayAudienceSeriesData?
    let meta: String?
    
    var parsedMeta: HistoryPayAudienceMeta? {
        guard let meta = meta,
              let jsonData = meta.data(using: .utf8) else { return nil }
        return try? JSONDecoder().decode(HistoryPayAudienceMeta.self, from: jsonData)
    }
    
    enum CodingKeys: String, CodingKey {
        case code
        case componentID = "componentID"
        case data
        case meta
    }
}

// MARK: - SeriesData
struct HistoryPayAudienceSeriesData: Codable {
    let series: [HistoryPayAudienceSeries]?
}

// MARK: - Series
struct HistoryPayAudienceSeries: Codable {
    let enterRoomRatio: String?
    let enterRoomRatio7d: String?
    let enterRoomUV: String?
    let payRatio: String?
    let payRatio7d: String?
    let payUV: String?
    let showUV: String?
}

// MARK: - Meta
struct HistoryPayAudienceMeta: Codable {
    let field: [HistoryPayAudienceField]?
    let packages: [HistoryPayAudiencePackage]?
    let componentID: String?
}

// MARK: - Field
struct HistoryPayAudienceField: Codable {
    let key: String?
    let title: String?
    let showType: String?
    let unit: String?
    let type: String?
    
    enum CodingKeys: String, CodingKey {
        case key
        case title
        case showType = "_showType"
        case unit
        case type
    }
}

// MARK: - Package
struct HistoryPayAudiencePackage: Codable {
    let title: String?
    let keys: [String]?
}

// MARK: - Extra
struct HistoryPayAudienceExtra: Codable {
    let now: Int64?
}
