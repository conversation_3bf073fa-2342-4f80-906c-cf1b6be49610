//
//  MessageListResponse.swift
//  MockAnchor
//
//  Created by 郭炜 on 2024/12/5.
//

import UIKit

/**
 {"status_code":0,"message":"success","data":{"need_ack":false,"payload":[10,241,4,10,11,82,111,111,109,77,101,115,115,97,103,101,18,165,4,10,47,10,18,87,101,98,99,97,115,116,82,111,111,109,77,101,115,115,97,103,101,16,183,145,191,197,238,153,157,167,103,24,165,150,137,188,163,129,154,167,103,32,132,145,225,211,184,50,18,239,3,230,172,162,232,191,142,230,157,165,229,136,176,231,155,180,230,146,173,233,151,180,239,188,129,230,138,150,233,159,179,228,184,165,231,166,129,230,156,170,230,136,144,229,185,180,228,186,186,231,155,180,230,146,173,227,128,129,230,137,147,232,181,143,230,136,150,229,144,145,230,156,170,230,136,144,229,185,180,228,186,186,233,148,128,229,148,174,233,133,146,231,177,187,229,149,134,229,147,129,227,128,130,232,139,165,228,184,187,230,146,173,233,148,128,229,148,174,233,133,146,231,177,187,229,149,134,229,147,129,239,188,140,232,175,183,230,156,170,230,136,144,229,185,180,228,186,186,229,156,168,231,155,145,230,138,164,228,186,186,233,153,170,229,144,140,228,184,139,232,167,130,231,156,139,227,128,130,231,155,180,230,146,173,233,151,180,229,134,133,228,184,165,231,166,129,229,135,186,231,142,176,232,191,157,230,179,149,232,191,157,232,167,132,227,128,129,228,189,142,228,191,151,232,137,178,230,131,133,227,128,129,229,144,184,231,131,159,233,133,151,233,133,146,227,128,129,228,186,186,232,186,171,228,188,164,229,174,179,231,173,137,229,134,133,229,174,185,227,128,130,229,166,130,228,184,187,230,146,173,229,156,168,231,155,180,230,146,173,228,184,173,228,187,165,228,184,141,229,189,147,230,150,185,229,188,143,232,175,177,229,175,188,230,137,147,232,181,143,227,128,129,231,167,129,228,184,139,228,186,164,230,152,147,239,188,140,232,175,183,232,176,168,230,133,142,229,136,164,230,150,173,228,187,165,233,152,178,228,186,186,232,186,171,232,180,162,228,186,167,230,141,159,229,164,177,227,128,130,232,180,173,228,185,176,229,149,134,229,147,129,232,175,183,231,130,185,229,135,187,228,184,139,230,150,185,232,180,173,231,137,169,232,189,166,230,140,137,233,146,174,239,188,140,232,175,183,229,139,191,231,167,129,228,184,139,228,186,164,230,152,147,227,128,130,232,175,183,229,164,167,229,174,182,230,179,168,230,132,143,232,180,162,228,186,167,229,174,137,229,133,168,239,188,140,232,176,168,233,152,178,231,189,145,231,187,156,232,175,136,233,170,151,227,128,130,40,1,24,183,145,191,197,238,153,157,167,103,74,30,10,20,104,105,115,116,111,114,121,95,99,111,109,109,101,110,116,95,116,121,112,101,18,6,110,111,114,109,97,108,74,16,10,11,116,111,112,95,109,101,115,115,97,103,101,18,1,49,18,31,100,45,49,95,117,45,49,95,104,45,49,95,116,45,49,55,51,51,49,57,52,57,53,57,48,50,50,95,114,45,49,24,232,7,32,174,145,225,211,184,50,42,158,1,105,110,116,101,114,110,97,108,95,115,114,99,58,100,105,109,124,119,115,115,95,112,117,115,104,95,114,111,111,109,95,105,100,58,55,52,52,52,48,48,49,54,50,55,49,56,48,50,53,54,48,51,55,124,119,115,115,95,112,117,115,104,95,100,105,100,58,51,51,56,57,49,50,52,57,52,54,52,51,56,48,57,48,124,102,105,114,115,116,95,114,101,113,95,109,115,58,49,55,51,51,49,57,52,57,53,56,57,50,53,124,102,101,116,99,104,95,116,105,109,101,58,49,55,51,51,49,57,52,57,53,57,48,50,50,124,115,101,113,58,49,124,119,115,115,95,105,110,102,111,58,48,45,48,45,48,45,48,90,7,117,45,49,95,100,45,49],"operation_message":[],"messages":[{"payload":"Ci8KEldlYmNhc3RSb29tTWVzc2FnZRC3kb/F7pmdp2cYpZaJvKOBmqdnIISR4dO4MhLvA+asoui/juadpeWIsOebtOaSremXtO+8geaKlumfs+S4peemgeacquaIkOW5tOS6uuebtOaSreOAgeaJk+i1j+aIluWQkeacquaIkOW5tOS6uumUgOWUrumFkuexu+WVhuWTgeOAguiLpeS4u+aSremUgOWUrumFkuexu+WVhuWTge+8jOivt+acquaIkOW5tOS6uuWcqOebkeaKpOS6uumZquWQjOS4i+ingueci+OAguebtOaSremXtOWGheS4peemgeWHuueOsOi/neazlei/neinhOOAgeS9juS/l+iJsuaDheOAgeWQuOeDn+mFl+mFkuOAgeS6uui6q+S8pOWus+etieWGheWuueOAguWmguS4u+aSreWcqOebtOaSreS4reS7peS4jeW9k+aWueW8j+ivseWvvOaJk+i1j+OAgeengeS4i+S6pOaYk++8jOivt+iwqOaFjuWIpOaWreS7pemYsuS6uui6q+i0ouS6p+aNn+WkseOAgui0reS5sOWVhuWTgeivt+eCueWHu+S4i+aWuei0reeJqei9puaMiemSru+8jOivt+WLv+engeS4i+S6pOaYk+OAguivt+Wkp+WutuazqOaEj+i0ouS6p+WuieWFqO+8jOiwqOmYsue9kee7nOiviOmql+OAgigB","msg_id":"7444015666105927863","msg_type":0,"offset":"0","method":"RoomMessage"}],"cursor":"d-1_u-1_h-1_t-1733194959022_r-1","curr_rtt":"107","song_suggest":null,"now":"1733194959022","heartbeat_duration":"0","fetch_type":0,"route_params":{},"push_server":"","fetch_interval":"1000","internal_ext":"internal_src:dim|wss_push_room_id:7444001627180256037|wss_push_did:3389124946438090|first_req_ms:1733194958925|fetch_time:1733194959022|seq:1|wss_info:0-0-0-0"},"extra":{"now":1733194959031}}
 */

// MARK: - 弹幕数据模型
struct MessageListResponse: Codable {
    let statusCode: Int
    let message: String
    let data: MessageListData
    let extra: MessageListExtra
    
    enum CodingKeys: String, CodingKey {
        case statusCode = "status_code"
        case message
        case data
        case extra
    }
}

struct MessageListData: Codable {
    let operationMessage: [String]
    let songSuggest: String?
    let routeParams: [String: String]
    let heartbeatDuration: String
    let now: String
    let fetchType: Int
    let needAck: Bool
    let payload: [UInt8]
    let currRtt: String
    let cursor: String
    let fetchInterval: String
    let pushServer: String
    let messages: [MessageWrapper]
    let internalExt: String
    
    enum CodingKeys: String, CodingKey {
        case operationMessage = "operation_message"
        case songSuggest = "song_suggest"
        case routeParams = "route_params"
        case heartbeatDuration = "heartbeat_duration"
        case now
        case fetchType = "fetch_type"
        case needAck = "need_ack"
        case payload
        case currRtt = "curr_rtt"
        case cursor
        case fetchInterval = "fetch_interval"
        case pushServer = "push_server"
        case messages
        case internalExt = "internal_ext"
    }
}

struct MessageWrapper: Codable {
    let offset: String
    let method: String
    let payload: String
    let msgId: String
    let msgType: Int
    
    enum CodingKeys: String, CodingKey {
        case offset
        case method
        case payload
        case msgId = "msg_id"
        case msgType = "msg_type"
    }
    
    var decodedMessage: Message? {
        guard let data = Data(base64Encoded: payload) else { return nil }
        return try? JSONDecoder().decode(Message.self, from: data)
    }
}

struct Message: Codable {
    let content: String
    let user: MessageUser?
    let createTime: Int64?
    let prediction: MessagePrediction?
    
    enum CodingKeys: String, CodingKey {
        case content
        case user
        case createTime = "create_time"
        case prediction
    }
}

struct MessageUser: Codable {
    let nickname: String
    let userId: String
    let avatarThumb: MessageAvatar?
    
    enum CodingKeys: String, CodingKey {
        case nickname
        case userId = "user_id"
        case avatarThumb = "avatar_thumb"
    }
}

struct MessageAvatar: Codable {
    let urlList: [String]
    
    enum CodingKeys: String, CodingKey {
        case urlList = "url_list"
    }
}

struct MessageListExtra: Codable {
    let now: Int64
}

struct MessagePrediction: Codable {
    let informationRoomScore: String?
    let informationGlobalScore: String?
    let informationUserScore: String?
    
    enum CodingKeys: String, CodingKey {
        case informationRoomScore = "information_room_score"
        case informationGlobalScore = "information_global_score"
        case informationUserScore = "information_user_score"
    }
}
