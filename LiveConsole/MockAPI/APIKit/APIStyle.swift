//
//  APIStyle.swift
//  MockAnchor
//
//  Created by 郭炜 on 2024/12/3.
//

import UIKit

/// API 接口列表
enum APIStyle {
    // MARK: - 首页
    case liveStatus
    case anchorBaseInfo
    case baseLivingData(aid: String, roomId: String)
    case baseHistoryData(aid: String, beforeDay: String)
    case fansAnalysis(aid: String)
    case fansAgeDistribution(aid: String, limit: String)
    case fansGenterDistribution(aid: String)
    case fansInterest(aid: String, limit: String)
    case fansTrend(aid: String, startDate: String, endDate: String)
    
    // MARK: - 直播大屏
    case entrance(aid: String, roomId: String, roomStatus: String)
    case overview(aid: String, roomId: String, roomStatus: String)
    case conversion(aid: String, roomId: String, roomStatus: String)
    case message(roomId: String, cursor: String?, last_rtt: String?, internal_ext: String?)
    case sendMessage(aid: String, roomId: String, content: String, type: String)
    
    // MARK: - 直播复盘
    case historyList(aid: String, devicePlatform: String, versionName: String, deviceType: String, startDate: String, endDate: String)
    case roomInfo(roomId: String, isLive: String)
    case replyOverview(aid: String, devicePlatform: String, versionName: String, deviceType: String, roomId: String)
    case keyFragment(aid: String, devicePlatform: String, versionName: String, deviceType: String, roomId: String)
    case statsTopList(rankType: String, aid: String, devicePlatform: String, versionName: String, deviceType: String, roomId: String, cid: String)
    case replyEntrance(aid: String, devicePlatform: String, versionName: String, deviceType: String, roomId: String)
    case newFansConvert(aid: String, devicePlatform: String, versionName: String, deviceType: String, roomId: String)
    case audiencePayConvert(aid: String, devicePlatform: String, versionName: String, deviceType: String, roomId: String)
    
    // MARK: - 直播数据
    case liveDataBase(aid: String, startDate: String, endDate: String)
    
    // MARK: - API标识符
    private var identifier: String {
        switch self {
        case .liveStatus: return "liveStatus"
        case .anchorBaseInfo: return "anchorBaseInfo"
        case .baseLivingData: return "baseLivingData"
        case .baseHistoryData: return "baseHistoryData"
        case .fansAnalysis: return "fansAnalysis"
        case .fansAgeDistribution: return "fansAgeDistribution"
        case .fansGenterDistribution: return "fansGenterDistribution"
        case .fansInterest: return "fansInterest"
        case .fansTrend: return "fansTrend"
        case .entrance: return "entrance"
        case .overview: return "overview"
        case .conversion: return "conversion"
        case .message: return "message"
        case .sendMessage: return "sendMessage"
        case .historyList: return "historyList"
        case .roomInfo: return "roomInfo"
        case .replyOverview: return "replyOverview"
        case .keyFragment: return "keyFragment"
        case .statsTopList: return "statsTopList"
        case .replyEntrance: return "replyEntrance"
        case .newFansConvert: return "newFansConvert"
        case .audiencePayConvert: return "audiencePayConvert"
        case .liveDataBase: return "liveDataBase"
        }
    }
    
    // MARK: - API URL
    var apiUrl: String {
        return APIConfigurationManager.shared.getEndpoint(identifier)?.url ?? ""
    }
    
    // MARK: - 请求参数
    var params: [String: String] {
        var parameters = [String: String]()
        
        // 获取配置的默认参数
        if let config = APIConfigurationManager.shared.getEndpoint(identifier) {
            parameters.merge(config.params.default) { (_, new) in new }
        }
        
        // 添加动态参数
        switch self {
        case .liveStatus, .anchorBaseInfo:
            break
            
        case .baseLivingData(let aid, let roomId):
            parameters["aid"] = aid
            parameters["roomID"] = roomId
            
        case .baseHistoryData(let aid, let beforeDay):
            parameters["aid"] = aid
            parameters["beforeDay"] = beforeDay
            
        case .fansAnalysis(let aid):
            parameters["aid"] = aid
            
        case .fansAgeDistribution(let aid, let limit):
            parameters["aid"] = aid
            parameters["limit"] = limit
            
        case .fansGenterDistribution(let aid):
            parameters["aid"] = aid
            
        case .fansInterest(let aid, let limit):
            parameters["aid"] = aid
            parameters["limit"] = limit
            
        case .fansTrend(let aid, let startDate, let endDate):
            parameters["aid"] = aid
            parameters["startDate"] = startDate
            parameters["endDate"] = endDate
            
        case .entrance(let aid, let roomId, let roomStatus),
             .overview(let aid, let roomId, let roomStatus),
             .conversion(let aid, let roomId, let roomStatus):
            parameters["aid"] = aid
            parameters["roomID"] = roomId
            parameters["_roomStatus"] = roomStatus
            
        case .message(let roomId, let cursor, let last_rtt, let internal_ext):
            parameters["room_id"] = roomId
            if let cursor = cursor {
                parameters["cursor"] = cursor
            }
            if let last_rtt = last_rtt {
                parameters["last_rtt"] = last_rtt
            }
            if let internal_ext = internal_ext {
                parameters["internal_ext"] = internal_ext
            }
            
        case .sendMessage(let aid, let roomId, let content, let type):
            parameters["aid"] = aid
            parameters["room_id"] = roomId
            parameters["content"] = content
            parameters["type"] = type
            
        case .historyList(let aid, let devicePlatform, let versionName, let deviceType, let startDate, let endDate):
            parameters["aid"] = aid
            parameters["device_platform"] = devicePlatform
            parameters["version_name"] = versionName
            parameters["device_type"] = deviceType
            parameters["startDate"] = startDate
            parameters["endDate"] = endDate
            
        case .roomInfo(let roomId, let isLive):
            parameters["room_id"] = roomId
            parameters["is_live"] = isLive
            
        case .replyOverview(let aid, let devicePlatform, let versionName, let deviceType, let roomId),
             .keyFragment(let aid, let devicePlatform, let versionName, let deviceType, let roomId),
             .replyEntrance(let aid, let devicePlatform, let versionName, let deviceType, let roomId),
             .newFansConvert(let aid, let devicePlatform, let versionName, let deviceType, let roomId),
             .audiencePayConvert(let aid, let devicePlatform, let versionName, let deviceType, let roomId):
            parameters["aid"] = aid
            parameters["device_platform"] = devicePlatform
            parameters["version_name"] = versionName
            parameters["device_type"] = deviceType
            parameters["roomID"] = roomId
            
        case .statsTopList(let rankType, let aid, let devicePlatform, let versionName, let deviceType, let roomId, let cid):
            parameters["rankType"] = rankType
            parameters["aid"] = aid
            parameters["device_platform"] = devicePlatform
            parameters["version_name"] = versionName
            parameters["device_type"] = deviceType
            parameters["roomID"] = roomId
            parameters["cid"] = cid
            
        case .liveDataBase(let aid, let startDate, let endDate):
            parameters["aid"] = aid
            parameters["startDate"] = startDate
            parameters["endDate"] = endDate
        }
        
        return parameters
    }
    
    // MARK: - 请求类型
    var httpMethod: HttpMethod {
        if let config = APIConfigurationManager.shared.getEndpoint(identifier) {
            return config.method == "POST" ? .POST : .GET
        }
        return .GET
    }
}
