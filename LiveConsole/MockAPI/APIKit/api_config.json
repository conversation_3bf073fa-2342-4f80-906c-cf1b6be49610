{"version": "1.0.0", "apis": {"liveStatus": {"url": "https://anchor.douyin.com/webcast/api/platform_following/outside/room/v1/live_status", "method": "GET", "params": {"required": [], "optional": [], "default": {"msToken": "", "a_bogus": ""}}}, "anchorBaseInfo": {"url": "https://anchor.douyin.com/webcast/anchor_platform/api/v1/anchor_detail/get_anchor_card", "method": "GET", "params": {"required": [], "optional": [], "default": {"msToken": "", "a_bogus": ""}}}, "baseLivingData": {"url": "https://anchor.douyin.com/webcast/data/api/v1/component/lego/webcast_api/data/home/<USER>/base_data", "method": "GET", "params": {"required": ["aid", "roomId"], "optional": [], "default": {"msToken": "", "a_bogus": ""}}}, "baseHistoryData": {"url": "https://anchor.douyin.com/webcast/data/api/v1/component/lego/webcast_api/data/home/<USER>/base_data", "method": "GET", "params": {"required": ["aid", "beforeDay"], "optional": [], "default": {"msToken": "", "a_bogus": ""}}}, "fansAnalysis": {"url": "https://anchor.douyin.com/webcast/data/api/v1/component/lego/webcast_api/data/anchor/fans/analysis", "method": "GET", "params": {"required": ["aid"], "optional": [], "default": {"msToken": "", "a_bogus": ""}}}, "fansAgeDistribution": {"url": "https://anchor.douyin.com/webcast/data/api/v1/component/lego/webcast_api/data/anchor/fans/age_distribution", "method": "GET", "params": {"required": ["aid", "limit"], "optional": [], "default": {"msToken": "", "a_bogus": ""}}}, "fansGenterDistribution": {"url": "https://anchor.douyin.com/webcast/data/api/v1/component/lego/webcast_api/data/anchor/fans/gender_distribution", "method": "GET", "params": {"required": ["aid"], "optional": [], "default": {"msToken": "", "a_bogus": ""}}}, "fansInterest": {"url": "https://anchor.douyin.com/webcast/data/api/v1/component/lego/webcast_api/data/anchor/fans/interest_distribution", "method": "GET", "params": {"required": ["aid", "limit"], "optional": [], "default": {"msToken": "", "a_bogus": ""}}}, "fansTrend": {"url": "https://anchor.douyin.com/webcast/data/api/v1/component/lego/webcast_api/data/anchor/fans/trend", "method": "GET", "params": {"required": ["aid", "startDate", "endDate"], "optional": [], "default": {"msToken": "", "a_bogus": ""}}}, "entrance": {"url": "https://anchor.douyin.com/webcast/data/api/v1/component/lego/webcast_api/data/room/follow/entrance", "method": "GET", "params": {"required": ["aid", "roomId", "roomStatus"], "optional": [], "default": {"msToken": "", "a_bogus": ""}}}, "overview": {"url": "https://anchor.douyin.com/webcast/data/api/v1/component/lego/webcast_api/data/room/follow/overview", "method": "GET", "params": {"required": ["aid", "roomId", "roomStatus"], "optional": [], "default": {"msToken": "", "a_bogus": ""}}}, "conversion": {"url": "https://anchor.douyin.com/webcast/data/api/v1/component/lego/webcast_api/data/room/follow/conversion", "method": "GET", "params": {"required": ["aid", "roomId", "roomStatus"], "optional": [], "default": {"msToken": "", "a_bogus": ""}}}, "message": {"url": "https://anchor.douyin.com/webcast/api/platform_following/outside/room/v1/room_message", "method": "GET", "params": {"required": ["roomId"], "optional": ["cursor", "last_rtt", "internal_ext"], "default": {"fetch_method": "RoomMessage_WebcastRoomMessage_WebcastDiggMessage_WebcastSocialMessage_WebcastScreenChatMessage_WebcastControlMessage_WebcastFansclubMessage_WebcastMemberMessage_WebcastRoomVerifyMessage_WebcastChatMessage_WebcastGiftMessage_WebcastLikeMessage_WebcastRoomUserSeqMessage_WebcastEmojiChatMessage"}}}, "sendMessage": {"url": "https://anchor.douyin.com/webcast/anchor_platform/api/v1/following/chat/", "method": "POST", "params": {"required": ["aid", "roomId", "content", "type"], "optional": [], "default": {}}}, "historyList": {"url": "https://anchor.douyin.com/webcast/data/api/v1/component/lego/webcast_api/data/room/replay/history_list", "method": "GET", "params": {"required": ["aid", "devicePlatform", "versionName", "deviceType", "startDate", "endDate"], "optional": [], "default": {"msToken": "", "a_bogus": ""}}}, "roomInfo": {"url": "https://anchor.douyin.com/webcast/api/content_player/room/v1/get_room_info", "method": "GET", "params": {"required": ["roomId", "isLive"], "optional": [], "default": {"msToken": "", "a_bogus": ""}}}, "replyOverview": {"url": "https://anchor.douyin.com/webcast/data/api/v1/component/lego/webcast_api/data/room/replay/overview", "method": "GET", "params": {"required": ["aid", "devicePlatform", "versionName", "deviceType", "roomId"], "optional": [], "default": {"msToken": "", "a_bogus": ""}}}, "keyFragment": {"url": "https://anchor.douyin.com/webcast/data/api/v1/component/lego/webcast_api/data/room/replay/key_fragment", "method": "GET", "params": {"required": ["aid", "devicePlatform", "versionName", "deviceType", "roomId"], "optional": [], "default": {"msToken": "", "a_bogus": ""}}}, "statsTopList": {"url": "https://anchor.douyin.com/webcast/data/api/v1/component/lego/webcast_api/data/room/detail/room_stats_top_list", "method": "GET", "params": {"required": ["rankType", "aid", "devicePlatform", "versionName", "deviceType", "roomId", "cid"], "optional": [], "default": {"msToken": "", "a_bogus": ""}}}, "replyEntrance": {"url": "https://anchor.douyin.com/webcast/data/api/v1/component/lego/webcast_api/data/room/follow/entrance", "method": "GET", "params": {"required": ["aid", "devicePlatform", "versionName", "deviceType", "roomId"], "optional": [], "default": {"msToken": "", "a_bogus": ""}}}, "newFansConvert": {"url": "https://anchor.douyin.com/webcast/data/api/v1/component/lego/webcast_api/data/room/replay/new_fans_convert", "method": "GET", "params": {"required": ["aid", "devicePlatform", "versionName", "deviceType", "roomId"], "optional": [], "default": {"msToken": "", "a_bogus": ""}}}, "audiencePayConvert": {"url": "https://anchor.douyin.com/webcast/data/api/v1/component/lego/webcast_api/data/room/replay/audience_pay_convert", "method": "GET", "params": {"required": ["aid", "devicePlatform", "versionName", "deviceType", "roomId"], "optional": [], "default": {"msToken": "", "a_bogus": ""}}}, "liveDataBase": {"url": "https://anchor.douyin.com/webcast/data/api/v1/component/lego/webcast_api/data/room/live/base_data", "method": "GET", "params": {"required": ["aid", "startDate", "endDate"], "optional": [], "default": {"msToken": "", "a_bogus": ""}}}}}