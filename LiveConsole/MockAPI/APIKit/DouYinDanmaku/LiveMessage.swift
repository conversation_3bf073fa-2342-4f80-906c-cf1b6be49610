//
//  DouyinSite.swift
//  LivePlus
//
//  Created by simon on 28.10.24.
//

import Foundation

enum  LiveMessageType: Int, Codable {
    case CHAT // 聊天0
    case ONLINE // 在线人数1
    case SUPER_CHAT // 醒目留言2
    case SOCIAL// 关注3
    case MEMBER// 来了4
    case LIKE// 点赞5
    case GIFT// 礼物6
    case CONTROL// 控制7
    case ROOM_RANK// 排行8
    case ROOM_STATS// 直播间统计9
    case FANSCLUB// 粉丝团10
    case EMOJI_CHAT// 聊天表情包11
    
    var name: String {
        switch self {
        case .CHAT:
            return "聊天"
        case .ONLINE:
            return "在线数"
        case .SUPER_CHAT:
            return "醒目留言"
        case .SOCIAL:
            return "关注"
        case .MEMBER:
            return "来了"
        case .LIKE:
            return "点赞"
        case .GIFT:
            return "礼物"
        case .CONTROL:
            return "控制"
        case .ROOM_RANK:
            return "排行"
        case .ROOM_STATS:
            return "直播间统计"
        case .FANSCLUB:
            return "粉丝团"
        case .EMOJI_CHAT:
            return "聊天表情包"
        }
    }
}



struct LiveMessage: Codable {
    let method: String
    let type: LiveMessageType
    let userName: String?
    let color: LiveMessageColor
    var data: DouyinMessageData
    init(method: String, type: LiveMessageType, userName: String? = nil, color: LiveMessageColor,data:DouyinMessageData) {
        self.method = method
        self.type = type
        self.userName = userName
        self.data = data
        self.color = color
        self.data = data
    }
    
    
}



// 定义LiveMessageColor结构体，遵循Codable协议用于序列化
struct LiveMessageColor: Codable {
    let r: Int
    let g: Int
    let b: Int
    
    // 定义静态属性white
    static let white = LiveMessageColor(r: 255, g: 255, b: 255)
    
    // 定义静态方法numberToColor
    static func numberToColor(intColor: Int) throws -> LiveMessageColor {
        guard intColor >= 0 else {
            throw ColorConversionError.invalidColorValue
        }
        let hex = String(intColor, radix: 16).padding(toLength: 6, withPad: "0", startingAt: 0)
        guard hex.count == 6 else {
            return white
        }
        // 使用substring方法获取颜色分量
        let r = Int(hex.substring(with: hex.index(hex.startIndex, offsetBy: 0)..<hex.index(hex.startIndex, offsetBy: 2)), radix: 16)!
        let g = Int(hex.substring(with: hex.index(hex.startIndex, offsetBy: 2)..<hex.index(hex.startIndex, offsetBy: 4)), radix: 16)!
//        let b = Int(hex.substring(with: hex.index(hex.startIndex, offsetBy: 4)..<hex.endIndex)), radix: 16)!
        return LiveMessageColor(r: r, g: g, b: g)
    }


}

// 定义错误类型
enum ColorConversionError: Error {
    case invalidColorValue
}


struct LiveSuperChatMessage: Codable {
    let userName: String
    let face: String
    let message: String
    let price: Int
    let startTime: String
    let endTime: String
    let backgroundColor: String
    let backgroundBottomColor: String

}
