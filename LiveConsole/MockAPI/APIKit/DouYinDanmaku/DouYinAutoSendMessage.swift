//
//  MessageItem.swift
//  liveplusexplore
//
//  Created by 郭炜 on 2024/12/16.
//

import UIKit

class SendMessageManager {
    
    static let shared = SendMessageManager()
    
    /// 发送消息
    func sendMessage(msg: String, endBack: @escaping (Bool) -> Void) {
        guard let roomId = roomID else {
            print("---弹幕---未获取到开播信息")
            endBack(true)
            return
        }
        let aid = CookieInfo.shared.getAid()
        Task {
            do {
                let response: SendMessageResponse = try await RequestManager.shared.requestApi(style: .sendMessage(aid: aid, roomId: roomId, content: msg, type: "3"))
                print("---弹幕---发送弹幕成功：\(response)")
                endBack(true)
            } catch {
                print("---弹幕---\(msg) 发送弹幕失败：\(error.localizedDescription)")
                endBack(true)
            }
        } 
    }
}

class GiftStatisticsItem: NSObject {
    var giftName: String
    private var userCounts: [String: Int] = [:]
    // 添加总数属性
    private(set) var count: Int = 0
    
    init(giftName: String) {
        self.giftName = giftName
        super.init()
    }
    
    func addGift(from username: String) {
        userCounts[username] = (userCounts[username] ?? 0) + 1
        // 更新总数
        count += 1
    }
    
    func getTopUsers(limit: Int = 5) -> [String] {
        return userCounts.sorted { $0.value > $1.value }
            .prefix(limit)
            .map { $0.key }
    }
    
    func getAllUsers() -> [String] {
        return Array(userCounts.keys)
    }
}

class GiftStatistics: NSObject {
    private var giftItems: [String: GiftStatisticsItem] = [:]
    
    func addGift(name: String, from username: String) {
        let item = giftItems[name] ?? GiftStatisticsItem(giftName: name)
        item.addGift(from: username)
        giftItems[name] = item
    }
    
    func getAllGiftItems() -> [GiftStatisticsItem] {
        return Array(giftItems.values)
    }
    
    func clear() {
        giftItems.removeAll()
    }
}
