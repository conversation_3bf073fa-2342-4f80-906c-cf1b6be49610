//
//  DouyinDecode.swift
//  LivePlus
//
//  Created by simon on 21.11.24.
//

import Foundation
import SwiftProtobuf
import Gzip

var webRid: String? = nil
var roomId: String? = nil

class DouyinDecode: NSObject {
    public static let shared = DouyinDecode()
    
    func decodeMessage(data: Data) -> [LiveMessage]? {
        do {
            let payloadPackage = try Douyin_Response(serializedBytes: data)
            print("\(payloadPackage)")
            
            // 遍历所有消息
            var newMessages: [LiveMessage] = []
            for msg in payloadPackage.messagesList {
                if let message = processMessage(method: msg.method, payload: msg.payload) {
                    newMessages.append(message)
                }
            }
            return newMessages
        } catch {
            print("Failed to decode payloadPackage: \(error)")
        }
        return nil
    }
    
    private func processMessage(method: String, payload: Data) -> LiveMessage? {
        switch method {
        case "WebcastChatMessage":
            return unPackWebcastChatMessage(method: method, payload: payload)
        case "WebcastRoomUserSeqMessage":
            return unPackWebcastRoomUserSeqMessage(method: method, payload: payload)
        case "WebcastSocialMessage":
            return unPackWebcastSocialMessage(method: method, payload: payload)
        case "WebcastMemberMessage":
            return unPackWebcastMemberMessage(method: method, payload: payload)
        case "WebcastLikeMessage":
            return unPackWebcastLikeMessage(method: method, payload: payload)
        case "WebcastGiftMessage":
            return unPackWebcastGiftMessage(method: method, payload: payload)
        case "WebcastFansclubMessage":
            return unPackWebcastFansclubMessage(method: method, payload: payload)
        case "WebcastEmojiChatMessage":
            return unPackWebcastEmojiChatMessage(method: method, payload: payload)
        default:
            return nil
        }
    }
    
    private func unPackWebcastSocialMessage(method: String, payload: Data) -> LiveMessage? {
        guard let socialMessage = try? Douyin_SocialMessage(serializedBytes: payload) else {
            print("Failed to decode social message")
            return nil
        }
        
        let messagedata = DouyinMessageData(webRid: webRid,
                                            roomId: roomId,
                                            msgType: DouyinMessageType.SOCIAL.rawValue,
                                            user: User.create(user: socialMessage.user),
                                            socialInfo: SocialMessage.create(socialMessage: socialMessage))
        
        let message = LiveMessage(method: method,
                                  type: LiveMessageType.SOCIAL,
                                  userName: socialMessage.user.nickName,
                                  color: LiveMessageColor.white,
                                  data: messagedata)
        return message
    }
    
    private func unPackWebcastChatMessage(method: String, payload: Data) -> LiveMessage? {
        guard let chatMessage = try? Douyin_ChatMessage(serializedBytes: payload) else {
            print("Failed to decode chat message")
            return nil
        }
        
        let messagedata = DouyinMessageData(webRid: webRid,
                                            roomId: roomId,
                                            msgType: DouyinMessageType.CHAT.rawValue,
                                            user: User.create(user: chatMessage.user),
                                            chatInfo: ChatMessage.create(chatMessage: chatMessage))
        
        let message = LiveMessage(method: method,
                                  type: LiveMessageType.CHAT,
                                  userName: chatMessage.user.nickName,
                                  color: LiveMessageColor.white,
                                  data: messagedata)
        return message
    }
    
    private func unPackWebcastRoomUserSeqMessage(method: String, payload: Data) -> LiveMessage? {
        guard let roomUserSeqMessage = try? Douyin_RoomUserSeqMessage(serializedBytes: payload) else {
            print("Failed to decode room user seq message")
            return nil
        }
        
        let messagedata = DouyinMessageData(webRid:webRid,
                                            roomId: roomId,
                                            msgType: DouyinMessageType.ONLINE.rawValue,
                                            roomUserSeqInfo: RoomUserSeqMessage.create(roomUserSeqMessage: roomUserSeqMessage))
        
        let message = LiveMessage(method: method,
                                  type: LiveMessageType.ONLINE,
                                  color: LiveMessageColor.white,
                                  data: messagedata)
        return message
    }
    
    private func unPackWebcastMemberMessage(method: String, payload: Data) -> LiveMessage? {
        guard let memberMessage = try? Douyin_MemberMessage(serializedBytes: payload) else {
            print("Failed to decode member message")
            return nil
        }
        
        let actionCode = memberMessage.action
        
        
        let messagedata = DouyinMessageData(webRid: webRid,
                                            roomId: roomId,
                                            msgType: DouyinMessageType.MEMBER.rawValue,
                                            user: User.create(user: memberMessage.user),
                                            memberInfo: MemberMessage.create(memberMessage: memberMessage))
        
        let message = LiveMessage(method: method,
                                  type: LiveMessageType.MEMBER,
                                  userName: memberMessage.user.nickName,
                                  color: LiveMessageColor.white,
                                  data: messagedata)
        return message
    }
    
    private func unPackWebcastLikeMessage(method: String, payload: Data) -> LiveMessage? {
        guard let likeMessage = try? Douyin_LikeMessage(serializedBytes: payload) else {
            print("Failed to decode like message")
            return nil
        }
        
        let messagedata = DouyinMessageData(webRid: webRid,
                                            roomId: roomId,
                                            msgType: DouyinMessageType.LIKE.rawValue,
                                            user: User.create(user: likeMessage.user),
                                            likeInfo: LikeMessage.create(likeMessage: likeMessage))
        
        let message = LiveMessage(method: method,
                                  type: LiveMessageType.LIKE,
                                  userName: likeMessage.user.nickName,
                                  color: LiveMessageColor.white,
                                  data: messagedata)
        return message
    }
    
    private func unPackWebcastGiftMessage(method: String, payload: Data) -> LiveMessage? {
        guard let giftMessage = try? Douyin_GiftMessage(serializedBytes: payload) else {
            print("Failed to decode gift message")
            return nil
        }
        
        let messagedata = DouyinMessageData(webRid: webRid,
                                            roomId: roomId,
                                            msgType: DouyinMessageType.GIFT.rawValue,
                                            user: User.create(user: giftMessage.user),
                                            giftInfo: GiftMessage.create(giftMessage: giftMessage))
        
        let message = LiveMessage(method: method,
                                  type: LiveMessageType.GIFT,
                                  userName: giftMessage.user.nickName,
                                  color: LiveMessageColor.white,
                                  data: messagedata)
        
        return message
    }
    
    private func unPackWebcastFansclubMessage(method: String, payload: Data) -> LiveMessage? {
        guard let fansClubMessage = try? Douyin_FansclubMessage(serializedBytes: payload) else {
            print("Failed to decode fans club message")
            return nil
        }
        
        let messagedata = DouyinMessageData(webRid: webRid,
                                            roomId: roomId,
                                            msgType: DouyinMessageType.FANSCLUB.rawValue,
                                            user: User.create(user: fansClubMessage.user),
                                            fansclubInfo: FansclubMessage.create(fansclubMessage: fansClubMessage))
        
        let message = LiveMessage(method: method,
                                  type: LiveMessageType.FANSCLUB,
                                  userName: fansClubMessage.user.nickName,
                                  color: LiveMessageColor.white,
                                  data: messagedata)
        return message
    }
    
    private func unPackWebcastEmojiChatMessage(method: String, payload: Data) -> LiveMessage? {
        guard let emojiChatMessage = try? Douyin_EmojiChatMessage(serializedBytes: payload) else {
            print("Failed to decode emoji message")
            return nil
        }
        
        let messagedata = DouyinMessageData(webRid: webRid,
                                            roomId: roomId,
                                            msgType: DouyinMessageType.EMOJI_CHAT.rawValue,
                                            user: User.create(user: emojiChatMessage.user),
                                            emojiChatInfo: EmojiChatMessage.create(emojiChatMessage: emojiChatMessage))
        
        let message = LiveMessage(method: method,
                                  type: LiveMessageType.EMOJI_CHAT,
                                  userName: emojiChatMessage.user.nickName,
                                  color: LiveMessageColor.white,
                                  data: messagedata)
        return message
    }
}

// Helper extensions
//extension Data {
//    var bytes: [UInt8] { [UInt8](self) }
//    
//    var hexString: String {
//        bytes.map { String(format: "0x%02x ", $0) }.joined()
//    }
//}
