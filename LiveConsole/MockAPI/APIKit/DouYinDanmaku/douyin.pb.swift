// DO NOT EDIT.
// swift-format-ignore-file
// swiftlint:disable all
//
// Generated by the Swift generator plugin for the protocol buffer compiler.
// Source: douyin.proto
//
// For information on using the generated types, please see the documentation:
//   https://github.com/apple/swift-protobuf/

import Foundation
import SwiftProtobuf

// If the compiler emits an error on this type, it is because this file
// was generated by a version of the `protoc` Swift plug-in that is
// incompatible with the version of SwiftProtobuf to which you are linking.
// Please ensure that you are building against the same version of the API
// that was used to generate this file.
fileprivate struct _GeneratedWithProtocGenSwiftVersion: SwiftProtobuf.ProtobufAPIVersionCheck {
  struct _2: SwiftProtobuf.ProtobufAPIVersion_2 {}
  typealias Version = _2
}

enum Douyin_CommentTypeTag: SwiftProtobuf.Enum, Swift.CaseIterable {
  typealias RawValue = Int
  case unknown // = 0
  case star // = 1
  case UNRECOGNIZED(Int)

  init() {
    self = .unknown
  }

  init?(rawValue: Int) {
    switch rawValue {
    case 0: self = .unknown
    case 1: self = .star
    default: self = .UNRECOGNIZED(rawValue)
    }
  }

  var rawValue: Int {
    switch self {
    case .unknown: return 0
    case .star: return 1
    case .UNRECOGNIZED(let i): return i
    }
  }

  // The compiler won't synthesize support with the UNRECOGNIZED case.
  static let allCases: [Douyin_CommentTypeTag] = [
    .unknown,
    .star,
  ]

}

/// from https://github.com/scx567888/live-room-watcher/blob/master/src/main/proto/douyin_hack/webcast/im/RoomMsgTypeEnum.proto
enum Douyin_RoomMsgTypeEnum: SwiftProtobuf.Enum, Swift.CaseIterable {
  typealias RawValue = Int
  case defaultroommsg // = 0
  case ecomlivereplaysaveroommsg // = 1
  case consumerrelationroommsg // = 2
  case jumanjidataauthnotifymsg // = 3
  case vswelcomemsg // = 4
  case minorrefundmsg // = 5
  case paidliveroomnotifyanchormsg // = 6
  case hostteamsystemmsg // = 7
  case UNRECOGNIZED(Int)

  init() {
    self = .defaultroommsg
  }

  init?(rawValue: Int) {
    switch rawValue {
    case 0: self = .defaultroommsg
    case 1: self = .ecomlivereplaysaveroommsg
    case 2: self = .consumerrelationroommsg
    case 3: self = .jumanjidataauthnotifymsg
    case 4: self = .vswelcomemsg
    case 5: self = .minorrefundmsg
    case 6: self = .paidliveroomnotifyanchormsg
    case 7: self = .hostteamsystemmsg
    default: self = .UNRECOGNIZED(rawValue)
    }
  }

  var rawValue: Int {
    switch self {
    case .defaultroommsg: return 0
    case .ecomlivereplaysaveroommsg: return 1
    case .consumerrelationroommsg: return 2
    case .jumanjidataauthnotifymsg: return 3
    case .vswelcomemsg: return 4
    case .minorrefundmsg: return 5
    case .paidliveroomnotifyanchormsg: return 6
    case .hostteamsystemmsg: return 7
    case .UNRECOGNIZED(let i): return i
    }
  }

  // The compiler won't synthesize support with the UNRECOGNIZED case.
  static let allCases: [Douyin_RoomMsgTypeEnum] = [
    .defaultroommsg,
    .ecomlivereplaysaveroommsg,
    .consumerrelationroommsg,
    .jumanjidataauthnotifymsg,
    .vswelcomemsg,
    .minorrefundmsg,
    .paidliveroomnotifyanchormsg,
    .hostteamsystemmsg,
  ]

}

struct Douyin_Response: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var messagesList: [Douyin_Message] = []

  var cursor: String = String()

  var fetchInterval: UInt64 = 0

  var now: UInt64 = 0

  var internalExt: String = String()

  var fetchType: UInt32 = 0

  var routeParams: Dictionary<String,String> = [:]

  var heartbeatDuration: UInt64 = 0

  var needAck: Bool = false

  var pushServer: String = String()

  var liveCursor: String = String()

  var historyNoMore: Bool = false

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}
}

struct Douyin_Message: @unchecked Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var method: String = String()

  var payload: Data = Data()

  var msgID: Int64 = 0

  var msgType: Int32 = 0

  var offset: Int64 = 0

  var needWrdsStore: Bool = false

  var wrdsVersion: Int64 = 0

  var wrdsSubKey: String = String()

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}
}

///*
///•	Common common = 1;
///包含消息的通用信息，如消息ID、房间ID、创建时间等。
///•	User user = 2;
///发送表情的用户信息，存储在User类型中，包含发送者的详细信息。
///•	int64 emojiId = 3;
///表情的唯一标识符，表示具体使用的表情。
///•	Text emojiContent = 4;
///表情的文本内容，使用Text类型表示，可能包含富文本格式，如颜色、字体大小等。
///•	string defaultContent = 5;
///表情的默认文本内容，当无法显示自定义内容时使用。
///•	Image backgroundImage = 6;
///表情的背景图片，以Image类型表示，可能用于装饰表情消息的背景。
///•	bool fromIntercom = 7;
///表示消息是否来自对讲机功能，true表示消息来源于此。
///•	bool intercomHideUserCard = 8;
///当消息来自对讲机时，是否隐藏用户卡片信息，true表示隐藏。
struct Douyin_EmojiChatMessage: @unchecked Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var common: Douyin_Common {
    get {return _storage._common ?? Douyin_Common()}
    set {_uniqueStorage()._common = newValue}
  }
  /// Returns true if `common` has been explicitly set.
  var hasCommon: Bool {return _storage._common != nil}
  /// Clears the value of `common`. Subsequent reads from it will return its default value.
  mutating func clearCommon() {_uniqueStorage()._common = nil}

  var user: Douyin_User {
    get {return _storage._user ?? Douyin_User()}
    set {_uniqueStorage()._user = newValue}
  }
  /// Returns true if `user` has been explicitly set.
  var hasUser: Bool {return _storage._user != nil}
  /// Clears the value of `user`. Subsequent reads from it will return its default value.
  mutating func clearUser() {_uniqueStorage()._user = nil}

  var emojiID: Int64 {
    get {return _storage._emojiID}
    set {_uniqueStorage()._emojiID = newValue}
  }

  var emojiContent: Douyin_Text {
    get {return _storage._emojiContent ?? Douyin_Text()}
    set {_uniqueStorage()._emojiContent = newValue}
  }
  /// Returns true if `emojiContent` has been explicitly set.
  var hasEmojiContent: Bool {return _storage._emojiContent != nil}
  /// Clears the value of `emojiContent`. Subsequent reads from it will return its default value.
  mutating func clearEmojiContent() {_uniqueStorage()._emojiContent = nil}

  var defaultContent: String {
    get {return _storage._defaultContent}
    set {_uniqueStorage()._defaultContent = newValue}
  }

  var backgroundImage: Douyin_Image {
    get {return _storage._backgroundImage ?? Douyin_Image()}
    set {_uniqueStorage()._backgroundImage = newValue}
  }
  /// Returns true if `backgroundImage` has been explicitly set.
  var hasBackgroundImage: Bool {return _storage._backgroundImage != nil}
  /// Clears the value of `backgroundImage`. Subsequent reads from it will return its default value.
  mutating func clearBackgroundImage() {_uniqueStorage()._backgroundImage = nil}

  var fromIntercom: Bool {
    get {return _storage._fromIntercom}
    set {_uniqueStorage()._fromIntercom = newValue}
  }

  var intercomHideUserCard: Bool {
    get {return _storage._intercomHideUserCard}
    set {_uniqueStorage()._intercomHideUserCard = newValue}
  }

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}

  fileprivate var _storage = _StorageClass.defaultInstance
}

/// 聊天
struct Douyin_ChatMessage: @unchecked Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var common: Douyin_Common {
    get {return _storage._common ?? Douyin_Common()}
    set {_uniqueStorage()._common = newValue}
  }
  /// Returns true if `common` has been explicitly set.
  var hasCommon: Bool {return _storage._common != nil}
  /// Clears the value of `common`. Subsequent reads from it will return its default value.
  mutating func clearCommon() {_uniqueStorage()._common = nil}

  var user: Douyin_User {
    get {return _storage._user ?? Douyin_User()}
    set {_uniqueStorage()._user = newValue}
  }
  /// Returns true if `user` has been explicitly set.
  var hasUser: Bool {return _storage._user != nil}
  /// Clears the value of `user`. Subsequent reads from it will return its default value.
  mutating func clearUser() {_uniqueStorage()._user = nil}

  var content: String {
    get {return _storage._content}
    set {_uniqueStorage()._content = newValue}
  }

  var visibleToSender: Bool {
    get {return _storage._visibleToSender}
    set {_uniqueStorage()._visibleToSender = newValue}
  }

  var backgroundImage: Douyin_Image {
    get {return _storage._backgroundImage ?? Douyin_Image()}
    set {_uniqueStorage()._backgroundImage = newValue}
  }
  /// Returns true if `backgroundImage` has been explicitly set.
  var hasBackgroundImage: Bool {return _storage._backgroundImage != nil}
  /// Clears the value of `backgroundImage`. Subsequent reads from it will return its default value.
  mutating func clearBackgroundImage() {_uniqueStorage()._backgroundImage = nil}

  var fullScreenTextColor: String {
    get {return _storage._fullScreenTextColor}
    set {_uniqueStorage()._fullScreenTextColor = newValue}
  }

  var backgroundImageV2: Douyin_Image {
    get {return _storage._backgroundImageV2 ?? Douyin_Image()}
    set {_uniqueStorage()._backgroundImageV2 = newValue}
  }
  /// Returns true if `backgroundImageV2` has been explicitly set.
  var hasBackgroundImageV2: Bool {return _storage._backgroundImageV2 != nil}
  /// Clears the value of `backgroundImageV2`. Subsequent reads from it will return its default value.
  mutating func clearBackgroundImageV2() {_uniqueStorage()._backgroundImageV2 = nil}

  var publicAreaCommon: Douyin_PublicAreaCommon {
    get {return _storage._publicAreaCommon ?? Douyin_PublicAreaCommon()}
    set {_uniqueStorage()._publicAreaCommon = newValue}
  }
  /// Returns true if `publicAreaCommon` has been explicitly set.
  var hasPublicAreaCommon: Bool {return _storage._publicAreaCommon != nil}
  /// Clears the value of `publicAreaCommon`. Subsequent reads from it will return its default value.
  mutating func clearPublicAreaCommon() {_uniqueStorage()._publicAreaCommon = nil}

  var giftImage: Douyin_Image {
    get {return _storage._giftImage ?? Douyin_Image()}
    set {_uniqueStorage()._giftImage = newValue}
  }
  /// Returns true if `giftImage` has been explicitly set.
  var hasGiftImage: Bool {return _storage._giftImage != nil}
  /// Clears the value of `giftImage`. Subsequent reads from it will return its default value.
  mutating func clearGiftImage() {_uniqueStorage()._giftImage = nil}

  var agreeMsgID: UInt64 {
    get {return _storage._agreeMsgID}
    set {_uniqueStorage()._agreeMsgID = newValue}
  }

  var priorityLevel: UInt32 {
    get {return _storage._priorityLevel}
    set {_uniqueStorage()._priorityLevel = newValue}
  }

  var landscapeAreaCommon: Douyin_LandscapeAreaCommon {
    get {return _storage._landscapeAreaCommon ?? Douyin_LandscapeAreaCommon()}
    set {_uniqueStorage()._landscapeAreaCommon = newValue}
  }
  /// Returns true if `landscapeAreaCommon` has been explicitly set.
  var hasLandscapeAreaCommon: Bool {return _storage._landscapeAreaCommon != nil}
  /// Clears the value of `landscapeAreaCommon`. Subsequent reads from it will return its default value.
  mutating func clearLandscapeAreaCommon() {_uniqueStorage()._landscapeAreaCommon = nil}

  var eventTime: UInt64 {
    get {return _storage._eventTime}
    set {_uniqueStorage()._eventTime = newValue}
  }

  var sendReview: Bool {
    get {return _storage._sendReview}
    set {_uniqueStorage()._sendReview = newValue}
  }

  var fromIntercom: Bool {
    get {return _storage._fromIntercom}
    set {_uniqueStorage()._fromIntercom = newValue}
  }

  var intercomHideUserCard: Bool {
    get {return _storage._intercomHideUserCard}
    set {_uniqueStorage()._intercomHideUserCard = newValue}
  }

  ///  repeated chatTagsList = 19;
  var chatBy: String {
    get {return _storage._chatBy}
    set {_uniqueStorage()._chatBy = newValue}
  }

  var individualChatPriority: UInt32 {
    get {return _storage._individualChatPriority}
    set {_uniqueStorage()._individualChatPriority = newValue}
  }

  var rtfContent: Douyin_Text {
    get {return _storage._rtfContent ?? Douyin_Text()}
    set {_uniqueStorage()._rtfContent = newValue}
  }
  /// Returns true if `rtfContent` has been explicitly set.
  var hasRtfContent: Bool {return _storage._rtfContent != nil}
  /// Clears the value of `rtfContent`. Subsequent reads from it will return its default value.
  mutating func clearRtfContent() {_uniqueStorage()._rtfContent = nil}

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}

  fileprivate var _storage = _StorageClass.defaultInstance
}

struct Douyin_LandscapeAreaCommon: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var showHead: Bool = false

  var showNickname: Bool = false

  var showFontColor: Bool = false

  var colorValueList: [String] = []

  var commentTypeTagsList: [Douyin_CommentTypeTag] = []

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}
}

///*
///•	Common common = 1;
///包含消息的通用信息，如消息ID、房间ID、创建时间等。
///•	repeated RoomUserSeqMessageContributor ranksList = 2;
///房间中用户的排名列表，RoomUserSeqMessageContributor类型的数组，表示在房间中的用户排名信息。
///•	int64 total = 3;
///总计的用户数量，表示房间中的用户总数。
///•	string popStr = 4;
///受欢迎程度的字符串表示，可能是一个用于显示人气的文案。
///•	repeated RoomUserSeqMessageContributor seatsList = 5;
///座位列表，RoomUserSeqMessageContributor类型的数组，表示当前房间中有特殊席位的用户。
///•	int64 popularity = 6;
///房间的人气值，通常用于显示房间当前的热度。
///•	int64 totalUser = 7;
///房间中在线的总用户数，以64位整数表示。
///•	string totalUserStr = 8;
///用户总数的字符串表示，用于展示在界面上。
///•	string totalStr = 9;
///总体数量的字符串表示，用于显示总体用户数量等数据。
///•	string onlineUserForAnchor = 10;
///为主播显示的在线用户数字符串，可能用于提供更简洁的在线用户信息。
///•	string totalPvForAnchor = 11;
///为主播显示的总PV（页面浏览量）字符串，可能用于统计和显示直播的页面浏览量。
///•	string upRightStatsStr = 12;
///右上角统计信息的字符串表示，用于显示在界面的右上角。
///•	string upRightStatsStrComplete = 13;
///完整的右上角统计信息字符串，可能提供比upRightStatsStr更多的详细信息。
struct Douyin_RoomUserSeqMessage: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var common: Douyin_Common {
    get {return _common ?? Douyin_Common()}
    set {_common = newValue}
  }
  /// Returns true if `common` has been explicitly set.
  var hasCommon: Bool {return self._common != nil}
  /// Clears the value of `common`. Subsequent reads from it will return its default value.
  mutating func clearCommon() {self._common = nil}

  var ranksList: [Douyin_RoomUserSeqMessageContributor] = []

  var total: Int64 = 0

  var popStr: String = String()

  var seatsList: [Douyin_RoomUserSeqMessageContributor] = []

  var popularity: Int64 = 0

  var totalUser: Int64 = 0

  var totalUserStr: String = String()

  var totalStr: String = String()

  var onlineUserForAnchor: String = String()

  var totalPvForAnchor: String = String()

  var upRightStatsStr: String = String()

  var upRightStatsStrComplete: String = String()

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}

  fileprivate var _common: Douyin_Common? = nil
}

struct Douyin_CommonTextMessage: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var common: Douyin_Common {
    get {return _common ?? Douyin_Common()}
    set {_common = newValue}
  }
  /// Returns true if `common` has been explicitly set.
  var hasCommon: Bool {return self._common != nil}
  /// Clears the value of `common`. Subsequent reads from it will return its default value.
  mutating func clearCommon() {self._common = nil}

  var user: Douyin_User {
    get {return _user ?? Douyin_User()}
    set {_user = newValue}
  }
  /// Returns true if `user` has been explicitly set.
  var hasUser: Bool {return self._user != nil}
  /// Clears the value of `user`. Subsequent reads from it will return its default value.
  mutating func clearUser() {self._user = nil}

  var scene: String = String()

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}

  fileprivate var _common: Douyin_Common? = nil
  fileprivate var _user: Douyin_User? = nil
}

struct Douyin_UpdateFanTicketMessage: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var common: Douyin_Common {
    get {return _common ?? Douyin_Common()}
    set {_common = newValue}
  }
  /// Returns true if `common` has been explicitly set.
  var hasCommon: Bool {return self._common != nil}
  /// Clears the value of `common`. Subsequent reads from it will return its default value.
  mutating func clearCommon() {self._common = nil}

  var roomFanTicketCountText: String = String()

  var roomFanTicketCount: UInt64 = 0

  var forceUpdate: Bool = false

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}

  fileprivate var _common: Douyin_Common? = nil
}

///*
///•	uint64 score = 1;
///用户的贡献分数，用64位无符号整数表示，通常用于表示用户对房间的贡献度。
///•	User user = 2;
///该用户的详细信息，使用User结构体类型，包含用户的具体资料。
///•	uint64 rank = 3;
///用户的排名，表示用户在房间中的排名位置。
///•	uint64 delta = 4;
///分数的增量，表示用户贡献分数的变化。
///•	bool isHidden = 5;
///是否隐藏该用户，布尔类型，true表示该用户的排名或信息在界面上被隐藏。
///•	string scoreDescription = 6;
///分数描述，提供关于分数的文本描述，用于展示用户的贡献或排名信息。
///•	string exactlyScore = 7;
///用户的精确分数，用字符串形式表示，通常用于精确展示或计算用户的具体分数。
struct Douyin_RoomUserSeqMessageContributor: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var score: UInt64 = 0

  var user: Douyin_User {
    get {return _user ?? Douyin_User()}
    set {_user = newValue}
  }
  /// Returns true if `user` has been explicitly set.
  var hasUser: Bool {return self._user != nil}
  /// Clears the value of `user`. Subsequent reads from it will return its default value.
  mutating func clearUser() {self._user = nil}

  var rank: UInt64 = 0

  var delta: UInt64 = 0

  var isHidden: Bool = false

  var scoreDescription: String = String()

  var exactlyScore: String = String()

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}

  fileprivate var _user: Douyin_User? = nil
}

/// 礼物消息
struct Douyin_GiftMessage: @unchecked Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var common: Douyin_Common {
    get {return _storage._common ?? Douyin_Common()}
    set {_uniqueStorage()._common = newValue}
  }
  /// Returns true if `common` has been explicitly set.
  var hasCommon: Bool {return _storage._common != nil}
  /// Clears the value of `common`. Subsequent reads from it will return its default value.
  mutating func clearCommon() {_uniqueStorage()._common = nil}

  var giftID: UInt64 {
    get {return _storage._giftID}
    set {_uniqueStorage()._giftID = newValue}
  }

  var fanTicketCount: UInt64 {
    get {return _storage._fanTicketCount}
    set {_uniqueStorage()._fanTicketCount = newValue}
  }

  var groupCount: UInt64 {
    get {return _storage._groupCount}
    set {_uniqueStorage()._groupCount = newValue}
  }

  var repeatCount: UInt64 {
    get {return _storage._repeatCount}
    set {_uniqueStorage()._repeatCount = newValue}
  }

  var comboCount: UInt64 {
    get {return _storage._comboCount}
    set {_uniqueStorage()._comboCount = newValue}
  }

  var user: Douyin_User {
    get {return _storage._user ?? Douyin_User()}
    set {_uniqueStorage()._user = newValue}
  }
  /// Returns true if `user` has been explicitly set.
  var hasUser: Bool {return _storage._user != nil}
  /// Clears the value of `user`. Subsequent reads from it will return its default value.
  mutating func clearUser() {_uniqueStorage()._user = nil}

  var toUser: Douyin_User {
    get {return _storage._toUser ?? Douyin_User()}
    set {_uniqueStorage()._toUser = newValue}
  }
  /// Returns true if `toUser` has been explicitly set.
  var hasToUser: Bool {return _storage._toUser != nil}
  /// Clears the value of `toUser`. Subsequent reads from it will return its default value.
  mutating func clearToUser() {_uniqueStorage()._toUser = nil}

  var repeatEnd: UInt32 {
    get {return _storage._repeatEnd}
    set {_uniqueStorage()._repeatEnd = newValue}
  }

  var textEffect: Douyin_TextEffect {
    get {return _storage._textEffect ?? Douyin_TextEffect()}
    set {_uniqueStorage()._textEffect = newValue}
  }
  /// Returns true if `textEffect` has been explicitly set.
  var hasTextEffect: Bool {return _storage._textEffect != nil}
  /// Clears the value of `textEffect`. Subsequent reads from it will return its default value.
  mutating func clearTextEffect() {_uniqueStorage()._textEffect = nil}

  var groupID: UInt64 {
    get {return _storage._groupID}
    set {_uniqueStorage()._groupID = newValue}
  }

  var incomeTaskgifts: UInt64 {
    get {return _storage._incomeTaskgifts}
    set {_uniqueStorage()._incomeTaskgifts = newValue}
  }

  var roomFanTicketCount: UInt64 {
    get {return _storage._roomFanTicketCount}
    set {_uniqueStorage()._roomFanTicketCount = newValue}
  }

  var priority: Douyin_GiftIMPriority {
    get {return _storage._priority ?? Douyin_GiftIMPriority()}
    set {_uniqueStorage()._priority = newValue}
  }
  /// Returns true if `priority` has been explicitly set.
  var hasPriority: Bool {return _storage._priority != nil}
  /// Clears the value of `priority`. Subsequent reads from it will return its default value.
  mutating func clearPriority() {_uniqueStorage()._priority = nil}

  var gift: Douyin_GiftStruct {
    get {return _storage._gift ?? Douyin_GiftStruct()}
    set {_uniqueStorage()._gift = newValue}
  }
  /// Returns true if `gift` has been explicitly set.
  var hasGift: Bool {return _storage._gift != nil}
  /// Clears the value of `gift`. Subsequent reads from it will return its default value.
  mutating func clearGift() {_uniqueStorage()._gift = nil}

  var logID: String {
    get {return _storage._logID}
    set {_uniqueStorage()._logID = newValue}
  }

  var sendType: UInt64 {
    get {return _storage._sendType}
    set {_uniqueStorage()._sendType = newValue}
  }

  var publicAreaCommon: Douyin_PublicAreaCommon {
    get {return _storage._publicAreaCommon ?? Douyin_PublicAreaCommon()}
    set {_uniqueStorage()._publicAreaCommon = newValue}
  }
  /// Returns true if `publicAreaCommon` has been explicitly set.
  var hasPublicAreaCommon: Bool {return _storage._publicAreaCommon != nil}
  /// Clears the value of `publicAreaCommon`. Subsequent reads from it will return its default value.
  mutating func clearPublicAreaCommon() {_uniqueStorage()._publicAreaCommon = nil}

  var trayDisplayText: Douyin_Text {
    get {return _storage._trayDisplayText ?? Douyin_Text()}
    set {_uniqueStorage()._trayDisplayText = newValue}
  }
  /// Returns true if `trayDisplayText` has been explicitly set.
  var hasTrayDisplayText: Bool {return _storage._trayDisplayText != nil}
  /// Clears the value of `trayDisplayText`. Subsequent reads from it will return its default value.
  mutating func clearTrayDisplayText() {_uniqueStorage()._trayDisplayText = nil}

  var bannedDisplayEffects: UInt64 {
    get {return _storage._bannedDisplayEffects}
    set {_uniqueStorage()._bannedDisplayEffects = newValue}
  }

  ///  GiftTrayInfo trayInfo = 21;
  ///  AssetEffectMixInfo assetEffectMixInfo = 22;
  var displayForSelf: Bool {
    get {return _storage._displayForSelf}
    set {_uniqueStorage()._displayForSelf = newValue}
  }

  var interactGiftInfo: String {
    get {return _storage._interactGiftInfo}
    set {_uniqueStorage()._interactGiftInfo = newValue}
  }

  var diyItemInfo: String {
    get {return _storage._diyItemInfo}
    set {_uniqueStorage()._diyItemInfo = newValue}
  }

  var minAssetSetList: [UInt64] {
    get {return _storage._minAssetSetList}
    set {_uniqueStorage()._minAssetSetList = newValue}
  }

  var totalCount: UInt64 {
    get {return _storage._totalCount}
    set {_uniqueStorage()._totalCount = newValue}
  }

  var clientGiftSource: UInt32 {
    get {return _storage._clientGiftSource}
    set {_uniqueStorage()._clientGiftSource = newValue}
  }

  ///  AnchorGiftData anchorGift = 31;
  var toUserIdsList: [UInt64] {
    get {return _storage._toUserIdsList}
    set {_uniqueStorage()._toUserIdsList = newValue}
  }

  var sendTime: UInt64 {
    get {return _storage._sendTime}
    set {_uniqueStorage()._sendTime = newValue}
  }

  var forceDisplayEffects: UInt64 {
    get {return _storage._forceDisplayEffects}
    set {_uniqueStorage()._forceDisplayEffects = newValue}
  }

  var traceID: String {
    get {return _storage._traceID}
    set {_uniqueStorage()._traceID = newValue}
  }

  var effectDisplayTs: UInt64 {
    get {return _storage._effectDisplayTs}
    set {_uniqueStorage()._effectDisplayTs = newValue}
  }

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}

  fileprivate var _storage = _StorageClass.defaultInstance
}

///*
///字段解析：
///1.	Image image = 1;
///•	这个字段表示礼物的图片，可能是礼物的外观或相关的视觉元素。
///2.	string describe = 2;
///•	礼物的描述信息，以字符串形式存储，可能是对礼物的简单说明或展示文本。
///3.	bool notify = 3;
///•	布尔类型字段，表示是否需要通知或提醒。true 可能意味着收到该礼物时要通知用户。
///4.	uint64 duration = 4;
///•	表示礼物的持续时间，可能是某些特效或礼物展示的时长，以64位无符号整数表示。
///5.	uint64 id = 5;
///•	礼物的唯一标识符，使用64位无符号整数存储，用于在系统中区分不同的礼物。
///6.	bool forLinkmic = 7;
///•	表示礼物是否用于连麦场景，true 可能意味着这个礼物专为连麦互动设计。
///7.	bool doodle = 8;
///•	布尔字段，表示礼物是否涉及涂鸦功能，true 可能意味着可以进行互动涂鸦。
///8.	bool forFansclub = 9;
///•	是否为粉丝团专属礼物，true 表示该礼物是粉丝俱乐部专属的。
///9.	bool combo = 10;
///•	表示是否为连击（combo）礼物。连击礼物通常意味着可以连续多次发送，产生连击效果。
///10.	uint32 type = 11;
///•	礼物的类型，用32位无符号整数表示，可能表示不同类型的礼物（如虚拟物品、特效等）。
///11.	uint32 diamondCount = 12;
///•	礼物的价值，表示该礼物消耗的钻石数量。
///12.	bool isDisplayedOnPanel = 13;
///•	是否在面板上展示，true 表示该礼物会出现在礼物选择面板中。
///13.	uint64 primaryEffectId = 14;
///•	礼物的主要特效ID，指向一个特效资源，用于展示礼物的主要动画效果。
///14.	Image giftLabelIcon = 15;
///•	礼物标签图标，通常用于在礼物面板或界面上标示该礼物。
///15.	string name = 16;
///•	礼物的名称，以字符串形式存储。
///16.	string region = 17;
///•	表示礼物适用的区域或国家，可能用于限制该礼物在哪些地区可用。
///17.	string manual = 18;
///•	礼物的手动说明，可能用于提供关于该礼物的使用说明或背景信息。
///18.	bool forCustom = 19;
///•	是否为定制礼物，true 表示该礼物是为某些场景或用户定制的。
///19.	Image icon = 21;
///•	礼物的图标，用于在界面上展示该礼物的缩略图或标志。
///20.	uint32 actionType = 22;
///•	礼物的动作类型，可能表示该礼物触发的交互行为或事件类型。
struct Douyin_GiftStruct: @unchecked Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var image: Douyin_Image {
    get {return _storage._image ?? Douyin_Image()}
    set {_uniqueStorage()._image = newValue}
  }
  /// Returns true if `image` has been explicitly set.
  var hasImage: Bool {return _storage._image != nil}
  /// Clears the value of `image`. Subsequent reads from it will return its default value.
  mutating func clearImage() {_uniqueStorage()._image = nil}

  var describe: String {
    get {return _storage._describe}
    set {_uniqueStorage()._describe = newValue}
  }

  var notify: Bool {
    get {return _storage._notify}
    set {_uniqueStorage()._notify = newValue}
  }

  var duration: UInt64 {
    get {return _storage._duration}
    set {_uniqueStorage()._duration = newValue}
  }

  var id: UInt64 {
    get {return _storage._id}
    set {_uniqueStorage()._id = newValue}
  }

  ///  GiftStructFansClubInfo fansclubInfo = 6;
  var forLinkmic: Bool {
    get {return _storage._forLinkmic}
    set {_uniqueStorage()._forLinkmic = newValue}
  }

  var doodle: Bool {
    get {return _storage._doodle}
    set {_uniqueStorage()._doodle = newValue}
  }

  var forFansclub: Bool {
    get {return _storage._forFansclub}
    set {_uniqueStorage()._forFansclub = newValue}
  }

  var combo: Bool {
    get {return _storage._combo}
    set {_uniqueStorage()._combo = newValue}
  }

  var type: UInt32 {
    get {return _storage._type}
    set {_uniqueStorage()._type = newValue}
  }

  var diamondCount: UInt32 {
    get {return _storage._diamondCount}
    set {_uniqueStorage()._diamondCount = newValue}
  }

  var isDisplayedOnPanel: Bool {
    get {return _storage._isDisplayedOnPanel}
    set {_uniqueStorage()._isDisplayedOnPanel = newValue}
  }

  var primaryEffectID: UInt64 {
    get {return _storage._primaryEffectID}
    set {_uniqueStorage()._primaryEffectID = newValue}
  }

  var giftLabelIcon: Douyin_Image {
    get {return _storage._giftLabelIcon ?? Douyin_Image()}
    set {_uniqueStorage()._giftLabelIcon = newValue}
  }
  /// Returns true if `giftLabelIcon` has been explicitly set.
  var hasGiftLabelIcon: Bool {return _storage._giftLabelIcon != nil}
  /// Clears the value of `giftLabelIcon`. Subsequent reads from it will return its default value.
  mutating func clearGiftLabelIcon() {_uniqueStorage()._giftLabelIcon = nil}

  var name: String {
    get {return _storage._name}
    set {_uniqueStorage()._name = newValue}
  }

  var region: String {
    get {return _storage._region}
    set {_uniqueStorage()._region = newValue}
  }

  var manual: String {
    get {return _storage._manual}
    set {_uniqueStorage()._manual = newValue}
  }

  var forCustom: Bool {
    get {return _storage._forCustom}
    set {_uniqueStorage()._forCustom = newValue}
  }

  ///  specialEffectsMap = 20;
  var icon: Douyin_Image {
    get {return _storage._icon ?? Douyin_Image()}
    set {_uniqueStorage()._icon = newValue}
  }
  /// Returns true if `icon` has been explicitly set.
  var hasIcon: Bool {return _storage._icon != nil}
  /// Clears the value of `icon`. Subsequent reads from it will return its default value.
  mutating func clearIcon() {_uniqueStorage()._icon = nil}

  /// fixme 后面的就不写了还有几十个属性
  var actionType: UInt32 {
    get {return _storage._actionType}
    set {_uniqueStorage()._actionType = newValue}
  }

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}

  fileprivate var _storage = _StorageClass.defaultInstance
}

///*
///字段解析：
///
///1.	repeated uint64 queueSizesList = 1;
///•	一个无符号64位整数的列表，用于表示多个队列的大小。可能代表系统中存在多个消息队列，这些队列的大小可能影响消息的优先级或发送顺序。
///2.	uint64 selfQueuePriority = 2;
///•	用户自身队列的优先级。这个字段用64位无符号整数表示，可能用于决定发送者自己礼物消息在队列中的优先级。
///3.	uint64 priority = 3;
///•	礼物消息的全局优先级。这个字段用64位无符号整数存储，决定该礼物消息在整个系统中的处理优先级。数值越高，优先级越高，消息可能会更快被处理或显示。
struct Douyin_GiftIMPriority: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var queueSizesList: [UInt64] = []

  var selfQueuePriority: UInt64 = 0

  var priority: UInt64 = 0

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}
}

struct Douyin_TextEffect: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var portrait: Douyin_TextEffectDetail {
    get {return _portrait ?? Douyin_TextEffectDetail()}
    set {_portrait = newValue}
  }
  /// Returns true if `portrait` has been explicitly set.
  var hasPortrait: Bool {return self._portrait != nil}
  /// Clears the value of `portrait`. Subsequent reads from it will return its default value.
  mutating func clearPortrait() {self._portrait = nil}

  var landscape: Douyin_TextEffectDetail {
    get {return _landscape ?? Douyin_TextEffectDetail()}
    set {_landscape = newValue}
  }
  /// Returns true if `landscape` has been explicitly set.
  var hasLandscape: Bool {return self._landscape != nil}
  /// Clears the value of `landscape`. Subsequent reads from it will return its default value.
  mutating func clearLandscape() {self._landscape = nil}

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}

  fileprivate var _portrait: Douyin_TextEffectDetail? = nil
  fileprivate var _landscape: Douyin_TextEffectDetail? = nil
}

struct Douyin_TextEffectDetail: @unchecked Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var text: Douyin_Text {
    get {return _storage._text ?? Douyin_Text()}
    set {_uniqueStorage()._text = newValue}
  }
  /// Returns true if `text` has been explicitly set.
  var hasText: Bool {return _storage._text != nil}
  /// Clears the value of `text`. Subsequent reads from it will return its default value.
  mutating func clearText() {_uniqueStorage()._text = nil}

  var textFontSize: UInt32 {
    get {return _storage._textFontSize}
    set {_uniqueStorage()._textFontSize = newValue}
  }

  var background: Douyin_Image {
    get {return _storage._background ?? Douyin_Image()}
    set {_uniqueStorage()._background = newValue}
  }
  /// Returns true if `background` has been explicitly set.
  var hasBackground: Bool {return _storage._background != nil}
  /// Clears the value of `background`. Subsequent reads from it will return its default value.
  mutating func clearBackground() {_uniqueStorage()._background = nil}

  var start: UInt32 {
    get {return _storage._start}
    set {_uniqueStorage()._start = newValue}
  }

  var duration: UInt32 {
    get {return _storage._duration}
    set {_uniqueStorage()._duration = newValue}
  }

  var x: UInt32 {
    get {return _storage._x}
    set {_uniqueStorage()._x = newValue}
  }

  var y: UInt32 {
    get {return _storage._y}
    set {_uniqueStorage()._y = newValue}
  }

  var width: UInt32 {
    get {return _storage._width}
    set {_uniqueStorage()._width = newValue}
  }

  var height: UInt32 {
    get {return _storage._height}
    set {_uniqueStorage()._height = newValue}
  }

  var shadowDx: UInt32 {
    get {return _storage._shadowDx}
    set {_uniqueStorage()._shadowDx = newValue}
  }

  var shadowDy: UInt32 {
    get {return _storage._shadowDy}
    set {_uniqueStorage()._shadowDy = newValue}
  }

  var shadowRadius: UInt32 {
    get {return _storage._shadowRadius}
    set {_uniqueStorage()._shadowRadius = newValue}
  }

  var shadowColor: String {
    get {return _storage._shadowColor}
    set {_uniqueStorage()._shadowColor = newValue}
  }

  var strokeColor: String {
    get {return _storage._strokeColor}
    set {_uniqueStorage()._strokeColor = newValue}
  }

  var strokeWidth: UInt32 {
    get {return _storage._strokeWidth}
    set {_uniqueStorage()._strokeWidth = newValue}
  }

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}

  fileprivate var _storage = _StorageClass.defaultInstance
}

/// 成员消息
struct Douyin_MemberMessage: @unchecked Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var common: Douyin_Common {
    get {return _storage._common ?? Douyin_Common()}
    set {_uniqueStorage()._common = newValue}
  }
  /// Returns true if `common` has been explicitly set.
  var hasCommon: Bool {return _storage._common != nil}
  /// Clears the value of `common`. Subsequent reads from it will return its default value.
  mutating func clearCommon() {_uniqueStorage()._common = nil}

  var user: Douyin_User {
    get {return _storage._user ?? Douyin_User()}
    set {_uniqueStorage()._user = newValue}
  }
  /// Returns true if `user` has been explicitly set.
  var hasUser: Bool {return _storage._user != nil}
  /// Clears the value of `user`. Subsequent reads from it will return its default value.
  mutating func clearUser() {_uniqueStorage()._user = nil}

  var memberCount: UInt64 {
    get {return _storage._memberCount}
    set {_uniqueStorage()._memberCount = newValue}
  }

  var `operator`: Douyin_User {
    get {return _storage._operator ?? Douyin_User()}
    set {_uniqueStorage()._operator = newValue}
  }
  /// Returns true if ``operator`` has been explicitly set.
  var hasOperator: Bool {return _storage._operator != nil}
  /// Clears the value of ``operator``. Subsequent reads from it will return its default value.
  mutating func clearOperator() {_uniqueStorage()._operator = nil}

  var isSetToAdmin: Bool {
    get {return _storage._isSetToAdmin}
    set {_uniqueStorage()._isSetToAdmin = newValue}
  }

  var isTopUser: Bool {
    get {return _storage._isTopUser}
    set {_uniqueStorage()._isTopUser = newValue}
  }

  var rankScore: UInt64 {
    get {return _storage._rankScore}
    set {_uniqueStorage()._rankScore = newValue}
  }

  var topUserNo: UInt64 {
    get {return _storage._topUserNo}
    set {_uniqueStorage()._topUserNo = newValue}
  }

  var enterType: UInt64 {
    get {return _storage._enterType}
    set {_uniqueStorage()._enterType = newValue}
  }

  var action: UInt64 {
    get {return _storage._action}
    set {_uniqueStorage()._action = newValue}
  }

  var actionDescription: String {
    get {return _storage._actionDescription}
    set {_uniqueStorage()._actionDescription = newValue}
  }

  var userID: UInt64 {
    get {return _storage._userID}
    set {_uniqueStorage()._userID = newValue}
  }

  var effectConfig: Douyin_EffectConfig {
    get {return _storage._effectConfig ?? Douyin_EffectConfig()}
    set {_uniqueStorage()._effectConfig = newValue}
  }
  /// Returns true if `effectConfig` has been explicitly set.
  var hasEffectConfig: Bool {return _storage._effectConfig != nil}
  /// Clears the value of `effectConfig`. Subsequent reads from it will return its default value.
  mutating func clearEffectConfig() {_uniqueStorage()._effectConfig = nil}

  var popStr: String {
    get {return _storage._popStr}
    set {_uniqueStorage()._popStr = newValue}
  }

  var enterEffectConfig: Douyin_EffectConfig {
    get {return _storage._enterEffectConfig ?? Douyin_EffectConfig()}
    set {_uniqueStorage()._enterEffectConfig = newValue}
  }
  /// Returns true if `enterEffectConfig` has been explicitly set.
  var hasEnterEffectConfig: Bool {return _storage._enterEffectConfig != nil}
  /// Clears the value of `enterEffectConfig`. Subsequent reads from it will return its default value.
  mutating func clearEnterEffectConfig() {_uniqueStorage()._enterEffectConfig = nil}

  var backgroundImage: Douyin_Image {
    get {return _storage._backgroundImage ?? Douyin_Image()}
    set {_uniqueStorage()._backgroundImage = newValue}
  }
  /// Returns true if `backgroundImage` has been explicitly set.
  var hasBackgroundImage: Bool {return _storage._backgroundImage != nil}
  /// Clears the value of `backgroundImage`. Subsequent reads from it will return its default value.
  mutating func clearBackgroundImage() {_uniqueStorage()._backgroundImage = nil}

  var backgroundImageV2: Douyin_Image {
    get {return _storage._backgroundImageV2 ?? Douyin_Image()}
    set {_uniqueStorage()._backgroundImageV2 = newValue}
  }
  /// Returns true if `backgroundImageV2` has been explicitly set.
  var hasBackgroundImageV2: Bool {return _storage._backgroundImageV2 != nil}
  /// Clears the value of `backgroundImageV2`. Subsequent reads from it will return its default value.
  mutating func clearBackgroundImageV2() {_uniqueStorage()._backgroundImageV2 = nil}

  var anchorDisplayText: Douyin_Text {
    get {return _storage._anchorDisplayText ?? Douyin_Text()}
    set {_uniqueStorage()._anchorDisplayText = newValue}
  }
  /// Returns true if `anchorDisplayText` has been explicitly set.
  var hasAnchorDisplayText: Bool {return _storage._anchorDisplayText != nil}
  /// Clears the value of `anchorDisplayText`. Subsequent reads from it will return its default value.
  mutating func clearAnchorDisplayText() {_uniqueStorage()._anchorDisplayText = nil}

  var publicAreaCommon: Douyin_PublicAreaCommon {
    get {return _storage._publicAreaCommon ?? Douyin_PublicAreaCommon()}
    set {_uniqueStorage()._publicAreaCommon = newValue}
  }
  /// Returns true if `publicAreaCommon` has been explicitly set.
  var hasPublicAreaCommon: Bool {return _storage._publicAreaCommon != nil}
  /// Clears the value of `publicAreaCommon`. Subsequent reads from it will return its default value.
  mutating func clearPublicAreaCommon() {_uniqueStorage()._publicAreaCommon = nil}

  var userEnterTipType: UInt64 {
    get {return _storage._userEnterTipType}
    set {_uniqueStorage()._userEnterTipType = newValue}
  }

  var anchorEnterTipType: UInt64 {
    get {return _storage._anchorEnterTipType}
    set {_uniqueStorage()._anchorEnterTipType = newValue}
  }

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}

  fileprivate var _storage = _StorageClass.defaultInstance
}

///*
///1.	Image userLabel = 1;
///用户标签的图片对象，可能表示用户的身份标签、等级图标或特殊身份（例如 VIP 用户）的标志。
///2.	uint64 userConsumeInRoom = 2;
///用户在房间内的消费总数，使用 64 位无符号整数表示，记录用户在当前房间中的消费金额或虚拟物品（如礼物）的总价值。
///3.	uint64 userSendGiftCntInRoom = 3;
///用户在房间内发送的礼物次数，使用 64 位无符号整数表示，记录用户在当前房间中发送礼物的累计次数。
struct Douyin_PublicAreaCommon: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var userLabel: Douyin_Image {
    get {return _userLabel ?? Douyin_Image()}
    set {_userLabel = newValue}
  }
  /// Returns true if `userLabel` has been explicitly set.
  var hasUserLabel: Bool {return self._userLabel != nil}
  /// Clears the value of `userLabel`. Subsequent reads from it will return its default value.
  mutating func clearUserLabel() {self._userLabel = nil}

  var userConsumeInRoom: UInt64 = 0

  var userSendGiftCntInRoom: UInt64 = 0

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}

  fileprivate var _userLabel: Douyin_Image? = nil
}

struct Douyin_EffectConfig: @unchecked Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var type: UInt64 {
    get {return _storage._type}
    set {_uniqueStorage()._type = newValue}
  }

  var icon: Douyin_Image {
    get {return _storage._icon ?? Douyin_Image()}
    set {_uniqueStorage()._icon = newValue}
  }
  /// Returns true if `icon` has been explicitly set.
  var hasIcon: Bool {return _storage._icon != nil}
  /// Clears the value of `icon`. Subsequent reads from it will return its default value.
  mutating func clearIcon() {_uniqueStorage()._icon = nil}

  var avatarPos: UInt64 {
    get {return _storage._avatarPos}
    set {_uniqueStorage()._avatarPos = newValue}
  }

  var text: Douyin_Text {
    get {return _storage._text ?? Douyin_Text()}
    set {_uniqueStorage()._text = newValue}
  }
  /// Returns true if `text` has been explicitly set.
  var hasText: Bool {return _storage._text != nil}
  /// Clears the value of `text`. Subsequent reads from it will return its default value.
  mutating func clearText() {_uniqueStorage()._text = nil}

  var textIcon: Douyin_Image {
    get {return _storage._textIcon ?? Douyin_Image()}
    set {_uniqueStorage()._textIcon = newValue}
  }
  /// Returns true if `textIcon` has been explicitly set.
  var hasTextIcon: Bool {return _storage._textIcon != nil}
  /// Clears the value of `textIcon`. Subsequent reads from it will return its default value.
  mutating func clearTextIcon() {_uniqueStorage()._textIcon = nil}

  var stayTime: UInt32 {
    get {return _storage._stayTime}
    set {_uniqueStorage()._stayTime = newValue}
  }

  var animAssetID: UInt64 {
    get {return _storage._animAssetID}
    set {_uniqueStorage()._animAssetID = newValue}
  }

  var badge: Douyin_Image {
    get {return _storage._badge ?? Douyin_Image()}
    set {_uniqueStorage()._badge = newValue}
  }
  /// Returns true if `badge` has been explicitly set.
  var hasBadge: Bool {return _storage._badge != nil}
  /// Clears the value of `badge`. Subsequent reads from it will return its default value.
  mutating func clearBadge() {_uniqueStorage()._badge = nil}

  var flexSettingArrayList: [UInt64] {
    get {return _storage._flexSettingArrayList}
    set {_uniqueStorage()._flexSettingArrayList = newValue}
  }

  var textIconOverlay: Douyin_Image {
    get {return _storage._textIconOverlay ?? Douyin_Image()}
    set {_uniqueStorage()._textIconOverlay = newValue}
  }
  /// Returns true if `textIconOverlay` has been explicitly set.
  var hasTextIconOverlay: Bool {return _storage._textIconOverlay != nil}
  /// Clears the value of `textIconOverlay`. Subsequent reads from it will return its default value.
  mutating func clearTextIconOverlay() {_uniqueStorage()._textIconOverlay = nil}

  var animatedBadge: Douyin_Image {
    get {return _storage._animatedBadge ?? Douyin_Image()}
    set {_uniqueStorage()._animatedBadge = newValue}
  }
  /// Returns true if `animatedBadge` has been explicitly set.
  var hasAnimatedBadge: Bool {return _storage._animatedBadge != nil}
  /// Clears the value of `animatedBadge`. Subsequent reads from it will return its default value.
  mutating func clearAnimatedBadge() {_uniqueStorage()._animatedBadge = nil}

  var hasSweepLight_p: Bool {
    get {return _storage._hasSweepLight_p}
    set {_uniqueStorage()._hasSweepLight_p = newValue}
  }

  var textFlexSettingArrayList: [UInt64] {
    get {return _storage._textFlexSettingArrayList}
    set {_uniqueStorage()._textFlexSettingArrayList = newValue}
  }

  var centerAnimAssetID: UInt64 {
    get {return _storage._centerAnimAssetID}
    set {_uniqueStorage()._centerAnimAssetID = newValue}
  }

  var dynamicImage: Douyin_Image {
    get {return _storage._dynamicImage ?? Douyin_Image()}
    set {_uniqueStorage()._dynamicImage = newValue}
  }
  /// Returns true if `dynamicImage` has been explicitly set.
  var hasDynamicImage: Bool {return _storage._dynamicImage != nil}
  /// Clears the value of `dynamicImage`. Subsequent reads from it will return its default value.
  mutating func clearDynamicImage() {_uniqueStorage()._dynamicImage = nil}

  var extraMap: Dictionary<String,String> {
    get {return _storage._extraMap}
    set {_uniqueStorage()._extraMap = newValue}
  }

  var mp4AnimAssetID: UInt64 {
    get {return _storage._mp4AnimAssetID}
    set {_uniqueStorage()._mp4AnimAssetID = newValue}
  }

  var priority: UInt64 {
    get {return _storage._priority}
    set {_uniqueStorage()._priority = newValue}
  }

  var maxWaitTime: UInt64 {
    get {return _storage._maxWaitTime}
    set {_uniqueStorage()._maxWaitTime = newValue}
  }

  var dressID: String {
    get {return _storage._dressID}
    set {_uniqueStorage()._dressID = newValue}
  }

  var alignment: UInt64 {
    get {return _storage._alignment}
    set {_uniqueStorage()._alignment = newValue}
  }

  var alignmentOffset: UInt64 {
    get {return _storage._alignmentOffset}
    set {_uniqueStorage()._alignmentOffset = newValue}
  }

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}

  fileprivate var _storage = _StorageClass.defaultInstance
}

///*
///1.	string key = 1;
///文本的键，用于标识文本内容，可能用作引用或本地化的标识符。
///2.	string defaultPatter = 2;
///默认的文本模式，表示文本的默认字符串内容，当没有指定其他格式化时使用。
///3.	TextFormat defaultFormat = 3;
///文本的默认格式，使用 TextFormat 结构体，定义了字体颜色、加粗、斜体、字号等格式化选项。
///4.	repeated TextPiece piecesList = 4;
///文本片段列表，包含多个 TextPiece，表示该文本的不同部分，每个片段可以有不同的格式化设置。该字段允许富文本展示，支持不同样式的文本拼接。
struct Douyin_Text: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var key: String = String()

  var defaultPatter: String = String()

  var defaultFormat: Douyin_TextFormat {
    get {return _defaultFormat ?? Douyin_TextFormat()}
    set {_defaultFormat = newValue}
  }
  /// Returns true if `defaultFormat` has been explicitly set.
  var hasDefaultFormat: Bool {return self._defaultFormat != nil}
  /// Clears the value of `defaultFormat`. Subsequent reads from it will return its default value.
  mutating func clearDefaultFormat() {self._defaultFormat = nil}

  var piecesList: [Douyin_TextPiece] = []

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}

  fileprivate var _defaultFormat: Douyin_TextFormat? = nil
}

struct Douyin_TextPiece: @unchecked Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var type: Bool {
    get {return _storage._type}
    set {_uniqueStorage()._type = newValue}
  }

  var format: Douyin_TextFormat {
    get {return _storage._format ?? Douyin_TextFormat()}
    set {_uniqueStorage()._format = newValue}
  }
  /// Returns true if `format` has been explicitly set.
  var hasFormat: Bool {return _storage._format != nil}
  /// Clears the value of `format`. Subsequent reads from it will return its default value.
  mutating func clearFormat() {_uniqueStorage()._format = nil}

  var stringValue: String {
    get {return _storage._stringValue}
    set {_uniqueStorage()._stringValue = newValue}
  }

  var userValue: Douyin_TextPieceUser {
    get {return _storage._userValue ?? Douyin_TextPieceUser()}
    set {_uniqueStorage()._userValue = newValue}
  }
  /// Returns true if `userValue` has been explicitly set.
  var hasUserValue: Bool {return _storage._userValue != nil}
  /// Clears the value of `userValue`. Subsequent reads from it will return its default value.
  mutating func clearUserValue() {_uniqueStorage()._userValue = nil}

  var giftValue: Douyin_TextPieceGift {
    get {return _storage._giftValue ?? Douyin_TextPieceGift()}
    set {_uniqueStorage()._giftValue = newValue}
  }
  /// Returns true if `giftValue` has been explicitly set.
  var hasGiftValue: Bool {return _storage._giftValue != nil}
  /// Clears the value of `giftValue`. Subsequent reads from it will return its default value.
  mutating func clearGiftValue() {_uniqueStorage()._giftValue = nil}

  var heartValue: Douyin_TextPieceHeart {
    get {return _storage._heartValue ?? Douyin_TextPieceHeart()}
    set {_uniqueStorage()._heartValue = newValue}
  }
  /// Returns true if `heartValue` has been explicitly set.
  var hasHeartValue: Bool {return _storage._heartValue != nil}
  /// Clears the value of `heartValue`. Subsequent reads from it will return its default value.
  mutating func clearHeartValue() {_uniqueStorage()._heartValue = nil}

  var patternRefValue: Douyin_TextPiecePatternRef {
    get {return _storage._patternRefValue ?? Douyin_TextPiecePatternRef()}
    set {_uniqueStorage()._patternRefValue = newValue}
  }
  /// Returns true if `patternRefValue` has been explicitly set.
  var hasPatternRefValue: Bool {return _storage._patternRefValue != nil}
  /// Clears the value of `patternRefValue`. Subsequent reads from it will return its default value.
  mutating func clearPatternRefValue() {_uniqueStorage()._patternRefValue = nil}

  var imageValue: Douyin_TextPieceImage {
    get {return _storage._imageValue ?? Douyin_TextPieceImage()}
    set {_uniqueStorage()._imageValue = newValue}
  }
  /// Returns true if `imageValue` has been explicitly set.
  var hasImageValue: Bool {return _storage._imageValue != nil}
  /// Clears the value of `imageValue`. Subsequent reads from it will return its default value.
  mutating func clearImageValue() {_uniqueStorage()._imageValue = nil}

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}

  fileprivate var _storage = _StorageClass.defaultInstance
}

struct Douyin_TextPieceImage: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var image: Douyin_Image {
    get {return _image ?? Douyin_Image()}
    set {_image = newValue}
  }
  /// Returns true if `image` has been explicitly set.
  var hasImage: Bool {return self._image != nil}
  /// Clears the value of `image`. Subsequent reads from it will return its default value.
  mutating func clearImage() {self._image = nil}

  var scalingRate: Float = 0

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}

  fileprivate var _image: Douyin_Image? = nil
}

struct Douyin_TextPiecePatternRef: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var key: String = String()

  var defaultPattern: String = String()

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}
}

struct Douyin_TextPieceHeart: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var color: String = String()

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}
}

struct Douyin_TextPieceGift: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var giftID: UInt64 = 0

  var nameRef: Douyin_PatternRef {
    get {return _nameRef ?? Douyin_PatternRef()}
    set {_nameRef = newValue}
  }
  /// Returns true if `nameRef` has been explicitly set.
  var hasNameRef: Bool {return self._nameRef != nil}
  /// Clears the value of `nameRef`. Subsequent reads from it will return its default value.
  mutating func clearNameRef() {self._nameRef = nil}

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}

  fileprivate var _nameRef: Douyin_PatternRef? = nil
}

struct Douyin_PatternRef: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var key: String = String()

  var defaultPattern: String = String()

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}
}

struct Douyin_TextPieceUser: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var user: Douyin_User {
    get {return _user ?? Douyin_User()}
    set {_user = newValue}
  }
  /// Returns true if `user` has been explicitly set.
  var hasUser: Bool {return self._user != nil}
  /// Clears the value of `user`. Subsequent reads from it will return its default value.
  mutating func clearUser() {self._user = nil}

  var withColon: Bool = false

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}

  fileprivate var _user: Douyin_User? = nil
}

///*
///1.	string color = 1;
///文本的颜色，使用字符串形式表示，可以是颜色代码（如 #FFFFFF 表示白色），用于指定文本的颜色。
///2.	bool bold = 2;
///布尔类型，表示文本是否加粗。true 表示文本加粗，false 表示正常字体。
///3.	bool italic = 3;
///布尔类型，表示文本是否斜体。true 表示斜体，false 表示正常字体。
///4.	uint32 weight = 4;
///字体的权重，通常表示字体的粗细程度，数值越大字体越粗，类似于 CSS 中的 font-weight 属性。
///5.	uint32 italicAngle = 5;
///斜体的角度，表示字体的倾斜角度，值越大表示倾斜的角度越大。
///6.	uint32 fontSize = 6;
///字体大小，使用 32 位无符号整数表示，指定文本的字号。
///7.	bool useHeighLightColor = 7;
///布尔类型，表示是否使用高亮颜色，true 表示启用高亮颜色，false 表示关闭。
///8.	bool useRemoteClor = 8;
///布尔类型，表示是否使用远程指定的颜色，true 表示从远程服务器获取颜色配置，false 表示使用本地定义的颜色。
struct Douyin_TextFormat: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var color: String = String()

  var bold: Bool = false

  var italic: Bool = false

  var weight: UInt32 = 0

  var italicAngle: UInt32 = 0

  var fontSize: UInt32 = 0

  var useHeighLightColor: Bool = false

  var useRemoteClor: Bool = false

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}
}

/// 点赞
struct Douyin_LikeMessage: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var common: Douyin_Common {
    get {return _common ?? Douyin_Common()}
    set {_common = newValue}
  }
  /// Returns true if `common` has been explicitly set.
  var hasCommon: Bool {return self._common != nil}
  /// Clears the value of `common`. Subsequent reads from it will return its default value.
  mutating func clearCommon() {self._common = nil}

  var count: UInt64 = 0

  var total: UInt64 = 0

  var color: UInt64 = 0

  var user: Douyin_User {
    get {return _user ?? Douyin_User()}
    set {_user = newValue}
  }
  /// Returns true if `user` has been explicitly set.
  var hasUser: Bool {return self._user != nil}
  /// Clears the value of `user`. Subsequent reads from it will return its default value.
  mutating func clearUser() {self._user = nil}

  var icon: String = String()

  var doubleLikeDetail: Douyin_DoubleLikeDetail {
    get {return _doubleLikeDetail ?? Douyin_DoubleLikeDetail()}
    set {_doubleLikeDetail = newValue}
  }
  /// Returns true if `doubleLikeDetail` has been explicitly set.
  var hasDoubleLikeDetail: Bool {return self._doubleLikeDetail != nil}
  /// Clears the value of `doubleLikeDetail`. Subsequent reads from it will return its default value.
  mutating func clearDoubleLikeDetail() {self._doubleLikeDetail = nil}

  var displayControlInfo: Douyin_DisplayControlInfo {
    get {return _displayControlInfo ?? Douyin_DisplayControlInfo()}
    set {_displayControlInfo = newValue}
  }
  /// Returns true if `displayControlInfo` has been explicitly set.
  var hasDisplayControlInfo: Bool {return self._displayControlInfo != nil}
  /// Clears the value of `displayControlInfo`. Subsequent reads from it will return its default value.
  mutating func clearDisplayControlInfo() {self._displayControlInfo = nil}

  var linkmicGuestUid: UInt64 = 0

  var scene: String = String()

  var picoDisplayInfo: Douyin_PicoDisplayInfo {
    get {return _picoDisplayInfo ?? Douyin_PicoDisplayInfo()}
    set {_picoDisplayInfo = newValue}
  }
  /// Returns true if `picoDisplayInfo` has been explicitly set.
  var hasPicoDisplayInfo: Bool {return self._picoDisplayInfo != nil}
  /// Clears the value of `picoDisplayInfo`. Subsequent reads from it will return its default value.
  mutating func clearPicoDisplayInfo() {self._picoDisplayInfo = nil}

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}

  fileprivate var _common: Douyin_Common? = nil
  fileprivate var _user: Douyin_User? = nil
  fileprivate var _doubleLikeDetail: Douyin_DoubleLikeDetail? = nil
  fileprivate var _displayControlInfo: Douyin_DisplayControlInfo? = nil
  fileprivate var _picoDisplayInfo: Douyin_PicoDisplayInfo? = nil
}

///*
///1.	Common common = 1;
///通用字段，包含社交消息的一些基础信息，例如消息 ID、房间 ID、创建时间等。
///2.	User user = 2;
///用户对象，表示触发该社交行为的用户，User 类型存储了用户的详细信息。
///3.	uint64 shareType = 3;
///分享类型，使用 64 位无符号整数表示，可能用于区分不同的分享类型或场景（例如分享至社交平台、发送到好友等）。
///4.	uint64 action = 4;
///行为类型，表示用户在社交场景中的具体操作行为，使用 64 位无符号整数存储。可能包括分享、关注、点赞等社交行为。
///5.	string shareTarget = 5;
///分享目标，表示分享的对象或平台，存储为字符串。例如，这可能是分享的社交平台、用户 ID、或是分享的具体内容（如视频、图片等）。
///6.	uint64 followCount = 6;
///关注数，表示用户的关注总数，可能在用户进行关注操作后更新这个值。
///7.	PublicAreaCommon publicAreaCommon = 7;
///公共区域信息，PublicAreaCommon 用于存储一些与消息展示相关的公共区域信息，可能涉及到直播间或互动区域的展示。
struct Douyin_SocialMessage: @unchecked Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var common: Douyin_Common {
    get {return _storage._common ?? Douyin_Common()}
    set {_uniqueStorage()._common = newValue}
  }
  /// Returns true if `common` has been explicitly set.
  var hasCommon: Bool {return _storage._common != nil}
  /// Clears the value of `common`. Subsequent reads from it will return its default value.
  mutating func clearCommon() {_uniqueStorage()._common = nil}

  var user: Douyin_User {
    get {return _storage._user ?? Douyin_User()}
    set {_uniqueStorage()._user = newValue}
  }
  /// Returns true if `user` has been explicitly set.
  var hasUser: Bool {return _storage._user != nil}
  /// Clears the value of `user`. Subsequent reads from it will return its default value.
  mutating func clearUser() {_uniqueStorage()._user = nil}

  var shareType: UInt64 {
    get {return _storage._shareType}
    set {_uniqueStorage()._shareType = newValue}
  }

  var action: UInt64 {
    get {return _storage._action}
    set {_uniqueStorage()._action = newValue}
  }

  var shareTarget: String {
    get {return _storage._shareTarget}
    set {_uniqueStorage()._shareTarget = newValue}
  }

  var followCount: UInt64 {
    get {return _storage._followCount}
    set {_uniqueStorage()._followCount = newValue}
  }

  var publicAreaCommon: Douyin_PublicAreaCommon {
    get {return _storage._publicAreaCommon ?? Douyin_PublicAreaCommon()}
    set {_uniqueStorage()._publicAreaCommon = newValue}
  }
  /// Returns true if `publicAreaCommon` has been explicitly set.
  var hasPublicAreaCommon: Bool {return _storage._publicAreaCommon != nil}
  /// Clears the value of `publicAreaCommon`. Subsequent reads from it will return its default value.
  mutating func clearPublicAreaCommon() {_uniqueStorage()._publicAreaCommon = nil}

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}

  fileprivate var _storage = _StorageClass.defaultInstance
}

struct Douyin_PicoDisplayInfo: @unchecked Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var comboSumCount: UInt64 {
    get {return _storage._comboSumCount}
    set {_uniqueStorage()._comboSumCount = newValue}
  }

  var emoji: String {
    get {return _storage._emoji}
    set {_uniqueStorage()._emoji = newValue}
  }

  var emojiIcon: Douyin_Image {
    get {return _storage._emojiIcon ?? Douyin_Image()}
    set {_uniqueStorage()._emojiIcon = newValue}
  }
  /// Returns true if `emojiIcon` has been explicitly set.
  var hasEmojiIcon: Bool {return _storage._emojiIcon != nil}
  /// Clears the value of `emojiIcon`. Subsequent reads from it will return its default value.
  mutating func clearEmojiIcon() {_uniqueStorage()._emojiIcon = nil}

  var emojiText: String {
    get {return _storage._emojiText}
    set {_uniqueStorage()._emojiText = newValue}
  }

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}

  fileprivate var _storage = _StorageClass.defaultInstance
}

///*
///1.	bool doubleFlag = 1;
///表示是否为双击点赞。true 表示这是一个双击点赞行为，false 表示不是。
///2.	uint32 seqId = 2;
///序列号，用于标识该双击点赞行为的顺序或唯一性。可能用于跟踪不同的双击点赞操作。
///3.	uint32 renewalsNum = 3;
///续订次数，表示用户在某个时段内多次触发双击点赞的次数。可以理解为重复的双击行为数。
///4.	uint32 triggersNum = 4;
///触发次数，表示该双击点赞行为已经触发的次数。可能用于统计一个用户在当前互动或直播中双击了多少次。
struct Douyin_DoubleLikeDetail: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var doubleFlag: Bool = false

  var seqID: UInt32 = 0

  var renewalsNum: UInt32 = 0

  var triggersNum: UInt32 = 0

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}
}

///*
///1.	bool showText = 1;
///布尔类型字段，用于指示是否显示文本信息。true 表示显示点赞相关的文本信息，false 表示隐藏。
///2.	bool showIcons = 2;
///布尔类型字段，用于指示是否显示图标。true 表示显示点赞相关的图标或视觉元素，false 表示隐藏。
struct Douyin_DisplayControlInfo: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var showText: Bool = false

  var showIcons: Bool = false

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}
}

struct Douyin_EpisodeChatMessage: @unchecked Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var common: Douyin_Message {
    get {return _storage._common ?? Douyin_Message()}
    set {_uniqueStorage()._common = newValue}
  }
  /// Returns true if `common` has been explicitly set.
  var hasCommon: Bool {return _storage._common != nil}
  /// Clears the value of `common`. Subsequent reads from it will return its default value.
  mutating func clearCommon() {_uniqueStorage()._common = nil}

  var user: Douyin_User {
    get {return _storage._user ?? Douyin_User()}
    set {_uniqueStorage()._user = newValue}
  }
  /// Returns true if `user` has been explicitly set.
  var hasUser: Bool {return _storage._user != nil}
  /// Clears the value of `user`. Subsequent reads from it will return its default value.
  mutating func clearUser() {_uniqueStorage()._user = nil}

  var content: String {
    get {return _storage._content}
    set {_uniqueStorage()._content = newValue}
  }

  var visibleToSende: Bool {
    get {return _storage._visibleToSende}
    set {_uniqueStorage()._visibleToSende = newValue}
  }

  ///   BackgroundImage backgroundImage = 5;
  ///   PublicAreaCommon publicAreaCommon = 6;
  var giftImage: Douyin_Image {
    get {return _storage._giftImage ?? Douyin_Image()}
    set {_uniqueStorage()._giftImage = newValue}
  }
  /// Returns true if `giftImage` has been explicitly set.
  var hasGiftImage: Bool {return _storage._giftImage != nil}
  /// Clears the value of `giftImage`. Subsequent reads from it will return its default value.
  mutating func clearGiftImage() {_uniqueStorage()._giftImage = nil}

  var agreeMsgID: UInt64 {
    get {return _storage._agreeMsgID}
    set {_uniqueStorage()._agreeMsgID = newValue}
  }

  var colorValueList: [String] {
    get {return _storage._colorValueList}
    set {_uniqueStorage()._colorValueList = newValue}
  }

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}

  fileprivate var _storage = _StorageClass.defaultInstance
}

struct Douyin_MatchAgainstScoreMessage: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var common: Douyin_Common {
    get {return _common ?? Douyin_Common()}
    set {_common = newValue}
  }
  /// Returns true if `common` has been explicitly set.
  var hasCommon: Bool {return self._common != nil}
  /// Clears the value of `common`. Subsequent reads from it will return its default value.
  mutating func clearCommon() {self._common = nil}

  var against: Douyin_Against {
    get {return _against ?? Douyin_Against()}
    set {_against = newValue}
  }
  /// Returns true if `against` has been explicitly set.
  var hasAgainst: Bool {return self._against != nil}
  /// Clears the value of `against`. Subsequent reads from it will return its default value.
  mutating func clearAgainst() {self._against = nil}

  var matchStatus: UInt32 = 0

  var displayStatus: UInt32 = 0

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}

  fileprivate var _common: Douyin_Common? = nil
  fileprivate var _against: Douyin_Against? = nil
}

struct Douyin_Against: @unchecked Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var leftName: String {
    get {return _storage._leftName}
    set {_uniqueStorage()._leftName = newValue}
  }

  var leftLogo: Douyin_Image {
    get {return _storage._leftLogo ?? Douyin_Image()}
    set {_uniqueStorage()._leftLogo = newValue}
  }
  /// Returns true if `leftLogo` has been explicitly set.
  var hasLeftLogo: Bool {return _storage._leftLogo != nil}
  /// Clears the value of `leftLogo`. Subsequent reads from it will return its default value.
  mutating func clearLeftLogo() {_uniqueStorage()._leftLogo = nil}

  var leftGoal: String {
    get {return _storage._leftGoal}
    set {_uniqueStorage()._leftGoal = newValue}
  }

  ///  LeftPlayersList leftPlayersList = 4;
  ///  LeftGoalStageDetail leftGoalStageDetail = 5;
  var rightName: String {
    get {return _storage._rightName}
    set {_uniqueStorage()._rightName = newValue}
  }

  var rightLogo: Douyin_Image {
    get {return _storage._rightLogo ?? Douyin_Image()}
    set {_uniqueStorage()._rightLogo = newValue}
  }
  /// Returns true if `rightLogo` has been explicitly set.
  var hasRightLogo: Bool {return _storage._rightLogo != nil}
  /// Clears the value of `rightLogo`. Subsequent reads from it will return its default value.
  mutating func clearRightLogo() {_uniqueStorage()._rightLogo = nil}

  var rightGoal: String {
    get {return _storage._rightGoal}
    set {_uniqueStorage()._rightGoal = newValue}
  }

  ///  RightPlayersList rightPlayersList  = 9;
  ///  RightGoalStageDetail rightGoalStageDetail = 10;
  var timestamp: UInt64 {
    get {return _storage._timestamp}
    set {_uniqueStorage()._timestamp = newValue}
  }

  var version: UInt64 {
    get {return _storage._version}
    set {_uniqueStorage()._version = newValue}
  }

  var leftTeamID: UInt64 {
    get {return _storage._leftTeamID}
    set {_uniqueStorage()._leftTeamID = newValue}
  }

  var rightTeamID: UInt64 {
    get {return _storage._rightTeamID}
    set {_uniqueStorage()._rightTeamID = newValue}
  }

  var diffSei2AbsSecond: UInt64 {
    get {return _storage._diffSei2AbsSecond}
    set {_uniqueStorage()._diffSei2AbsSecond = newValue}
  }

  var finalGoalStage: UInt32 {
    get {return _storage._finalGoalStage}
    set {_uniqueStorage()._finalGoalStage = newValue}
  }

  var currentGoalStage: UInt32 {
    get {return _storage._currentGoalStage}
    set {_uniqueStorage()._currentGoalStage = newValue}
  }

  var leftScoreAddition: UInt32 {
    get {return _storage._leftScoreAddition}
    set {_uniqueStorage()._leftScoreAddition = newValue}
  }

  var rightScoreAddition: UInt32 {
    get {return _storage._rightScoreAddition}
    set {_uniqueStorage()._rightScoreAddition = newValue}
  }

  var leftGoalInt: UInt64 {
    get {return _storage._leftGoalInt}
    set {_uniqueStorage()._leftGoalInt = newValue}
  }

  var rightGoalInt: UInt64 {
    get {return _storage._rightGoalInt}
    set {_uniqueStorage()._rightGoalInt = newValue}
  }

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}

  fileprivate var _storage = _StorageClass.defaultInstance
}

///*
///字段解析：
///
///1.	string method = 1;
///表示消息的处理方法，可能是用于标识当前消息类型或处理机制的字符串。
///2.	uint64 msgId = 2;
///消息的唯一标识符，以 64 位无符号整数存储，用于区分不同的消息。
///3.	uint64 roomId = 3;
///房间的唯一标识符，表示该消息所属的直播房间或聊天房间。
///4.	uint64 createTime = 4;
///消息的创建时间戳，用 64 位无符号整数表示。可以用于消息的时间排序。
///5.	uint32 monitor = 5;
///用于监控的字段，可能用于跟踪或分析消息的状态。
///6.	bool isShowMsg = 6;
///布尔字段，表示该消息是否应该展示。true 表示消息应该显示，false 表示不显示。
///7.	string describe = 7;
///消息的描述信息，存储为字符串，用于对消息进行额外的说明或解释。
///8.	uint64 foldType = 9;
///折叠类型，表示消息是否可以折叠显示，用于控制消息的展示形态。
///9.	uint64 anchorFoldType = 10;
///主播折叠类型，可能是专用于主播消息的折叠控制。
///10.	uint64 priorityScore = 11;
///消息的优先级分数，用 64 位无符号整数表示。分数越高的消息可能会被优先处理或展示。
///11.	string logId = 12;
///日志标识符，用于记录或跟踪该消息在日志系统中的信息。
///12.	string msgProcessFilterK = 13;
///消息处理过滤器的键，可能用于消息的处理和过滤逻辑。
///13.	string msgProcessFilterV = 14;
///消息处理过滤器的值，配合键一起用于消息的处理和过滤。
///14.	User user = 15;
///用户信息对象，包含发送消息的用户的详细信息。
///15.	uint64 anchorFoldTypeV2 = 17;
///主播折叠类型的第二版本，表示可能是针对某些新的功能或逻辑。
///16.	uint64 processAtSeiTimeMs = 18;
///消息在 SEI（Supplemental Enhancement Information）时间的处理时间戳，可能与视频或媒体流同步。
///17.	uint64 randomDispatchMs = 19;
///随机分发时间，可能用于消息的随机分发延迟，以毫秒为单位。
///18.	bool isDispatch = 20;
///是否需要分发消息，true 表示消息需要分发给其他用户。
///19.	uint64 channelId = 21;
///频道标识符，表示消息所属的频道。
///20.	uint64 diffSei2absSecond = 22;
///SEI 与绝对时间的差异，可能用于视频流和消息的同步计算。
///21.	uint64 anchorFoldDuration = 23;
///主播消息折叠的持续时间，可能用于控制消息在折叠状态下保持多长时间。
struct Douyin_Common: @unchecked Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var method: String {
    get {return _storage._method}
    set {_uniqueStorage()._method = newValue}
  }

  var msgID: UInt64 {
    get {return _storage._msgID}
    set {_uniqueStorage()._msgID = newValue}
  }

  var roomID: UInt64 {
    get {return _storage._roomID}
    set {_uniqueStorage()._roomID = newValue}
  }

  var createTime: UInt64 {
    get {return _storage._createTime}
    set {_uniqueStorage()._createTime = newValue}
  }

  var monitor: UInt32 {
    get {return _storage._monitor}
    set {_uniqueStorage()._monitor = newValue}
  }

  var isShowMsg: Bool {
    get {return _storage._isShowMsg}
    set {_uniqueStorage()._isShowMsg = newValue}
  }

  var describe: String {
    get {return _storage._describe}
    set {_uniqueStorage()._describe = newValue}
  }

  ///  DisplayText displayText = 8;
  var foldType: UInt64 {
    get {return _storage._foldType}
    set {_uniqueStorage()._foldType = newValue}
  }

  var anchorFoldType: UInt64 {
    get {return _storage._anchorFoldType}
    set {_uniqueStorage()._anchorFoldType = newValue}
  }

  var priorityScore: UInt64 {
    get {return _storage._priorityScore}
    set {_uniqueStorage()._priorityScore = newValue}
  }

  var logID: String {
    get {return _storage._logID}
    set {_uniqueStorage()._logID = newValue}
  }

  var msgProcessFilterK: String {
    get {return _storage._msgProcessFilterK}
    set {_uniqueStorage()._msgProcessFilterK = newValue}
  }

  var msgProcessFilterV: String {
    get {return _storage._msgProcessFilterV}
    set {_uniqueStorage()._msgProcessFilterV = newValue}
  }

  var user: Douyin_User {
    get {return _storage._user ?? Douyin_User()}
    set {_uniqueStorage()._user = newValue}
  }
  /// Returns true if `user` has been explicitly set.
  var hasUser: Bool {return _storage._user != nil}
  /// Clears the value of `user`. Subsequent reads from it will return its default value.
  mutating func clearUser() {_uniqueStorage()._user = nil}

  ///  Room room = 16;
  var anchorFoldTypeV2: UInt64 {
    get {return _storage._anchorFoldTypeV2}
    set {_uniqueStorage()._anchorFoldTypeV2 = newValue}
  }

  var processAtSeiTimeMs: UInt64 {
    get {return _storage._processAtSeiTimeMs}
    set {_uniqueStorage()._processAtSeiTimeMs = newValue}
  }

  var randomDispatchMs: UInt64 {
    get {return _storage._randomDispatchMs}
    set {_uniqueStorage()._randomDispatchMs = newValue}
  }

  var isDispatch: Bool {
    get {return _storage._isDispatch}
    set {_uniqueStorage()._isDispatch = newValue}
  }

  var channelID: UInt64 {
    get {return _storage._channelID}
    set {_uniqueStorage()._channelID = newValue}
  }

  var diffSei2AbsSecond: UInt64 {
    get {return _storage._diffSei2AbsSecond}
    set {_uniqueStorage()._diffSei2AbsSecond = newValue}
  }

  var anchorFoldDuration: UInt64 {
    get {return _storage._anchorFoldDuration}
    set {_uniqueStorage()._anchorFoldDuration = newValue}
  }

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}

  fileprivate var _storage = _StorageClass.defaultInstance
}

///*
///字段解析：
///
///1.	uint64 id = 1;
///•	用户的唯一标识符，用64位无符号整数存储，用于在系统中区分不同用户。
///2.	uint64 shortId = 2;
///•	用户的短ID，通常是一个更简短的用户标识符，便于展示或查找。
///3.	string nickName = 3;
///•	用户的昵称，以字符串形式存储，表示用户在平台上的显示名称。
///4.	uint32 gender = 4;
///•	用户的性别，用32位无符号整数表示，可能使用数值来表示男性、女性或其他。
///5.	string Signature = 5;
///•	用户的个性签名，通常用于描述用户的自我介绍或个性化信息。
///6.	uint32 Level = 6;
///•	用户的等级，用32位无符号整数表示，可能与用户的活跃度、贡献度或经验相关。
///7.	uint64 Birthday = 7;
///•	用户的生日，以64位无符号整数存储，通常为时间戳格式。
///8.	string Telephone = 8;
///•	用户的电话号码，通常用于登录、验证或找回账号。
///9.	Image AvatarThumb = 9;
///•	用户头像的缩略图，Image 类型存储了用户头像的小图版本。
///10.	Image AvatarMedium = 10;
///•	用户头像的中等尺寸图像，Image 类型存储用户头像的中等版本。
///11.	Image AvatarLarge = 11;
///•	用户头像的大图，Image 类型存储用户头像的大图版本。
///12.	bool Verified = 12;
///•	用户是否经过验证，true 表示用户已通过平台的身份或资质验证。
///13.	uint32 Experience = 13;
///•	用户的经验值，用32位无符号整数存储，可能影响用户等级或权利。
///14.	string city = 14;
///•	用户所在的城市，表示用户的地理位置信息。
///15.	int32 Status = 15;
///•	用户的状态，用32位整数表示，可能指示用户当前是否活跃、禁用等状态。
///16.	uint64 CreateTime = 16;
///•	用户账户创建时间，以64位无符号整数存储，通常是时间戳格式。
///17.	uint64 ModifyTime = 17;
///•	用户信息的最后修改时间，以64位无符号整数存储。
///18.	uint32 Secret = 18;
///•	用户的隐私设置或安全级别，用32位无符号整数表示，控制用户信息的公开范围。
///19.	string ShareQrcodeUri = 19;
///•	用户的分享二维码链接，通常用于生成用户的二维码以进行分享。
///20.	uint32 IncomeSharePercent = 20;
///•	用户的收益分成百分比，表示用户在平台上的收入分成比例。
///21.	repeated Image BadgeImageList = 21;
///•	用户获得的徽章图片列表，Image 类型存储，表示用户的荣誉或成就。
///22.	FollowInfo FollowInfo = 22;
///•	用户的关注信息，FollowInfo 类型存储用户的关注和粉丝数量。
///23.	FansClub FansClub = 24;
///•	用户的粉丝俱乐部信息，FansClub 类型存储。
///24.	string SpecialId = 26;
///•	用户的特殊ID，可能用于某些特殊功能或内部标识。
///25.	Image AvatarBorder = 27;
///•	用户头像边框图片，Image 类型表示，可能是用户身份或等级的象征。
///26.	Image Medal = 28;
///•	用户的勋章图片，Image 类型表示，代表用户的特殊荣誉或成就。
///27.	repeated Image RealTimeIconsList = 29;
///•	实时图标列表，表示用户在不同场景下展示的实时图标，如在线状态等。
///28.	string displayId = 38;
///•	用户的展示ID，通常用于在前端展示给其他用户的唯一标识。
///29.	string secUid = 46;
///•	用户的安全UID，用于平台内部的用户安全管理。
///30.	uint64 fanTicketCount = 1022;
///•	用户的粉丝票数，通常用于表示用户的受欢迎程度或平台奖励。
///31.	string idStr = 1028;
///•	用户的ID字符串版本，提供一种更易展示和处理的用户ID。
///32.	uint32 ageRange = 1045;
///•	用户的年龄范围，用32位无符号整数表示，可能用于平台的内容推荐或过滤。
struct Douyin_User: @unchecked Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var id: UInt64 {
    get {return _storage._id}
    set {_uniqueStorage()._id = newValue}
  }

  var shortID: UInt64 {
    get {return _storage._shortID}
    set {_uniqueStorage()._shortID = newValue}
  }

  var nickName: String {
    get {return _storage._nickName}
    set {_uniqueStorage()._nickName = newValue}
  }

  var gender: UInt32 {
    get {return _storage._gender}
    set {_uniqueStorage()._gender = newValue}
  }

  var signature: String {
    get {return _storage._signature}
    set {_uniqueStorage()._signature = newValue}
  }

  var level: UInt32 {
    get {return _storage._level}
    set {_uniqueStorage()._level = newValue}
  }

  var birthday: UInt64 {
    get {return _storage._birthday}
    set {_uniqueStorage()._birthday = newValue}
  }

  var telephone: String {
    get {return _storage._telephone}
    set {_uniqueStorage()._telephone = newValue}
  }

  var avatarThumb: Douyin_Image {
    get {return _storage._avatarThumb ?? Douyin_Image()}
    set {_uniqueStorage()._avatarThumb = newValue}
  }
  /// Returns true if `avatarThumb` has been explicitly set.
  var hasAvatarThumb: Bool {return _storage._avatarThumb != nil}
  /// Clears the value of `avatarThumb`. Subsequent reads from it will return its default value.
  mutating func clearAvatarThumb() {_uniqueStorage()._avatarThumb = nil}

  var avatarMedium: Douyin_Image {
    get {return _storage._avatarMedium ?? Douyin_Image()}
    set {_uniqueStorage()._avatarMedium = newValue}
  }
  /// Returns true if `avatarMedium` has been explicitly set.
  var hasAvatarMedium: Bool {return _storage._avatarMedium != nil}
  /// Clears the value of `avatarMedium`. Subsequent reads from it will return its default value.
  mutating func clearAvatarMedium() {_uniqueStorage()._avatarMedium = nil}

  var avatarLarge: Douyin_Image {
    get {return _storage._avatarLarge ?? Douyin_Image()}
    set {_uniqueStorage()._avatarLarge = newValue}
  }
  /// Returns true if `avatarLarge` has been explicitly set.
  var hasAvatarLarge: Bool {return _storage._avatarLarge != nil}
  /// Clears the value of `avatarLarge`. Subsequent reads from it will return its default value.
  mutating func clearAvatarLarge() {_uniqueStorage()._avatarLarge = nil}

  var verified: Bool {
    get {return _storage._verified}
    set {_uniqueStorage()._verified = newValue}
  }

  var experience: UInt32 {
    get {return _storage._experience}
    set {_uniqueStorage()._experience = newValue}
  }

  var city: String {
    get {return _storage._city}
    set {_uniqueStorage()._city = newValue}
  }

  var status: Int32 {
    get {return _storage._status}
    set {_uniqueStorage()._status = newValue}
  }

  var createTime: UInt64 {
    get {return _storage._createTime}
    set {_uniqueStorage()._createTime = newValue}
  }

  var modifyTime: UInt64 {
    get {return _storage._modifyTime}
    set {_uniqueStorage()._modifyTime = newValue}
  }

  var secret: UInt32 {
    get {return _storage._secret}
    set {_uniqueStorage()._secret = newValue}
  }

  var shareQrcodeUri: String {
    get {return _storage._shareQrcodeUri}
    set {_uniqueStorage()._shareQrcodeUri = newValue}
  }

  var incomeSharePercent: UInt32 {
    get {return _storage._incomeSharePercent}
    set {_uniqueStorage()._incomeSharePercent = newValue}
  }

  var badgeImageList: [Douyin_Image] {
    get {return _storage._badgeImageList}
    set {_uniqueStorage()._badgeImageList = newValue}
  }

  var followInfo: Douyin_FollowInfo {
    get {return _storage._followInfo ?? Douyin_FollowInfo()}
    set {_uniqueStorage()._followInfo = newValue}
  }
  /// Returns true if `followInfo` has been explicitly set.
  var hasFollowInfo: Bool {return _storage._followInfo != nil}
  /// Clears the value of `followInfo`. Subsequent reads from it will return its default value.
  mutating func clearFollowInfo() {_uniqueStorage()._followInfo = nil}

  ///  PayGrade PayGrade = 23;
  var fansClub: Douyin_FansClub {
    get {return _storage._fansClub ?? Douyin_FansClub()}
    set {_uniqueStorage()._fansClub = newValue}
  }
  /// Returns true if `fansClub` has been explicitly set.
  var hasFansClub: Bool {return _storage._fansClub != nil}
  /// Clears the value of `fansClub`. Subsequent reads from it will return its default value.
  mutating func clearFansClub() {_uniqueStorage()._fansClub = nil}

  ///  Border Border = 25;
  var specialID: String {
    get {return _storage._specialID}
    set {_uniqueStorage()._specialID = newValue}
  }

  var avatarBorder: Douyin_Image {
    get {return _storage._avatarBorder ?? Douyin_Image()}
    set {_uniqueStorage()._avatarBorder = newValue}
  }
  /// Returns true if `avatarBorder` has been explicitly set.
  var hasAvatarBorder: Bool {return _storage._avatarBorder != nil}
  /// Clears the value of `avatarBorder`. Subsequent reads from it will return its default value.
  mutating func clearAvatarBorder() {_uniqueStorage()._avatarBorder = nil}

  var medal: Douyin_Image {
    get {return _storage._medal ?? Douyin_Image()}
    set {_uniqueStorage()._medal = newValue}
  }
  /// Returns true if `medal` has been explicitly set.
  var hasMedal: Bool {return _storage._medal != nil}
  /// Clears the value of `medal`. Subsequent reads from it will return its default value.
  mutating func clearMedal() {_uniqueStorage()._medal = nil}

  var realTimeIconsList: [Douyin_Image] {
    get {return _storage._realTimeIconsList}
    set {_uniqueStorage()._realTimeIconsList = newValue}
  }

  var displayID: String {
    get {return _storage._displayID}
    set {_uniqueStorage()._displayID = newValue}
  }

  var secUid: String {
    get {return _storage._secUid}
    set {_uniqueStorage()._secUid = newValue}
  }

  var fanTicketCount: UInt64 {
    get {return _storage._fanTicketCount}
    set {_uniqueStorage()._fanTicketCount = newValue}
  }

  var idStr: String {
    get {return _storage._idStr}
    set {_uniqueStorage()._idStr = newValue}
  }

  var ageRange: UInt32 {
    get {return _storage._ageRange}
    set {_uniqueStorage()._ageRange = newValue}
  }

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}

  fileprivate var _storage = _StorageClass.defaultInstance
}

///*
///1.	uint64 followingCount = 1;
///用户关注的人的数量。
///2.	uint64 followerCount = 2;
///用户的粉丝数量。
///3.	uint64 followStatus = 3;
///用户的关注状态，可能表示是否正在关注某人，或某人是否已关注用户。
///4.	uint64 pushStatus = 4;
///推送状态，表示是否接收来自关注对象的推送通知。
///5.	string remarkName = 5;
///对关注对象的备注名，允许用户为关注对象设置自定义的名字。
///6.	string followerCountStr = 6;
///粉丝数量的字符串形式，适用于展示大于一定数值的粉丝数（如 “1.2K”）。
///7.	string followingCountStr = 7;
///关注人数的字符串形式，类似于 followerCountStr，便于显示较大的数字。
struct Douyin_FollowInfo: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var followingCount: UInt64 = 0

  var followerCount: UInt64 = 0

  var followStatus: UInt64 = 0

  var pushStatus: UInt64 = 0

  var remarkName: String = String()

  var followerCountStr: String = String()

  var followingCountStr: String = String()

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}
}

///*
///1.	repeated string urlListList = 1;
///图片 URL 的列表，通常包含不同分辨率或版本的图片链接。
///2.	string uri = 2;
///图片的唯一标识符（URI），用于标记图片在系统中的位置。
///3.	uint64 height = 3;
///图片的高度，单位为像素。
///4.	uint64 width = 4;
///图片的宽度，单位为像素。
///5.	string avgColor = 5;
///图片的平均颜色值，通常用于背景色或占位符色。
///6.	uint32 imageType = 6;
///图片类型，表示不同类别或格式的图片（例如静态图片或动图）。
///7.	string openWebUrl = 7;
///点击图片时要打开的网页 URL，通常用于广告或跳转链接。
///8.	ImageContent content = 8;
///图片的内容信息，可能包含图片的详细描述或元数据。
///9.	bool isAnimated = 9;
///是否为动图，true 表示该图片是动态图片（如 GIF）。
///10.	NinePatchSetting FlexSettingList = 10;
///图片的 9-patch 设置，允许对图片的拉伸和缩放进行灵活设置（用于 Android UI 开发中的 9-patch 图片）。
///11.	NinePatchSetting TextSettingList = 11;
///文本的 9-patch 设置，通常用于定义图片上文本区域的拉伸和显示。
struct Douyin_Image: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var urlListList: [String] = []

  var uri: String = String()

  var height: UInt64 = 0

  var width: UInt64 = 0

  var avgColor: String = String()

  var imageType: UInt32 = 0

  var openWebURL: String = String()

  var content: Douyin_ImageContent {
    get {return _content ?? Douyin_ImageContent()}
    set {_content = newValue}
  }
  /// Returns true if `content` has been explicitly set.
  var hasContent: Bool {return self._content != nil}
  /// Clears the value of `content`. Subsequent reads from it will return its default value.
  mutating func clearContent() {self._content = nil}

  var isAnimated: Bool = false

  var flexSettingList: Douyin_NinePatchSetting {
    get {return _flexSettingList ?? Douyin_NinePatchSetting()}
    set {_flexSettingList = newValue}
  }
  /// Returns true if `flexSettingList` has been explicitly set.
  var hasFlexSettingList: Bool {return self._flexSettingList != nil}
  /// Clears the value of `flexSettingList`. Subsequent reads from it will return its default value.
  mutating func clearFlexSettingList() {self._flexSettingList = nil}

  var textSettingList: Douyin_NinePatchSetting {
    get {return _textSettingList ?? Douyin_NinePatchSetting()}
    set {_textSettingList = newValue}
  }
  /// Returns true if `textSettingList` has been explicitly set.
  var hasTextSettingList: Bool {return self._textSettingList != nil}
  /// Clears the value of `textSettingList`. Subsequent reads from it will return its default value.
  mutating func clearTextSettingList() {self._textSettingList = nil}

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}

  fileprivate var _content: Douyin_ImageContent? = nil
  fileprivate var _flexSettingList: Douyin_NinePatchSetting? = nil
  fileprivate var _textSettingList: Douyin_NinePatchSetting? = nil
}

struct Douyin_NinePatchSetting: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var settingListList: [String] = []

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}
}

///*
///1.	string name = 1;
///图片的名称，表示图片的标识或说明。
///2.	string fontColor = 2;
///字体颜色，可能是用于显示在图片上的文本颜色，格式通常为 HEX 色值（如 #FFFFFF）。
///3.	uint64 level = 3;
///图片的等级，通常用于表示图片的质量、优先级或其他分类信息（例如用户头像的等级）。
///4.	string alternativeText = 4;
///图片的替代文本，用于在图片无法加载时显示的说明文本，或者用于无障碍辅助功能（如屏幕阅读器）。
struct Douyin_ImageContent: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var name: String = String()

  var fontColor: String = String()

  var level: UInt64 = 0

  var alternativeText: String = String()

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}
}

struct Douyin_PushFrame: @unchecked Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var seqID: UInt64 = 0

  var logID: UInt64 = 0

  var service: UInt64 = 0

  var method: UInt64 = 0

  var headersList: [Douyin_HeadersList] = []

  var payloadEncoding: String = String()

  var payloadType: String = String()

  var payload: Data = Data()

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}
}

struct Douyin_kk: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var k: UInt32 = 0

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}
}

struct Douyin_SendMessageBody: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var conversationID: String = String()

  var conversationType: UInt32 = 0

  var conversationShortID: UInt64 = 0

  var content: String = String()

  var ext: [Douyin_ExtList] = []

  var messageType: UInt32 = 0

  var ticket: String = String()

  var clientMessageID: String = String()

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}
}

struct Douyin_ExtList: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var key: String = String()

  var value: String = String()

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}
}

struct Douyin_Rsp: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var a: Int32 = 0

  var b: Int32 = 0

  var c: Int32 = 0

  var d: String = String()

  var e: Int32 = 0

  var f: Douyin_Rsp.F {
    get {return _f ?? Douyin_Rsp.F()}
    set {_f = newValue}
  }
  /// Returns true if `f` has been explicitly set.
  var hasF: Bool {return self._f != nil}
  /// Clears the value of `f`. Subsequent reads from it will return its default value.
  mutating func clearF() {self._f = nil}

  var g: String = String()

  var h: UInt64 = 0

  var i: UInt64 = 0

  var j: UInt64 = 0

  var unknownFields = SwiftProtobuf.UnknownStorage()

  struct F: Sendable {
    // SwiftProtobuf.Message conformance is added in an extension below. See the
    // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
    // methods supported on all messages.

    var q1: UInt64 = 0

    var q3: UInt64 = 0

    var q4: String = String()

    var q5: UInt64 = 0

    var unknownFields = SwiftProtobuf.UnknownStorage()

    init() {}
  }

  init() {}

  fileprivate var _f: Douyin_Rsp.F? = nil
}

struct Douyin_PreMessage: @unchecked Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var cmd: UInt32 {
    get {return _storage._cmd}
    set {_uniqueStorage()._cmd = newValue}
  }

  var sequenceID: UInt32 {
    get {return _storage._sequenceID}
    set {_uniqueStorage()._sequenceID = newValue}
  }

  var sdkVersion: String {
    get {return _storage._sdkVersion}
    set {_uniqueStorage()._sdkVersion = newValue}
  }

  var token: String {
    get {return _storage._token}
    set {_uniqueStorage()._token = newValue}
  }

  var refer: UInt32 {
    get {return _storage._refer}
    set {_uniqueStorage()._refer = newValue}
  }

  var inboxType: UInt32 {
    get {return _storage._inboxType}
    set {_uniqueStorage()._inboxType = newValue}
  }

  var buildNumber: String {
    get {return _storage._buildNumber}
    set {_uniqueStorage()._buildNumber = newValue}
  }

  var sendMessageBody: Douyin_SendMessageBody {
    get {return _storage._sendMessageBody ?? Douyin_SendMessageBody()}
    set {_uniqueStorage()._sendMessageBody = newValue}
  }
  /// Returns true if `sendMessageBody` has been explicitly set.
  var hasSendMessageBody: Bool {return _storage._sendMessageBody != nil}
  /// Clears the value of `sendMessageBody`. Subsequent reads from it will return its default value.
  mutating func clearSendMessageBody() {_uniqueStorage()._sendMessageBody = nil}

  /// 字段名待定
  var aa: String {
    get {return _storage._aa}
    set {_uniqueStorage()._aa = newValue}
  }

  var devicePlatform: String {
    get {return _storage._devicePlatform}
    set {_uniqueStorage()._devicePlatform = newValue}
  }

  var headers: [Douyin_HeadersList] {
    get {return _storage._headers}
    set {_uniqueStorage()._headers = newValue}
  }

  var authType: UInt32 {
    get {return _storage._authType}
    set {_uniqueStorage()._authType = newValue}
  }

  var biz: String {
    get {return _storage._biz}
    set {_uniqueStorage()._biz = newValue}
  }

  var access: String {
    get {return _storage._access}
    set {_uniqueStorage()._access = newValue}
  }

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}

  fileprivate var _storage = _StorageClass.defaultInstance
}

struct Douyin_HeadersList: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var key: String = String()

  var value: String = String()

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}
}

///*
///•	string listFansGroupUrl = 1;
///表示粉丝群组的URL链接，用于访问或查看粉丝群的列表。
struct Douyin_FansGroupInfo: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var listFansGroupURL: String = String()

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}
}

///*
///•	FansClubData data = 1;
///代表粉丝俱乐部的主要数据，使用FansClubData类型存储粉丝俱乐部的详细信息。
///•	map<int32, FansClubData> preferData = 2;
///映射结构，用于存储多个FansClubData对象的集合。映射键为32位整型，值为FansClubData，可能表示不同等级或优先级的粉丝俱乐部数据。
struct Douyin_FansClub: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var data: Douyin_FansClubData {
    get {return _data ?? Douyin_FansClubData()}
    set {_data = newValue}
  }
  /// Returns true if `data` has been explicitly set.
  var hasData: Bool {return self._data != nil}
  /// Clears the value of `data`. Subsequent reads from it will return its default value.
  mutating func clearData() {self._data = nil}

  var preferData: Dictionary<Int32,Douyin_FansClubData> = [:]

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}

  fileprivate var _data: Douyin_FansClubData? = nil
}

///*
///•	string clubName = 1;
///粉丝俱乐部的名称，表示俱乐部的标识或显示名称。
///•	int32 level = 2;
///粉丝俱乐部的等级，用于表示用户在该俱乐部中的等级或身份。
///•	int32 userFansClubStatus = 3;
///用户在粉丝俱乐部的状态，可能用于表示用户是否是该俱乐部的成员、管理员等。
///•	UserBadge badge = 4;
///用户的徽章信息，表示用户在俱乐部中的身份或成就。
///•	repeated int64 availableGiftIds = 5;
///可用礼物ID的列表，表示用户可以使用的礼物，存储为64位整型。
///•	int64 anchorId = 6;
///主播的ID，表示该粉丝俱乐部关联的主播的标识。
struct Douyin_FansClubData: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var clubName: String = String()

  var level: Int32 = 0

  var userFansClubStatus: Int32 = 0

  var badge: Douyin_UserBadge {
    get {return _badge ?? Douyin_UserBadge()}
    set {_badge = newValue}
  }
  /// Returns true if `badge` has been explicitly set.
  var hasBadge: Bool {return self._badge != nil}
  /// Clears the value of `badge`. Subsequent reads from it will return its default value.
  mutating func clearBadge() {self._badge = nil}

  var availableGiftIds: [Int64] = []

  var anchorID: Int64 = 0

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}

  fileprivate var _badge: Douyin_UserBadge? = nil
}

///*
///
///•	map<int32, Image> icons = 1;
///表示用户徽章的图标集合。映射的键是32位整型，可能表示不同类型的徽章或等级，值是Image类型，存储徽章的图像信息。
///•	string title = 2;
///徽章的标题，用于描述徽章的名称或类别，帮助用户理解该徽章的含义或成就。
struct Douyin_UserBadge: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var icons: Dictionary<Int32,Douyin_Image> = [:]

  var title: String = String()

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}
}

/// from https://github.com/HaoDong108/DouyinBarrageGrab/blob/main/BarrageGrab/proto/message.proto
/// status = 3 下播
struct Douyin_ControlMessage: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var common: Douyin_Common {
    get {return _common ?? Douyin_Common()}
    set {_common = newValue}
  }
  /// Returns true if `common` has been explicitly set.
  var hasCommon: Bool {return self._common != nil}
  /// Clears the value of `common`. Subsequent reads from it will return its default value.
  mutating func clearCommon() {self._common = nil}

  var status: Int32 = 0

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}

  fileprivate var _common: Douyin_Common? = nil
}

/// from https://github.com/HaoDong108/DouyinBarrageGrab/blob/main/BarrageGrab/proto/message.proto
struct Douyin_FansclubMessage: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var commonInfo: Douyin_Common {
    get {return _commonInfo ?? Douyin_Common()}
    set {_commonInfo = newValue}
  }
  /// Returns true if `commonInfo` has been explicitly set.
  var hasCommonInfo: Bool {return self._commonInfo != nil}
  /// Clears the value of `commonInfo`. Subsequent reads from it will return its default value.
  mutating func clearCommonInfo() {self._commonInfo = nil}

  /// 升级是1，加入是2
  var type: Int32 = 0

  var content: String = String()

  var user: Douyin_User {
    get {return _user ?? Douyin_User()}
    set {_user = newValue}
  }
  /// Returns true if `user` has been explicitly set.
  var hasUser: Bool {return self._user != nil}
  /// Clears the value of `user`. Subsequent reads from it will return its default value.
  mutating func clearUser() {self._user = nil}

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}

  fileprivate var _commonInfo: Douyin_Common? = nil
  fileprivate var _user: Douyin_User? = nil
}

/// from https://github.com/scx567888/live-room-watcher/blob/master/src/main/proto/douyin_hack/webcast/im/RoomRankMessage.proto
/// 直播间排行榜
struct Douyin_RoomRankMessage: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var common: Douyin_Common {
    get {return _common ?? Douyin_Common()}
    set {_common = newValue}
  }
  /// Returns true if `common` has been explicitly set.
  var hasCommon: Bool {return self._common != nil}
  /// Clears the value of `common`. Subsequent reads from it will return its default value.
  mutating func clearCommon() {self._common = nil}

  var ranksList: [Douyin_RoomRankMessage.RoomRank] = []

  var unknownFields = SwiftProtobuf.UnknownStorage()

  struct RoomRank: Sendable {
    // SwiftProtobuf.Message conformance is added in an extension below. See the
    // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
    // methods supported on all messages.

    var user: Douyin_User {
      get {return _user ?? Douyin_User()}
      set {_user = newValue}
    }
    /// Returns true if `user` has been explicitly set.
    var hasUser: Bool {return self._user != nil}
    /// Clears the value of `user`. Subsequent reads from it will return its default value.
    mutating func clearUser() {self._user = nil}

    var scoreStr: String = String()

    var profileHidden: Bool = false

    var unknownFields = SwiftProtobuf.UnknownStorage()

    init() {}

    fileprivate var _user: Douyin_User? = nil
  }

  init() {}

  fileprivate var _common: Douyin_Common? = nil
}

/// from https://github.com/scx567888/live-room-watcher/blob/master/src/main/proto/douyin_hack/webcast/im/RoomMessage.proto
struct Douyin_RoomMessage: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var common: Douyin_Common {
    get {return _common ?? Douyin_Common()}
    set {_common = newValue}
  }
  /// Returns true if `common` has been explicitly set.
  var hasCommon: Bool {return self._common != nil}
  /// Clears the value of `common`. Subsequent reads from it will return its default value.
  mutating func clearCommon() {self._common = nil}

  var content: String = String()

  var supprotLandscape: Bool = false

  var roommessagetype: Douyin_RoomMsgTypeEnum = .defaultroommsg

  var systemTopMsg: Bool = false

  var forcedGuarantee: Bool = false

  var bizScene: String = String()

  var buriedPointMap: Dictionary<String,String> = [:]

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}

  fileprivate var _common: Douyin_Common? = nil
}

// MARK: - Code below here is support for the SwiftProtobuf runtime.

fileprivate let _protobuf_package = "douyin"

extension Douyin_CommentTypeTag: SwiftProtobuf._ProtoNameProviding {
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    0: .same(proto: "COMMENTTYPETAGUNKNOWN"),
    1: .same(proto: "COMMENTTYPETAGSTAR"),
  ]
}

extension Douyin_RoomMsgTypeEnum: SwiftProtobuf._ProtoNameProviding {
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    0: .same(proto: "DEFAULTROOMMSG"),
    1: .same(proto: "ECOMLIVEREPLAYSAVEROOMMSG"),
    2: .same(proto: "CONSUMERRELATIONROOMMSG"),
    3: .same(proto: "JUMANJIDATAAUTHNOTIFYMSG"),
    4: .same(proto: "VSWELCOMEMSG"),
    5: .same(proto: "MINORREFUNDMSG"),
    6: .same(proto: "PAIDLIVEROOMNOTIFYANCHORMSG"),
    7: .same(proto: "HOSTTEAMSYSTEMMSG"),
  ]
}

extension Douyin_Response: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".Response"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "messagesList"),
    2: .same(proto: "cursor"),
    3: .same(proto: "fetchInterval"),
    4: .same(proto: "now"),
    5: .same(proto: "internalExt"),
    6: .same(proto: "fetchType"),
    7: .same(proto: "routeParams"),
    8: .same(proto: "heartbeatDuration"),
    9: .same(proto: "needAck"),
    10: .same(proto: "pushServer"),
    11: .same(proto: "liveCursor"),
    12: .same(proto: "historyNoMore"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeRepeatedMessageField(value: &self.messagesList) }()
      case 2: try { try decoder.decodeSingularStringField(value: &self.cursor) }()
      case 3: try { try decoder.decodeSingularUInt64Field(value: &self.fetchInterval) }()
      case 4: try { try decoder.decodeSingularUInt64Field(value: &self.now) }()
      case 5: try { try decoder.decodeSingularStringField(value: &self.internalExt) }()
      case 6: try { try decoder.decodeSingularUInt32Field(value: &self.fetchType) }()
      case 7: try { try decoder.decodeMapField(fieldType: SwiftProtobuf._ProtobufMap<SwiftProtobuf.ProtobufString,SwiftProtobuf.ProtobufString>.self, value: &self.routeParams) }()
      case 8: try { try decoder.decodeSingularUInt64Field(value: &self.heartbeatDuration) }()
      case 9: try { try decoder.decodeSingularBoolField(value: &self.needAck) }()
      case 10: try { try decoder.decodeSingularStringField(value: &self.pushServer) }()
      case 11: try { try decoder.decodeSingularStringField(value: &self.liveCursor) }()
      case 12: try { try decoder.decodeSingularBoolField(value: &self.historyNoMore) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    if !self.messagesList.isEmpty {
      try visitor.visitRepeatedMessageField(value: self.messagesList, fieldNumber: 1)
    }
    if !self.cursor.isEmpty {
      try visitor.visitSingularStringField(value: self.cursor, fieldNumber: 2)
    }
    if self.fetchInterval != 0 {
      try visitor.visitSingularUInt64Field(value: self.fetchInterval, fieldNumber: 3)
    }
    if self.now != 0 {
      try visitor.visitSingularUInt64Field(value: self.now, fieldNumber: 4)
    }
    if !self.internalExt.isEmpty {
      try visitor.visitSingularStringField(value: self.internalExt, fieldNumber: 5)
    }
    if self.fetchType != 0 {
      try visitor.visitSingularUInt32Field(value: self.fetchType, fieldNumber: 6)
    }
    if !self.routeParams.isEmpty {
      try visitor.visitMapField(fieldType: SwiftProtobuf._ProtobufMap<SwiftProtobuf.ProtobufString,SwiftProtobuf.ProtobufString>.self, value: self.routeParams, fieldNumber: 7)
    }
    if self.heartbeatDuration != 0 {
      try visitor.visitSingularUInt64Field(value: self.heartbeatDuration, fieldNumber: 8)
    }
    if self.needAck != false {
      try visitor.visitSingularBoolField(value: self.needAck, fieldNumber: 9)
    }
    if !self.pushServer.isEmpty {
      try visitor.visitSingularStringField(value: self.pushServer, fieldNumber: 10)
    }
    if !self.liveCursor.isEmpty {
      try visitor.visitSingularStringField(value: self.liveCursor, fieldNumber: 11)
    }
    if self.historyNoMore != false {
      try visitor.visitSingularBoolField(value: self.historyNoMore, fieldNumber: 12)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: Douyin_Response, rhs: Douyin_Response) -> Bool {
    if lhs.messagesList != rhs.messagesList {return false}
    if lhs.cursor != rhs.cursor {return false}
    if lhs.fetchInterval != rhs.fetchInterval {return false}
    if lhs.now != rhs.now {return false}
    if lhs.internalExt != rhs.internalExt {return false}
    if lhs.fetchType != rhs.fetchType {return false}
    if lhs.routeParams != rhs.routeParams {return false}
    if lhs.heartbeatDuration != rhs.heartbeatDuration {return false}
    if lhs.needAck != rhs.needAck {return false}
    if lhs.pushServer != rhs.pushServer {return false}
    if lhs.liveCursor != rhs.liveCursor {return false}
    if lhs.historyNoMore != rhs.historyNoMore {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Douyin_Message: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".Message"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "method"),
    2: .same(proto: "payload"),
    3: .same(proto: "msgId"),
    4: .same(proto: "msgType"),
    5: .same(proto: "offset"),
    6: .same(proto: "needWrdsStore"),
    7: .same(proto: "wrdsVersion"),
    8: .same(proto: "wrdsSubKey"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularStringField(value: &self.method) }()
      case 2: try { try decoder.decodeSingularBytesField(value: &self.payload) }()
      case 3: try { try decoder.decodeSingularInt64Field(value: &self.msgID) }()
      case 4: try { try decoder.decodeSingularInt32Field(value: &self.msgType) }()
      case 5: try { try decoder.decodeSingularInt64Field(value: &self.offset) }()
      case 6: try { try decoder.decodeSingularBoolField(value: &self.needWrdsStore) }()
      case 7: try { try decoder.decodeSingularInt64Field(value: &self.wrdsVersion) }()
      case 8: try { try decoder.decodeSingularStringField(value: &self.wrdsSubKey) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    if !self.method.isEmpty {
      try visitor.visitSingularStringField(value: self.method, fieldNumber: 1)
    }
    if !self.payload.isEmpty {
      try visitor.visitSingularBytesField(value: self.payload, fieldNumber: 2)
    }
    if self.msgID != 0 {
      try visitor.visitSingularInt64Field(value: self.msgID, fieldNumber: 3)
    }
    if self.msgType != 0 {
      try visitor.visitSingularInt32Field(value: self.msgType, fieldNumber: 4)
    }
    if self.offset != 0 {
      try visitor.visitSingularInt64Field(value: self.offset, fieldNumber: 5)
    }
    if self.needWrdsStore != false {
      try visitor.visitSingularBoolField(value: self.needWrdsStore, fieldNumber: 6)
    }
    if self.wrdsVersion != 0 {
      try visitor.visitSingularInt64Field(value: self.wrdsVersion, fieldNumber: 7)
    }
    if !self.wrdsSubKey.isEmpty {
      try visitor.visitSingularStringField(value: self.wrdsSubKey, fieldNumber: 8)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: Douyin_Message, rhs: Douyin_Message) -> Bool {
    if lhs.method != rhs.method {return false}
    if lhs.payload != rhs.payload {return false}
    if lhs.msgID != rhs.msgID {return false}
    if lhs.msgType != rhs.msgType {return false}
    if lhs.offset != rhs.offset {return false}
    if lhs.needWrdsStore != rhs.needWrdsStore {return false}
    if lhs.wrdsVersion != rhs.wrdsVersion {return false}
    if lhs.wrdsSubKey != rhs.wrdsSubKey {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Douyin_EmojiChatMessage: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".EmojiChatMessage"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "common"),
    2: .same(proto: "user"),
    3: .same(proto: "emojiId"),
    4: .same(proto: "emojiContent"),
    5: .same(proto: "defaultContent"),
    6: .same(proto: "backgroundImage"),
    7: .same(proto: "fromIntercom"),
    8: .same(proto: "intercomHideUserCard"),
  ]

  fileprivate class _StorageClass {
    var _common: Douyin_Common? = nil
    var _user: Douyin_User? = nil
    var _emojiID: Int64 = 0
    var _emojiContent: Douyin_Text? = nil
    var _defaultContent: String = String()
    var _backgroundImage: Douyin_Image? = nil
    var _fromIntercom: Bool = false
    var _intercomHideUserCard: Bool = false

    #if swift(>=5.10)
      // This property is used as the initial default value for new instances of the type.
      // The type itself is protecting the reference to its storage via CoW semantics.
      // This will force a copy to be made of this reference when the first mutation occurs;
      // hence, it is safe to mark this as `nonisolated(unsafe)`.
      static nonisolated(unsafe) let defaultInstance = _StorageClass()
    #else
      static let defaultInstance = _StorageClass()
    #endif

    private init() {}

    init(copying source: _StorageClass) {
      _common = source._common
      _user = source._user
      _emojiID = source._emojiID
      _emojiContent = source._emojiContent
      _defaultContent = source._defaultContent
      _backgroundImage = source._backgroundImage
      _fromIntercom = source._fromIntercom
      _intercomHideUserCard = source._intercomHideUserCard
    }
  }

  fileprivate mutating func _uniqueStorage() -> _StorageClass {
    if !isKnownUniquelyReferenced(&_storage) {
      _storage = _StorageClass(copying: _storage)
    }
    return _storage
  }

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    _ = _uniqueStorage()
    try withExtendedLifetime(_storage) { (_storage: _StorageClass) in
      while let fieldNumber = try decoder.nextFieldNumber() {
        // The use of inline closures is to circumvent an issue where the compiler
        // allocates stack space for every case branch when no optimizations are
        // enabled. https://github.com/apple/swift-protobuf/issues/1034
        switch fieldNumber {
        case 1: try { try decoder.decodeSingularMessageField(value: &_storage._common) }()
        case 2: try { try decoder.decodeSingularMessageField(value: &_storage._user) }()
        case 3: try { try decoder.decodeSingularInt64Field(value: &_storage._emojiID) }()
        case 4: try { try decoder.decodeSingularMessageField(value: &_storage._emojiContent) }()
        case 5: try { try decoder.decodeSingularStringField(value: &_storage._defaultContent) }()
        case 6: try { try decoder.decodeSingularMessageField(value: &_storage._backgroundImage) }()
        case 7: try { try decoder.decodeSingularBoolField(value: &_storage._fromIntercom) }()
        case 8: try { try decoder.decodeSingularBoolField(value: &_storage._intercomHideUserCard) }()
        default: break
        }
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    try withExtendedLifetime(_storage) { (_storage: _StorageClass) in
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every if/case branch local when no optimizations
      // are enabled. https://github.com/apple/swift-protobuf/issues/1034 and
      // https://github.com/apple/swift-protobuf/issues/1182
      try { if let v = _storage._common {
        try visitor.visitSingularMessageField(value: v, fieldNumber: 1)
      } }()
      try { if let v = _storage._user {
        try visitor.visitSingularMessageField(value: v, fieldNumber: 2)
      } }()
      if _storage._emojiID != 0 {
        try visitor.visitSingularInt64Field(value: _storage._emojiID, fieldNumber: 3)
      }
      try { if let v = _storage._emojiContent {
        try visitor.visitSingularMessageField(value: v, fieldNumber: 4)
      } }()
      if !_storage._defaultContent.isEmpty {
        try visitor.visitSingularStringField(value: _storage._defaultContent, fieldNumber: 5)
      }
      try { if let v = _storage._backgroundImage {
        try visitor.visitSingularMessageField(value: v, fieldNumber: 6)
      } }()
      if _storage._fromIntercom != false {
        try visitor.visitSingularBoolField(value: _storage._fromIntercom, fieldNumber: 7)
      }
      if _storage._intercomHideUserCard != false {
        try visitor.visitSingularBoolField(value: _storage._intercomHideUserCard, fieldNumber: 8)
      }
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: Douyin_EmojiChatMessage, rhs: Douyin_EmojiChatMessage) -> Bool {
    if lhs._storage !== rhs._storage {
      let storagesAreEqual: Bool = withExtendedLifetime((lhs._storage, rhs._storage)) { (_args: (_StorageClass, _StorageClass)) in
        let _storage = _args.0
        let rhs_storage = _args.1
        if _storage._common != rhs_storage._common {return false}
        if _storage._user != rhs_storage._user {return false}
        if _storage._emojiID != rhs_storage._emojiID {return false}
        if _storage._emojiContent != rhs_storage._emojiContent {return false}
        if _storage._defaultContent != rhs_storage._defaultContent {return false}
        if _storage._backgroundImage != rhs_storage._backgroundImage {return false}
        if _storage._fromIntercom != rhs_storage._fromIntercom {return false}
        if _storage._intercomHideUserCard != rhs_storage._intercomHideUserCard {return false}
        return true
      }
      if !storagesAreEqual {return false}
    }
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Douyin_ChatMessage: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".ChatMessage"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "common"),
    2: .same(proto: "user"),
    3: .same(proto: "content"),
    4: .same(proto: "visibleToSender"),
    5: .same(proto: "backgroundImage"),
    6: .same(proto: "fullScreenTextColor"),
    7: .same(proto: "backgroundImageV2"),
    8: .same(proto: "publicAreaCommon"),
    9: .same(proto: "giftImage"),
    11: .same(proto: "agreeMsgId"),
    12: .same(proto: "priorityLevel"),
    13: .same(proto: "landscapeAreaCommon"),
    15: .same(proto: "eventTime"),
    16: .same(proto: "sendReview"),
    17: .same(proto: "fromIntercom"),
    18: .same(proto: "intercomHideUserCard"),
    20: .same(proto: "chatBy"),
    21: .same(proto: "individualChatPriority"),
    22: .same(proto: "rtfContent"),
  ]

  fileprivate class _StorageClass {
    var _common: Douyin_Common? = nil
    var _user: Douyin_User? = nil
    var _content: String = String()
    var _visibleToSender: Bool = false
    var _backgroundImage: Douyin_Image? = nil
    var _fullScreenTextColor: String = String()
    var _backgroundImageV2: Douyin_Image? = nil
    var _publicAreaCommon: Douyin_PublicAreaCommon? = nil
    var _giftImage: Douyin_Image? = nil
    var _agreeMsgID: UInt64 = 0
    var _priorityLevel: UInt32 = 0
    var _landscapeAreaCommon: Douyin_LandscapeAreaCommon? = nil
    var _eventTime: UInt64 = 0
    var _sendReview: Bool = false
    var _fromIntercom: Bool = false
    var _intercomHideUserCard: Bool = false
    var _chatBy: String = String()
    var _individualChatPriority: UInt32 = 0
    var _rtfContent: Douyin_Text? = nil

    #if swift(>=5.10)
      // This property is used as the initial default value for new instances of the type.
      // The type itself is protecting the reference to its storage via CoW semantics.
      // This will force a copy to be made of this reference when the first mutation occurs;
      // hence, it is safe to mark this as `nonisolated(unsafe)`.
      static nonisolated(unsafe) let defaultInstance = _StorageClass()
    #else
      static let defaultInstance = _StorageClass()
    #endif

    private init() {}

    init(copying source: _StorageClass) {
      _common = source._common
      _user = source._user
      _content = source._content
      _visibleToSender = source._visibleToSender
      _backgroundImage = source._backgroundImage
      _fullScreenTextColor = source._fullScreenTextColor
      _backgroundImageV2 = source._backgroundImageV2
      _publicAreaCommon = source._publicAreaCommon
      _giftImage = source._giftImage
      _agreeMsgID = source._agreeMsgID
      _priorityLevel = source._priorityLevel
      _landscapeAreaCommon = source._landscapeAreaCommon
      _eventTime = source._eventTime
      _sendReview = source._sendReview
      _fromIntercom = source._fromIntercom
      _intercomHideUserCard = source._intercomHideUserCard
      _chatBy = source._chatBy
      _individualChatPriority = source._individualChatPriority
      _rtfContent = source._rtfContent
    }
  }

  fileprivate mutating func _uniqueStorage() -> _StorageClass {
    if !isKnownUniquelyReferenced(&_storage) {
      _storage = _StorageClass(copying: _storage)
    }
    return _storage
  }

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    _ = _uniqueStorage()
    try withExtendedLifetime(_storage) { (_storage: _StorageClass) in
      while let fieldNumber = try decoder.nextFieldNumber() {
        // The use of inline closures is to circumvent an issue where the compiler
        // allocates stack space for every case branch when no optimizations are
        // enabled. https://github.com/apple/swift-protobuf/issues/1034
        switch fieldNumber {
        case 1: try { try decoder.decodeSingularMessageField(value: &_storage._common) }()
        case 2: try { try decoder.decodeSingularMessageField(value: &_storage._user) }()
        case 3: try { try decoder.decodeSingularStringField(value: &_storage._content) }()
        case 4: try { try decoder.decodeSingularBoolField(value: &_storage._visibleToSender) }()
        case 5: try { try decoder.decodeSingularMessageField(value: &_storage._backgroundImage) }()
        case 6: try { try decoder.decodeSingularStringField(value: &_storage._fullScreenTextColor) }()
        case 7: try { try decoder.decodeSingularMessageField(value: &_storage._backgroundImageV2) }()
        case 8: try { try decoder.decodeSingularMessageField(value: &_storage._publicAreaCommon) }()
        case 9: try { try decoder.decodeSingularMessageField(value: &_storage._giftImage) }()
        case 11: try { try decoder.decodeSingularUInt64Field(value: &_storage._agreeMsgID) }()
        case 12: try { try decoder.decodeSingularUInt32Field(value: &_storage._priorityLevel) }()
        case 13: try { try decoder.decodeSingularMessageField(value: &_storage._landscapeAreaCommon) }()
        case 15: try { try decoder.decodeSingularUInt64Field(value: &_storage._eventTime) }()
        case 16: try { try decoder.decodeSingularBoolField(value: &_storage._sendReview) }()
        case 17: try { try decoder.decodeSingularBoolField(value: &_storage._fromIntercom) }()
        case 18: try { try decoder.decodeSingularBoolField(value: &_storage._intercomHideUserCard) }()
        case 20: try { try decoder.decodeSingularStringField(value: &_storage._chatBy) }()
        case 21: try { try decoder.decodeSingularUInt32Field(value: &_storage._individualChatPriority) }()
        case 22: try { try decoder.decodeSingularMessageField(value: &_storage._rtfContent) }()
        default: break
        }
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    try withExtendedLifetime(_storage) { (_storage: _StorageClass) in
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every if/case branch local when no optimizations
      // are enabled. https://github.com/apple/swift-protobuf/issues/1034 and
      // https://github.com/apple/swift-protobuf/issues/1182
      try { if let v = _storage._common {
        try visitor.visitSingularMessageField(value: v, fieldNumber: 1)
      } }()
      try { if let v = _storage._user {
        try visitor.visitSingularMessageField(value: v, fieldNumber: 2)
      } }()
      if !_storage._content.isEmpty {
        try visitor.visitSingularStringField(value: _storage._content, fieldNumber: 3)
      }
      if _storage._visibleToSender != false {
        try visitor.visitSingularBoolField(value: _storage._visibleToSender, fieldNumber: 4)
      }
      try { if let v = _storage._backgroundImage {
        try visitor.visitSingularMessageField(value: v, fieldNumber: 5)
      } }()
      if !_storage._fullScreenTextColor.isEmpty {
        try visitor.visitSingularStringField(value: _storage._fullScreenTextColor, fieldNumber: 6)
      }
      try { if let v = _storage._backgroundImageV2 {
        try visitor.visitSingularMessageField(value: v, fieldNumber: 7)
      } }()
      try { if let v = _storage._publicAreaCommon {
        try visitor.visitSingularMessageField(value: v, fieldNumber: 8)
      } }()
      try { if let v = _storage._giftImage {
        try visitor.visitSingularMessageField(value: v, fieldNumber: 9)
      } }()
      if _storage._agreeMsgID != 0 {
        try visitor.visitSingularUInt64Field(value: _storage._agreeMsgID, fieldNumber: 11)
      }
      if _storage._priorityLevel != 0 {
        try visitor.visitSingularUInt32Field(value: _storage._priorityLevel, fieldNumber: 12)
      }
      try { if let v = _storage._landscapeAreaCommon {
        try visitor.visitSingularMessageField(value: v, fieldNumber: 13)
      } }()
      if _storage._eventTime != 0 {
        try visitor.visitSingularUInt64Field(value: _storage._eventTime, fieldNumber: 15)
      }
      if _storage._sendReview != false {
        try visitor.visitSingularBoolField(value: _storage._sendReview, fieldNumber: 16)
      }
      if _storage._fromIntercom != false {
        try visitor.visitSingularBoolField(value: _storage._fromIntercom, fieldNumber: 17)
      }
      if _storage._intercomHideUserCard != false {
        try visitor.visitSingularBoolField(value: _storage._intercomHideUserCard, fieldNumber: 18)
      }
      if !_storage._chatBy.isEmpty {
        try visitor.visitSingularStringField(value: _storage._chatBy, fieldNumber: 20)
      }
      if _storage._individualChatPriority != 0 {
        try visitor.visitSingularUInt32Field(value: _storage._individualChatPriority, fieldNumber: 21)
      }
      try { if let v = _storage._rtfContent {
        try visitor.visitSingularMessageField(value: v, fieldNumber: 22)
      } }()
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: Douyin_ChatMessage, rhs: Douyin_ChatMessage) -> Bool {
    if lhs._storage !== rhs._storage {
      let storagesAreEqual: Bool = withExtendedLifetime((lhs._storage, rhs._storage)) { (_args: (_StorageClass, _StorageClass)) in
        let _storage = _args.0
        let rhs_storage = _args.1
        if _storage._common != rhs_storage._common {return false}
        if _storage._user != rhs_storage._user {return false}
        if _storage._content != rhs_storage._content {return false}
        if _storage._visibleToSender != rhs_storage._visibleToSender {return false}
        if _storage._backgroundImage != rhs_storage._backgroundImage {return false}
        if _storage._fullScreenTextColor != rhs_storage._fullScreenTextColor {return false}
        if _storage._backgroundImageV2 != rhs_storage._backgroundImageV2 {return false}
        if _storage._publicAreaCommon != rhs_storage._publicAreaCommon {return false}
        if _storage._giftImage != rhs_storage._giftImage {return false}
        if _storage._agreeMsgID != rhs_storage._agreeMsgID {return false}
        if _storage._priorityLevel != rhs_storage._priorityLevel {return false}
        if _storage._landscapeAreaCommon != rhs_storage._landscapeAreaCommon {return false}
        if _storage._eventTime != rhs_storage._eventTime {return false}
        if _storage._sendReview != rhs_storage._sendReview {return false}
        if _storage._fromIntercom != rhs_storage._fromIntercom {return false}
        if _storage._intercomHideUserCard != rhs_storage._intercomHideUserCard {return false}
        if _storage._chatBy != rhs_storage._chatBy {return false}
        if _storage._individualChatPriority != rhs_storage._individualChatPriority {return false}
        if _storage._rtfContent != rhs_storage._rtfContent {return false}
        return true
      }
      if !storagesAreEqual {return false}
    }
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Douyin_LandscapeAreaCommon: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".LandscapeAreaCommon"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "showHead"),
    2: .same(proto: "showNickname"),
    3: .same(proto: "showFontColor"),
    4: .same(proto: "colorValueList"),
    5: .same(proto: "commentTypeTagsList"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularBoolField(value: &self.showHead) }()
      case 2: try { try decoder.decodeSingularBoolField(value: &self.showNickname) }()
      case 3: try { try decoder.decodeSingularBoolField(value: &self.showFontColor) }()
      case 4: try { try decoder.decodeRepeatedStringField(value: &self.colorValueList) }()
      case 5: try { try decoder.decodeRepeatedEnumField(value: &self.commentTypeTagsList) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    if self.showHead != false {
      try visitor.visitSingularBoolField(value: self.showHead, fieldNumber: 1)
    }
    if self.showNickname != false {
      try visitor.visitSingularBoolField(value: self.showNickname, fieldNumber: 2)
    }
    if self.showFontColor != false {
      try visitor.visitSingularBoolField(value: self.showFontColor, fieldNumber: 3)
    }
    if !self.colorValueList.isEmpty {
      try visitor.visitRepeatedStringField(value: self.colorValueList, fieldNumber: 4)
    }
    if !self.commentTypeTagsList.isEmpty {
      try visitor.visitPackedEnumField(value: self.commentTypeTagsList, fieldNumber: 5)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: Douyin_LandscapeAreaCommon, rhs: Douyin_LandscapeAreaCommon) -> Bool {
    if lhs.showHead != rhs.showHead {return false}
    if lhs.showNickname != rhs.showNickname {return false}
    if lhs.showFontColor != rhs.showFontColor {return false}
    if lhs.colorValueList != rhs.colorValueList {return false}
    if lhs.commentTypeTagsList != rhs.commentTypeTagsList {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Douyin_RoomUserSeqMessage: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".RoomUserSeqMessage"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "common"),
    2: .same(proto: "ranksList"),
    3: .same(proto: "total"),
    4: .same(proto: "popStr"),
    5: .same(proto: "seatsList"),
    6: .same(proto: "popularity"),
    7: .same(proto: "totalUser"),
    8: .same(proto: "totalUserStr"),
    9: .same(proto: "totalStr"),
    10: .same(proto: "onlineUserForAnchor"),
    11: .same(proto: "totalPvForAnchor"),
    12: .same(proto: "upRightStatsStr"),
    13: .same(proto: "upRightStatsStrComplete"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularMessageField(value: &self._common) }()
      case 2: try { try decoder.decodeRepeatedMessageField(value: &self.ranksList) }()
      case 3: try { try decoder.decodeSingularInt64Field(value: &self.total) }()
      case 4: try { try decoder.decodeSingularStringField(value: &self.popStr) }()
      case 5: try { try decoder.decodeRepeatedMessageField(value: &self.seatsList) }()
      case 6: try { try decoder.decodeSingularInt64Field(value: &self.popularity) }()
      case 7: try { try decoder.decodeSingularInt64Field(value: &self.totalUser) }()
      case 8: try { try decoder.decodeSingularStringField(value: &self.totalUserStr) }()
      case 9: try { try decoder.decodeSingularStringField(value: &self.totalStr) }()
      case 10: try { try decoder.decodeSingularStringField(value: &self.onlineUserForAnchor) }()
      case 11: try { try decoder.decodeSingularStringField(value: &self.totalPvForAnchor) }()
      case 12: try { try decoder.decodeSingularStringField(value: &self.upRightStatsStr) }()
      case 13: try { try decoder.decodeSingularStringField(value: &self.upRightStatsStrComplete) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    // The use of inline closures is to circumvent an issue where the compiler
    // allocates stack space for every if/case branch local when no optimizations
    // are enabled. https://github.com/apple/swift-protobuf/issues/1034 and
    // https://github.com/apple/swift-protobuf/issues/1182
    try { if let v = self._common {
      try visitor.visitSingularMessageField(value: v, fieldNumber: 1)
    } }()
    if !self.ranksList.isEmpty {
      try visitor.visitRepeatedMessageField(value: self.ranksList, fieldNumber: 2)
    }
    if self.total != 0 {
      try visitor.visitSingularInt64Field(value: self.total, fieldNumber: 3)
    }
    if !self.popStr.isEmpty {
      try visitor.visitSingularStringField(value: self.popStr, fieldNumber: 4)
    }
    if !self.seatsList.isEmpty {
      try visitor.visitRepeatedMessageField(value: self.seatsList, fieldNumber: 5)
    }
    if self.popularity != 0 {
      try visitor.visitSingularInt64Field(value: self.popularity, fieldNumber: 6)
    }
    if self.totalUser != 0 {
      try visitor.visitSingularInt64Field(value: self.totalUser, fieldNumber: 7)
    }
    if !self.totalUserStr.isEmpty {
      try visitor.visitSingularStringField(value: self.totalUserStr, fieldNumber: 8)
    }
    if !self.totalStr.isEmpty {
      try visitor.visitSingularStringField(value: self.totalStr, fieldNumber: 9)
    }
    if !self.onlineUserForAnchor.isEmpty {
      try visitor.visitSingularStringField(value: self.onlineUserForAnchor, fieldNumber: 10)
    }
    if !self.totalPvForAnchor.isEmpty {
      try visitor.visitSingularStringField(value: self.totalPvForAnchor, fieldNumber: 11)
    }
    if !self.upRightStatsStr.isEmpty {
      try visitor.visitSingularStringField(value: self.upRightStatsStr, fieldNumber: 12)
    }
    if !self.upRightStatsStrComplete.isEmpty {
      try visitor.visitSingularStringField(value: self.upRightStatsStrComplete, fieldNumber: 13)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: Douyin_RoomUserSeqMessage, rhs: Douyin_RoomUserSeqMessage) -> Bool {
    if lhs._common != rhs._common {return false}
    if lhs.ranksList != rhs.ranksList {return false}
    if lhs.total != rhs.total {return false}
    if lhs.popStr != rhs.popStr {return false}
    if lhs.seatsList != rhs.seatsList {return false}
    if lhs.popularity != rhs.popularity {return false}
    if lhs.totalUser != rhs.totalUser {return false}
    if lhs.totalUserStr != rhs.totalUserStr {return false}
    if lhs.totalStr != rhs.totalStr {return false}
    if lhs.onlineUserForAnchor != rhs.onlineUserForAnchor {return false}
    if lhs.totalPvForAnchor != rhs.totalPvForAnchor {return false}
    if lhs.upRightStatsStr != rhs.upRightStatsStr {return false}
    if lhs.upRightStatsStrComplete != rhs.upRightStatsStrComplete {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Douyin_CommonTextMessage: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".CommonTextMessage"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "common"),
    2: .same(proto: "user"),
    3: .same(proto: "scene"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularMessageField(value: &self._common) }()
      case 2: try { try decoder.decodeSingularMessageField(value: &self._user) }()
      case 3: try { try decoder.decodeSingularStringField(value: &self.scene) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    // The use of inline closures is to circumvent an issue where the compiler
    // allocates stack space for every if/case branch local when no optimizations
    // are enabled. https://github.com/apple/swift-protobuf/issues/1034 and
    // https://github.com/apple/swift-protobuf/issues/1182
    try { if let v = self._common {
      try visitor.visitSingularMessageField(value: v, fieldNumber: 1)
    } }()
    try { if let v = self._user {
      try visitor.visitSingularMessageField(value: v, fieldNumber: 2)
    } }()
    if !self.scene.isEmpty {
      try visitor.visitSingularStringField(value: self.scene, fieldNumber: 3)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: Douyin_CommonTextMessage, rhs: Douyin_CommonTextMessage) -> Bool {
    if lhs._common != rhs._common {return false}
    if lhs._user != rhs._user {return false}
    if lhs.scene != rhs.scene {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Douyin_UpdateFanTicketMessage: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".UpdateFanTicketMessage"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "common"),
    2: .same(proto: "roomFanTicketCountText"),
    3: .same(proto: "roomFanTicketCount"),
    4: .same(proto: "forceUpdate"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularMessageField(value: &self._common) }()
      case 2: try { try decoder.decodeSingularStringField(value: &self.roomFanTicketCountText) }()
      case 3: try { try decoder.decodeSingularUInt64Field(value: &self.roomFanTicketCount) }()
      case 4: try { try decoder.decodeSingularBoolField(value: &self.forceUpdate) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    // The use of inline closures is to circumvent an issue where the compiler
    // allocates stack space for every if/case branch local when no optimizations
    // are enabled. https://github.com/apple/swift-protobuf/issues/1034 and
    // https://github.com/apple/swift-protobuf/issues/1182
    try { if let v = self._common {
      try visitor.visitSingularMessageField(value: v, fieldNumber: 1)
    } }()
    if !self.roomFanTicketCountText.isEmpty {
      try visitor.visitSingularStringField(value: self.roomFanTicketCountText, fieldNumber: 2)
    }
    if self.roomFanTicketCount != 0 {
      try visitor.visitSingularUInt64Field(value: self.roomFanTicketCount, fieldNumber: 3)
    }
    if self.forceUpdate != false {
      try visitor.visitSingularBoolField(value: self.forceUpdate, fieldNumber: 4)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: Douyin_UpdateFanTicketMessage, rhs: Douyin_UpdateFanTicketMessage) -> Bool {
    if lhs._common != rhs._common {return false}
    if lhs.roomFanTicketCountText != rhs.roomFanTicketCountText {return false}
    if lhs.roomFanTicketCount != rhs.roomFanTicketCount {return false}
    if lhs.forceUpdate != rhs.forceUpdate {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Douyin_RoomUserSeqMessageContributor: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".RoomUserSeqMessageContributor"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "score"),
    2: .same(proto: "user"),
    3: .same(proto: "rank"),
    4: .same(proto: "delta"),
    5: .same(proto: "isHidden"),
    6: .same(proto: "scoreDescription"),
    7: .same(proto: "exactlyScore"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularUInt64Field(value: &self.score) }()
      case 2: try { try decoder.decodeSingularMessageField(value: &self._user) }()
      case 3: try { try decoder.decodeSingularUInt64Field(value: &self.rank) }()
      case 4: try { try decoder.decodeSingularUInt64Field(value: &self.delta) }()
      case 5: try { try decoder.decodeSingularBoolField(value: &self.isHidden) }()
      case 6: try { try decoder.decodeSingularStringField(value: &self.scoreDescription) }()
      case 7: try { try decoder.decodeSingularStringField(value: &self.exactlyScore) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    // The use of inline closures is to circumvent an issue where the compiler
    // allocates stack space for every if/case branch local when no optimizations
    // are enabled. https://github.com/apple/swift-protobuf/issues/1034 and
    // https://github.com/apple/swift-protobuf/issues/1182
    if self.score != 0 {
      try visitor.visitSingularUInt64Field(value: self.score, fieldNumber: 1)
    }
    try { if let v = self._user {
      try visitor.visitSingularMessageField(value: v, fieldNumber: 2)
    } }()
    if self.rank != 0 {
      try visitor.visitSingularUInt64Field(value: self.rank, fieldNumber: 3)
    }
    if self.delta != 0 {
      try visitor.visitSingularUInt64Field(value: self.delta, fieldNumber: 4)
    }
    if self.isHidden != false {
      try visitor.visitSingularBoolField(value: self.isHidden, fieldNumber: 5)
    }
    if !self.scoreDescription.isEmpty {
      try visitor.visitSingularStringField(value: self.scoreDescription, fieldNumber: 6)
    }
    if !self.exactlyScore.isEmpty {
      try visitor.visitSingularStringField(value: self.exactlyScore, fieldNumber: 7)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: Douyin_RoomUserSeqMessageContributor, rhs: Douyin_RoomUserSeqMessageContributor) -> Bool {
    if lhs.score != rhs.score {return false}
    if lhs._user != rhs._user {return false}
    if lhs.rank != rhs.rank {return false}
    if lhs.delta != rhs.delta {return false}
    if lhs.isHidden != rhs.isHidden {return false}
    if lhs.scoreDescription != rhs.scoreDescription {return false}
    if lhs.exactlyScore != rhs.exactlyScore {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Douyin_GiftMessage: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".GiftMessage"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "common"),
    2: .same(proto: "giftId"),
    3: .same(proto: "fanTicketCount"),
    4: .same(proto: "groupCount"),
    5: .same(proto: "repeatCount"),
    6: .same(proto: "comboCount"),
    7: .same(proto: "user"),
    8: .same(proto: "toUser"),
    9: .same(proto: "repeatEnd"),
    10: .same(proto: "textEffect"),
    11: .same(proto: "groupId"),
    12: .same(proto: "incomeTaskgifts"),
    13: .same(proto: "roomFanTicketCount"),
    14: .same(proto: "priority"),
    15: .same(proto: "gift"),
    16: .same(proto: "logId"),
    17: .same(proto: "sendType"),
    18: .same(proto: "publicAreaCommon"),
    19: .same(proto: "trayDisplayText"),
    20: .same(proto: "bannedDisplayEffects"),
    25: .same(proto: "displayForSelf"),
    26: .same(proto: "interactGiftInfo"),
    27: .same(proto: "diyItemInfo"),
    28: .same(proto: "minAssetSetList"),
    29: .same(proto: "totalCount"),
    30: .same(proto: "clientGiftSource"),
    32: .same(proto: "toUserIdsList"),
    33: .same(proto: "sendTime"),
    34: .same(proto: "forceDisplayEffects"),
    35: .same(proto: "traceId"),
    36: .same(proto: "effectDisplayTs"),
  ]

  fileprivate class _StorageClass {
    var _common: Douyin_Common? = nil
    var _giftID: UInt64 = 0
    var _fanTicketCount: UInt64 = 0
    var _groupCount: UInt64 = 0
    var _repeatCount: UInt64 = 0
    var _comboCount: UInt64 = 0
    var _user: Douyin_User? = nil
    var _toUser: Douyin_User? = nil
    var _repeatEnd: UInt32 = 0
    var _textEffect: Douyin_TextEffect? = nil
    var _groupID: UInt64 = 0
    var _incomeTaskgifts: UInt64 = 0
    var _roomFanTicketCount: UInt64 = 0
    var _priority: Douyin_GiftIMPriority? = nil
    var _gift: Douyin_GiftStruct? = nil
    var _logID: String = String()
    var _sendType: UInt64 = 0
    var _publicAreaCommon: Douyin_PublicAreaCommon? = nil
    var _trayDisplayText: Douyin_Text? = nil
    var _bannedDisplayEffects: UInt64 = 0
    var _displayForSelf: Bool = false
    var _interactGiftInfo: String = String()
    var _diyItemInfo: String = String()
    var _minAssetSetList: [UInt64] = []
    var _totalCount: UInt64 = 0
    var _clientGiftSource: UInt32 = 0
    var _toUserIdsList: [UInt64] = []
    var _sendTime: UInt64 = 0
    var _forceDisplayEffects: UInt64 = 0
    var _traceID: String = String()
    var _effectDisplayTs: UInt64 = 0

    #if swift(>=5.10)
      // This property is used as the initial default value for new instances of the type.
      // The type itself is protecting the reference to its storage via CoW semantics.
      // This will force a copy to be made of this reference when the first mutation occurs;
      // hence, it is safe to mark this as `nonisolated(unsafe)`.
      static nonisolated(unsafe) let defaultInstance = _StorageClass()
    #else
      static let defaultInstance = _StorageClass()
    #endif

    private init() {}

    init(copying source: _StorageClass) {
      _common = source._common
      _giftID = source._giftID
      _fanTicketCount = source._fanTicketCount
      _groupCount = source._groupCount
      _repeatCount = source._repeatCount
      _comboCount = source._comboCount
      _user = source._user
      _toUser = source._toUser
      _repeatEnd = source._repeatEnd
      _textEffect = source._textEffect
      _groupID = source._groupID
      _incomeTaskgifts = source._incomeTaskgifts
      _roomFanTicketCount = source._roomFanTicketCount
      _priority = source._priority
      _gift = source._gift
      _logID = source._logID
      _sendType = source._sendType
      _publicAreaCommon = source._publicAreaCommon
      _trayDisplayText = source._trayDisplayText
      _bannedDisplayEffects = source._bannedDisplayEffects
      _displayForSelf = source._displayForSelf
      _interactGiftInfo = source._interactGiftInfo
      _diyItemInfo = source._diyItemInfo
      _minAssetSetList = source._minAssetSetList
      _totalCount = source._totalCount
      _clientGiftSource = source._clientGiftSource
      _toUserIdsList = source._toUserIdsList
      _sendTime = source._sendTime
      _forceDisplayEffects = source._forceDisplayEffects
      _traceID = source._traceID
      _effectDisplayTs = source._effectDisplayTs
    }
  }

  fileprivate mutating func _uniqueStorage() -> _StorageClass {
    if !isKnownUniquelyReferenced(&_storage) {
      _storage = _StorageClass(copying: _storage)
    }
    return _storage
  }

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    _ = _uniqueStorage()
    try withExtendedLifetime(_storage) { (_storage: _StorageClass) in
      while let fieldNumber = try decoder.nextFieldNumber() {
        // The use of inline closures is to circumvent an issue where the compiler
        // allocates stack space for every case branch when no optimizations are
        // enabled. https://github.com/apple/swift-protobuf/issues/1034
        switch fieldNumber {
        case 1: try { try decoder.decodeSingularMessageField(value: &_storage._common) }()
        case 2: try { try decoder.decodeSingularUInt64Field(value: &_storage._giftID) }()
        case 3: try { try decoder.decodeSingularUInt64Field(value: &_storage._fanTicketCount) }()
        case 4: try { try decoder.decodeSingularUInt64Field(value: &_storage._groupCount) }()
        case 5: try { try decoder.decodeSingularUInt64Field(value: &_storage._repeatCount) }()
        case 6: try { try decoder.decodeSingularUInt64Field(value: &_storage._comboCount) }()
        case 7: try { try decoder.decodeSingularMessageField(value: &_storage._user) }()
        case 8: try { try decoder.decodeSingularMessageField(value: &_storage._toUser) }()
        case 9: try { try decoder.decodeSingularUInt32Field(value: &_storage._repeatEnd) }()
        case 10: try { try decoder.decodeSingularMessageField(value: &_storage._textEffect) }()
        case 11: try { try decoder.decodeSingularUInt64Field(value: &_storage._groupID) }()
        case 12: try { try decoder.decodeSingularUInt64Field(value: &_storage._incomeTaskgifts) }()
        case 13: try { try decoder.decodeSingularUInt64Field(value: &_storage._roomFanTicketCount) }()
        case 14: try { try decoder.decodeSingularMessageField(value: &_storage._priority) }()
        case 15: try { try decoder.decodeSingularMessageField(value: &_storage._gift) }()
        case 16: try { try decoder.decodeSingularStringField(value: &_storage._logID) }()
        case 17: try { try decoder.decodeSingularUInt64Field(value: &_storage._sendType) }()
        case 18: try { try decoder.decodeSingularMessageField(value: &_storage._publicAreaCommon) }()
        case 19: try { try decoder.decodeSingularMessageField(value: &_storage._trayDisplayText) }()
        case 20: try { try decoder.decodeSingularUInt64Field(value: &_storage._bannedDisplayEffects) }()
        case 25: try { try decoder.decodeSingularBoolField(value: &_storage._displayForSelf) }()
        case 26: try { try decoder.decodeSingularStringField(value: &_storage._interactGiftInfo) }()
        case 27: try { try decoder.decodeSingularStringField(value: &_storage._diyItemInfo) }()
        case 28: try { try decoder.decodeRepeatedUInt64Field(value: &_storage._minAssetSetList) }()
        case 29: try { try decoder.decodeSingularUInt64Field(value: &_storage._totalCount) }()
        case 30: try { try decoder.decodeSingularUInt32Field(value: &_storage._clientGiftSource) }()
        case 32: try { try decoder.decodeRepeatedUInt64Field(value: &_storage._toUserIdsList) }()
        case 33: try { try decoder.decodeSingularUInt64Field(value: &_storage._sendTime) }()
        case 34: try { try decoder.decodeSingularUInt64Field(value: &_storage._forceDisplayEffects) }()
        case 35: try { try decoder.decodeSingularStringField(value: &_storage._traceID) }()
        case 36: try { try decoder.decodeSingularUInt64Field(value: &_storage._effectDisplayTs) }()
        default: break
        }
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    try withExtendedLifetime(_storage) { (_storage: _StorageClass) in
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every if/case branch local when no optimizations
      // are enabled. https://github.com/apple/swift-protobuf/issues/1034 and
      // https://github.com/apple/swift-protobuf/issues/1182
      try { if let v = _storage._common {
        try visitor.visitSingularMessageField(value: v, fieldNumber: 1)
      } }()
      if _storage._giftID != 0 {
        try visitor.visitSingularUInt64Field(value: _storage._giftID, fieldNumber: 2)
      }
      if _storage._fanTicketCount != 0 {
        try visitor.visitSingularUInt64Field(value: _storage._fanTicketCount, fieldNumber: 3)
      }
      if _storage._groupCount != 0 {
        try visitor.visitSingularUInt64Field(value: _storage._groupCount, fieldNumber: 4)
      }
      if _storage._repeatCount != 0 {
        try visitor.visitSingularUInt64Field(value: _storage._repeatCount, fieldNumber: 5)
      }
      if _storage._comboCount != 0 {
        try visitor.visitSingularUInt64Field(value: _storage._comboCount, fieldNumber: 6)
      }
      try { if let v = _storage._user {
        try visitor.visitSingularMessageField(value: v, fieldNumber: 7)
      } }()
      try { if let v = _storage._toUser {
        try visitor.visitSingularMessageField(value: v, fieldNumber: 8)
      } }()
      if _storage._repeatEnd != 0 {
        try visitor.visitSingularUInt32Field(value: _storage._repeatEnd, fieldNumber: 9)
      }
      try { if let v = _storage._textEffect {
        try visitor.visitSingularMessageField(value: v, fieldNumber: 10)
      } }()
      if _storage._groupID != 0 {
        try visitor.visitSingularUInt64Field(value: _storage._groupID, fieldNumber: 11)
      }
      if _storage._incomeTaskgifts != 0 {
        try visitor.visitSingularUInt64Field(value: _storage._incomeTaskgifts, fieldNumber: 12)
      }
      if _storage._roomFanTicketCount != 0 {
        try visitor.visitSingularUInt64Field(value: _storage._roomFanTicketCount, fieldNumber: 13)
      }
      try { if let v = _storage._priority {
        try visitor.visitSingularMessageField(value: v, fieldNumber: 14)
      } }()
      try { if let v = _storage._gift {
        try visitor.visitSingularMessageField(value: v, fieldNumber: 15)
      } }()
      if !_storage._logID.isEmpty {
        try visitor.visitSingularStringField(value: _storage._logID, fieldNumber: 16)
      }
      if _storage._sendType != 0 {
        try visitor.visitSingularUInt64Field(value: _storage._sendType, fieldNumber: 17)
      }
      try { if let v = _storage._publicAreaCommon {
        try visitor.visitSingularMessageField(value: v, fieldNumber: 18)
      } }()
      try { if let v = _storage._trayDisplayText {
        try visitor.visitSingularMessageField(value: v, fieldNumber: 19)
      } }()
      if _storage._bannedDisplayEffects != 0 {
        try visitor.visitSingularUInt64Field(value: _storage._bannedDisplayEffects, fieldNumber: 20)
      }
      if _storage._displayForSelf != false {
        try visitor.visitSingularBoolField(value: _storage._displayForSelf, fieldNumber: 25)
      }
      if !_storage._interactGiftInfo.isEmpty {
        try visitor.visitSingularStringField(value: _storage._interactGiftInfo, fieldNumber: 26)
      }
      if !_storage._diyItemInfo.isEmpty {
        try visitor.visitSingularStringField(value: _storage._diyItemInfo, fieldNumber: 27)
      }
      if !_storage._minAssetSetList.isEmpty {
        try visitor.visitPackedUInt64Field(value: _storage._minAssetSetList, fieldNumber: 28)
      }
      if _storage._totalCount != 0 {
        try visitor.visitSingularUInt64Field(value: _storage._totalCount, fieldNumber: 29)
      }
      if _storage._clientGiftSource != 0 {
        try visitor.visitSingularUInt32Field(value: _storage._clientGiftSource, fieldNumber: 30)
      }
      if !_storage._toUserIdsList.isEmpty {
        try visitor.visitPackedUInt64Field(value: _storage._toUserIdsList, fieldNumber: 32)
      }
      if _storage._sendTime != 0 {
        try visitor.visitSingularUInt64Field(value: _storage._sendTime, fieldNumber: 33)
      }
      if _storage._forceDisplayEffects != 0 {
        try visitor.visitSingularUInt64Field(value: _storage._forceDisplayEffects, fieldNumber: 34)
      }
      if !_storage._traceID.isEmpty {
        try visitor.visitSingularStringField(value: _storage._traceID, fieldNumber: 35)
      }
      if _storage._effectDisplayTs != 0 {
        try visitor.visitSingularUInt64Field(value: _storage._effectDisplayTs, fieldNumber: 36)
      }
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: Douyin_GiftMessage, rhs: Douyin_GiftMessage) -> Bool {
    if lhs._storage !== rhs._storage {
      let storagesAreEqual: Bool = withExtendedLifetime((lhs._storage, rhs._storage)) { (_args: (_StorageClass, _StorageClass)) in
        let _storage = _args.0
        let rhs_storage = _args.1
        if _storage._common != rhs_storage._common {return false}
        if _storage._giftID != rhs_storage._giftID {return false}
        if _storage._fanTicketCount != rhs_storage._fanTicketCount {return false}
        if _storage._groupCount != rhs_storage._groupCount {return false}
        if _storage._repeatCount != rhs_storage._repeatCount {return false}
        if _storage._comboCount != rhs_storage._comboCount {return false}
        if _storage._user != rhs_storage._user {return false}
        if _storage._toUser != rhs_storage._toUser {return false}
        if _storage._repeatEnd != rhs_storage._repeatEnd {return false}
        if _storage._textEffect != rhs_storage._textEffect {return false}
        if _storage._groupID != rhs_storage._groupID {return false}
        if _storage._incomeTaskgifts != rhs_storage._incomeTaskgifts {return false}
        if _storage._roomFanTicketCount != rhs_storage._roomFanTicketCount {return false}
        if _storage._priority != rhs_storage._priority {return false}
        if _storage._gift != rhs_storage._gift {return false}
        if _storage._logID != rhs_storage._logID {return false}
        if _storage._sendType != rhs_storage._sendType {return false}
        if _storage._publicAreaCommon != rhs_storage._publicAreaCommon {return false}
        if _storage._trayDisplayText != rhs_storage._trayDisplayText {return false}
        if _storage._bannedDisplayEffects != rhs_storage._bannedDisplayEffects {return false}
        if _storage._displayForSelf != rhs_storage._displayForSelf {return false}
        if _storage._interactGiftInfo != rhs_storage._interactGiftInfo {return false}
        if _storage._diyItemInfo != rhs_storage._diyItemInfo {return false}
        if _storage._minAssetSetList != rhs_storage._minAssetSetList {return false}
        if _storage._totalCount != rhs_storage._totalCount {return false}
        if _storage._clientGiftSource != rhs_storage._clientGiftSource {return false}
        if _storage._toUserIdsList != rhs_storage._toUserIdsList {return false}
        if _storage._sendTime != rhs_storage._sendTime {return false}
        if _storage._forceDisplayEffects != rhs_storage._forceDisplayEffects {return false}
        if _storage._traceID != rhs_storage._traceID {return false}
        if _storage._effectDisplayTs != rhs_storage._effectDisplayTs {return false}
        return true
      }
      if !storagesAreEqual {return false}
    }
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Douyin_GiftStruct: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".GiftStruct"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "image"),
    2: .same(proto: "describe"),
    3: .same(proto: "notify"),
    4: .same(proto: "duration"),
    5: .same(proto: "id"),
    7: .same(proto: "forLinkmic"),
    8: .same(proto: "doodle"),
    9: .same(proto: "forFansclub"),
    10: .same(proto: "combo"),
    11: .same(proto: "type"),
    12: .same(proto: "diamondCount"),
    13: .same(proto: "isDisplayedOnPanel"),
    14: .same(proto: "primaryEffectId"),
    15: .same(proto: "giftLabelIcon"),
    16: .same(proto: "name"),
    17: .same(proto: "region"),
    18: .same(proto: "manual"),
    19: .same(proto: "forCustom"),
    21: .same(proto: "icon"),
    22: .same(proto: "actionType"),
  ]

  fileprivate class _StorageClass {
    var _image: Douyin_Image? = nil
    var _describe: String = String()
    var _notify: Bool = false
    var _duration: UInt64 = 0
    var _id: UInt64 = 0
    var _forLinkmic: Bool = false
    var _doodle: Bool = false
    var _forFansclub: Bool = false
    var _combo: Bool = false
    var _type: UInt32 = 0
    var _diamondCount: UInt32 = 0
    var _isDisplayedOnPanel: Bool = false
    var _primaryEffectID: UInt64 = 0
    var _giftLabelIcon: Douyin_Image? = nil
    var _name: String = String()
    var _region: String = String()
    var _manual: String = String()
    var _forCustom: Bool = false
    var _icon: Douyin_Image? = nil
    var _actionType: UInt32 = 0

    #if swift(>=5.10)
      // This property is used as the initial default value for new instances of the type.
      // The type itself is protecting the reference to its storage via CoW semantics.
      // This will force a copy to be made of this reference when the first mutation occurs;
      // hence, it is safe to mark this as `nonisolated(unsafe)`.
      static nonisolated(unsafe) let defaultInstance = _StorageClass()
    #else
      static let defaultInstance = _StorageClass()
    #endif

    private init() {}

    init(copying source: _StorageClass) {
      _image = source._image
      _describe = source._describe
      _notify = source._notify
      _duration = source._duration
      _id = source._id
      _forLinkmic = source._forLinkmic
      _doodle = source._doodle
      _forFansclub = source._forFansclub
      _combo = source._combo
      _type = source._type
      _diamondCount = source._diamondCount
      _isDisplayedOnPanel = source._isDisplayedOnPanel
      _primaryEffectID = source._primaryEffectID
      _giftLabelIcon = source._giftLabelIcon
      _name = source._name
      _region = source._region
      _manual = source._manual
      _forCustom = source._forCustom
      _icon = source._icon
      _actionType = source._actionType
    }
  }

  fileprivate mutating func _uniqueStorage() -> _StorageClass {
    if !isKnownUniquelyReferenced(&_storage) {
      _storage = _StorageClass(copying: _storage)
    }
    return _storage
  }

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    _ = _uniqueStorage()
    try withExtendedLifetime(_storage) { (_storage: _StorageClass) in
      while let fieldNumber = try decoder.nextFieldNumber() {
        // The use of inline closures is to circumvent an issue where the compiler
        // allocates stack space for every case branch when no optimizations are
        // enabled. https://github.com/apple/swift-protobuf/issues/1034
        switch fieldNumber {
        case 1: try { try decoder.decodeSingularMessageField(value: &_storage._image) }()
        case 2: try { try decoder.decodeSingularStringField(value: &_storage._describe) }()
        case 3: try { try decoder.decodeSingularBoolField(value: &_storage._notify) }()
        case 4: try { try decoder.decodeSingularUInt64Field(value: &_storage._duration) }()
        case 5: try { try decoder.decodeSingularUInt64Field(value: &_storage._id) }()
        case 7: try { try decoder.decodeSingularBoolField(value: &_storage._forLinkmic) }()
        case 8: try { try decoder.decodeSingularBoolField(value: &_storage._doodle) }()
        case 9: try { try decoder.decodeSingularBoolField(value: &_storage._forFansclub) }()
        case 10: try { try decoder.decodeSingularBoolField(value: &_storage._combo) }()
        case 11: try { try decoder.decodeSingularUInt32Field(value: &_storage._type) }()
        case 12: try { try decoder.decodeSingularUInt32Field(value: &_storage._diamondCount) }()
        case 13: try { try decoder.decodeSingularBoolField(value: &_storage._isDisplayedOnPanel) }()
        case 14: try { try decoder.decodeSingularUInt64Field(value: &_storage._primaryEffectID) }()
        case 15: try { try decoder.decodeSingularMessageField(value: &_storage._giftLabelIcon) }()
        case 16: try { try decoder.decodeSingularStringField(value: &_storage._name) }()
        case 17: try { try decoder.decodeSingularStringField(value: &_storage._region) }()
        case 18: try { try decoder.decodeSingularStringField(value: &_storage._manual) }()
        case 19: try { try decoder.decodeSingularBoolField(value: &_storage._forCustom) }()
        case 21: try { try decoder.decodeSingularMessageField(value: &_storage._icon) }()
        case 22: try { try decoder.decodeSingularUInt32Field(value: &_storage._actionType) }()
        default: break
        }
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    try withExtendedLifetime(_storage) { (_storage: _StorageClass) in
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every if/case branch local when no optimizations
      // are enabled. https://github.com/apple/swift-protobuf/issues/1034 and
      // https://github.com/apple/swift-protobuf/issues/1182
      try { if let v = _storage._image {
        try visitor.visitSingularMessageField(value: v, fieldNumber: 1)
      } }()
      if !_storage._describe.isEmpty {
        try visitor.visitSingularStringField(value: _storage._describe, fieldNumber: 2)
      }
      if _storage._notify != false {
        try visitor.visitSingularBoolField(value: _storage._notify, fieldNumber: 3)
      }
      if _storage._duration != 0 {
        try visitor.visitSingularUInt64Field(value: _storage._duration, fieldNumber: 4)
      }
      if _storage._id != 0 {
        try visitor.visitSingularUInt64Field(value: _storage._id, fieldNumber: 5)
      }
      if _storage._forLinkmic != false {
        try visitor.visitSingularBoolField(value: _storage._forLinkmic, fieldNumber: 7)
      }
      if _storage._doodle != false {
        try visitor.visitSingularBoolField(value: _storage._doodle, fieldNumber: 8)
      }
      if _storage._forFansclub != false {
        try visitor.visitSingularBoolField(value: _storage._forFansclub, fieldNumber: 9)
      }
      if _storage._combo != false {
        try visitor.visitSingularBoolField(value: _storage._combo, fieldNumber: 10)
      }
      if _storage._type != 0 {
        try visitor.visitSingularUInt32Field(value: _storage._type, fieldNumber: 11)
      }
      if _storage._diamondCount != 0 {
        try visitor.visitSingularUInt32Field(value: _storage._diamondCount, fieldNumber: 12)
      }
      if _storage._isDisplayedOnPanel != false {
        try visitor.visitSingularBoolField(value: _storage._isDisplayedOnPanel, fieldNumber: 13)
      }
      if _storage._primaryEffectID != 0 {
        try visitor.visitSingularUInt64Field(value: _storage._primaryEffectID, fieldNumber: 14)
      }
      try { if let v = _storage._giftLabelIcon {
        try visitor.visitSingularMessageField(value: v, fieldNumber: 15)
      } }()
      if !_storage._name.isEmpty {
        try visitor.visitSingularStringField(value: _storage._name, fieldNumber: 16)
      }
      if !_storage._region.isEmpty {
        try visitor.visitSingularStringField(value: _storage._region, fieldNumber: 17)
      }
      if !_storage._manual.isEmpty {
        try visitor.visitSingularStringField(value: _storage._manual, fieldNumber: 18)
      }
      if _storage._forCustom != false {
        try visitor.visitSingularBoolField(value: _storage._forCustom, fieldNumber: 19)
      }
      try { if let v = _storage._icon {
        try visitor.visitSingularMessageField(value: v, fieldNumber: 21)
      } }()
      if _storage._actionType != 0 {
        try visitor.visitSingularUInt32Field(value: _storage._actionType, fieldNumber: 22)
      }
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: Douyin_GiftStruct, rhs: Douyin_GiftStruct) -> Bool {
    if lhs._storage !== rhs._storage {
      let storagesAreEqual: Bool = withExtendedLifetime((lhs._storage, rhs._storage)) { (_args: (_StorageClass, _StorageClass)) in
        let _storage = _args.0
        let rhs_storage = _args.1
        if _storage._image != rhs_storage._image {return false}
        if _storage._describe != rhs_storage._describe {return false}
        if _storage._notify != rhs_storage._notify {return false}
        if _storage._duration != rhs_storage._duration {return false}
        if _storage._id != rhs_storage._id {return false}
        if _storage._forLinkmic != rhs_storage._forLinkmic {return false}
        if _storage._doodle != rhs_storage._doodle {return false}
        if _storage._forFansclub != rhs_storage._forFansclub {return false}
        if _storage._combo != rhs_storage._combo {return false}
        if _storage._type != rhs_storage._type {return false}
        if _storage._diamondCount != rhs_storage._diamondCount {return false}
        if _storage._isDisplayedOnPanel != rhs_storage._isDisplayedOnPanel {return false}
        if _storage._primaryEffectID != rhs_storage._primaryEffectID {return false}
        if _storage._giftLabelIcon != rhs_storage._giftLabelIcon {return false}
        if _storage._name != rhs_storage._name {return false}
        if _storage._region != rhs_storage._region {return false}
        if _storage._manual != rhs_storage._manual {return false}
        if _storage._forCustom != rhs_storage._forCustom {return false}
        if _storage._icon != rhs_storage._icon {return false}
        if _storage._actionType != rhs_storage._actionType {return false}
        return true
      }
      if !storagesAreEqual {return false}
    }
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Douyin_GiftIMPriority: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".GiftIMPriority"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "queueSizesList"),
    2: .same(proto: "selfQueuePriority"),
    3: .same(proto: "priority"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeRepeatedUInt64Field(value: &self.queueSizesList) }()
      case 2: try { try decoder.decodeSingularUInt64Field(value: &self.selfQueuePriority) }()
      case 3: try { try decoder.decodeSingularUInt64Field(value: &self.priority) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    if !self.queueSizesList.isEmpty {
      try visitor.visitPackedUInt64Field(value: self.queueSizesList, fieldNumber: 1)
    }
    if self.selfQueuePriority != 0 {
      try visitor.visitSingularUInt64Field(value: self.selfQueuePriority, fieldNumber: 2)
    }
    if self.priority != 0 {
      try visitor.visitSingularUInt64Field(value: self.priority, fieldNumber: 3)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: Douyin_GiftIMPriority, rhs: Douyin_GiftIMPriority) -> Bool {
    if lhs.queueSizesList != rhs.queueSizesList {return false}
    if lhs.selfQueuePriority != rhs.selfQueuePriority {return false}
    if lhs.priority != rhs.priority {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Douyin_TextEffect: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".TextEffect"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "portrait"),
    2: .same(proto: "landscape"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularMessageField(value: &self._portrait) }()
      case 2: try { try decoder.decodeSingularMessageField(value: &self._landscape) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    // The use of inline closures is to circumvent an issue where the compiler
    // allocates stack space for every if/case branch local when no optimizations
    // are enabled. https://github.com/apple/swift-protobuf/issues/1034 and
    // https://github.com/apple/swift-protobuf/issues/1182
    try { if let v = self._portrait {
      try visitor.visitSingularMessageField(value: v, fieldNumber: 1)
    } }()
    try { if let v = self._landscape {
      try visitor.visitSingularMessageField(value: v, fieldNumber: 2)
    } }()
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: Douyin_TextEffect, rhs: Douyin_TextEffect) -> Bool {
    if lhs._portrait != rhs._portrait {return false}
    if lhs._landscape != rhs._landscape {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Douyin_TextEffectDetail: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".TextEffectDetail"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "text"),
    2: .same(proto: "textFontSize"),
    3: .same(proto: "background"),
    4: .same(proto: "start"),
    5: .same(proto: "duration"),
    6: .same(proto: "x"),
    7: .same(proto: "y"),
    8: .same(proto: "width"),
    9: .same(proto: "height"),
    10: .same(proto: "shadowDx"),
    11: .same(proto: "shadowDy"),
    12: .same(proto: "shadowRadius"),
    13: .same(proto: "shadowColor"),
    14: .same(proto: "strokeColor"),
    15: .same(proto: "strokeWidth"),
  ]

  fileprivate class _StorageClass {
    var _text: Douyin_Text? = nil
    var _textFontSize: UInt32 = 0
    var _background: Douyin_Image? = nil
    var _start: UInt32 = 0
    var _duration: UInt32 = 0
    var _x: UInt32 = 0
    var _y: UInt32 = 0
    var _width: UInt32 = 0
    var _height: UInt32 = 0
    var _shadowDx: UInt32 = 0
    var _shadowDy: UInt32 = 0
    var _shadowRadius: UInt32 = 0
    var _shadowColor: String = String()
    var _strokeColor: String = String()
    var _strokeWidth: UInt32 = 0

    #if swift(>=5.10)
      // This property is used as the initial default value for new instances of the type.
      // The type itself is protecting the reference to its storage via CoW semantics.
      // This will force a copy to be made of this reference when the first mutation occurs;
      // hence, it is safe to mark this as `nonisolated(unsafe)`.
      static nonisolated(unsafe) let defaultInstance = _StorageClass()
    #else
      static let defaultInstance = _StorageClass()
    #endif

    private init() {}

    init(copying source: _StorageClass) {
      _text = source._text
      _textFontSize = source._textFontSize
      _background = source._background
      _start = source._start
      _duration = source._duration
      _x = source._x
      _y = source._y
      _width = source._width
      _height = source._height
      _shadowDx = source._shadowDx
      _shadowDy = source._shadowDy
      _shadowRadius = source._shadowRadius
      _shadowColor = source._shadowColor
      _strokeColor = source._strokeColor
      _strokeWidth = source._strokeWidth
    }
  }

  fileprivate mutating func _uniqueStorage() -> _StorageClass {
    if !isKnownUniquelyReferenced(&_storage) {
      _storage = _StorageClass(copying: _storage)
    }
    return _storage
  }

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    _ = _uniqueStorage()
    try withExtendedLifetime(_storage) { (_storage: _StorageClass) in
      while let fieldNumber = try decoder.nextFieldNumber() {
        // The use of inline closures is to circumvent an issue where the compiler
        // allocates stack space for every case branch when no optimizations are
        // enabled. https://github.com/apple/swift-protobuf/issues/1034
        switch fieldNumber {
        case 1: try { try decoder.decodeSingularMessageField(value: &_storage._text) }()
        case 2: try { try decoder.decodeSingularUInt32Field(value: &_storage._textFontSize) }()
        case 3: try { try decoder.decodeSingularMessageField(value: &_storage._background) }()
        case 4: try { try decoder.decodeSingularUInt32Field(value: &_storage._start) }()
        case 5: try { try decoder.decodeSingularUInt32Field(value: &_storage._duration) }()
        case 6: try { try decoder.decodeSingularUInt32Field(value: &_storage._x) }()
        case 7: try { try decoder.decodeSingularUInt32Field(value: &_storage._y) }()
        case 8: try { try decoder.decodeSingularUInt32Field(value: &_storage._width) }()
        case 9: try { try decoder.decodeSingularUInt32Field(value: &_storage._height) }()
        case 10: try { try decoder.decodeSingularUInt32Field(value: &_storage._shadowDx) }()
        case 11: try { try decoder.decodeSingularUInt32Field(value: &_storage._shadowDy) }()
        case 12: try { try decoder.decodeSingularUInt32Field(value: &_storage._shadowRadius) }()
        case 13: try { try decoder.decodeSingularStringField(value: &_storage._shadowColor) }()
        case 14: try { try decoder.decodeSingularStringField(value: &_storage._strokeColor) }()
        case 15: try { try decoder.decodeSingularUInt32Field(value: &_storage._strokeWidth) }()
        default: break
        }
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    try withExtendedLifetime(_storage) { (_storage: _StorageClass) in
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every if/case branch local when no optimizations
      // are enabled. https://github.com/apple/swift-protobuf/issues/1034 and
      // https://github.com/apple/swift-protobuf/issues/1182
      try { if let v = _storage._text {
        try visitor.visitSingularMessageField(value: v, fieldNumber: 1)
      } }()
      if _storage._textFontSize != 0 {
        try visitor.visitSingularUInt32Field(value: _storage._textFontSize, fieldNumber: 2)
      }
      try { if let v = _storage._background {
        try visitor.visitSingularMessageField(value: v, fieldNumber: 3)
      } }()
      if _storage._start != 0 {
        try visitor.visitSingularUInt32Field(value: _storage._start, fieldNumber: 4)
      }
      if _storage._duration != 0 {
        try visitor.visitSingularUInt32Field(value: _storage._duration, fieldNumber: 5)
      }
      if _storage._x != 0 {
        try visitor.visitSingularUInt32Field(value: _storage._x, fieldNumber: 6)
      }
      if _storage._y != 0 {
        try visitor.visitSingularUInt32Field(value: _storage._y, fieldNumber: 7)
      }
      if _storage._width != 0 {
        try visitor.visitSingularUInt32Field(value: _storage._width, fieldNumber: 8)
      }
      if _storage._height != 0 {
        try visitor.visitSingularUInt32Field(value: _storage._height, fieldNumber: 9)
      }
      if _storage._shadowDx != 0 {
        try visitor.visitSingularUInt32Field(value: _storage._shadowDx, fieldNumber: 10)
      }
      if _storage._shadowDy != 0 {
        try visitor.visitSingularUInt32Field(value: _storage._shadowDy, fieldNumber: 11)
      }
      if _storage._shadowRadius != 0 {
        try visitor.visitSingularUInt32Field(value: _storage._shadowRadius, fieldNumber: 12)
      }
      if !_storage._shadowColor.isEmpty {
        try visitor.visitSingularStringField(value: _storage._shadowColor, fieldNumber: 13)
      }
      if !_storage._strokeColor.isEmpty {
        try visitor.visitSingularStringField(value: _storage._strokeColor, fieldNumber: 14)
      }
      if _storage._strokeWidth != 0 {
        try visitor.visitSingularUInt32Field(value: _storage._strokeWidth, fieldNumber: 15)
      }
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: Douyin_TextEffectDetail, rhs: Douyin_TextEffectDetail) -> Bool {
    if lhs._storage !== rhs._storage {
      let storagesAreEqual: Bool = withExtendedLifetime((lhs._storage, rhs._storage)) { (_args: (_StorageClass, _StorageClass)) in
        let _storage = _args.0
        let rhs_storage = _args.1
        if _storage._text != rhs_storage._text {return false}
        if _storage._textFontSize != rhs_storage._textFontSize {return false}
        if _storage._background != rhs_storage._background {return false}
        if _storage._start != rhs_storage._start {return false}
        if _storage._duration != rhs_storage._duration {return false}
        if _storage._x != rhs_storage._x {return false}
        if _storage._y != rhs_storage._y {return false}
        if _storage._width != rhs_storage._width {return false}
        if _storage._height != rhs_storage._height {return false}
        if _storage._shadowDx != rhs_storage._shadowDx {return false}
        if _storage._shadowDy != rhs_storage._shadowDy {return false}
        if _storage._shadowRadius != rhs_storage._shadowRadius {return false}
        if _storage._shadowColor != rhs_storage._shadowColor {return false}
        if _storage._strokeColor != rhs_storage._strokeColor {return false}
        if _storage._strokeWidth != rhs_storage._strokeWidth {return false}
        return true
      }
      if !storagesAreEqual {return false}
    }
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Douyin_MemberMessage: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".MemberMessage"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "common"),
    2: .same(proto: "user"),
    3: .same(proto: "memberCount"),
    4: .same(proto: "operator"),
    5: .same(proto: "isSetToAdmin"),
    6: .same(proto: "isTopUser"),
    7: .same(proto: "rankScore"),
    8: .same(proto: "topUserNo"),
    9: .same(proto: "enterType"),
    10: .same(proto: "action"),
    11: .same(proto: "actionDescription"),
    12: .same(proto: "userId"),
    13: .same(proto: "effectConfig"),
    14: .same(proto: "popStr"),
    15: .same(proto: "enterEffectConfig"),
    16: .same(proto: "backgroundImage"),
    17: .same(proto: "backgroundImageV2"),
    18: .same(proto: "anchorDisplayText"),
    19: .same(proto: "publicAreaCommon"),
    20: .same(proto: "userEnterTipType"),
    21: .same(proto: "anchorEnterTipType"),
  ]

  fileprivate class _StorageClass {
    var _common: Douyin_Common? = nil
    var _user: Douyin_User? = nil
    var _memberCount: UInt64 = 0
    var _operator: Douyin_User? = nil
    var _isSetToAdmin: Bool = false
    var _isTopUser: Bool = false
    var _rankScore: UInt64 = 0
    var _topUserNo: UInt64 = 0
    var _enterType: UInt64 = 0
    var _action: UInt64 = 0
    var _actionDescription: String = String()
    var _userID: UInt64 = 0
    var _effectConfig: Douyin_EffectConfig? = nil
    var _popStr: String = String()
    var _enterEffectConfig: Douyin_EffectConfig? = nil
    var _backgroundImage: Douyin_Image? = nil
    var _backgroundImageV2: Douyin_Image? = nil
    var _anchorDisplayText: Douyin_Text? = nil
    var _publicAreaCommon: Douyin_PublicAreaCommon? = nil
    var _userEnterTipType: UInt64 = 0
    var _anchorEnterTipType: UInt64 = 0

    #if swift(>=5.10)
      // This property is used as the initial default value for new instances of the type.
      // The type itself is protecting the reference to its storage via CoW semantics.
      // This will force a copy to be made of this reference when the first mutation occurs;
      // hence, it is safe to mark this as `nonisolated(unsafe)`.
      static nonisolated(unsafe) let defaultInstance = _StorageClass()
    #else
      static let defaultInstance = _StorageClass()
    #endif

    private init() {}

    init(copying source: _StorageClass) {
      _common = source._common
      _user = source._user
      _memberCount = source._memberCount
      _operator = source._operator
      _isSetToAdmin = source._isSetToAdmin
      _isTopUser = source._isTopUser
      _rankScore = source._rankScore
      _topUserNo = source._topUserNo
      _enterType = source._enterType
      _action = source._action
      _actionDescription = source._actionDescription
      _userID = source._userID
      _effectConfig = source._effectConfig
      _popStr = source._popStr
      _enterEffectConfig = source._enterEffectConfig
      _backgroundImage = source._backgroundImage
      _backgroundImageV2 = source._backgroundImageV2
      _anchorDisplayText = source._anchorDisplayText
      _publicAreaCommon = source._publicAreaCommon
      _userEnterTipType = source._userEnterTipType
      _anchorEnterTipType = source._anchorEnterTipType
    }
  }

  fileprivate mutating func _uniqueStorage() -> _StorageClass {
    if !isKnownUniquelyReferenced(&_storage) {
      _storage = _StorageClass(copying: _storage)
    }
    return _storage
  }

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    _ = _uniqueStorage()
    try withExtendedLifetime(_storage) { (_storage: _StorageClass) in
      while let fieldNumber = try decoder.nextFieldNumber() {
        // The use of inline closures is to circumvent an issue where the compiler
        // allocates stack space for every case branch when no optimizations are
        // enabled. https://github.com/apple/swift-protobuf/issues/1034
        switch fieldNumber {
        case 1: try { try decoder.decodeSingularMessageField(value: &_storage._common) }()
        case 2: try { try decoder.decodeSingularMessageField(value: &_storage._user) }()
        case 3: try { try decoder.decodeSingularUInt64Field(value: &_storage._memberCount) }()
        case 4: try { try decoder.decodeSingularMessageField(value: &_storage._operator) }()
        case 5: try { try decoder.decodeSingularBoolField(value: &_storage._isSetToAdmin) }()
        case 6: try { try decoder.decodeSingularBoolField(value: &_storage._isTopUser) }()
        case 7: try { try decoder.decodeSingularUInt64Field(value: &_storage._rankScore) }()
        case 8: try { try decoder.decodeSingularUInt64Field(value: &_storage._topUserNo) }()
        case 9: try { try decoder.decodeSingularUInt64Field(value: &_storage._enterType) }()
        case 10: try { try decoder.decodeSingularUInt64Field(value: &_storage._action) }()
        case 11: try { try decoder.decodeSingularStringField(value: &_storage._actionDescription) }()
        case 12: try { try decoder.decodeSingularUInt64Field(value: &_storage._userID) }()
        case 13: try { try decoder.decodeSingularMessageField(value: &_storage._effectConfig) }()
        case 14: try { try decoder.decodeSingularStringField(value: &_storage._popStr) }()
        case 15: try { try decoder.decodeSingularMessageField(value: &_storage._enterEffectConfig) }()
        case 16: try { try decoder.decodeSingularMessageField(value: &_storage._backgroundImage) }()
        case 17: try { try decoder.decodeSingularMessageField(value: &_storage._backgroundImageV2) }()
        case 18: try { try decoder.decodeSingularMessageField(value: &_storage._anchorDisplayText) }()
        case 19: try { try decoder.decodeSingularMessageField(value: &_storage._publicAreaCommon) }()
        case 20: try { try decoder.decodeSingularUInt64Field(value: &_storage._userEnterTipType) }()
        case 21: try { try decoder.decodeSingularUInt64Field(value: &_storage._anchorEnterTipType) }()
        default: break
        }
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    try withExtendedLifetime(_storage) { (_storage: _StorageClass) in
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every if/case branch local when no optimizations
      // are enabled. https://github.com/apple/swift-protobuf/issues/1034 and
      // https://github.com/apple/swift-protobuf/issues/1182
      try { if let v = _storage._common {
        try visitor.visitSingularMessageField(value: v, fieldNumber: 1)
      } }()
      try { if let v = _storage._user {
        try visitor.visitSingularMessageField(value: v, fieldNumber: 2)
      } }()
      if _storage._memberCount != 0 {
        try visitor.visitSingularUInt64Field(value: _storage._memberCount, fieldNumber: 3)
      }
      try { if let v = _storage._operator {
        try visitor.visitSingularMessageField(value: v, fieldNumber: 4)
      } }()
      if _storage._isSetToAdmin != false {
        try visitor.visitSingularBoolField(value: _storage._isSetToAdmin, fieldNumber: 5)
      }
      if _storage._isTopUser != false {
        try visitor.visitSingularBoolField(value: _storage._isTopUser, fieldNumber: 6)
      }
      if _storage._rankScore != 0 {
        try visitor.visitSingularUInt64Field(value: _storage._rankScore, fieldNumber: 7)
      }
      if _storage._topUserNo != 0 {
        try visitor.visitSingularUInt64Field(value: _storage._topUserNo, fieldNumber: 8)
      }
      if _storage._enterType != 0 {
        try visitor.visitSingularUInt64Field(value: _storage._enterType, fieldNumber: 9)
      }
      if _storage._action != 0 {
        try visitor.visitSingularUInt64Field(value: _storage._action, fieldNumber: 10)
      }
      if !_storage._actionDescription.isEmpty {
        try visitor.visitSingularStringField(value: _storage._actionDescription, fieldNumber: 11)
      }
      if _storage._userID != 0 {
        try visitor.visitSingularUInt64Field(value: _storage._userID, fieldNumber: 12)
      }
      try { if let v = _storage._effectConfig {
        try visitor.visitSingularMessageField(value: v, fieldNumber: 13)
      } }()
      if !_storage._popStr.isEmpty {
        try visitor.visitSingularStringField(value: _storage._popStr, fieldNumber: 14)
      }
      try { if let v = _storage._enterEffectConfig {
        try visitor.visitSingularMessageField(value: v, fieldNumber: 15)
      } }()
      try { if let v = _storage._backgroundImage {
        try visitor.visitSingularMessageField(value: v, fieldNumber: 16)
      } }()
      try { if let v = _storage._backgroundImageV2 {
        try visitor.visitSingularMessageField(value: v, fieldNumber: 17)
      } }()
      try { if let v = _storage._anchorDisplayText {
        try visitor.visitSingularMessageField(value: v, fieldNumber: 18)
      } }()
      try { if let v = _storage._publicAreaCommon {
        try visitor.visitSingularMessageField(value: v, fieldNumber: 19)
      } }()
      if _storage._userEnterTipType != 0 {
        try visitor.visitSingularUInt64Field(value: _storage._userEnterTipType, fieldNumber: 20)
      }
      if _storage._anchorEnterTipType != 0 {
        try visitor.visitSingularUInt64Field(value: _storage._anchorEnterTipType, fieldNumber: 21)
      }
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: Douyin_MemberMessage, rhs: Douyin_MemberMessage) -> Bool {
    if lhs._storage !== rhs._storage {
      let storagesAreEqual: Bool = withExtendedLifetime((lhs._storage, rhs._storage)) { (_args: (_StorageClass, _StorageClass)) in
        let _storage = _args.0
        let rhs_storage = _args.1
        if _storage._common != rhs_storage._common {return false}
        if _storage._user != rhs_storage._user {return false}
        if _storage._memberCount != rhs_storage._memberCount {return false}
        if _storage._operator != rhs_storage._operator {return false}
        if _storage._isSetToAdmin != rhs_storage._isSetToAdmin {return false}
        if _storage._isTopUser != rhs_storage._isTopUser {return false}
        if _storage._rankScore != rhs_storage._rankScore {return false}
        if _storage._topUserNo != rhs_storage._topUserNo {return false}
        if _storage._enterType != rhs_storage._enterType {return false}
        if _storage._action != rhs_storage._action {return false}
        if _storage._actionDescription != rhs_storage._actionDescription {return false}
        if _storage._userID != rhs_storage._userID {return false}
        if _storage._effectConfig != rhs_storage._effectConfig {return false}
        if _storage._popStr != rhs_storage._popStr {return false}
        if _storage._enterEffectConfig != rhs_storage._enterEffectConfig {return false}
        if _storage._backgroundImage != rhs_storage._backgroundImage {return false}
        if _storage._backgroundImageV2 != rhs_storage._backgroundImageV2 {return false}
        if _storage._anchorDisplayText != rhs_storage._anchorDisplayText {return false}
        if _storage._publicAreaCommon != rhs_storage._publicAreaCommon {return false}
        if _storage._userEnterTipType != rhs_storage._userEnterTipType {return false}
        if _storage._anchorEnterTipType != rhs_storage._anchorEnterTipType {return false}
        return true
      }
      if !storagesAreEqual {return false}
    }
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Douyin_PublicAreaCommon: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".PublicAreaCommon"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "userLabel"),
    2: .same(proto: "userConsumeInRoom"),
    3: .same(proto: "userSendGiftCntInRoom"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularMessageField(value: &self._userLabel) }()
      case 2: try { try decoder.decodeSingularUInt64Field(value: &self.userConsumeInRoom) }()
      case 3: try { try decoder.decodeSingularUInt64Field(value: &self.userSendGiftCntInRoom) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    // The use of inline closures is to circumvent an issue where the compiler
    // allocates stack space for every if/case branch local when no optimizations
    // are enabled. https://github.com/apple/swift-protobuf/issues/1034 and
    // https://github.com/apple/swift-protobuf/issues/1182
    try { if let v = self._userLabel {
      try visitor.visitSingularMessageField(value: v, fieldNumber: 1)
    } }()
    if self.userConsumeInRoom != 0 {
      try visitor.visitSingularUInt64Field(value: self.userConsumeInRoom, fieldNumber: 2)
    }
    if self.userSendGiftCntInRoom != 0 {
      try visitor.visitSingularUInt64Field(value: self.userSendGiftCntInRoom, fieldNumber: 3)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: Douyin_PublicAreaCommon, rhs: Douyin_PublicAreaCommon) -> Bool {
    if lhs._userLabel != rhs._userLabel {return false}
    if lhs.userConsumeInRoom != rhs.userConsumeInRoom {return false}
    if lhs.userSendGiftCntInRoom != rhs.userSendGiftCntInRoom {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Douyin_EffectConfig: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".EffectConfig"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "type"),
    2: .same(proto: "icon"),
    3: .same(proto: "avatarPos"),
    4: .same(proto: "text"),
    5: .same(proto: "textIcon"),
    6: .same(proto: "stayTime"),
    7: .same(proto: "animAssetId"),
    8: .same(proto: "badge"),
    9: .same(proto: "flexSettingArrayList"),
    10: .same(proto: "textIconOverlay"),
    11: .same(proto: "animatedBadge"),
    12: .same(proto: "hasSweepLight"),
    13: .same(proto: "textFlexSettingArrayList"),
    14: .same(proto: "centerAnimAssetId"),
    15: .same(proto: "dynamicImage"),
    16: .same(proto: "extraMap"),
    17: .same(proto: "mp4AnimAssetId"),
    18: .same(proto: "priority"),
    19: .same(proto: "maxWaitTime"),
    20: .same(proto: "dressId"),
    21: .same(proto: "alignment"),
    22: .same(proto: "alignmentOffset"),
  ]

  fileprivate class _StorageClass {
    var _type: UInt64 = 0
    var _icon: Douyin_Image? = nil
    var _avatarPos: UInt64 = 0
    var _text: Douyin_Text? = nil
    var _textIcon: Douyin_Image? = nil
    var _stayTime: UInt32 = 0
    var _animAssetID: UInt64 = 0
    var _badge: Douyin_Image? = nil
    var _flexSettingArrayList: [UInt64] = []
    var _textIconOverlay: Douyin_Image? = nil
    var _animatedBadge: Douyin_Image? = nil
    var _hasSweepLight_p: Bool = false
    var _textFlexSettingArrayList: [UInt64] = []
    var _centerAnimAssetID: UInt64 = 0
    var _dynamicImage: Douyin_Image? = nil
    var _extraMap: Dictionary<String,String> = [:]
    var _mp4AnimAssetID: UInt64 = 0
    var _priority: UInt64 = 0
    var _maxWaitTime: UInt64 = 0
    var _dressID: String = String()
    var _alignment: UInt64 = 0
    var _alignmentOffset: UInt64 = 0

    #if swift(>=5.10)
      // This property is used as the initial default value for new instances of the type.
      // The type itself is protecting the reference to its storage via CoW semantics.
      // This will force a copy to be made of this reference when the first mutation occurs;
      // hence, it is safe to mark this as `nonisolated(unsafe)`.
      static nonisolated(unsafe) let defaultInstance = _StorageClass()
    #else
      static let defaultInstance = _StorageClass()
    #endif

    private init() {}

    init(copying source: _StorageClass) {
      _type = source._type
      _icon = source._icon
      _avatarPos = source._avatarPos
      _text = source._text
      _textIcon = source._textIcon
      _stayTime = source._stayTime
      _animAssetID = source._animAssetID
      _badge = source._badge
      _flexSettingArrayList = source._flexSettingArrayList
      _textIconOverlay = source._textIconOverlay
      _animatedBadge = source._animatedBadge
      _hasSweepLight_p = source._hasSweepLight_p
      _textFlexSettingArrayList = source._textFlexSettingArrayList
      _centerAnimAssetID = source._centerAnimAssetID
      _dynamicImage = source._dynamicImage
      _extraMap = source._extraMap
      _mp4AnimAssetID = source._mp4AnimAssetID
      _priority = source._priority
      _maxWaitTime = source._maxWaitTime
      _dressID = source._dressID
      _alignment = source._alignment
      _alignmentOffset = source._alignmentOffset
    }
  }

  fileprivate mutating func _uniqueStorage() -> _StorageClass {
    if !isKnownUniquelyReferenced(&_storage) {
      _storage = _StorageClass(copying: _storage)
    }
    return _storage
  }

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    _ = _uniqueStorage()
    try withExtendedLifetime(_storage) { (_storage: _StorageClass) in
      while let fieldNumber = try decoder.nextFieldNumber() {
        // The use of inline closures is to circumvent an issue where the compiler
        // allocates stack space for every case branch when no optimizations are
        // enabled. https://github.com/apple/swift-protobuf/issues/1034
        switch fieldNumber {
        case 1: try { try decoder.decodeSingularUInt64Field(value: &_storage._type) }()
        case 2: try { try decoder.decodeSingularMessageField(value: &_storage._icon) }()
        case 3: try { try decoder.decodeSingularUInt64Field(value: &_storage._avatarPos) }()
        case 4: try { try decoder.decodeSingularMessageField(value: &_storage._text) }()
        case 5: try { try decoder.decodeSingularMessageField(value: &_storage._textIcon) }()
        case 6: try { try decoder.decodeSingularUInt32Field(value: &_storage._stayTime) }()
        case 7: try { try decoder.decodeSingularUInt64Field(value: &_storage._animAssetID) }()
        case 8: try { try decoder.decodeSingularMessageField(value: &_storage._badge) }()
        case 9: try { try decoder.decodeRepeatedUInt64Field(value: &_storage._flexSettingArrayList) }()
        case 10: try { try decoder.decodeSingularMessageField(value: &_storage._textIconOverlay) }()
        case 11: try { try decoder.decodeSingularMessageField(value: &_storage._animatedBadge) }()
        case 12: try { try decoder.decodeSingularBoolField(value: &_storage._hasSweepLight_p) }()
        case 13: try { try decoder.decodeRepeatedUInt64Field(value: &_storage._textFlexSettingArrayList) }()
        case 14: try { try decoder.decodeSingularUInt64Field(value: &_storage._centerAnimAssetID) }()
        case 15: try { try decoder.decodeSingularMessageField(value: &_storage._dynamicImage) }()
        case 16: try { try decoder.decodeMapField(fieldType: SwiftProtobuf._ProtobufMap<SwiftProtobuf.ProtobufString,SwiftProtobuf.ProtobufString>.self, value: &_storage._extraMap) }()
        case 17: try { try decoder.decodeSingularUInt64Field(value: &_storage._mp4AnimAssetID) }()
        case 18: try { try decoder.decodeSingularUInt64Field(value: &_storage._priority) }()
        case 19: try { try decoder.decodeSingularUInt64Field(value: &_storage._maxWaitTime) }()
        case 20: try { try decoder.decodeSingularStringField(value: &_storage._dressID) }()
        case 21: try { try decoder.decodeSingularUInt64Field(value: &_storage._alignment) }()
        case 22: try { try decoder.decodeSingularUInt64Field(value: &_storage._alignmentOffset) }()
        default: break
        }
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    try withExtendedLifetime(_storage) { (_storage: _StorageClass) in
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every if/case branch local when no optimizations
      // are enabled. https://github.com/apple/swift-protobuf/issues/1034 and
      // https://github.com/apple/swift-protobuf/issues/1182
      if _storage._type != 0 {
        try visitor.visitSingularUInt64Field(value: _storage._type, fieldNumber: 1)
      }
      try { if let v = _storage._icon {
        try visitor.visitSingularMessageField(value: v, fieldNumber: 2)
      } }()
      if _storage._avatarPos != 0 {
        try visitor.visitSingularUInt64Field(value: _storage._avatarPos, fieldNumber: 3)
      }
      try { if let v = _storage._text {
        try visitor.visitSingularMessageField(value: v, fieldNumber: 4)
      } }()
      try { if let v = _storage._textIcon {
        try visitor.visitSingularMessageField(value: v, fieldNumber: 5)
      } }()
      if _storage._stayTime != 0 {
        try visitor.visitSingularUInt32Field(value: _storage._stayTime, fieldNumber: 6)
      }
      if _storage._animAssetID != 0 {
        try visitor.visitSingularUInt64Field(value: _storage._animAssetID, fieldNumber: 7)
      }
      try { if let v = _storage._badge {
        try visitor.visitSingularMessageField(value: v, fieldNumber: 8)
      } }()
      if !_storage._flexSettingArrayList.isEmpty {
        try visitor.visitPackedUInt64Field(value: _storage._flexSettingArrayList, fieldNumber: 9)
      }
      try { if let v = _storage._textIconOverlay {
        try visitor.visitSingularMessageField(value: v, fieldNumber: 10)
      } }()
      try { if let v = _storage._animatedBadge {
        try visitor.visitSingularMessageField(value: v, fieldNumber: 11)
      } }()
      if _storage._hasSweepLight_p != false {
        try visitor.visitSingularBoolField(value: _storage._hasSweepLight_p, fieldNumber: 12)
      }
      if !_storage._textFlexSettingArrayList.isEmpty {
        try visitor.visitPackedUInt64Field(value: _storage._textFlexSettingArrayList, fieldNumber: 13)
      }
      if _storage._centerAnimAssetID != 0 {
        try visitor.visitSingularUInt64Field(value: _storage._centerAnimAssetID, fieldNumber: 14)
      }
      try { if let v = _storage._dynamicImage {
        try visitor.visitSingularMessageField(value: v, fieldNumber: 15)
      } }()
      if !_storage._extraMap.isEmpty {
        try visitor.visitMapField(fieldType: SwiftProtobuf._ProtobufMap<SwiftProtobuf.ProtobufString,SwiftProtobuf.ProtobufString>.self, value: _storage._extraMap, fieldNumber: 16)
      }
      if _storage._mp4AnimAssetID != 0 {
        try visitor.visitSingularUInt64Field(value: _storage._mp4AnimAssetID, fieldNumber: 17)
      }
      if _storage._priority != 0 {
        try visitor.visitSingularUInt64Field(value: _storage._priority, fieldNumber: 18)
      }
      if _storage._maxWaitTime != 0 {
        try visitor.visitSingularUInt64Field(value: _storage._maxWaitTime, fieldNumber: 19)
      }
      if !_storage._dressID.isEmpty {
        try visitor.visitSingularStringField(value: _storage._dressID, fieldNumber: 20)
      }
      if _storage._alignment != 0 {
        try visitor.visitSingularUInt64Field(value: _storage._alignment, fieldNumber: 21)
      }
      if _storage._alignmentOffset != 0 {
        try visitor.visitSingularUInt64Field(value: _storage._alignmentOffset, fieldNumber: 22)
      }
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: Douyin_EffectConfig, rhs: Douyin_EffectConfig) -> Bool {
    if lhs._storage !== rhs._storage {
      let storagesAreEqual: Bool = withExtendedLifetime((lhs._storage, rhs._storage)) { (_args: (_StorageClass, _StorageClass)) in
        let _storage = _args.0
        let rhs_storage = _args.1
        if _storage._type != rhs_storage._type {return false}
        if _storage._icon != rhs_storage._icon {return false}
        if _storage._avatarPos != rhs_storage._avatarPos {return false}
        if _storage._text != rhs_storage._text {return false}
        if _storage._textIcon != rhs_storage._textIcon {return false}
        if _storage._stayTime != rhs_storage._stayTime {return false}
        if _storage._animAssetID != rhs_storage._animAssetID {return false}
        if _storage._badge != rhs_storage._badge {return false}
        if _storage._flexSettingArrayList != rhs_storage._flexSettingArrayList {return false}
        if _storage._textIconOverlay != rhs_storage._textIconOverlay {return false}
        if _storage._animatedBadge != rhs_storage._animatedBadge {return false}
        if _storage._hasSweepLight_p != rhs_storage._hasSweepLight_p {return false}
        if _storage._textFlexSettingArrayList != rhs_storage._textFlexSettingArrayList {return false}
        if _storage._centerAnimAssetID != rhs_storage._centerAnimAssetID {return false}
        if _storage._dynamicImage != rhs_storage._dynamicImage {return false}
        if _storage._extraMap != rhs_storage._extraMap {return false}
        if _storage._mp4AnimAssetID != rhs_storage._mp4AnimAssetID {return false}
        if _storage._priority != rhs_storage._priority {return false}
        if _storage._maxWaitTime != rhs_storage._maxWaitTime {return false}
        if _storage._dressID != rhs_storage._dressID {return false}
        if _storage._alignment != rhs_storage._alignment {return false}
        if _storage._alignmentOffset != rhs_storage._alignmentOffset {return false}
        return true
      }
      if !storagesAreEqual {return false}
    }
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Douyin_Text: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".Text"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "key"),
    2: .same(proto: "defaultPatter"),
    3: .same(proto: "defaultFormat"),
    4: .same(proto: "piecesList"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularStringField(value: &self.key) }()
      case 2: try { try decoder.decodeSingularStringField(value: &self.defaultPatter) }()
      case 3: try { try decoder.decodeSingularMessageField(value: &self._defaultFormat) }()
      case 4: try { try decoder.decodeRepeatedMessageField(value: &self.piecesList) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    // The use of inline closures is to circumvent an issue where the compiler
    // allocates stack space for every if/case branch local when no optimizations
    // are enabled. https://github.com/apple/swift-protobuf/issues/1034 and
    // https://github.com/apple/swift-protobuf/issues/1182
    if !self.key.isEmpty {
      try visitor.visitSingularStringField(value: self.key, fieldNumber: 1)
    }
    if !self.defaultPatter.isEmpty {
      try visitor.visitSingularStringField(value: self.defaultPatter, fieldNumber: 2)
    }
    try { if let v = self._defaultFormat {
      try visitor.visitSingularMessageField(value: v, fieldNumber: 3)
    } }()
    if !self.piecesList.isEmpty {
      try visitor.visitRepeatedMessageField(value: self.piecesList, fieldNumber: 4)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: Douyin_Text, rhs: Douyin_Text) -> Bool {
    if lhs.key != rhs.key {return false}
    if lhs.defaultPatter != rhs.defaultPatter {return false}
    if lhs._defaultFormat != rhs._defaultFormat {return false}
    if lhs.piecesList != rhs.piecesList {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Douyin_TextPiece: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".TextPiece"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "type"),
    2: .same(proto: "format"),
    3: .same(proto: "stringValue"),
    4: .same(proto: "userValue"),
    5: .same(proto: "giftValue"),
    6: .same(proto: "heartValue"),
    7: .same(proto: "patternRefValue"),
    8: .same(proto: "imageValue"),
  ]

  fileprivate class _StorageClass {
    var _type: Bool = false
    var _format: Douyin_TextFormat? = nil
    var _stringValue: String = String()
    var _userValue: Douyin_TextPieceUser? = nil
    var _giftValue: Douyin_TextPieceGift? = nil
    var _heartValue: Douyin_TextPieceHeart? = nil
    var _patternRefValue: Douyin_TextPiecePatternRef? = nil
    var _imageValue: Douyin_TextPieceImage? = nil

    #if swift(>=5.10)
      // This property is used as the initial default value for new instances of the type.
      // The type itself is protecting the reference to its storage via CoW semantics.
      // This will force a copy to be made of this reference when the first mutation occurs;
      // hence, it is safe to mark this as `nonisolated(unsafe)`.
      static nonisolated(unsafe) let defaultInstance = _StorageClass()
    #else
      static let defaultInstance = _StorageClass()
    #endif

    private init() {}

    init(copying source: _StorageClass) {
      _type = source._type
      _format = source._format
      _stringValue = source._stringValue
      _userValue = source._userValue
      _giftValue = source._giftValue
      _heartValue = source._heartValue
      _patternRefValue = source._patternRefValue
      _imageValue = source._imageValue
    }
  }

  fileprivate mutating func _uniqueStorage() -> _StorageClass {
    if !isKnownUniquelyReferenced(&_storage) {
      _storage = _StorageClass(copying: _storage)
    }
    return _storage
  }

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    _ = _uniqueStorage()
    try withExtendedLifetime(_storage) { (_storage: _StorageClass) in
      while let fieldNumber = try decoder.nextFieldNumber() {
        // The use of inline closures is to circumvent an issue where the compiler
        // allocates stack space for every case branch when no optimizations are
        // enabled. https://github.com/apple/swift-protobuf/issues/1034
        switch fieldNumber {
        case 1: try { try decoder.decodeSingularBoolField(value: &_storage._type) }()
        case 2: try { try decoder.decodeSingularMessageField(value: &_storage._format) }()
        case 3: try { try decoder.decodeSingularStringField(value: &_storage._stringValue) }()
        case 4: try { try decoder.decodeSingularMessageField(value: &_storage._userValue) }()
        case 5: try { try decoder.decodeSingularMessageField(value: &_storage._giftValue) }()
        case 6: try { try decoder.decodeSingularMessageField(value: &_storage._heartValue) }()
        case 7: try { try decoder.decodeSingularMessageField(value: &_storage._patternRefValue) }()
        case 8: try { try decoder.decodeSingularMessageField(value: &_storage._imageValue) }()
        default: break
        }
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    try withExtendedLifetime(_storage) { (_storage: _StorageClass) in
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every if/case branch local when no optimizations
      // are enabled. https://github.com/apple/swift-protobuf/issues/1034 and
      // https://github.com/apple/swift-protobuf/issues/1182
      if _storage._type != false {
        try visitor.visitSingularBoolField(value: _storage._type, fieldNumber: 1)
      }
      try { if let v = _storage._format {
        try visitor.visitSingularMessageField(value: v, fieldNumber: 2)
      } }()
      if !_storage._stringValue.isEmpty {
        try visitor.visitSingularStringField(value: _storage._stringValue, fieldNumber: 3)
      }
      try { if let v = _storage._userValue {
        try visitor.visitSingularMessageField(value: v, fieldNumber: 4)
      } }()
      try { if let v = _storage._giftValue {
        try visitor.visitSingularMessageField(value: v, fieldNumber: 5)
      } }()
      try { if let v = _storage._heartValue {
        try visitor.visitSingularMessageField(value: v, fieldNumber: 6)
      } }()
      try { if let v = _storage._patternRefValue {
        try visitor.visitSingularMessageField(value: v, fieldNumber: 7)
      } }()
      try { if let v = _storage._imageValue {
        try visitor.visitSingularMessageField(value: v, fieldNumber: 8)
      } }()
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: Douyin_TextPiece, rhs: Douyin_TextPiece) -> Bool {
    if lhs._storage !== rhs._storage {
      let storagesAreEqual: Bool = withExtendedLifetime((lhs._storage, rhs._storage)) { (_args: (_StorageClass, _StorageClass)) in
        let _storage = _args.0
        let rhs_storage = _args.1
        if _storage._type != rhs_storage._type {return false}
        if _storage._format != rhs_storage._format {return false}
        if _storage._stringValue != rhs_storage._stringValue {return false}
        if _storage._userValue != rhs_storage._userValue {return false}
        if _storage._giftValue != rhs_storage._giftValue {return false}
        if _storage._heartValue != rhs_storage._heartValue {return false}
        if _storage._patternRefValue != rhs_storage._patternRefValue {return false}
        if _storage._imageValue != rhs_storage._imageValue {return false}
        return true
      }
      if !storagesAreEqual {return false}
    }
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Douyin_TextPieceImage: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".TextPieceImage"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "image"),
    2: .same(proto: "scalingRate"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularMessageField(value: &self._image) }()
      case 2: try { try decoder.decodeSingularFloatField(value: &self.scalingRate) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    // The use of inline closures is to circumvent an issue where the compiler
    // allocates stack space for every if/case branch local when no optimizations
    // are enabled. https://github.com/apple/swift-protobuf/issues/1034 and
    // https://github.com/apple/swift-protobuf/issues/1182
    try { if let v = self._image {
      try visitor.visitSingularMessageField(value: v, fieldNumber: 1)
    } }()
    if self.scalingRate.bitPattern != 0 {
      try visitor.visitSingularFloatField(value: self.scalingRate, fieldNumber: 2)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: Douyin_TextPieceImage, rhs: Douyin_TextPieceImage) -> Bool {
    if lhs._image != rhs._image {return false}
    if lhs.scalingRate != rhs.scalingRate {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Douyin_TextPiecePatternRef: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".TextPiecePatternRef"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "key"),
    2: .same(proto: "defaultPattern"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularStringField(value: &self.key) }()
      case 2: try { try decoder.decodeSingularStringField(value: &self.defaultPattern) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    if !self.key.isEmpty {
      try visitor.visitSingularStringField(value: self.key, fieldNumber: 1)
    }
    if !self.defaultPattern.isEmpty {
      try visitor.visitSingularStringField(value: self.defaultPattern, fieldNumber: 2)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: Douyin_TextPiecePatternRef, rhs: Douyin_TextPiecePatternRef) -> Bool {
    if lhs.key != rhs.key {return false}
    if lhs.defaultPattern != rhs.defaultPattern {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Douyin_TextPieceHeart: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".TextPieceHeart"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "color"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularStringField(value: &self.color) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    if !self.color.isEmpty {
      try visitor.visitSingularStringField(value: self.color, fieldNumber: 1)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: Douyin_TextPieceHeart, rhs: Douyin_TextPieceHeart) -> Bool {
    if lhs.color != rhs.color {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Douyin_TextPieceGift: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".TextPieceGift"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "giftId"),
    2: .same(proto: "nameRef"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularUInt64Field(value: &self.giftID) }()
      case 2: try { try decoder.decodeSingularMessageField(value: &self._nameRef) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    // The use of inline closures is to circumvent an issue where the compiler
    // allocates stack space for every if/case branch local when no optimizations
    // are enabled. https://github.com/apple/swift-protobuf/issues/1034 and
    // https://github.com/apple/swift-protobuf/issues/1182
    if self.giftID != 0 {
      try visitor.visitSingularUInt64Field(value: self.giftID, fieldNumber: 1)
    }
    try { if let v = self._nameRef {
      try visitor.visitSingularMessageField(value: v, fieldNumber: 2)
    } }()
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: Douyin_TextPieceGift, rhs: Douyin_TextPieceGift) -> Bool {
    if lhs.giftID != rhs.giftID {return false}
    if lhs._nameRef != rhs._nameRef {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Douyin_PatternRef: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".PatternRef"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "key"),
    2: .same(proto: "defaultPattern"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularStringField(value: &self.key) }()
      case 2: try { try decoder.decodeSingularStringField(value: &self.defaultPattern) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    if !self.key.isEmpty {
      try visitor.visitSingularStringField(value: self.key, fieldNumber: 1)
    }
    if !self.defaultPattern.isEmpty {
      try visitor.visitSingularStringField(value: self.defaultPattern, fieldNumber: 2)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: Douyin_PatternRef, rhs: Douyin_PatternRef) -> Bool {
    if lhs.key != rhs.key {return false}
    if lhs.defaultPattern != rhs.defaultPattern {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Douyin_TextPieceUser: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".TextPieceUser"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "user"),
    2: .same(proto: "withColon"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularMessageField(value: &self._user) }()
      case 2: try { try decoder.decodeSingularBoolField(value: &self.withColon) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    // The use of inline closures is to circumvent an issue where the compiler
    // allocates stack space for every if/case branch local when no optimizations
    // are enabled. https://github.com/apple/swift-protobuf/issues/1034 and
    // https://github.com/apple/swift-protobuf/issues/1182
    try { if let v = self._user {
      try visitor.visitSingularMessageField(value: v, fieldNumber: 1)
    } }()
    if self.withColon != false {
      try visitor.visitSingularBoolField(value: self.withColon, fieldNumber: 2)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: Douyin_TextPieceUser, rhs: Douyin_TextPieceUser) -> Bool {
    if lhs._user != rhs._user {return false}
    if lhs.withColon != rhs.withColon {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Douyin_TextFormat: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".TextFormat"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "color"),
    2: .same(proto: "bold"),
    3: .same(proto: "italic"),
    4: .same(proto: "weight"),
    5: .same(proto: "italicAngle"),
    6: .same(proto: "fontSize"),
    7: .same(proto: "useHeighLightColor"),
    8: .same(proto: "useRemoteClor"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularStringField(value: &self.color) }()
      case 2: try { try decoder.decodeSingularBoolField(value: &self.bold) }()
      case 3: try { try decoder.decodeSingularBoolField(value: &self.italic) }()
      case 4: try { try decoder.decodeSingularUInt32Field(value: &self.weight) }()
      case 5: try { try decoder.decodeSingularUInt32Field(value: &self.italicAngle) }()
      case 6: try { try decoder.decodeSingularUInt32Field(value: &self.fontSize) }()
      case 7: try { try decoder.decodeSingularBoolField(value: &self.useHeighLightColor) }()
      case 8: try { try decoder.decodeSingularBoolField(value: &self.useRemoteClor) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    if !self.color.isEmpty {
      try visitor.visitSingularStringField(value: self.color, fieldNumber: 1)
    }
    if self.bold != false {
      try visitor.visitSingularBoolField(value: self.bold, fieldNumber: 2)
    }
    if self.italic != false {
      try visitor.visitSingularBoolField(value: self.italic, fieldNumber: 3)
    }
    if self.weight != 0 {
      try visitor.visitSingularUInt32Field(value: self.weight, fieldNumber: 4)
    }
    if self.italicAngle != 0 {
      try visitor.visitSingularUInt32Field(value: self.italicAngle, fieldNumber: 5)
    }
    if self.fontSize != 0 {
      try visitor.visitSingularUInt32Field(value: self.fontSize, fieldNumber: 6)
    }
    if self.useHeighLightColor != false {
      try visitor.visitSingularBoolField(value: self.useHeighLightColor, fieldNumber: 7)
    }
    if self.useRemoteClor != false {
      try visitor.visitSingularBoolField(value: self.useRemoteClor, fieldNumber: 8)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: Douyin_TextFormat, rhs: Douyin_TextFormat) -> Bool {
    if lhs.color != rhs.color {return false}
    if lhs.bold != rhs.bold {return false}
    if lhs.italic != rhs.italic {return false}
    if lhs.weight != rhs.weight {return false}
    if lhs.italicAngle != rhs.italicAngle {return false}
    if lhs.fontSize != rhs.fontSize {return false}
    if lhs.useHeighLightColor != rhs.useHeighLightColor {return false}
    if lhs.useRemoteClor != rhs.useRemoteClor {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Douyin_LikeMessage: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".LikeMessage"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "common"),
    2: .same(proto: "count"),
    3: .same(proto: "total"),
    4: .same(proto: "color"),
    5: .same(proto: "user"),
    6: .same(proto: "icon"),
    7: .same(proto: "doubleLikeDetail"),
    8: .same(proto: "displayControlInfo"),
    9: .same(proto: "linkmicGuestUid"),
    10: .same(proto: "scene"),
    11: .same(proto: "picoDisplayInfo"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularMessageField(value: &self._common) }()
      case 2: try { try decoder.decodeSingularUInt64Field(value: &self.count) }()
      case 3: try { try decoder.decodeSingularUInt64Field(value: &self.total) }()
      case 4: try { try decoder.decodeSingularUInt64Field(value: &self.color) }()
      case 5: try { try decoder.decodeSingularMessageField(value: &self._user) }()
      case 6: try { try decoder.decodeSingularStringField(value: &self.icon) }()
      case 7: try { try decoder.decodeSingularMessageField(value: &self._doubleLikeDetail) }()
      case 8: try { try decoder.decodeSingularMessageField(value: &self._displayControlInfo) }()
      case 9: try { try decoder.decodeSingularUInt64Field(value: &self.linkmicGuestUid) }()
      case 10: try { try decoder.decodeSingularStringField(value: &self.scene) }()
      case 11: try { try decoder.decodeSingularMessageField(value: &self._picoDisplayInfo) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    // The use of inline closures is to circumvent an issue where the compiler
    // allocates stack space for every if/case branch local when no optimizations
    // are enabled. https://github.com/apple/swift-protobuf/issues/1034 and
    // https://github.com/apple/swift-protobuf/issues/1182
    try { if let v = self._common {
      try visitor.visitSingularMessageField(value: v, fieldNumber: 1)
    } }()
    if self.count != 0 {
      try visitor.visitSingularUInt64Field(value: self.count, fieldNumber: 2)
    }
    if self.total != 0 {
      try visitor.visitSingularUInt64Field(value: self.total, fieldNumber: 3)
    }
    if self.color != 0 {
      try visitor.visitSingularUInt64Field(value: self.color, fieldNumber: 4)
    }
    try { if let v = self._user {
      try visitor.visitSingularMessageField(value: v, fieldNumber: 5)
    } }()
    if !self.icon.isEmpty {
      try visitor.visitSingularStringField(value: self.icon, fieldNumber: 6)
    }
    try { if let v = self._doubleLikeDetail {
      try visitor.visitSingularMessageField(value: v, fieldNumber: 7)
    } }()
    try { if let v = self._displayControlInfo {
      try visitor.visitSingularMessageField(value: v, fieldNumber: 8)
    } }()
    if self.linkmicGuestUid != 0 {
      try visitor.visitSingularUInt64Field(value: self.linkmicGuestUid, fieldNumber: 9)
    }
    if !self.scene.isEmpty {
      try visitor.visitSingularStringField(value: self.scene, fieldNumber: 10)
    }
    try { if let v = self._picoDisplayInfo {
      try visitor.visitSingularMessageField(value: v, fieldNumber: 11)
    } }()
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: Douyin_LikeMessage, rhs: Douyin_LikeMessage) -> Bool {
    if lhs._common != rhs._common {return false}
    if lhs.count != rhs.count {return false}
    if lhs.total != rhs.total {return false}
    if lhs.color != rhs.color {return false}
    if lhs._user != rhs._user {return false}
    if lhs.icon != rhs.icon {return false}
    if lhs._doubleLikeDetail != rhs._doubleLikeDetail {return false}
    if lhs._displayControlInfo != rhs._displayControlInfo {return false}
    if lhs.linkmicGuestUid != rhs.linkmicGuestUid {return false}
    if lhs.scene != rhs.scene {return false}
    if lhs._picoDisplayInfo != rhs._picoDisplayInfo {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Douyin_SocialMessage: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".SocialMessage"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "common"),
    2: .same(proto: "user"),
    3: .same(proto: "shareType"),
    4: .same(proto: "action"),
    5: .same(proto: "shareTarget"),
    6: .same(proto: "followCount"),
    7: .same(proto: "publicAreaCommon"),
  ]

  fileprivate class _StorageClass {
    var _common: Douyin_Common? = nil
    var _user: Douyin_User? = nil
    var _shareType: UInt64 = 0
    var _action: UInt64 = 0
    var _shareTarget: String = String()
    var _followCount: UInt64 = 0
    var _publicAreaCommon: Douyin_PublicAreaCommon? = nil

    #if swift(>=5.10)
      // This property is used as the initial default value for new instances of the type.
      // The type itself is protecting the reference to its storage via CoW semantics.
      // This will force a copy to be made of this reference when the first mutation occurs;
      // hence, it is safe to mark this as `nonisolated(unsafe)`.
      static nonisolated(unsafe) let defaultInstance = _StorageClass()
    #else
      static let defaultInstance = _StorageClass()
    #endif

    private init() {}

    init(copying source: _StorageClass) {
      _common = source._common
      _user = source._user
      _shareType = source._shareType
      _action = source._action
      _shareTarget = source._shareTarget
      _followCount = source._followCount
      _publicAreaCommon = source._publicAreaCommon
    }
  }

  fileprivate mutating func _uniqueStorage() -> _StorageClass {
    if !isKnownUniquelyReferenced(&_storage) {
      _storage = _StorageClass(copying: _storage)
    }
    return _storage
  }

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    _ = _uniqueStorage()
    try withExtendedLifetime(_storage) { (_storage: _StorageClass) in
      while let fieldNumber = try decoder.nextFieldNumber() {
        // The use of inline closures is to circumvent an issue where the compiler
        // allocates stack space for every case branch when no optimizations are
        // enabled. https://github.com/apple/swift-protobuf/issues/1034
        switch fieldNumber {
        case 1: try { try decoder.decodeSingularMessageField(value: &_storage._common) }()
        case 2: try { try decoder.decodeSingularMessageField(value: &_storage._user) }()
        case 3: try { try decoder.decodeSingularUInt64Field(value: &_storage._shareType) }()
        case 4: try { try decoder.decodeSingularUInt64Field(value: &_storage._action) }()
        case 5: try { try decoder.decodeSingularStringField(value: &_storage._shareTarget) }()
        case 6: try { try decoder.decodeSingularUInt64Field(value: &_storage._followCount) }()
        case 7: try { try decoder.decodeSingularMessageField(value: &_storage._publicAreaCommon) }()
        default: break
        }
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    try withExtendedLifetime(_storage) { (_storage: _StorageClass) in
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every if/case branch local when no optimizations
      // are enabled. https://github.com/apple/swift-protobuf/issues/1034 and
      // https://github.com/apple/swift-protobuf/issues/1182
      try { if let v = _storage._common {
        try visitor.visitSingularMessageField(value: v, fieldNumber: 1)
      } }()
      try { if let v = _storage._user {
        try visitor.visitSingularMessageField(value: v, fieldNumber: 2)
      } }()
      if _storage._shareType != 0 {
        try visitor.visitSingularUInt64Field(value: _storage._shareType, fieldNumber: 3)
      }
      if _storage._action != 0 {
        try visitor.visitSingularUInt64Field(value: _storage._action, fieldNumber: 4)
      }
      if !_storage._shareTarget.isEmpty {
        try visitor.visitSingularStringField(value: _storage._shareTarget, fieldNumber: 5)
      }
      if _storage._followCount != 0 {
        try visitor.visitSingularUInt64Field(value: _storage._followCount, fieldNumber: 6)
      }
      try { if let v = _storage._publicAreaCommon {
        try visitor.visitSingularMessageField(value: v, fieldNumber: 7)
      } }()
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: Douyin_SocialMessage, rhs: Douyin_SocialMessage) -> Bool {
    if lhs._storage !== rhs._storage {
      let storagesAreEqual: Bool = withExtendedLifetime((lhs._storage, rhs._storage)) { (_args: (_StorageClass, _StorageClass)) in
        let _storage = _args.0
        let rhs_storage = _args.1
        if _storage._common != rhs_storage._common {return false}
        if _storage._user != rhs_storage._user {return false}
        if _storage._shareType != rhs_storage._shareType {return false}
        if _storage._action != rhs_storage._action {return false}
        if _storage._shareTarget != rhs_storage._shareTarget {return false}
        if _storage._followCount != rhs_storage._followCount {return false}
        if _storage._publicAreaCommon != rhs_storage._publicAreaCommon {return false}
        return true
      }
      if !storagesAreEqual {return false}
    }
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Douyin_PicoDisplayInfo: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".PicoDisplayInfo"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "comboSumCount"),
    2: .same(proto: "emoji"),
    3: .same(proto: "emojiIcon"),
    4: .same(proto: "emojiText"),
  ]

  fileprivate class _StorageClass {
    var _comboSumCount: UInt64 = 0
    var _emoji: String = String()
    var _emojiIcon: Douyin_Image? = nil
    var _emojiText: String = String()

    #if swift(>=5.10)
      // This property is used as the initial default value for new instances of the type.
      // The type itself is protecting the reference to its storage via CoW semantics.
      // This will force a copy to be made of this reference when the first mutation occurs;
      // hence, it is safe to mark this as `nonisolated(unsafe)`.
      static nonisolated(unsafe) let defaultInstance = _StorageClass()
    #else
      static let defaultInstance = _StorageClass()
    #endif

    private init() {}

    init(copying source: _StorageClass) {
      _comboSumCount = source._comboSumCount
      _emoji = source._emoji
      _emojiIcon = source._emojiIcon
      _emojiText = source._emojiText
    }
  }

  fileprivate mutating func _uniqueStorage() -> _StorageClass {
    if !isKnownUniquelyReferenced(&_storage) {
      _storage = _StorageClass(copying: _storage)
    }
    return _storage
  }

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    _ = _uniqueStorage()
    try withExtendedLifetime(_storage) { (_storage: _StorageClass) in
      while let fieldNumber = try decoder.nextFieldNumber() {
        // The use of inline closures is to circumvent an issue where the compiler
        // allocates stack space for every case branch when no optimizations are
        // enabled. https://github.com/apple/swift-protobuf/issues/1034
        switch fieldNumber {
        case 1: try { try decoder.decodeSingularUInt64Field(value: &_storage._comboSumCount) }()
        case 2: try { try decoder.decodeSingularStringField(value: &_storage._emoji) }()
        case 3: try { try decoder.decodeSingularMessageField(value: &_storage._emojiIcon) }()
        case 4: try { try decoder.decodeSingularStringField(value: &_storage._emojiText) }()
        default: break
        }
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    try withExtendedLifetime(_storage) { (_storage: _StorageClass) in
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every if/case branch local when no optimizations
      // are enabled. https://github.com/apple/swift-protobuf/issues/1034 and
      // https://github.com/apple/swift-protobuf/issues/1182
      if _storage._comboSumCount != 0 {
        try visitor.visitSingularUInt64Field(value: _storage._comboSumCount, fieldNumber: 1)
      }
      if !_storage._emoji.isEmpty {
        try visitor.visitSingularStringField(value: _storage._emoji, fieldNumber: 2)
      }
      try { if let v = _storage._emojiIcon {
        try visitor.visitSingularMessageField(value: v, fieldNumber: 3)
      } }()
      if !_storage._emojiText.isEmpty {
        try visitor.visitSingularStringField(value: _storage._emojiText, fieldNumber: 4)
      }
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: Douyin_PicoDisplayInfo, rhs: Douyin_PicoDisplayInfo) -> Bool {
    if lhs._storage !== rhs._storage {
      let storagesAreEqual: Bool = withExtendedLifetime((lhs._storage, rhs._storage)) { (_args: (_StorageClass, _StorageClass)) in
        let _storage = _args.0
        let rhs_storage = _args.1
        if _storage._comboSumCount != rhs_storage._comboSumCount {return false}
        if _storage._emoji != rhs_storage._emoji {return false}
        if _storage._emojiIcon != rhs_storage._emojiIcon {return false}
        if _storage._emojiText != rhs_storage._emojiText {return false}
        return true
      }
      if !storagesAreEqual {return false}
    }
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Douyin_DoubleLikeDetail: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".DoubleLikeDetail"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "doubleFlag"),
    2: .same(proto: "seqId"),
    3: .same(proto: "renewalsNum"),
    4: .same(proto: "triggersNum"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularBoolField(value: &self.doubleFlag) }()
      case 2: try { try decoder.decodeSingularUInt32Field(value: &self.seqID) }()
      case 3: try { try decoder.decodeSingularUInt32Field(value: &self.renewalsNum) }()
      case 4: try { try decoder.decodeSingularUInt32Field(value: &self.triggersNum) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    if self.doubleFlag != false {
      try visitor.visitSingularBoolField(value: self.doubleFlag, fieldNumber: 1)
    }
    if self.seqID != 0 {
      try visitor.visitSingularUInt32Field(value: self.seqID, fieldNumber: 2)
    }
    if self.renewalsNum != 0 {
      try visitor.visitSingularUInt32Field(value: self.renewalsNum, fieldNumber: 3)
    }
    if self.triggersNum != 0 {
      try visitor.visitSingularUInt32Field(value: self.triggersNum, fieldNumber: 4)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: Douyin_DoubleLikeDetail, rhs: Douyin_DoubleLikeDetail) -> Bool {
    if lhs.doubleFlag != rhs.doubleFlag {return false}
    if lhs.seqID != rhs.seqID {return false}
    if lhs.renewalsNum != rhs.renewalsNum {return false}
    if lhs.triggersNum != rhs.triggersNum {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Douyin_DisplayControlInfo: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".DisplayControlInfo"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "showText"),
    2: .same(proto: "showIcons"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularBoolField(value: &self.showText) }()
      case 2: try { try decoder.decodeSingularBoolField(value: &self.showIcons) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    if self.showText != false {
      try visitor.visitSingularBoolField(value: self.showText, fieldNumber: 1)
    }
    if self.showIcons != false {
      try visitor.visitSingularBoolField(value: self.showIcons, fieldNumber: 2)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: Douyin_DisplayControlInfo, rhs: Douyin_DisplayControlInfo) -> Bool {
    if lhs.showText != rhs.showText {return false}
    if lhs.showIcons != rhs.showIcons {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Douyin_EpisodeChatMessage: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".EpisodeChatMessage"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "common"),
    2: .same(proto: "user"),
    3: .same(proto: "content"),
    4: .same(proto: "visibleToSende"),
    7: .same(proto: "giftImage"),
    8: .same(proto: "agreeMsgId"),
    9: .same(proto: "colorValueList"),
  ]

  fileprivate class _StorageClass {
    var _common: Douyin_Message? = nil
    var _user: Douyin_User? = nil
    var _content: String = String()
    var _visibleToSende: Bool = false
    var _giftImage: Douyin_Image? = nil
    var _agreeMsgID: UInt64 = 0
    var _colorValueList: [String] = []

    #if swift(>=5.10)
      // This property is used as the initial default value for new instances of the type.
      // The type itself is protecting the reference to its storage via CoW semantics.
      // This will force a copy to be made of this reference when the first mutation occurs;
      // hence, it is safe to mark this as `nonisolated(unsafe)`.
      static nonisolated(unsafe) let defaultInstance = _StorageClass()
    #else
      static let defaultInstance = _StorageClass()
    #endif

    private init() {}

    init(copying source: _StorageClass) {
      _common = source._common
      _user = source._user
      _content = source._content
      _visibleToSende = source._visibleToSende
      _giftImage = source._giftImage
      _agreeMsgID = source._agreeMsgID
      _colorValueList = source._colorValueList
    }
  }

  fileprivate mutating func _uniqueStorage() -> _StorageClass {
    if !isKnownUniquelyReferenced(&_storage) {
      _storage = _StorageClass(copying: _storage)
    }
    return _storage
  }

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    _ = _uniqueStorage()
    try withExtendedLifetime(_storage) { (_storage: _StorageClass) in
      while let fieldNumber = try decoder.nextFieldNumber() {
        // The use of inline closures is to circumvent an issue where the compiler
        // allocates stack space for every case branch when no optimizations are
        // enabled. https://github.com/apple/swift-protobuf/issues/1034
        switch fieldNumber {
        case 1: try { try decoder.decodeSingularMessageField(value: &_storage._common) }()
        case 2: try { try decoder.decodeSingularMessageField(value: &_storage._user) }()
        case 3: try { try decoder.decodeSingularStringField(value: &_storage._content) }()
        case 4: try { try decoder.decodeSingularBoolField(value: &_storage._visibleToSende) }()
        case 7: try { try decoder.decodeSingularMessageField(value: &_storage._giftImage) }()
        case 8: try { try decoder.decodeSingularUInt64Field(value: &_storage._agreeMsgID) }()
        case 9: try { try decoder.decodeRepeatedStringField(value: &_storage._colorValueList) }()
        default: break
        }
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    try withExtendedLifetime(_storage) { (_storage: _StorageClass) in
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every if/case branch local when no optimizations
      // are enabled. https://github.com/apple/swift-protobuf/issues/1034 and
      // https://github.com/apple/swift-protobuf/issues/1182
      try { if let v = _storage._common {
        try visitor.visitSingularMessageField(value: v, fieldNumber: 1)
      } }()
      try { if let v = _storage._user {
        try visitor.visitSingularMessageField(value: v, fieldNumber: 2)
      } }()
      if !_storage._content.isEmpty {
        try visitor.visitSingularStringField(value: _storage._content, fieldNumber: 3)
      }
      if _storage._visibleToSende != false {
        try visitor.visitSingularBoolField(value: _storage._visibleToSende, fieldNumber: 4)
      }
      try { if let v = _storage._giftImage {
        try visitor.visitSingularMessageField(value: v, fieldNumber: 7)
      } }()
      if _storage._agreeMsgID != 0 {
        try visitor.visitSingularUInt64Field(value: _storage._agreeMsgID, fieldNumber: 8)
      }
      if !_storage._colorValueList.isEmpty {
        try visitor.visitRepeatedStringField(value: _storage._colorValueList, fieldNumber: 9)
      }
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: Douyin_EpisodeChatMessage, rhs: Douyin_EpisodeChatMessage) -> Bool {
    if lhs._storage !== rhs._storage {
      let storagesAreEqual: Bool = withExtendedLifetime((lhs._storage, rhs._storage)) { (_args: (_StorageClass, _StorageClass)) in
        let _storage = _args.0
        let rhs_storage = _args.1
        if _storage._common != rhs_storage._common {return false}
        if _storage._user != rhs_storage._user {return false}
        if _storage._content != rhs_storage._content {return false}
        if _storage._visibleToSende != rhs_storage._visibleToSende {return false}
        if _storage._giftImage != rhs_storage._giftImage {return false}
        if _storage._agreeMsgID != rhs_storage._agreeMsgID {return false}
        if _storage._colorValueList != rhs_storage._colorValueList {return false}
        return true
      }
      if !storagesAreEqual {return false}
    }
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Douyin_MatchAgainstScoreMessage: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".MatchAgainstScoreMessage"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "common"),
    2: .same(proto: "against"),
    3: .same(proto: "matchStatus"),
    4: .same(proto: "displayStatus"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularMessageField(value: &self._common) }()
      case 2: try { try decoder.decodeSingularMessageField(value: &self._against) }()
      case 3: try { try decoder.decodeSingularUInt32Field(value: &self.matchStatus) }()
      case 4: try { try decoder.decodeSingularUInt32Field(value: &self.displayStatus) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    // The use of inline closures is to circumvent an issue where the compiler
    // allocates stack space for every if/case branch local when no optimizations
    // are enabled. https://github.com/apple/swift-protobuf/issues/1034 and
    // https://github.com/apple/swift-protobuf/issues/1182
    try { if let v = self._common {
      try visitor.visitSingularMessageField(value: v, fieldNumber: 1)
    } }()
    try { if let v = self._against {
      try visitor.visitSingularMessageField(value: v, fieldNumber: 2)
    } }()
    if self.matchStatus != 0 {
      try visitor.visitSingularUInt32Field(value: self.matchStatus, fieldNumber: 3)
    }
    if self.displayStatus != 0 {
      try visitor.visitSingularUInt32Field(value: self.displayStatus, fieldNumber: 4)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: Douyin_MatchAgainstScoreMessage, rhs: Douyin_MatchAgainstScoreMessage) -> Bool {
    if lhs._common != rhs._common {return false}
    if lhs._against != rhs._against {return false}
    if lhs.matchStatus != rhs.matchStatus {return false}
    if lhs.displayStatus != rhs.displayStatus {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Douyin_Against: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".Against"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "leftName"),
    2: .same(proto: "leftLogo"),
    3: .same(proto: "leftGoal"),
    6: .same(proto: "rightName"),
    7: .same(proto: "rightLogo"),
    8: .same(proto: "rightGoal"),
    11: .same(proto: "timestamp"),
    12: .same(proto: "version"),
    13: .same(proto: "leftTeamId"),
    14: .same(proto: "rightTeamId"),
    15: .same(proto: "diffSei2absSecond"),
    16: .same(proto: "finalGoalStage"),
    17: .same(proto: "currentGoalStage"),
    18: .same(proto: "leftScoreAddition"),
    19: .same(proto: "rightScoreAddition"),
    20: .same(proto: "leftGoalInt"),
    21: .same(proto: "rightGoalInt"),
  ]

  fileprivate class _StorageClass {
    var _leftName: String = String()
    var _leftLogo: Douyin_Image? = nil
    var _leftGoal: String = String()
    var _rightName: String = String()
    var _rightLogo: Douyin_Image? = nil
    var _rightGoal: String = String()
    var _timestamp: UInt64 = 0
    var _version: UInt64 = 0
    var _leftTeamID: UInt64 = 0
    var _rightTeamID: UInt64 = 0
    var _diffSei2AbsSecond: UInt64 = 0
    var _finalGoalStage: UInt32 = 0
    var _currentGoalStage: UInt32 = 0
    var _leftScoreAddition: UInt32 = 0
    var _rightScoreAddition: UInt32 = 0
    var _leftGoalInt: UInt64 = 0
    var _rightGoalInt: UInt64 = 0

    #if swift(>=5.10)
      // This property is used as the initial default value for new instances of the type.
      // The type itself is protecting the reference to its storage via CoW semantics.
      // This will force a copy to be made of this reference when the first mutation occurs;
      // hence, it is safe to mark this as `nonisolated(unsafe)`.
      static nonisolated(unsafe) let defaultInstance = _StorageClass()
    #else
      static let defaultInstance = _StorageClass()
    #endif

    private init() {}

    init(copying source: _StorageClass) {
      _leftName = source._leftName
      _leftLogo = source._leftLogo
      _leftGoal = source._leftGoal
      _rightName = source._rightName
      _rightLogo = source._rightLogo
      _rightGoal = source._rightGoal
      _timestamp = source._timestamp
      _version = source._version
      _leftTeamID = source._leftTeamID
      _rightTeamID = source._rightTeamID
      _diffSei2AbsSecond = source._diffSei2AbsSecond
      _finalGoalStage = source._finalGoalStage
      _currentGoalStage = source._currentGoalStage
      _leftScoreAddition = source._leftScoreAddition
      _rightScoreAddition = source._rightScoreAddition
      _leftGoalInt = source._leftGoalInt
      _rightGoalInt = source._rightGoalInt
    }
  }

  fileprivate mutating func _uniqueStorage() -> _StorageClass {
    if !isKnownUniquelyReferenced(&_storage) {
      _storage = _StorageClass(copying: _storage)
    }
    return _storage
  }

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    _ = _uniqueStorage()
    try withExtendedLifetime(_storage) { (_storage: _StorageClass) in
      while let fieldNumber = try decoder.nextFieldNumber() {
        // The use of inline closures is to circumvent an issue where the compiler
        // allocates stack space for every case branch when no optimizations are
        // enabled. https://github.com/apple/swift-protobuf/issues/1034
        switch fieldNumber {
        case 1: try { try decoder.decodeSingularStringField(value: &_storage._leftName) }()
        case 2: try { try decoder.decodeSingularMessageField(value: &_storage._leftLogo) }()
        case 3: try { try decoder.decodeSingularStringField(value: &_storage._leftGoal) }()
        case 6: try { try decoder.decodeSingularStringField(value: &_storage._rightName) }()
        case 7: try { try decoder.decodeSingularMessageField(value: &_storage._rightLogo) }()
        case 8: try { try decoder.decodeSingularStringField(value: &_storage._rightGoal) }()
        case 11: try { try decoder.decodeSingularUInt64Field(value: &_storage._timestamp) }()
        case 12: try { try decoder.decodeSingularUInt64Field(value: &_storage._version) }()
        case 13: try { try decoder.decodeSingularUInt64Field(value: &_storage._leftTeamID) }()
        case 14: try { try decoder.decodeSingularUInt64Field(value: &_storage._rightTeamID) }()
        case 15: try { try decoder.decodeSingularUInt64Field(value: &_storage._diffSei2AbsSecond) }()
        case 16: try { try decoder.decodeSingularUInt32Field(value: &_storage._finalGoalStage) }()
        case 17: try { try decoder.decodeSingularUInt32Field(value: &_storage._currentGoalStage) }()
        case 18: try { try decoder.decodeSingularUInt32Field(value: &_storage._leftScoreAddition) }()
        case 19: try { try decoder.decodeSingularUInt32Field(value: &_storage._rightScoreAddition) }()
        case 20: try { try decoder.decodeSingularUInt64Field(value: &_storage._leftGoalInt) }()
        case 21: try { try decoder.decodeSingularUInt64Field(value: &_storage._rightGoalInt) }()
        default: break
        }
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    try withExtendedLifetime(_storage) { (_storage: _StorageClass) in
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every if/case branch local when no optimizations
      // are enabled. https://github.com/apple/swift-protobuf/issues/1034 and
      // https://github.com/apple/swift-protobuf/issues/1182
      if !_storage._leftName.isEmpty {
        try visitor.visitSingularStringField(value: _storage._leftName, fieldNumber: 1)
      }
      try { if let v = _storage._leftLogo {
        try visitor.visitSingularMessageField(value: v, fieldNumber: 2)
      } }()
      if !_storage._leftGoal.isEmpty {
        try visitor.visitSingularStringField(value: _storage._leftGoal, fieldNumber: 3)
      }
      if !_storage._rightName.isEmpty {
        try visitor.visitSingularStringField(value: _storage._rightName, fieldNumber: 6)
      }
      try { if let v = _storage._rightLogo {
        try visitor.visitSingularMessageField(value: v, fieldNumber: 7)
      } }()
      if !_storage._rightGoal.isEmpty {
        try visitor.visitSingularStringField(value: _storage._rightGoal, fieldNumber: 8)
      }
      if _storage._timestamp != 0 {
        try visitor.visitSingularUInt64Field(value: _storage._timestamp, fieldNumber: 11)
      }
      if _storage._version != 0 {
        try visitor.visitSingularUInt64Field(value: _storage._version, fieldNumber: 12)
      }
      if _storage._leftTeamID != 0 {
        try visitor.visitSingularUInt64Field(value: _storage._leftTeamID, fieldNumber: 13)
      }
      if _storage._rightTeamID != 0 {
        try visitor.visitSingularUInt64Field(value: _storage._rightTeamID, fieldNumber: 14)
      }
      if _storage._diffSei2AbsSecond != 0 {
        try visitor.visitSingularUInt64Field(value: _storage._diffSei2AbsSecond, fieldNumber: 15)
      }
      if _storage._finalGoalStage != 0 {
        try visitor.visitSingularUInt32Field(value: _storage._finalGoalStage, fieldNumber: 16)
      }
      if _storage._currentGoalStage != 0 {
        try visitor.visitSingularUInt32Field(value: _storage._currentGoalStage, fieldNumber: 17)
      }
      if _storage._leftScoreAddition != 0 {
        try visitor.visitSingularUInt32Field(value: _storage._leftScoreAddition, fieldNumber: 18)
      }
      if _storage._rightScoreAddition != 0 {
        try visitor.visitSingularUInt32Field(value: _storage._rightScoreAddition, fieldNumber: 19)
      }
      if _storage._leftGoalInt != 0 {
        try visitor.visitSingularUInt64Field(value: _storage._leftGoalInt, fieldNumber: 20)
      }
      if _storage._rightGoalInt != 0 {
        try visitor.visitSingularUInt64Field(value: _storage._rightGoalInt, fieldNumber: 21)
      }
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: Douyin_Against, rhs: Douyin_Against) -> Bool {
    if lhs._storage !== rhs._storage {
      let storagesAreEqual: Bool = withExtendedLifetime((lhs._storage, rhs._storage)) { (_args: (_StorageClass, _StorageClass)) in
        let _storage = _args.0
        let rhs_storage = _args.1
        if _storage._leftName != rhs_storage._leftName {return false}
        if _storage._leftLogo != rhs_storage._leftLogo {return false}
        if _storage._leftGoal != rhs_storage._leftGoal {return false}
        if _storage._rightName != rhs_storage._rightName {return false}
        if _storage._rightLogo != rhs_storage._rightLogo {return false}
        if _storage._rightGoal != rhs_storage._rightGoal {return false}
        if _storage._timestamp != rhs_storage._timestamp {return false}
        if _storage._version != rhs_storage._version {return false}
        if _storage._leftTeamID != rhs_storage._leftTeamID {return false}
        if _storage._rightTeamID != rhs_storage._rightTeamID {return false}
        if _storage._diffSei2AbsSecond != rhs_storage._diffSei2AbsSecond {return false}
        if _storage._finalGoalStage != rhs_storage._finalGoalStage {return false}
        if _storage._currentGoalStage != rhs_storage._currentGoalStage {return false}
        if _storage._leftScoreAddition != rhs_storage._leftScoreAddition {return false}
        if _storage._rightScoreAddition != rhs_storage._rightScoreAddition {return false}
        if _storage._leftGoalInt != rhs_storage._leftGoalInt {return false}
        if _storage._rightGoalInt != rhs_storage._rightGoalInt {return false}
        return true
      }
      if !storagesAreEqual {return false}
    }
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Douyin_Common: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".Common"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "method"),
    2: .same(proto: "msgId"),
    3: .same(proto: "roomId"),
    4: .same(proto: "createTime"),
    5: .same(proto: "monitor"),
    6: .same(proto: "isShowMsg"),
    7: .same(proto: "describe"),
    9: .same(proto: "foldType"),
    10: .same(proto: "anchorFoldType"),
    11: .same(proto: "priorityScore"),
    12: .same(proto: "logId"),
    13: .same(proto: "msgProcessFilterK"),
    14: .same(proto: "msgProcessFilterV"),
    15: .same(proto: "user"),
    17: .same(proto: "anchorFoldTypeV2"),
    18: .same(proto: "processAtSeiTimeMs"),
    19: .same(proto: "randomDispatchMs"),
    20: .same(proto: "isDispatch"),
    21: .same(proto: "channelId"),
    22: .same(proto: "diffSei2absSecond"),
    23: .same(proto: "anchorFoldDuration"),
  ]

  fileprivate class _StorageClass {
    var _method: String = String()
    var _msgID: UInt64 = 0
    var _roomID: UInt64 = 0
    var _createTime: UInt64 = 0
    var _monitor: UInt32 = 0
    var _isShowMsg: Bool = false
    var _describe: String = String()
    var _foldType: UInt64 = 0
    var _anchorFoldType: UInt64 = 0
    var _priorityScore: UInt64 = 0
    var _logID: String = String()
    var _msgProcessFilterK: String = String()
    var _msgProcessFilterV: String = String()
    var _user: Douyin_User? = nil
    var _anchorFoldTypeV2: UInt64 = 0
    var _processAtSeiTimeMs: UInt64 = 0
    var _randomDispatchMs: UInt64 = 0
    var _isDispatch: Bool = false
    var _channelID: UInt64 = 0
    var _diffSei2AbsSecond: UInt64 = 0
    var _anchorFoldDuration: UInt64 = 0

    #if swift(>=5.10)
      // This property is used as the initial default value for new instances of the type.
      // The type itself is protecting the reference to its storage via CoW semantics.
      // This will force a copy to be made of this reference when the first mutation occurs;
      // hence, it is safe to mark this as `nonisolated(unsafe)`.
      static nonisolated(unsafe) let defaultInstance = _StorageClass()
    #else
      static let defaultInstance = _StorageClass()
    #endif

    private init() {}

    init(copying source: _StorageClass) {
      _method = source._method
      _msgID = source._msgID
      _roomID = source._roomID
      _createTime = source._createTime
      _monitor = source._monitor
      _isShowMsg = source._isShowMsg
      _describe = source._describe
      _foldType = source._foldType
      _anchorFoldType = source._anchorFoldType
      _priorityScore = source._priorityScore
      _logID = source._logID
      _msgProcessFilterK = source._msgProcessFilterK
      _msgProcessFilterV = source._msgProcessFilterV
      _user = source._user
      _anchorFoldTypeV2 = source._anchorFoldTypeV2
      _processAtSeiTimeMs = source._processAtSeiTimeMs
      _randomDispatchMs = source._randomDispatchMs
      _isDispatch = source._isDispatch
      _channelID = source._channelID
      _diffSei2AbsSecond = source._diffSei2AbsSecond
      _anchorFoldDuration = source._anchorFoldDuration
    }
  }

  fileprivate mutating func _uniqueStorage() -> _StorageClass {
    if !isKnownUniquelyReferenced(&_storage) {
      _storage = _StorageClass(copying: _storage)
    }
    return _storage
  }

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    _ = _uniqueStorage()
    try withExtendedLifetime(_storage) { (_storage: _StorageClass) in
      while let fieldNumber = try decoder.nextFieldNumber() {
        // The use of inline closures is to circumvent an issue where the compiler
        // allocates stack space for every case branch when no optimizations are
        // enabled. https://github.com/apple/swift-protobuf/issues/1034
        switch fieldNumber {
        case 1: try { try decoder.decodeSingularStringField(value: &_storage._method) }()
        case 2: try { try decoder.decodeSingularUInt64Field(value: &_storage._msgID) }()
        case 3: try { try decoder.decodeSingularUInt64Field(value: &_storage._roomID) }()
        case 4: try { try decoder.decodeSingularUInt64Field(value: &_storage._createTime) }()
        case 5: try { try decoder.decodeSingularUInt32Field(value: &_storage._monitor) }()
        case 6: try { try decoder.decodeSingularBoolField(value: &_storage._isShowMsg) }()
        case 7: try { try decoder.decodeSingularStringField(value: &_storage._describe) }()
        case 9: try { try decoder.decodeSingularUInt64Field(value: &_storage._foldType) }()
        case 10: try { try decoder.decodeSingularUInt64Field(value: &_storage._anchorFoldType) }()
        case 11: try { try decoder.decodeSingularUInt64Field(value: &_storage._priorityScore) }()
        case 12: try { try decoder.decodeSingularStringField(value: &_storage._logID) }()
        case 13: try { try decoder.decodeSingularStringField(value: &_storage._msgProcessFilterK) }()
        case 14: try { try decoder.decodeSingularStringField(value: &_storage._msgProcessFilterV) }()
        case 15: try { try decoder.decodeSingularMessageField(value: &_storage._user) }()
        case 17: try { try decoder.decodeSingularUInt64Field(value: &_storage._anchorFoldTypeV2) }()
        case 18: try { try decoder.decodeSingularUInt64Field(value: &_storage._processAtSeiTimeMs) }()
        case 19: try { try decoder.decodeSingularUInt64Field(value: &_storage._randomDispatchMs) }()
        case 20: try { try decoder.decodeSingularBoolField(value: &_storage._isDispatch) }()
        case 21: try { try decoder.decodeSingularUInt64Field(value: &_storage._channelID) }()
        case 22: try { try decoder.decodeSingularUInt64Field(value: &_storage._diffSei2AbsSecond) }()
        case 23: try { try decoder.decodeSingularUInt64Field(value: &_storage._anchorFoldDuration) }()
        default: break
        }
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    try withExtendedLifetime(_storage) { (_storage: _StorageClass) in
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every if/case branch local when no optimizations
      // are enabled. https://github.com/apple/swift-protobuf/issues/1034 and
      // https://github.com/apple/swift-protobuf/issues/1182
      if !_storage._method.isEmpty {
        try visitor.visitSingularStringField(value: _storage._method, fieldNumber: 1)
      }
      if _storage._msgID != 0 {
        try visitor.visitSingularUInt64Field(value: _storage._msgID, fieldNumber: 2)
      }
      if _storage._roomID != 0 {
        try visitor.visitSingularUInt64Field(value: _storage._roomID, fieldNumber: 3)
      }
      if _storage._createTime != 0 {
        try visitor.visitSingularUInt64Field(value: _storage._createTime, fieldNumber: 4)
      }
      if _storage._monitor != 0 {
        try visitor.visitSingularUInt32Field(value: _storage._monitor, fieldNumber: 5)
      }
      if _storage._isShowMsg != false {
        try visitor.visitSingularBoolField(value: _storage._isShowMsg, fieldNumber: 6)
      }
      if !_storage._describe.isEmpty {
        try visitor.visitSingularStringField(value: _storage._describe, fieldNumber: 7)
      }
      if _storage._foldType != 0 {
        try visitor.visitSingularUInt64Field(value: _storage._foldType, fieldNumber: 9)
      }
      if _storage._anchorFoldType != 0 {
        try visitor.visitSingularUInt64Field(value: _storage._anchorFoldType, fieldNumber: 10)
      }
      if _storage._priorityScore != 0 {
        try visitor.visitSingularUInt64Field(value: _storage._priorityScore, fieldNumber: 11)
      }
      if !_storage._logID.isEmpty {
        try visitor.visitSingularStringField(value: _storage._logID, fieldNumber: 12)
      }
      if !_storage._msgProcessFilterK.isEmpty {
        try visitor.visitSingularStringField(value: _storage._msgProcessFilterK, fieldNumber: 13)
      }
      if !_storage._msgProcessFilterV.isEmpty {
        try visitor.visitSingularStringField(value: _storage._msgProcessFilterV, fieldNumber: 14)
      }
      try { if let v = _storage._user {
        try visitor.visitSingularMessageField(value: v, fieldNumber: 15)
      } }()
      if _storage._anchorFoldTypeV2 != 0 {
        try visitor.visitSingularUInt64Field(value: _storage._anchorFoldTypeV2, fieldNumber: 17)
      }
      if _storage._processAtSeiTimeMs != 0 {
        try visitor.visitSingularUInt64Field(value: _storage._processAtSeiTimeMs, fieldNumber: 18)
      }
      if _storage._randomDispatchMs != 0 {
        try visitor.visitSingularUInt64Field(value: _storage._randomDispatchMs, fieldNumber: 19)
      }
      if _storage._isDispatch != false {
        try visitor.visitSingularBoolField(value: _storage._isDispatch, fieldNumber: 20)
      }
      if _storage._channelID != 0 {
        try visitor.visitSingularUInt64Field(value: _storage._channelID, fieldNumber: 21)
      }
      if _storage._diffSei2AbsSecond != 0 {
        try visitor.visitSingularUInt64Field(value: _storage._diffSei2AbsSecond, fieldNumber: 22)
      }
      if _storage._anchorFoldDuration != 0 {
        try visitor.visitSingularUInt64Field(value: _storage._anchorFoldDuration, fieldNumber: 23)
      }
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: Douyin_Common, rhs: Douyin_Common) -> Bool {
    if lhs._storage !== rhs._storage {
      let storagesAreEqual: Bool = withExtendedLifetime((lhs._storage, rhs._storage)) { (_args: (_StorageClass, _StorageClass)) in
        let _storage = _args.0
        let rhs_storage = _args.1
        if _storage._method != rhs_storage._method {return false}
        if _storage._msgID != rhs_storage._msgID {return false}
        if _storage._roomID != rhs_storage._roomID {return false}
        if _storage._createTime != rhs_storage._createTime {return false}
        if _storage._monitor != rhs_storage._monitor {return false}
        if _storage._isShowMsg != rhs_storage._isShowMsg {return false}
        if _storage._describe != rhs_storage._describe {return false}
        if _storage._foldType != rhs_storage._foldType {return false}
        if _storage._anchorFoldType != rhs_storage._anchorFoldType {return false}
        if _storage._priorityScore != rhs_storage._priorityScore {return false}
        if _storage._logID != rhs_storage._logID {return false}
        if _storage._msgProcessFilterK != rhs_storage._msgProcessFilterK {return false}
        if _storage._msgProcessFilterV != rhs_storage._msgProcessFilterV {return false}
        if _storage._user != rhs_storage._user {return false}
        if _storage._anchorFoldTypeV2 != rhs_storage._anchorFoldTypeV2 {return false}
        if _storage._processAtSeiTimeMs != rhs_storage._processAtSeiTimeMs {return false}
        if _storage._randomDispatchMs != rhs_storage._randomDispatchMs {return false}
        if _storage._isDispatch != rhs_storage._isDispatch {return false}
        if _storage._channelID != rhs_storage._channelID {return false}
        if _storage._diffSei2AbsSecond != rhs_storage._diffSei2AbsSecond {return false}
        if _storage._anchorFoldDuration != rhs_storage._anchorFoldDuration {return false}
        return true
      }
      if !storagesAreEqual {return false}
    }
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Douyin_User: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".User"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "id"),
    2: .same(proto: "shortId"),
    3: .same(proto: "nickName"),
    4: .same(proto: "gender"),
    5: .same(proto: "Signature"),
    6: .same(proto: "Level"),
    7: .same(proto: "Birthday"),
    8: .same(proto: "Telephone"),
    9: .same(proto: "AvatarThumb"),
    10: .same(proto: "AvatarMedium"),
    11: .same(proto: "AvatarLarge"),
    12: .same(proto: "Verified"),
    13: .same(proto: "Experience"),
    14: .same(proto: "city"),
    15: .same(proto: "Status"),
    16: .same(proto: "CreateTime"),
    17: .same(proto: "ModifyTime"),
    18: .same(proto: "Secret"),
    19: .same(proto: "ShareQrcodeUri"),
    20: .same(proto: "IncomeSharePercent"),
    21: .same(proto: "BadgeImageList"),
    22: .same(proto: "FollowInfo"),
    24: .same(proto: "FansClub"),
    26: .same(proto: "SpecialId"),
    27: .same(proto: "AvatarBorder"),
    28: .same(proto: "Medal"),
    29: .same(proto: "RealTimeIconsList"),
    38: .same(proto: "displayId"),
    46: .same(proto: "secUid"),
    1022: .same(proto: "fanTicketCount"),
    1028: .same(proto: "idStr"),
    1045: .same(proto: "ageRange"),
  ]

  fileprivate class _StorageClass {
    var _id: UInt64 = 0
    var _shortID: UInt64 = 0
    var _nickName: String = String()
    var _gender: UInt32 = 0
    var _signature: String = String()
    var _level: UInt32 = 0
    var _birthday: UInt64 = 0
    var _telephone: String = String()
    var _avatarThumb: Douyin_Image? = nil
    var _avatarMedium: Douyin_Image? = nil
    var _avatarLarge: Douyin_Image? = nil
    var _verified: Bool = false
    var _experience: UInt32 = 0
    var _city: String = String()
    var _status: Int32 = 0
    var _createTime: UInt64 = 0
    var _modifyTime: UInt64 = 0
    var _secret: UInt32 = 0
    var _shareQrcodeUri: String = String()
    var _incomeSharePercent: UInt32 = 0
    var _badgeImageList: [Douyin_Image] = []
    var _followInfo: Douyin_FollowInfo? = nil
    var _fansClub: Douyin_FansClub? = nil
    var _specialID: String = String()
    var _avatarBorder: Douyin_Image? = nil
    var _medal: Douyin_Image? = nil
    var _realTimeIconsList: [Douyin_Image] = []
    var _displayID: String = String()
    var _secUid: String = String()
    var _fanTicketCount: UInt64 = 0
    var _idStr: String = String()
    var _ageRange: UInt32 = 0

    #if swift(>=5.10)
      // This property is used as the initial default value for new instances of the type.
      // The type itself is protecting the reference to its storage via CoW semantics.
      // This will force a copy to be made of this reference when the first mutation occurs;
      // hence, it is safe to mark this as `nonisolated(unsafe)`.
      static nonisolated(unsafe) let defaultInstance = _StorageClass()
    #else
      static let defaultInstance = _StorageClass()
    #endif

    private init() {}

    init(copying source: _StorageClass) {
      _id = source._id
      _shortID = source._shortID
      _nickName = source._nickName
      _gender = source._gender
      _signature = source._signature
      _level = source._level
      _birthday = source._birthday
      _telephone = source._telephone
      _avatarThumb = source._avatarThumb
      _avatarMedium = source._avatarMedium
      _avatarLarge = source._avatarLarge
      _verified = source._verified
      _experience = source._experience
      _city = source._city
      _status = source._status
      _createTime = source._createTime
      _modifyTime = source._modifyTime
      _secret = source._secret
      _shareQrcodeUri = source._shareQrcodeUri
      _incomeSharePercent = source._incomeSharePercent
      _badgeImageList = source._badgeImageList
      _followInfo = source._followInfo
      _fansClub = source._fansClub
      _specialID = source._specialID
      _avatarBorder = source._avatarBorder
      _medal = source._medal
      _realTimeIconsList = source._realTimeIconsList
      _displayID = source._displayID
      _secUid = source._secUid
      _fanTicketCount = source._fanTicketCount
      _idStr = source._idStr
      _ageRange = source._ageRange
    }
  }

  fileprivate mutating func _uniqueStorage() -> _StorageClass {
    if !isKnownUniquelyReferenced(&_storage) {
      _storage = _StorageClass(copying: _storage)
    }
    return _storage
  }

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    _ = _uniqueStorage()
    try withExtendedLifetime(_storage) { (_storage: _StorageClass) in
      while let fieldNumber = try decoder.nextFieldNumber() {
        // The use of inline closures is to circumvent an issue where the compiler
        // allocates stack space for every case branch when no optimizations are
        // enabled. https://github.com/apple/swift-protobuf/issues/1034
        switch fieldNumber {
        case 1: try { try decoder.decodeSingularUInt64Field(value: &_storage._id) }()
        case 2: try { try decoder.decodeSingularUInt64Field(value: &_storage._shortID) }()
        case 3: try { try decoder.decodeSingularStringField(value: &_storage._nickName) }()
        case 4: try { try decoder.decodeSingularUInt32Field(value: &_storage._gender) }()
        case 5: try { try decoder.decodeSingularStringField(value: &_storage._signature) }()
        case 6: try { try decoder.decodeSingularUInt32Field(value: &_storage._level) }()
        case 7: try { try decoder.decodeSingularUInt64Field(value: &_storage._birthday) }()
        case 8: try { try decoder.decodeSingularStringField(value: &_storage._telephone) }()
        case 9: try { try decoder.decodeSingularMessageField(value: &_storage._avatarThumb) }()
        case 10: try { try decoder.decodeSingularMessageField(value: &_storage._avatarMedium) }()
        case 11: try { try decoder.decodeSingularMessageField(value: &_storage._avatarLarge) }()
        case 12: try { try decoder.decodeSingularBoolField(value: &_storage._verified) }()
        case 13: try { try decoder.decodeSingularUInt32Field(value: &_storage._experience) }()
        case 14: try { try decoder.decodeSingularStringField(value: &_storage._city) }()
        case 15: try { try decoder.decodeSingularInt32Field(value: &_storage._status) }()
        case 16: try { try decoder.decodeSingularUInt64Field(value: &_storage._createTime) }()
        case 17: try { try decoder.decodeSingularUInt64Field(value: &_storage._modifyTime) }()
        case 18: try { try decoder.decodeSingularUInt32Field(value: &_storage._secret) }()
        case 19: try { try decoder.decodeSingularStringField(value: &_storage._shareQrcodeUri) }()
        case 20: try { try decoder.decodeSingularUInt32Field(value: &_storage._incomeSharePercent) }()
        case 21: try { try decoder.decodeRepeatedMessageField(value: &_storage._badgeImageList) }()
        case 22: try { try decoder.decodeSingularMessageField(value: &_storage._followInfo) }()
        case 24: try { try decoder.decodeSingularMessageField(value: &_storage._fansClub) }()
        case 26: try { try decoder.decodeSingularStringField(value: &_storage._specialID) }()
        case 27: try { try decoder.decodeSingularMessageField(value: &_storage._avatarBorder) }()
        case 28: try { try decoder.decodeSingularMessageField(value: &_storage._medal) }()
        case 29: try { try decoder.decodeRepeatedMessageField(value: &_storage._realTimeIconsList) }()
        case 38: try { try decoder.decodeSingularStringField(value: &_storage._displayID) }()
        case 46: try { try decoder.decodeSingularStringField(value: &_storage._secUid) }()
        case 1022: try { try decoder.decodeSingularUInt64Field(value: &_storage._fanTicketCount) }()
        case 1028: try { try decoder.decodeSingularStringField(value: &_storage._idStr) }()
        case 1045: try { try decoder.decodeSingularUInt32Field(value: &_storage._ageRange) }()
        default: break
        }
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    try withExtendedLifetime(_storage) { (_storage: _StorageClass) in
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every if/case branch local when no optimizations
      // are enabled. https://github.com/apple/swift-protobuf/issues/1034 and
      // https://github.com/apple/swift-protobuf/issues/1182
      if _storage._id != 0 {
        try visitor.visitSingularUInt64Field(value: _storage._id, fieldNumber: 1)
      }
      if _storage._shortID != 0 {
        try visitor.visitSingularUInt64Field(value: _storage._shortID, fieldNumber: 2)
      }
      if !_storage._nickName.isEmpty {
        try visitor.visitSingularStringField(value: _storage._nickName, fieldNumber: 3)
      }
      if _storage._gender != 0 {
        try visitor.visitSingularUInt32Field(value: _storage._gender, fieldNumber: 4)
      }
      if !_storage._signature.isEmpty {
        try visitor.visitSingularStringField(value: _storage._signature, fieldNumber: 5)
      }
      if _storage._level != 0 {
        try visitor.visitSingularUInt32Field(value: _storage._level, fieldNumber: 6)
      }
      if _storage._birthday != 0 {
        try visitor.visitSingularUInt64Field(value: _storage._birthday, fieldNumber: 7)
      }
      if !_storage._telephone.isEmpty {
        try visitor.visitSingularStringField(value: _storage._telephone, fieldNumber: 8)
      }
      try { if let v = _storage._avatarThumb {
        try visitor.visitSingularMessageField(value: v, fieldNumber: 9)
      } }()
      try { if let v = _storage._avatarMedium {
        try visitor.visitSingularMessageField(value: v, fieldNumber: 10)
      } }()
      try { if let v = _storage._avatarLarge {
        try visitor.visitSingularMessageField(value: v, fieldNumber: 11)
      } }()
      if _storage._verified != false {
        try visitor.visitSingularBoolField(value: _storage._verified, fieldNumber: 12)
      }
      if _storage._experience != 0 {
        try visitor.visitSingularUInt32Field(value: _storage._experience, fieldNumber: 13)
      }
      if !_storage._city.isEmpty {
        try visitor.visitSingularStringField(value: _storage._city, fieldNumber: 14)
      }
      if _storage._status != 0 {
        try visitor.visitSingularInt32Field(value: _storage._status, fieldNumber: 15)
      }
      if _storage._createTime != 0 {
        try visitor.visitSingularUInt64Field(value: _storage._createTime, fieldNumber: 16)
      }
      if _storage._modifyTime != 0 {
        try visitor.visitSingularUInt64Field(value: _storage._modifyTime, fieldNumber: 17)
      }
      if _storage._secret != 0 {
        try visitor.visitSingularUInt32Field(value: _storage._secret, fieldNumber: 18)
      }
      if !_storage._shareQrcodeUri.isEmpty {
        try visitor.visitSingularStringField(value: _storage._shareQrcodeUri, fieldNumber: 19)
      }
      if _storage._incomeSharePercent != 0 {
        try visitor.visitSingularUInt32Field(value: _storage._incomeSharePercent, fieldNumber: 20)
      }
      if !_storage._badgeImageList.isEmpty {
        try visitor.visitRepeatedMessageField(value: _storage._badgeImageList, fieldNumber: 21)
      }
      try { if let v = _storage._followInfo {
        try visitor.visitSingularMessageField(value: v, fieldNumber: 22)
      } }()
      try { if let v = _storage._fansClub {
        try visitor.visitSingularMessageField(value: v, fieldNumber: 24)
      } }()
      if !_storage._specialID.isEmpty {
        try visitor.visitSingularStringField(value: _storage._specialID, fieldNumber: 26)
      }
      try { if let v = _storage._avatarBorder {
        try visitor.visitSingularMessageField(value: v, fieldNumber: 27)
      } }()
      try { if let v = _storage._medal {
        try visitor.visitSingularMessageField(value: v, fieldNumber: 28)
      } }()
      if !_storage._realTimeIconsList.isEmpty {
        try visitor.visitRepeatedMessageField(value: _storage._realTimeIconsList, fieldNumber: 29)
      }
      if !_storage._displayID.isEmpty {
        try visitor.visitSingularStringField(value: _storage._displayID, fieldNumber: 38)
      }
      if !_storage._secUid.isEmpty {
        try visitor.visitSingularStringField(value: _storage._secUid, fieldNumber: 46)
      }
      if _storage._fanTicketCount != 0 {
        try visitor.visitSingularUInt64Field(value: _storage._fanTicketCount, fieldNumber: 1022)
      }
      if !_storage._idStr.isEmpty {
        try visitor.visitSingularStringField(value: _storage._idStr, fieldNumber: 1028)
      }
      if _storage._ageRange != 0 {
        try visitor.visitSingularUInt32Field(value: _storage._ageRange, fieldNumber: 1045)
      }
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: Douyin_User, rhs: Douyin_User) -> Bool {
    if lhs._storage !== rhs._storage {
      let storagesAreEqual: Bool = withExtendedLifetime((lhs._storage, rhs._storage)) { (_args: (_StorageClass, _StorageClass)) in
        let _storage = _args.0
        let rhs_storage = _args.1
        if _storage._id != rhs_storage._id {return false}
        if _storage._shortID != rhs_storage._shortID {return false}
        if _storage._nickName != rhs_storage._nickName {return false}
        if _storage._gender != rhs_storage._gender {return false}
        if _storage._signature != rhs_storage._signature {return false}
        if _storage._level != rhs_storage._level {return false}
        if _storage._birthday != rhs_storage._birthday {return false}
        if _storage._telephone != rhs_storage._telephone {return false}
        if _storage._avatarThumb != rhs_storage._avatarThumb {return false}
        if _storage._avatarMedium != rhs_storage._avatarMedium {return false}
        if _storage._avatarLarge != rhs_storage._avatarLarge {return false}
        if _storage._verified != rhs_storage._verified {return false}
        if _storage._experience != rhs_storage._experience {return false}
        if _storage._city != rhs_storage._city {return false}
        if _storage._status != rhs_storage._status {return false}
        if _storage._createTime != rhs_storage._createTime {return false}
        if _storage._modifyTime != rhs_storage._modifyTime {return false}
        if _storage._secret != rhs_storage._secret {return false}
        if _storage._shareQrcodeUri != rhs_storage._shareQrcodeUri {return false}
        if _storage._incomeSharePercent != rhs_storage._incomeSharePercent {return false}
        if _storage._badgeImageList != rhs_storage._badgeImageList {return false}
        if _storage._followInfo != rhs_storage._followInfo {return false}
        if _storage._fansClub != rhs_storage._fansClub {return false}
        if _storage._specialID != rhs_storage._specialID {return false}
        if _storage._avatarBorder != rhs_storage._avatarBorder {return false}
        if _storage._medal != rhs_storage._medal {return false}
        if _storage._realTimeIconsList != rhs_storage._realTimeIconsList {return false}
        if _storage._displayID != rhs_storage._displayID {return false}
        if _storage._secUid != rhs_storage._secUid {return false}
        if _storage._fanTicketCount != rhs_storage._fanTicketCount {return false}
        if _storage._idStr != rhs_storage._idStr {return false}
        if _storage._ageRange != rhs_storage._ageRange {return false}
        return true
      }
      if !storagesAreEqual {return false}
    }
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Douyin_FollowInfo: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".FollowInfo"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "followingCount"),
    2: .same(proto: "followerCount"),
    3: .same(proto: "followStatus"),
    4: .same(proto: "pushStatus"),
    5: .same(proto: "remarkName"),
    6: .same(proto: "followerCountStr"),
    7: .same(proto: "followingCountStr"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularUInt64Field(value: &self.followingCount) }()
      case 2: try { try decoder.decodeSingularUInt64Field(value: &self.followerCount) }()
      case 3: try { try decoder.decodeSingularUInt64Field(value: &self.followStatus) }()
      case 4: try { try decoder.decodeSingularUInt64Field(value: &self.pushStatus) }()
      case 5: try { try decoder.decodeSingularStringField(value: &self.remarkName) }()
      case 6: try { try decoder.decodeSingularStringField(value: &self.followerCountStr) }()
      case 7: try { try decoder.decodeSingularStringField(value: &self.followingCountStr) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    if self.followingCount != 0 {
      try visitor.visitSingularUInt64Field(value: self.followingCount, fieldNumber: 1)
    }
    if self.followerCount != 0 {
      try visitor.visitSingularUInt64Field(value: self.followerCount, fieldNumber: 2)
    }
    if self.followStatus != 0 {
      try visitor.visitSingularUInt64Field(value: self.followStatus, fieldNumber: 3)
    }
    if self.pushStatus != 0 {
      try visitor.visitSingularUInt64Field(value: self.pushStatus, fieldNumber: 4)
    }
    if !self.remarkName.isEmpty {
      try visitor.visitSingularStringField(value: self.remarkName, fieldNumber: 5)
    }
    if !self.followerCountStr.isEmpty {
      try visitor.visitSingularStringField(value: self.followerCountStr, fieldNumber: 6)
    }
    if !self.followingCountStr.isEmpty {
      try visitor.visitSingularStringField(value: self.followingCountStr, fieldNumber: 7)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: Douyin_FollowInfo, rhs: Douyin_FollowInfo) -> Bool {
    if lhs.followingCount != rhs.followingCount {return false}
    if lhs.followerCount != rhs.followerCount {return false}
    if lhs.followStatus != rhs.followStatus {return false}
    if lhs.pushStatus != rhs.pushStatus {return false}
    if lhs.remarkName != rhs.remarkName {return false}
    if lhs.followerCountStr != rhs.followerCountStr {return false}
    if lhs.followingCountStr != rhs.followingCountStr {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Douyin_Image: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".Image"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "urlListList"),
    2: .same(proto: "uri"),
    3: .same(proto: "height"),
    4: .same(proto: "width"),
    5: .same(proto: "avgColor"),
    6: .same(proto: "imageType"),
    7: .same(proto: "openWebUrl"),
    8: .same(proto: "content"),
    9: .same(proto: "isAnimated"),
    10: .same(proto: "FlexSettingList"),
    11: .same(proto: "TextSettingList"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeRepeatedStringField(value: &self.urlListList) }()
      case 2: try { try decoder.decodeSingularStringField(value: &self.uri) }()
      case 3: try { try decoder.decodeSingularUInt64Field(value: &self.height) }()
      case 4: try { try decoder.decodeSingularUInt64Field(value: &self.width) }()
      case 5: try { try decoder.decodeSingularStringField(value: &self.avgColor) }()
      case 6: try { try decoder.decodeSingularUInt32Field(value: &self.imageType) }()
      case 7: try { try decoder.decodeSingularStringField(value: &self.openWebURL) }()
      case 8: try { try decoder.decodeSingularMessageField(value: &self._content) }()
      case 9: try { try decoder.decodeSingularBoolField(value: &self.isAnimated) }()
      case 10: try { try decoder.decodeSingularMessageField(value: &self._flexSettingList) }()
      case 11: try { try decoder.decodeSingularMessageField(value: &self._textSettingList) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    // The use of inline closures is to circumvent an issue where the compiler
    // allocates stack space for every if/case branch local when no optimizations
    // are enabled. https://github.com/apple/swift-protobuf/issues/1034 and
    // https://github.com/apple/swift-protobuf/issues/1182
    if !self.urlListList.isEmpty {
      try visitor.visitRepeatedStringField(value: self.urlListList, fieldNumber: 1)
    }
    if !self.uri.isEmpty {
      try visitor.visitSingularStringField(value: self.uri, fieldNumber: 2)
    }
    if self.height != 0 {
      try visitor.visitSingularUInt64Field(value: self.height, fieldNumber: 3)
    }
    if self.width != 0 {
      try visitor.visitSingularUInt64Field(value: self.width, fieldNumber: 4)
    }
    if !self.avgColor.isEmpty {
      try visitor.visitSingularStringField(value: self.avgColor, fieldNumber: 5)
    }
    if self.imageType != 0 {
      try visitor.visitSingularUInt32Field(value: self.imageType, fieldNumber: 6)
    }
    if !self.openWebURL.isEmpty {
      try visitor.visitSingularStringField(value: self.openWebURL, fieldNumber: 7)
    }
    try { if let v = self._content {
      try visitor.visitSingularMessageField(value: v, fieldNumber: 8)
    } }()
    if self.isAnimated != false {
      try visitor.visitSingularBoolField(value: self.isAnimated, fieldNumber: 9)
    }
    try { if let v = self._flexSettingList {
      try visitor.visitSingularMessageField(value: v, fieldNumber: 10)
    } }()
    try { if let v = self._textSettingList {
      try visitor.visitSingularMessageField(value: v, fieldNumber: 11)
    } }()
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: Douyin_Image, rhs: Douyin_Image) -> Bool {
    if lhs.urlListList != rhs.urlListList {return false}
    if lhs.uri != rhs.uri {return false}
    if lhs.height != rhs.height {return false}
    if lhs.width != rhs.width {return false}
    if lhs.avgColor != rhs.avgColor {return false}
    if lhs.imageType != rhs.imageType {return false}
    if lhs.openWebURL != rhs.openWebURL {return false}
    if lhs._content != rhs._content {return false}
    if lhs.isAnimated != rhs.isAnimated {return false}
    if lhs._flexSettingList != rhs._flexSettingList {return false}
    if lhs._textSettingList != rhs._textSettingList {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Douyin_NinePatchSetting: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".NinePatchSetting"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "settingListList"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeRepeatedStringField(value: &self.settingListList) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    if !self.settingListList.isEmpty {
      try visitor.visitRepeatedStringField(value: self.settingListList, fieldNumber: 1)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: Douyin_NinePatchSetting, rhs: Douyin_NinePatchSetting) -> Bool {
    if lhs.settingListList != rhs.settingListList {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Douyin_ImageContent: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".ImageContent"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "name"),
    2: .same(proto: "fontColor"),
    3: .same(proto: "level"),
    4: .same(proto: "alternativeText"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularStringField(value: &self.name) }()
      case 2: try { try decoder.decodeSingularStringField(value: &self.fontColor) }()
      case 3: try { try decoder.decodeSingularUInt64Field(value: &self.level) }()
      case 4: try { try decoder.decodeSingularStringField(value: &self.alternativeText) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    if !self.name.isEmpty {
      try visitor.visitSingularStringField(value: self.name, fieldNumber: 1)
    }
    if !self.fontColor.isEmpty {
      try visitor.visitSingularStringField(value: self.fontColor, fieldNumber: 2)
    }
    if self.level != 0 {
      try visitor.visitSingularUInt64Field(value: self.level, fieldNumber: 3)
    }
    if !self.alternativeText.isEmpty {
      try visitor.visitSingularStringField(value: self.alternativeText, fieldNumber: 4)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: Douyin_ImageContent, rhs: Douyin_ImageContent) -> Bool {
    if lhs.name != rhs.name {return false}
    if lhs.fontColor != rhs.fontColor {return false}
    if lhs.level != rhs.level {return false}
    if lhs.alternativeText != rhs.alternativeText {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Douyin_PushFrame: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".PushFrame"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "seqId"),
    2: .same(proto: "logId"),
    3: .same(proto: "service"),
    4: .same(proto: "method"),
    5: .same(proto: "headersList"),
    6: .same(proto: "payloadEncoding"),
    7: .same(proto: "payloadType"),
    8: .same(proto: "payload"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularUInt64Field(value: &self.seqID) }()
      case 2: try { try decoder.decodeSingularUInt64Field(value: &self.logID) }()
      case 3: try { try decoder.decodeSingularUInt64Field(value: &self.service) }()
      case 4: try { try decoder.decodeSingularUInt64Field(value: &self.method) }()
      case 5: try { try decoder.decodeRepeatedMessageField(value: &self.headersList) }()
      case 6: try { try decoder.decodeSingularStringField(value: &self.payloadEncoding) }()
      case 7: try { try decoder.decodeSingularStringField(value: &self.payloadType) }()
      case 8: try { try decoder.decodeSingularBytesField(value: &self.payload) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    if self.seqID != 0 {
      try visitor.visitSingularUInt64Field(value: self.seqID, fieldNumber: 1)
    }
    if self.logID != 0 {
      try visitor.visitSingularUInt64Field(value: self.logID, fieldNumber: 2)
    }
    if self.service != 0 {
      try visitor.visitSingularUInt64Field(value: self.service, fieldNumber: 3)
    }
    if self.method != 0 {
      try visitor.visitSingularUInt64Field(value: self.method, fieldNumber: 4)
    }
    if !self.headersList.isEmpty {
      try visitor.visitRepeatedMessageField(value: self.headersList, fieldNumber: 5)
    }
    if !self.payloadEncoding.isEmpty {
      try visitor.visitSingularStringField(value: self.payloadEncoding, fieldNumber: 6)
    }
    if !self.payloadType.isEmpty {
      try visitor.visitSingularStringField(value: self.payloadType, fieldNumber: 7)
    }
    if !self.payload.isEmpty {
      try visitor.visitSingularBytesField(value: self.payload, fieldNumber: 8)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: Douyin_PushFrame, rhs: Douyin_PushFrame) -> Bool {
    if lhs.seqID != rhs.seqID {return false}
    if lhs.logID != rhs.logID {return false}
    if lhs.service != rhs.service {return false}
    if lhs.method != rhs.method {return false}
    if lhs.headersList != rhs.headersList {return false}
    if lhs.payloadEncoding != rhs.payloadEncoding {return false}
    if lhs.payloadType != rhs.payloadType {return false}
    if lhs.payload != rhs.payload {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Douyin_kk: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".kk"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    14: .same(proto: "k"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 14: try { try decoder.decodeSingularUInt32Field(value: &self.k) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    if self.k != 0 {
      try visitor.visitSingularUInt32Field(value: self.k, fieldNumber: 14)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: Douyin_kk, rhs: Douyin_kk) -> Bool {
    if lhs.k != rhs.k {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Douyin_SendMessageBody: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".SendMessageBody"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "conversationId"),
    2: .same(proto: "conversationType"),
    3: .same(proto: "conversationShortId"),
    4: .same(proto: "content"),
    5: .same(proto: "ext"),
    6: .same(proto: "messageType"),
    7: .same(proto: "ticket"),
    8: .same(proto: "clientMessageId"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularStringField(value: &self.conversationID) }()
      case 2: try { try decoder.decodeSingularUInt32Field(value: &self.conversationType) }()
      case 3: try { try decoder.decodeSingularUInt64Field(value: &self.conversationShortID) }()
      case 4: try { try decoder.decodeSingularStringField(value: &self.content) }()
      case 5: try { try decoder.decodeRepeatedMessageField(value: &self.ext) }()
      case 6: try { try decoder.decodeSingularUInt32Field(value: &self.messageType) }()
      case 7: try { try decoder.decodeSingularStringField(value: &self.ticket) }()
      case 8: try { try decoder.decodeSingularStringField(value: &self.clientMessageID) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    if !self.conversationID.isEmpty {
      try visitor.visitSingularStringField(value: self.conversationID, fieldNumber: 1)
    }
    if self.conversationType != 0 {
      try visitor.visitSingularUInt32Field(value: self.conversationType, fieldNumber: 2)
    }
    if self.conversationShortID != 0 {
      try visitor.visitSingularUInt64Field(value: self.conversationShortID, fieldNumber: 3)
    }
    if !self.content.isEmpty {
      try visitor.visitSingularStringField(value: self.content, fieldNumber: 4)
    }
    if !self.ext.isEmpty {
      try visitor.visitRepeatedMessageField(value: self.ext, fieldNumber: 5)
    }
    if self.messageType != 0 {
      try visitor.visitSingularUInt32Field(value: self.messageType, fieldNumber: 6)
    }
    if !self.ticket.isEmpty {
      try visitor.visitSingularStringField(value: self.ticket, fieldNumber: 7)
    }
    if !self.clientMessageID.isEmpty {
      try visitor.visitSingularStringField(value: self.clientMessageID, fieldNumber: 8)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: Douyin_SendMessageBody, rhs: Douyin_SendMessageBody) -> Bool {
    if lhs.conversationID != rhs.conversationID {return false}
    if lhs.conversationType != rhs.conversationType {return false}
    if lhs.conversationShortID != rhs.conversationShortID {return false}
    if lhs.content != rhs.content {return false}
    if lhs.ext != rhs.ext {return false}
    if lhs.messageType != rhs.messageType {return false}
    if lhs.ticket != rhs.ticket {return false}
    if lhs.clientMessageID != rhs.clientMessageID {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Douyin_ExtList: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".ExtList"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "key"),
    2: .same(proto: "value"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularStringField(value: &self.key) }()
      case 2: try { try decoder.decodeSingularStringField(value: &self.value) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    if !self.key.isEmpty {
      try visitor.visitSingularStringField(value: self.key, fieldNumber: 1)
    }
    if !self.value.isEmpty {
      try visitor.visitSingularStringField(value: self.value, fieldNumber: 2)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: Douyin_ExtList, rhs: Douyin_ExtList) -> Bool {
    if lhs.key != rhs.key {return false}
    if lhs.value != rhs.value {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Douyin_Rsp: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".Rsp"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "a"),
    2: .same(proto: "b"),
    3: .same(proto: "c"),
    4: .same(proto: "d"),
    5: .same(proto: "e"),
    6: .same(proto: "f"),
    7: .same(proto: "g"),
    10: .same(proto: "h"),
    11: .same(proto: "i"),
    13: .same(proto: "j"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularInt32Field(value: &self.a) }()
      case 2: try { try decoder.decodeSingularInt32Field(value: &self.b) }()
      case 3: try { try decoder.decodeSingularInt32Field(value: &self.c) }()
      case 4: try { try decoder.decodeSingularStringField(value: &self.d) }()
      case 5: try { try decoder.decodeSingularInt32Field(value: &self.e) }()
      case 6: try { try decoder.decodeSingularMessageField(value: &self._f) }()
      case 7: try { try decoder.decodeSingularStringField(value: &self.g) }()
      case 10: try { try decoder.decodeSingularUInt64Field(value: &self.h) }()
      case 11: try { try decoder.decodeSingularUInt64Field(value: &self.i) }()
      case 13: try { try decoder.decodeSingularUInt64Field(value: &self.j) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    // The use of inline closures is to circumvent an issue where the compiler
    // allocates stack space for every if/case branch local when no optimizations
    // are enabled. https://github.com/apple/swift-protobuf/issues/1034 and
    // https://github.com/apple/swift-protobuf/issues/1182
    if self.a != 0 {
      try visitor.visitSingularInt32Field(value: self.a, fieldNumber: 1)
    }
    if self.b != 0 {
      try visitor.visitSingularInt32Field(value: self.b, fieldNumber: 2)
    }
    if self.c != 0 {
      try visitor.visitSingularInt32Field(value: self.c, fieldNumber: 3)
    }
    if !self.d.isEmpty {
      try visitor.visitSingularStringField(value: self.d, fieldNumber: 4)
    }
    if self.e != 0 {
      try visitor.visitSingularInt32Field(value: self.e, fieldNumber: 5)
    }
    try { if let v = self._f {
      try visitor.visitSingularMessageField(value: v, fieldNumber: 6)
    } }()
    if !self.g.isEmpty {
      try visitor.visitSingularStringField(value: self.g, fieldNumber: 7)
    }
    if self.h != 0 {
      try visitor.visitSingularUInt64Field(value: self.h, fieldNumber: 10)
    }
    if self.i != 0 {
      try visitor.visitSingularUInt64Field(value: self.i, fieldNumber: 11)
    }
    if self.j != 0 {
      try visitor.visitSingularUInt64Field(value: self.j, fieldNumber: 13)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: Douyin_Rsp, rhs: Douyin_Rsp) -> Bool {
    if lhs.a != rhs.a {return false}
    if lhs.b != rhs.b {return false}
    if lhs.c != rhs.c {return false}
    if lhs.d != rhs.d {return false}
    if lhs.e != rhs.e {return false}
    if lhs._f != rhs._f {return false}
    if lhs.g != rhs.g {return false}
    if lhs.h != rhs.h {return false}
    if lhs.i != rhs.i {return false}
    if lhs.j != rhs.j {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Douyin_Rsp.F: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = Douyin_Rsp.protoMessageName + ".F"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "q1"),
    3: .same(proto: "q3"),
    4: .same(proto: "q4"),
    5: .same(proto: "q5"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularUInt64Field(value: &self.q1) }()
      case 3: try { try decoder.decodeSingularUInt64Field(value: &self.q3) }()
      case 4: try { try decoder.decodeSingularStringField(value: &self.q4) }()
      case 5: try { try decoder.decodeSingularUInt64Field(value: &self.q5) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    if self.q1 != 0 {
      try visitor.visitSingularUInt64Field(value: self.q1, fieldNumber: 1)
    }
    if self.q3 != 0 {
      try visitor.visitSingularUInt64Field(value: self.q3, fieldNumber: 3)
    }
    if !self.q4.isEmpty {
      try visitor.visitSingularStringField(value: self.q4, fieldNumber: 4)
    }
    if self.q5 != 0 {
      try visitor.visitSingularUInt64Field(value: self.q5, fieldNumber: 5)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: Douyin_Rsp.F, rhs: Douyin_Rsp.F) -> Bool {
    if lhs.q1 != rhs.q1 {return false}
    if lhs.q3 != rhs.q3 {return false}
    if lhs.q4 != rhs.q4 {return false}
    if lhs.q5 != rhs.q5 {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Douyin_PreMessage: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".PreMessage"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "cmd"),
    2: .same(proto: "sequenceId"),
    3: .same(proto: "sdkVersion"),
    4: .same(proto: "token"),
    5: .same(proto: "refer"),
    6: .same(proto: "inboxType"),
    7: .same(proto: "buildNumber"),
    8: .same(proto: "sendMessageBody"),
    9: .same(proto: "aa"),
    11: .same(proto: "devicePlatform"),
    15: .same(proto: "headers"),
    18: .same(proto: "authType"),
    21: .same(proto: "biz"),
    22: .same(proto: "access"),
  ]

  fileprivate class _StorageClass {
    var _cmd: UInt32 = 0
    var _sequenceID: UInt32 = 0
    var _sdkVersion: String = String()
    var _token: String = String()
    var _refer: UInt32 = 0
    var _inboxType: UInt32 = 0
    var _buildNumber: String = String()
    var _sendMessageBody: Douyin_SendMessageBody? = nil
    var _aa: String = String()
    var _devicePlatform: String = String()
    var _headers: [Douyin_HeadersList] = []
    var _authType: UInt32 = 0
    var _biz: String = String()
    var _access: String = String()

    #if swift(>=5.10)
      // This property is used as the initial default value for new instances of the type.
      // The type itself is protecting the reference to its storage via CoW semantics.
      // This will force a copy to be made of this reference when the first mutation occurs;
      // hence, it is safe to mark this as `nonisolated(unsafe)`.
      static nonisolated(unsafe) let defaultInstance = _StorageClass()
    #else
      static let defaultInstance = _StorageClass()
    #endif

    private init() {}

    init(copying source: _StorageClass) {
      _cmd = source._cmd
      _sequenceID = source._sequenceID
      _sdkVersion = source._sdkVersion
      _token = source._token
      _refer = source._refer
      _inboxType = source._inboxType
      _buildNumber = source._buildNumber
      _sendMessageBody = source._sendMessageBody
      _aa = source._aa
      _devicePlatform = source._devicePlatform
      _headers = source._headers
      _authType = source._authType
      _biz = source._biz
      _access = source._access
    }
  }

  fileprivate mutating func _uniqueStorage() -> _StorageClass {
    if !isKnownUniquelyReferenced(&_storage) {
      _storage = _StorageClass(copying: _storage)
    }
    return _storage
  }

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    _ = _uniqueStorage()
    try withExtendedLifetime(_storage) { (_storage: _StorageClass) in
      while let fieldNumber = try decoder.nextFieldNumber() {
        // The use of inline closures is to circumvent an issue where the compiler
        // allocates stack space for every case branch when no optimizations are
        // enabled. https://github.com/apple/swift-protobuf/issues/1034
        switch fieldNumber {
        case 1: try { try decoder.decodeSingularUInt32Field(value: &_storage._cmd) }()
        case 2: try { try decoder.decodeSingularUInt32Field(value: &_storage._sequenceID) }()
        case 3: try { try decoder.decodeSingularStringField(value: &_storage._sdkVersion) }()
        case 4: try { try decoder.decodeSingularStringField(value: &_storage._token) }()
        case 5: try { try decoder.decodeSingularUInt32Field(value: &_storage._refer) }()
        case 6: try { try decoder.decodeSingularUInt32Field(value: &_storage._inboxType) }()
        case 7: try { try decoder.decodeSingularStringField(value: &_storage._buildNumber) }()
        case 8: try { try decoder.decodeSingularMessageField(value: &_storage._sendMessageBody) }()
        case 9: try { try decoder.decodeSingularStringField(value: &_storage._aa) }()
        case 11: try { try decoder.decodeSingularStringField(value: &_storage._devicePlatform) }()
        case 15: try { try decoder.decodeRepeatedMessageField(value: &_storage._headers) }()
        case 18: try { try decoder.decodeSingularUInt32Field(value: &_storage._authType) }()
        case 21: try { try decoder.decodeSingularStringField(value: &_storage._biz) }()
        case 22: try { try decoder.decodeSingularStringField(value: &_storage._access) }()
        default: break
        }
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    try withExtendedLifetime(_storage) { (_storage: _StorageClass) in
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every if/case branch local when no optimizations
      // are enabled. https://github.com/apple/swift-protobuf/issues/1034 and
      // https://github.com/apple/swift-protobuf/issues/1182
      if _storage._cmd != 0 {
        try visitor.visitSingularUInt32Field(value: _storage._cmd, fieldNumber: 1)
      }
      if _storage._sequenceID != 0 {
        try visitor.visitSingularUInt32Field(value: _storage._sequenceID, fieldNumber: 2)
      }
      if !_storage._sdkVersion.isEmpty {
        try visitor.visitSingularStringField(value: _storage._sdkVersion, fieldNumber: 3)
      }
      if !_storage._token.isEmpty {
        try visitor.visitSingularStringField(value: _storage._token, fieldNumber: 4)
      }
      if _storage._refer != 0 {
        try visitor.visitSingularUInt32Field(value: _storage._refer, fieldNumber: 5)
      }
      if _storage._inboxType != 0 {
        try visitor.visitSingularUInt32Field(value: _storage._inboxType, fieldNumber: 6)
      }
      if !_storage._buildNumber.isEmpty {
        try visitor.visitSingularStringField(value: _storage._buildNumber, fieldNumber: 7)
      }
      try { if let v = _storage._sendMessageBody {
        try visitor.visitSingularMessageField(value: v, fieldNumber: 8)
      } }()
      if !_storage._aa.isEmpty {
        try visitor.visitSingularStringField(value: _storage._aa, fieldNumber: 9)
      }
      if !_storage._devicePlatform.isEmpty {
        try visitor.visitSingularStringField(value: _storage._devicePlatform, fieldNumber: 11)
      }
      if !_storage._headers.isEmpty {
        try visitor.visitRepeatedMessageField(value: _storage._headers, fieldNumber: 15)
      }
      if _storage._authType != 0 {
        try visitor.visitSingularUInt32Field(value: _storage._authType, fieldNumber: 18)
      }
      if !_storage._biz.isEmpty {
        try visitor.visitSingularStringField(value: _storage._biz, fieldNumber: 21)
      }
      if !_storage._access.isEmpty {
        try visitor.visitSingularStringField(value: _storage._access, fieldNumber: 22)
      }
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: Douyin_PreMessage, rhs: Douyin_PreMessage) -> Bool {
    if lhs._storage !== rhs._storage {
      let storagesAreEqual: Bool = withExtendedLifetime((lhs._storage, rhs._storage)) { (_args: (_StorageClass, _StorageClass)) in
        let _storage = _args.0
        let rhs_storage = _args.1
        if _storage._cmd != rhs_storage._cmd {return false}
        if _storage._sequenceID != rhs_storage._sequenceID {return false}
        if _storage._sdkVersion != rhs_storage._sdkVersion {return false}
        if _storage._token != rhs_storage._token {return false}
        if _storage._refer != rhs_storage._refer {return false}
        if _storage._inboxType != rhs_storage._inboxType {return false}
        if _storage._buildNumber != rhs_storage._buildNumber {return false}
        if _storage._sendMessageBody != rhs_storage._sendMessageBody {return false}
        if _storage._aa != rhs_storage._aa {return false}
        if _storage._devicePlatform != rhs_storage._devicePlatform {return false}
        if _storage._headers != rhs_storage._headers {return false}
        if _storage._authType != rhs_storage._authType {return false}
        if _storage._biz != rhs_storage._biz {return false}
        if _storage._access != rhs_storage._access {return false}
        return true
      }
      if !storagesAreEqual {return false}
    }
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Douyin_HeadersList: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".HeadersList"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "key"),
    2: .same(proto: "value"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularStringField(value: &self.key) }()
      case 2: try { try decoder.decodeSingularStringField(value: &self.value) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    if !self.key.isEmpty {
      try visitor.visitSingularStringField(value: self.key, fieldNumber: 1)
    }
    if !self.value.isEmpty {
      try visitor.visitSingularStringField(value: self.value, fieldNumber: 2)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: Douyin_HeadersList, rhs: Douyin_HeadersList) -> Bool {
    if lhs.key != rhs.key {return false}
    if lhs.value != rhs.value {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Douyin_FansGroupInfo: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".FansGroupInfo"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "listFansGroupUrl"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularStringField(value: &self.listFansGroupURL) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    if !self.listFansGroupURL.isEmpty {
      try visitor.visitSingularStringField(value: self.listFansGroupURL, fieldNumber: 1)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: Douyin_FansGroupInfo, rhs: Douyin_FansGroupInfo) -> Bool {
    if lhs.listFansGroupURL != rhs.listFansGroupURL {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Douyin_FansClub: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".FansClub"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "data"),
    2: .same(proto: "preferData"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularMessageField(value: &self._data) }()
      case 2: try { try decoder.decodeMapField(fieldType: SwiftProtobuf._ProtobufMessageMap<SwiftProtobuf.ProtobufInt32,Douyin_FansClubData>.self, value: &self.preferData) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    // The use of inline closures is to circumvent an issue where the compiler
    // allocates stack space for every if/case branch local when no optimizations
    // are enabled. https://github.com/apple/swift-protobuf/issues/1034 and
    // https://github.com/apple/swift-protobuf/issues/1182
    try { if let v = self._data {
      try visitor.visitSingularMessageField(value: v, fieldNumber: 1)
    } }()
    if !self.preferData.isEmpty {
      try visitor.visitMapField(fieldType: SwiftProtobuf._ProtobufMessageMap<SwiftProtobuf.ProtobufInt32,Douyin_FansClubData>.self, value: self.preferData, fieldNumber: 2)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: Douyin_FansClub, rhs: Douyin_FansClub) -> Bool {
    if lhs._data != rhs._data {return false}
    if lhs.preferData != rhs.preferData {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Douyin_FansClubData: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".FansClubData"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "clubName"),
    2: .same(proto: "level"),
    3: .same(proto: "userFansClubStatus"),
    4: .same(proto: "badge"),
    5: .same(proto: "availableGiftIds"),
    6: .same(proto: "anchorId"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularStringField(value: &self.clubName) }()
      case 2: try { try decoder.decodeSingularInt32Field(value: &self.level) }()
      case 3: try { try decoder.decodeSingularInt32Field(value: &self.userFansClubStatus) }()
      case 4: try { try decoder.decodeSingularMessageField(value: &self._badge) }()
      case 5: try { try decoder.decodeRepeatedInt64Field(value: &self.availableGiftIds) }()
      case 6: try { try decoder.decodeSingularInt64Field(value: &self.anchorID) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    // The use of inline closures is to circumvent an issue where the compiler
    // allocates stack space for every if/case branch local when no optimizations
    // are enabled. https://github.com/apple/swift-protobuf/issues/1034 and
    // https://github.com/apple/swift-protobuf/issues/1182
    if !self.clubName.isEmpty {
      try visitor.visitSingularStringField(value: self.clubName, fieldNumber: 1)
    }
    if self.level != 0 {
      try visitor.visitSingularInt32Field(value: self.level, fieldNumber: 2)
    }
    if self.userFansClubStatus != 0 {
      try visitor.visitSingularInt32Field(value: self.userFansClubStatus, fieldNumber: 3)
    }
    try { if let v = self._badge {
      try visitor.visitSingularMessageField(value: v, fieldNumber: 4)
    } }()
    if !self.availableGiftIds.isEmpty {
      try visitor.visitPackedInt64Field(value: self.availableGiftIds, fieldNumber: 5)
    }
    if self.anchorID != 0 {
      try visitor.visitSingularInt64Field(value: self.anchorID, fieldNumber: 6)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: Douyin_FansClubData, rhs: Douyin_FansClubData) -> Bool {
    if lhs.clubName != rhs.clubName {return false}
    if lhs.level != rhs.level {return false}
    if lhs.userFansClubStatus != rhs.userFansClubStatus {return false}
    if lhs._badge != rhs._badge {return false}
    if lhs.availableGiftIds != rhs.availableGiftIds {return false}
    if lhs.anchorID != rhs.anchorID {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Douyin_UserBadge: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".UserBadge"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "icons"),
    2: .same(proto: "title"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeMapField(fieldType: SwiftProtobuf._ProtobufMessageMap<SwiftProtobuf.ProtobufInt32,Douyin_Image>.self, value: &self.icons) }()
      case 2: try { try decoder.decodeSingularStringField(value: &self.title) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    if !self.icons.isEmpty {
      try visitor.visitMapField(fieldType: SwiftProtobuf._ProtobufMessageMap<SwiftProtobuf.ProtobufInt32,Douyin_Image>.self, value: self.icons, fieldNumber: 1)
    }
    if !self.title.isEmpty {
      try visitor.visitSingularStringField(value: self.title, fieldNumber: 2)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: Douyin_UserBadge, rhs: Douyin_UserBadge) -> Bool {
    if lhs.icons != rhs.icons {return false}
    if lhs.title != rhs.title {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Douyin_ControlMessage: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".ControlMessage"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "common"),
    2: .same(proto: "status"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularMessageField(value: &self._common) }()
      case 2: try { try decoder.decodeSingularInt32Field(value: &self.status) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    // The use of inline closures is to circumvent an issue where the compiler
    // allocates stack space for every if/case branch local when no optimizations
    // are enabled. https://github.com/apple/swift-protobuf/issues/1034 and
    // https://github.com/apple/swift-protobuf/issues/1182
    try { if let v = self._common {
      try visitor.visitSingularMessageField(value: v, fieldNumber: 1)
    } }()
    if self.status != 0 {
      try visitor.visitSingularInt32Field(value: self.status, fieldNumber: 2)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: Douyin_ControlMessage, rhs: Douyin_ControlMessage) -> Bool {
    if lhs._common != rhs._common {return false}
    if lhs.status != rhs.status {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Douyin_FansclubMessage: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".FansclubMessage"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "commonInfo"),
    2: .same(proto: "type"),
    3: .same(proto: "content"),
    4: .same(proto: "user"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularMessageField(value: &self._commonInfo) }()
      case 2: try { try decoder.decodeSingularInt32Field(value: &self.type) }()
      case 3: try { try decoder.decodeSingularStringField(value: &self.content) }()
      case 4: try { try decoder.decodeSingularMessageField(value: &self._user) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    // The use of inline closures is to circumvent an issue where the compiler
    // allocates stack space for every if/case branch local when no optimizations
    // are enabled. https://github.com/apple/swift-protobuf/issues/1034 and
    // https://github.com/apple/swift-protobuf/issues/1182
    try { if let v = self._commonInfo {
      try visitor.visitSingularMessageField(value: v, fieldNumber: 1)
    } }()
    if self.type != 0 {
      try visitor.visitSingularInt32Field(value: self.type, fieldNumber: 2)
    }
    if !self.content.isEmpty {
      try visitor.visitSingularStringField(value: self.content, fieldNumber: 3)
    }
    try { if let v = self._user {
      try visitor.visitSingularMessageField(value: v, fieldNumber: 4)
    } }()
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: Douyin_FansclubMessage, rhs: Douyin_FansclubMessage) -> Bool {
    if lhs._commonInfo != rhs._commonInfo {return false}
    if lhs.type != rhs.type {return false}
    if lhs.content != rhs.content {return false}
    if lhs._user != rhs._user {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Douyin_RoomRankMessage: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".RoomRankMessage"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "common"),
    2: .same(proto: "ranksList"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularMessageField(value: &self._common) }()
      case 2: try { try decoder.decodeRepeatedMessageField(value: &self.ranksList) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    // The use of inline closures is to circumvent an issue where the compiler
    // allocates stack space for every if/case branch local when no optimizations
    // are enabled. https://github.com/apple/swift-protobuf/issues/1034 and
    // https://github.com/apple/swift-protobuf/issues/1182
    try { if let v = self._common {
      try visitor.visitSingularMessageField(value: v, fieldNumber: 1)
    } }()
    if !self.ranksList.isEmpty {
      try visitor.visitRepeatedMessageField(value: self.ranksList, fieldNumber: 2)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: Douyin_RoomRankMessage, rhs: Douyin_RoomRankMessage) -> Bool {
    if lhs._common != rhs._common {return false}
    if lhs.ranksList != rhs.ranksList {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Douyin_RoomRankMessage.RoomRank: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = Douyin_RoomRankMessage.protoMessageName + ".RoomRank"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "user"),
    2: .same(proto: "scoreStr"),
    3: .same(proto: "profileHidden"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularMessageField(value: &self._user) }()
      case 2: try { try decoder.decodeSingularStringField(value: &self.scoreStr) }()
      case 3: try { try decoder.decodeSingularBoolField(value: &self.profileHidden) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    // The use of inline closures is to circumvent an issue where the compiler
    // allocates stack space for every if/case branch local when no optimizations
    // are enabled. https://github.com/apple/swift-protobuf/issues/1034 and
    // https://github.com/apple/swift-protobuf/issues/1182
    try { if let v = self._user {
      try visitor.visitSingularMessageField(value: v, fieldNumber: 1)
    } }()
    if !self.scoreStr.isEmpty {
      try visitor.visitSingularStringField(value: self.scoreStr, fieldNumber: 2)
    }
    if self.profileHidden != false {
      try visitor.visitSingularBoolField(value: self.profileHidden, fieldNumber: 3)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: Douyin_RoomRankMessage.RoomRank, rhs: Douyin_RoomRankMessage.RoomRank) -> Bool {
    if lhs._user != rhs._user {return false}
    if lhs.scoreStr != rhs.scoreStr {return false}
    if lhs.profileHidden != rhs.profileHidden {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Douyin_RoomMessage: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".RoomMessage"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "common"),
    2: .same(proto: "content"),
    3: .same(proto: "supprotLandscape"),
    4: .same(proto: "roommessagetype"),
    5: .same(proto: "systemTopMsg"),
    6: .same(proto: "forcedGuarantee"),
    20: .same(proto: "bizScene"),
    30: .same(proto: "buriedPointMap"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularMessageField(value: &self._common) }()
      case 2: try { try decoder.decodeSingularStringField(value: &self.content) }()
      case 3: try { try decoder.decodeSingularBoolField(value: &self.supprotLandscape) }()
      case 4: try { try decoder.decodeSingularEnumField(value: &self.roommessagetype) }()
      case 5: try { try decoder.decodeSingularBoolField(value: &self.systemTopMsg) }()
      case 6: try { try decoder.decodeSingularBoolField(value: &self.forcedGuarantee) }()
      case 20: try { try decoder.decodeSingularStringField(value: &self.bizScene) }()
      case 30: try { try decoder.decodeMapField(fieldType: SwiftProtobuf._ProtobufMap<SwiftProtobuf.ProtobufString,SwiftProtobuf.ProtobufString>.self, value: &self.buriedPointMap) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    // The use of inline closures is to circumvent an issue where the compiler
    // allocates stack space for every if/case branch local when no optimizations
    // are enabled. https://github.com/apple/swift-protobuf/issues/1034 and
    // https://github.com/apple/swift-protobuf/issues/1182
    try { if let v = self._common {
      try visitor.visitSingularMessageField(value: v, fieldNumber: 1)
    } }()
    if !self.content.isEmpty {
      try visitor.visitSingularStringField(value: self.content, fieldNumber: 2)
    }
    if self.supprotLandscape != false {
      try visitor.visitSingularBoolField(value: self.supprotLandscape, fieldNumber: 3)
    }
    if self.roommessagetype != .defaultroommsg {
      try visitor.visitSingularEnumField(value: self.roommessagetype, fieldNumber: 4)
    }
    if self.systemTopMsg != false {
      try visitor.visitSingularBoolField(value: self.systemTopMsg, fieldNumber: 5)
    }
    if self.forcedGuarantee != false {
      try visitor.visitSingularBoolField(value: self.forcedGuarantee, fieldNumber: 6)
    }
    if !self.bizScene.isEmpty {
      try visitor.visitSingularStringField(value: self.bizScene, fieldNumber: 20)
    }
    if !self.buriedPointMap.isEmpty {
      try visitor.visitMapField(fieldType: SwiftProtobuf._ProtobufMap<SwiftProtobuf.ProtobufString,SwiftProtobuf.ProtobufString>.self, value: self.buriedPointMap, fieldNumber: 30)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: Douyin_RoomMessage, rhs: Douyin_RoomMessage) -> Bool {
    if lhs._common != rhs._common {return false}
    if lhs.content != rhs.content {return false}
    if lhs.supprotLandscape != rhs.supprotLandscape {return false}
    if lhs.roommessagetype != rhs.roommessagetype {return false}
    if lhs.systemTopMsg != rhs.systemTopMsg {return false}
    if lhs.forcedGuarantee != rhs.forcedGuarantee {return false}
    if lhs.bizScene != rhs.bizScene {return false}
    if lhs.buriedPointMap != rhs.buriedPointMap {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}
