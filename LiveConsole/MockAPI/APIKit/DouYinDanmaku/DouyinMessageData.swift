//
//  DouyinSite.swift
//  LivePlus
//
//  Created by simon on 28.10.24.
//

import Foundation

enum DouyinMessageType: Int, Codable {
    case CHAT = 0 // 聊天0
    case ONLINE = 1 // 在线人数1
    //        SUPER_CHAT, // 醒目留言2
    case SOCIAL = 3// 关注3
    case MEMBER = 4// 来了4
    case LIKE = 5// 点赞5
    case GIFT = 6// 礼物6
    case CONTROL = 7// 控制7
    case ROOM_RANK = 8// 排行8
    case ROOM_STATS = 9// 直播间统计9
    case FANSCLUB = 10// 粉丝团10
    case EMOJI_CHAT = 11// 表情包11
}



class DouyinMessageData: NSObject, Codable {
    var webRid: String?                  // 直播间webRid（网页后缀）
    var roomId: String?              // 直播间roomId
    var msgType: DouyinMessageType?               // 消息类型
    var user: User?                     // 发送者信息
    var chatInfo: ChatMessage?                 // 评论信息
    var giftInfo: GiftMessage?                   // 礼物信息
    var memberInfo: MemberMessage?             // 成员信息(来了)
    var socialInfo: SocialMessage?            // 关注信息
    var likeInfo: LikeMessage?                  // 点赞信息
    var fansclubInfo: FansclubMessage?         // 粉丝团信息
    var emojiChatInfo: EmojiChatMessage?        // 聊天表情包信息
    var roomUserSeqInfo: RoomUserSeqMessage?    // 直播间用户序列信息
    
    init(webRid: String? = nil, roomId: String? = nil, msgType: Int? = nil, user: User? = nil, chatInfo: ChatMessage? = nil, giftInfo: GiftMessage? = nil, memberInfo: MemberMessage? = nil, socialInfo: SocialMessage? = nil, likeInfo: LikeMessage? = nil, fansclubInfo: FansclubMessage? = nil, emojiChatInfo: EmojiChatMessage? = nil, roomUserSeqInfo: RoomUserSeqMessage? = nil) {
        self.webRid = webRid
        self.roomId = roomId
        if let msgType = msgType {
            self.msgType = DouyinMessageType(rawValue: msgType)
        }
        self.user = user
        self.chatInfo = chatInfo
        self.giftInfo = giftInfo
        self.memberInfo = memberInfo
        self.socialInfo = socialInfo
        self.likeInfo = likeInfo
        self.fansclubInfo = fansclubInfo
        self.emojiChatInfo = emojiChatInfo
        self.roomUserSeqInfo = roomUserSeqInfo
        super.init()
    }
    
    // 增加一个创建时间， 可以计算出是否来自同一个组
    var creatTime: String = ""
}


// 用户信息
class User: NSObject, Codable {
    // 用户的唯一标识符，用64位无符号整数存储，用于在系统中区分不同用户。
    var id: UInt64
    // 用户的短ID，通常是一个更简短的用户标识符，便于展示或查找。
    var shortId: UInt64
    // 用户的昵称，以字符串形式存储，表示用户在平台上的显示名称。（用户名）
    var nickName: String
    // 用户的性别，用32位无符号整数表示，可能使用数值来表示男性、女性或其他。（0未知，1男，2女）
    var gender: UInt32
    // 用户的个性签名，通常用于描述用户的自我介绍或个性化信息。
    var signature: String
    // 用户的等级，用32位无符号整数表示，可能与用户的活跃度、贡献度或经验相关。
    var level: UInt32
    // 用户的生日，以64位无符号整数存储，通常为时间戳格式。
    var birthday: UInt64
    // 用户的电话号码，通常用于登录、验证或找回账号。
    var telephone: String
    // 用户头像的缩略图，Image 类型存储了用户头像的小图版本。（用户头像）
    var avatarThumb: Image
    // 用户头像的中等尺寸图像，Image 类型存储用户头像的中等版本。
    var avatarMedium: Image
    // 用户头像的大图，Image 类型存储用户头像的大图版本。
    var avatarLarge: Image
    // 用户是否经过验证，true 表示用户已通过平台的身份或资质验证。
    var verified: Bool
    // 用户的经验值，用32位无符号整数存储，可能影响用户等级或权利。
    var experience: UInt32
    // 用户所在的城市，表示用户的地理位置信息。
    var city: String
    // 用户的状态，用32位整数表示，可能指示用户当前是否活跃、禁用等状态。
    var status: UInt32
    // 用户账户创建时间，以64位无符号整数存储，通常是时间戳格式。
    var createTime: UInt64
    // 用户信息的最后修改时间，以64位无符号整数存储。
    var modifyTime: UInt64
    // 用户的隐私设置或安全级别，用32位无符号整数表示，控制用户信息的公开范围。
    var secret: Int
    // 用户的分享二维码链接，通常用于生成用户的二维码以进行分享。
    var shareQrcodeUri: String
    // 用户的收益分成百分比，表示用户在平台上的收入分成比例。
    var incomeSharePercent: Int
    // 用户获得的徽章图片列表，Image 类型存储，表示用户的荣誉或成就。
    var badgeImageList: [Image]
    // 用户的关注信息，FollowInfo 类型存储用户的关注和粉丝数量。
    var followInfo: DouyinFollowInfo
    // 用户的粉丝俱乐部信息，FansClub 类型存储。（粉丝团信息）
    var fansClub: FansClub
    // 用户的特殊ID，可能用于某些特殊功能或内部标识。
    var specialId: String
    // 用户头像边框图片，Image 类型表示，可能是用户身份或等级的象征。
    var avatarBorder: Image
    // 用户的勋章图片，Image 类型表示，代表用户的特殊荣誉或成就。
    var medal: Image
    // 实时图标列表，表示用户在不同场景下展示的实时图标，如在线状态等。
    var realTimeIconsList: [Image]
    // 用户的展示ID，通常用于在前端展示给其他用户的唯一标识。（抖音号）
    var displayId: String
    // 用户的安全UID，用于平台内部的用户安全管理。
    var secUid: String
    // 用户的粉丝票数，通常用于表示用户的受欢迎程度或平台奖励。
    var fanTicketCount: UInt64
    // 用户的ID字符串版本，提供一种更易展示和处理的用户ID。
    var idStr: String
    // 用户的年龄范围，用32位无符号整数表示，可能用于平台的内容推荐或过滤。
    var ageRange: Int
    
    init(id: UInt64, shortId: UInt64, nickName: String, gender: UInt32, signature: String, level: UInt32, birthday: UInt64, telephone: String, avatarThumb: Image, avatarMedium: Image, avatarLarge: Image, verified: Bool, experience: UInt32, city: String, status: UInt32, createTime: UInt64, modifyTime: UInt64, secret: UInt32, shareQrcodeUri: String, incomeSharePercent: UInt32, badgeImageList: [Image], followInfo: DouyinFollowInfo, fansClub: FansClub, specialId: String, avatarBorder: Image, medal: Image, realTimeIconsList: [Image], displayId: String, secUid: String, fanTicketCount: UInt64, idStr: String, ageRange: UInt32) {
        self.id = id
        self.shortId = shortId
        self.nickName = nickName
        self.gender = gender
        self.signature = signature
        self.level = level
        self.birthday = birthday
        self.telephone = telephone
        self.avatarThumb = avatarThumb
        self.avatarMedium = avatarMedium
        self.avatarLarge = avatarLarge
        self.verified = verified
        self.experience = experience
        self.city = city
        self.status = status
        self.createTime = createTime
        self.modifyTime = modifyTime
        self.secret = Int(secret)
        self.shareQrcodeUri = shareQrcodeUri
        self.incomeSharePercent = Int(incomeSharePercent)
        self.badgeImageList = badgeImageList
        self.followInfo = followInfo
        self.fansClub = fansClub
        self.specialId = specialId
        self.avatarBorder = avatarBorder
        self.medal = medal
        self.realTimeIconsList = realTimeIconsList
        self.displayId = displayId
        self.secUid = secUid
        self.fanTicketCount = fanTicketCount
        self.idStr = idStr
        self.ageRange = Int(ageRange)
        
    }
    
    
    static func create(user: Douyin_User)-> User {
        var list:[Image] = []
        user.realTimeIconsList.forEach { img in
            list.append(Image.create(image: img))
        }
        
        var badgeImageList:[Image] = []
        user.badgeImageList.forEach { img in
            badgeImageList.append(Image.create(image: img))
        }
        
        return User(id: user.id,
                    shortId:  user.shortID,
                    nickName: user.nickName,
                    gender: user.gender,
                    signature: user.signature,
                    level:  user.level,
                    birthday: user.birthday,
                    telephone: user.telephone,
                    avatarThumb: Image.create(image: user.avatarThumb) ,
                    avatarMedium: Image.create(image: user.avatarMedium),
                    avatarLarge: Image.create(image: user.avatarLarge),
                    verified: user.verified,
                    experience: user.experience,
                    city: user.city,
                    status: UInt32(user.status),
                    createTime: user.createTime,
                    modifyTime: user.modifyTime,
                    secret: user.secret,
                    shareQrcodeUri: user.shareQrcodeUri,
                    incomeSharePercent: user.incomeSharePercent,
                    badgeImageList:  badgeImageList,
                    followInfo: DouyinFollowInfo.create(followInfo: user.followInfo),
                    fansClub: FansClub.create(fansClub: user.fansClub),
                    specialId: user.specialID,
                    avatarBorder: Image.create(image: user.avatarBorder),
                    medal: Image.create(image: user.medal),
                    realTimeIconsList:  list,
                    displayId: user.displayID,
                    secUid: user.secUid,
                    fanTicketCount: user.fanTicketCount,
                    idStr: user.idStr,
                    ageRange: user.ageRange)
        
    }
}

class DouyinFollowInfo: NSObject, Codable {
    // 用户关注的人的数量。
    var followingCount: UInt64
    // 用户的粉丝数量。
    var followerCount: UInt64
    // 用户的关注状态，可能表示是否正在关注某人，或某人是否已关注用户。
    var followStatus: UInt64
    // 推送状态，表示是否接收来自关注对象的推送通知。
    var pushStatus: UInt64
    // 对关注对象的备注名，允许用户为关注对象设置自定义的名字。
    var remarkName: String
    // 粉丝数量的字符串形式，适用于展示大于一定数值的粉丝数（如 “1.2K”）。
    var followerCountStr: String
    // 关注人数的字符串形式，类似于 followerCountStr，便于显示较大的数字。
    var followingCountStr: String
    
    init(followingCount: UInt64, followerCount: UInt64, followStatus: UInt64, pushStatus: UInt64, remarkName: String, followerCountStr: String, followingCountStr: String) {
        self.followingCount = followingCount
        self.followerCount = followerCount
        self.followStatus = followStatus
        self.pushStatus = pushStatus
        self.remarkName = remarkName
        self.followerCountStr = followerCountStr
        self.followingCountStr = followingCountStr
    }
    
    static func create(followInfo: Douyin_FollowInfo) -> DouyinFollowInfo {
        return DouyinFollowInfo(followingCount: followInfo.followingCount,
                          followerCount: followInfo.followerCount,
                          followStatus: followInfo.followStatus,
                          pushStatus: followInfo.pushStatus,
                          remarkName: followInfo.remarkName,
                          followerCountStr: followInfo.followerCountStr,
                          followingCountStr: followInfo.followingCountStr)
    }
    
}

class FansClub: NSObject, Codable {
    // 代表粉丝俱乐部的主要数据，使用FansClubData类型存储粉丝俱乐部的详细信息。
    var data: FansClubData
    
    // 映射结构，用于存储多个FansClubData对象的集合。映射键为32位整型，值为FansClubData，可能表示不同等级或优先级的粉丝俱乐部数据。
    var preferData: Dictionary<Int32,FansClubData> = [:]
    
    init(data: FansClubData, preferData: Dictionary<Int32,FansClubData>) {
        self.data = data
        self.preferData = preferData
    }
    
    static func create(fansClub: Douyin_FansClub) -> FansClub {
        var dats: Dictionary<Int32,FansClubData> = [:]
        
        fansClub.preferData.forEach { (key, value) in
            dats[key] = FansClubData.create(fansClubData: value)
        }
        
        return FansClub(data: FansClubData.create(fansClubData: fansClub.data),
                        preferData:  dats)
    }
    
}

class FansClubData: NSObject, Codable {
    // 粉丝俱乐部的名称，表示俱乐部的标识或显示名称。
    var clubName: String
    
    // 粉丝俱乐部的等级，用于表示用户在该俱乐部中的等级或身份。
    var level: Int
    
    // 用户在粉丝俱乐部的状态，可能用于表示用户是否是该俱乐部的成员、管理员等。
    var userFansClubStatus: Int
    
    // 用户的徽章信息，表示用户在俱乐部中的身份或成就。
    var badge: UserBadge
    
    // 可用礼物ID的列表，表示用户可以使用的礼物，存储为64位整型。
    var availableGiftIds: [Int64]
    
    // 主播的ID，表示该粉丝俱乐部关联的主播的标识。
    var anchorId: Int64
    
    init(clubName: String, level: Int, userFansClubStatus: Int, badge: UserBadge, availableGiftIds: [Int64], anchorId: Int64) {
        self.clubName = clubName
        self.level = level
        self.userFansClubStatus = userFansClubStatus
        self.badge = badge
        self.availableGiftIds = availableGiftIds
        self.anchorId = anchorId
    }
    
    static func create(fansClubData: Douyin_FansClubData)-> FansClubData {
        return FansClubData(
            clubName: fansClubData.clubName,
            level:Int(fansClubData.level),
            userFansClubStatus: Int(fansClubData.userFansClubStatus),
            badge: UserBadge.create(userBadge: fansClubData.badge),
            availableGiftIds: fansClubData.availableGiftIds,
            anchorId: fansClubData.anchorID
        )
    }
    
}

class UserBadge: NSObject, Codable {
    // 表示用户徽章的图标集合。映射的键是32位整型，可能表示不同类型的徽章或等级，值是Image类型，存储徽章的图像信息。
    var icons: Dictionary<Int32,Image> = [:]
    
    // 徽章的标题，用于描述徽章的名称或类别，帮助用户理解该徽章的含义或成就。
    var title: String
    
    init(icons: Dictionary<Int32,Image>, title: String) {
        self.icons = icons
        self.title = title
    }
    
    static func create(userBadge: Douyin_UserBadge)-> UserBadge {
        var dats: Dictionary<Int32,Image> = [:]
        
        userBadge.icons.forEach { (key, value) in
            dats[key] = Image.create(image:  value)
        }
        
        return UserBadge( icons: dats, title: userBadge.title)
    }
    
}

class Common: NSObject, Codable {
    
    var method: String? //表示消息的处理方法，可能是用于标识当前消息类型或处理机制的字符串。
    var msgId: UInt64 = 0//消息的唯一标识符，以 64 位无符号整数存储，用于区分不同的消息。
    var roomId: UInt64 = 0//房间的唯一标识符，表示该消息所属的直播房间或聊天房间。
    var createTime: UInt64 = 0//消息的创建时间戳，用 64 位无符号整数表示。可以用于消息的时间排序。
    var monitor: Int = 0//用于监控的字段，可能用于跟踪或分析消息的状态。
    var isShowMsg: Bool = false//布尔字段，表示该消息是否应该展示。true 表示消息应该显示，false 表示不显示。
    var describe: String? //消息的描述信息，存储为字符串，用于对消息进行额外的说明或解释。
    var foldType: UInt64 = 0//折叠类型，表示消息是否可以折叠显示，用于控制消息的展示形态。
    var anchorFoldType: UInt64 = 0//主播折叠类型，可能是专用于主播消息的折叠控制。
    var priorityScore: UInt64 = 0//消息的优先级分数，用 64 位无符号整数表示。分数越高的消息可能会被优先处理或展示。
    var msgProcessFilterK: String? //消息处理过滤器的键，可能用于消息的处理和过滤逻辑。
    var msgProcessFilterV: String? //消息处理过滤器的值，配合键一起用于消息的处理和过滤。
    var user: User? //用户信息对象，包含发送消息的用户的详细信息。
    var anchorFoldTypeV2: UInt64 = 0//主播折叠类型的第二版本，表示可能是针对某些新的功能或逻辑。
    var processAtSeiTimeMs: UInt64 = 0//消息在 SEI（Supplemental Enhancement Information）时间的处理时间戳，可能与视频或媒体流同步。
    var randomDispatchMs: UInt64 = 0//随机分发时间，可能用于消息的随机分发延迟，以毫秒为单位。
    var isDispatch: Bool = false//是否需要分发消息，true 表示消息需要分发给其他用户。
    var channelId: UInt64 = 0//频道标识符，表示消息所属的频道。
    var diffSei2AbsSecond: UInt64 = 0//SEI 与绝对时间的差异，可能用于视频流和消息的同步计算。
    var anchorFoldDuration: UInt64 = 0//主播消息折叠的持续时间，可能用于控制消息在折叠状态下保持多长时间。
    
    init(method: String? = nil, msgId: UInt64, roomId: UInt64, createTime: UInt64, monitor: Int, isShowMsg: Bool, describe: String? = nil, foldType: UInt64, anchorFoldType: UInt64, priorityScore: UInt64, msgProcessFilterK: String? = nil, msgProcessFilterV: String? = nil, user: User? = nil, anchorFoldTypeV2: UInt64, processAtSeiTimeMs: UInt64, randomDispatchMs: UInt64, isDispatch: Bool, channelId: UInt64, diffSei2AbsSecond: UInt64, anchorFoldDuration: UInt64) {
        self.method = method
        self.msgId = msgId
        self.roomId = roomId
        self.createTime = createTime
        self.monitor = monitor
        self.isShowMsg = isShowMsg
        self.describe = describe
        self.foldType = foldType
        self.anchorFoldType = anchorFoldType
        self.priorityScore = priorityScore
        self.msgProcessFilterK = msgProcessFilterK
        self.msgProcessFilterV = msgProcessFilterV
        self.user = user
        self.anchorFoldTypeV2 = anchorFoldTypeV2
        self.processAtSeiTimeMs = processAtSeiTimeMs
        self.randomDispatchMs = randomDispatchMs
        self.isDispatch = isDispatch
        self.channelId = channelId
        self.diffSei2AbsSecond = diffSei2AbsSecond
        self.anchorFoldDuration = anchorFoldDuration
    }
    
    static func create(common: Douyin_Common)->Common {
        return Common(
            method : common.method,
            msgId : common.msgID,
            roomId : common.roomID,
            createTime : common.createTime,
            monitor : Int(common.monitor),
            isShowMsg : common.isShowMsg,
            describe : common.describe,
            foldType : common.foldType,
            anchorFoldType : common.anchorFoldType,
            priorityScore : common.priorityScore,
            msgProcessFilterK : common.msgProcessFilterK,
            msgProcessFilterV : common.msgProcessFilterV,
            user : User.create(user: common.user),
            anchorFoldTypeV2 : common.anchorFoldTypeV2,
            processAtSeiTimeMs : common.processAtSeiTimeMs,
            randomDispatchMs : common.randomDispatchMs,
            isDispatch : common.isDispatch,
            channelId : common.channelID,
            diffSei2AbsSecond : common.diffSei2AbsSecond,
            anchorFoldDuration: common.anchorFoldDuration
        )
    }
    
}

// 图片信息
class Image: NSObject, Codable {
    // 图片 URL 的列表，通常包含不同分辨率或版本的图片链接。
    var urlListList: [String]
    // 图片的唯一标识符（URI），用于标记图片在系统中的位置。
    var uri: String
    // 图片的高度，单位为像素。
    var height: UInt64
    // 图片的宽度，单位为像素。
    var width: UInt64
    // 图片的平均颜色值，通常用于背景色或占位符色。
    var avgColor: String
    // 图片类型，表示不同类别或格式的图片（例如静态图片或动图）。
    var imageType: Int
    // 点击图片时要打开的网页 URL，通常用于广告或跳转链接。
    var openWebUrl: String
    // 图片的内容信息，可能包含图片的详细描述或元数据。
    var content: ImageContent
    // 是否为动图，true 表示该图片是动态图片（如 GIF）。
    var isAnimated: Bool
    
    init(urlListList: [String], uri: String, height: UInt64, width: UInt64, avgColor: String, imageType: Int, openWebUrl: String, content: ImageContent, isAnimated: Bool) {
        self.urlListList = urlListList
        self.uri = uri
        self.height = height
        self.width = width
        self.avgColor = avgColor
        self.imageType = imageType
        self.openWebUrl = openWebUrl
        self.content = content
        self.isAnimated = isAnimated
    }
    
    func getUrl() -> String? {
        return  urlListList.first
    }
    
    static  func create(image: Douyin_Image)-> Image {
        return Image(
            urlListList: image.urlListList,
            uri: image.uri,
            height:  image.height,
            width:  image.width,
            avgColor:  image.avgColor,
            imageType:  Int(image.imageType),
            openWebUrl:  image.openWebURL,
            content: ImageContent(name: image.content.name,
                                  fontColor: image.content.fontColor,
                                  level: image.content.level,
                                  alternativeText: image.content.alternativeText),
            isAnimated:  image.isAnimated
        )
    }
}

// 图片内容信息
class ImageContent: NSObject, Codable {
    // 名称
    var name: String
    // 字体颜色，可能是用于显示在图片上的文本颜色，格式通常为 HEX 色值（如 #FFFFFF）。
    var fontColor: String
    // 等级
    var level: UInt64
    // 图片的替代文本，用于在图片无法加载时显示的说明文本，或者用于无障碍辅助功能（如屏幕阅读器）。
    var alternativeText: String
    
    init(name: String, fontColor: String, level: UInt64, alternativeText: String) {
        self.name = name
        self.fontColor = fontColor
        self.level = level
        self.alternativeText = alternativeText
    }
}

// 公共区域信息
class PublicAreaCommon: NSObject, Codable {
    // 用户标签的图片对象，可能表示用户的身份标签、等级图标或特殊身份（例如 VIP 用户）的标志。
    var userLabel: Image
    // 用户在房间内的消费总数，使用 64 位无符号整数表示，记录用户在当前房间中的消费金额或虚拟物品（如礼物）的总价值。
    var userConsumeInRoom: UInt64
    // 用户在房间内发送的礼物次数，使用 64 位无符号整数表示，记录用户在当前房间中发送礼物的累计次数。
    var userSendGiftCntInRoom: UInt64
    
    init(userLabel: Image, userConsumeInRoom: UInt64, userSendGiftCntInRoom: UInt64) {
        self.userLabel = userLabel
        self.userConsumeInRoom = userConsumeInRoom
        self.userSendGiftCntInRoom = userSendGiftCntInRoom
    }
    
    static func create(publicAreaCommon: Douyin_PublicAreaCommon)-> PublicAreaCommon {
        return PublicAreaCommon(
            userLabel: Image.create(image: publicAreaCommon.userLabel),
            userConsumeInRoom:  publicAreaCommon.userConsumeInRoom,
            userSendGiftCntInRoom:  publicAreaCommon.userSendGiftCntInRoom
        )
    }
    
}


class ChatMessage: NSObject, Codable {
    var common: Common?  //通用字段，存储一些通用信息，具体内容根据 Common 类型定义，可能包括协议版本、来源等基本信息。
    var user: User?  //发送消息的用户对象，User 类型存储了用户的详细信息（如用户 ID、昵称、头像等）。
    var content: String?  //聊天消息的文本内容，以字符串形式存储。
    var visibleToSender: Bool =  false//布尔类型字段，表示消息是否对发送者可见，true 表示发送者可以看到自己发送的消息。
    var backgroundImage: String?  //消息的背景图片，以 Image 类型存储，可能用于装饰聊天框或提供视觉效果。
    var fullScreenTextColor: String?  //全屏消息的文本颜色，以字符串格式表示颜色值（可能是 HEX 格式或其他颜色表示法）。
    var backgroundImageV2: String?  //另一个版本的背景图片，可能是为了兼容或升级而引入的图片字段。
    var priorityLevel: Int =  0// 消息的优先级，数值越高，优先级越高，决定消息的显示顺序或处理顺序。
    var eventTime: UInt64 =  0//事件时间，表示消息的发送或关联事件的时间戳。
    var sendReview: Bool =  false//表示消息是否需要审查，true 表示该消息可能需要经过审核才能展示。
    var fromIntercom: Bool =  false//是否来自对讲系统，true 表示该消息来自一个内部的对讲或通信系统。
    var intercomHideUserCard: Bool =  false//是否在对讲消息中隐藏用户卡片，true 表示用户卡片信息将被隐藏。
    var chatBy: String?  //聊天消息的发送方式或渠道（如 “text”, “voice”, “gift” 等），用字符串形式存储。
    var individualChatPriority: Int = 0 //单独聊天的优先级，决定一对一聊天中的消息显示或处理顺序。
    var publicAreaCommon: PublicAreaCommon// 公共区域信息，PublicAreaCommon 用于存储一些与消息展示相关的公共区域信息，可能涉及到直播间或互动区域的展示。
    
    init(common: Common? = nil, user: User? = nil, content: String? = nil, visibleToSender: Bool, backgroundImage: String? = nil, fullScreenTextColor: String? = nil, backgroundImageV2: String? = nil, priorityLevel: Int, eventTime: UInt64, sendReview: Bool, fromIntercom: Bool, intercomHideUserCard: Bool, chatBy: String? = nil, individualChatPriority: Int, publicAreaCommon: PublicAreaCommon) {
        self.common = common
        self.user = user
        self.content = content
        self.visibleToSender = visibleToSender
        self.backgroundImage = backgroundImage
        self.fullScreenTextColor = fullScreenTextColor
        self.backgroundImageV2 = backgroundImageV2
        self.priorityLevel = priorityLevel
        self.eventTime = eventTime
        self.sendReview = sendReview
        self.fromIntercom = fromIntercom
        self.intercomHideUserCard = intercomHideUserCard
        self.chatBy = chatBy
        self.individualChatPriority = individualChatPriority
        self.publicAreaCommon = publicAreaCommon
    }
    
    static func create(chatMessage: Douyin_ChatMessage)-> ChatMessage {
        let bgimage = chatMessage.backgroundImage.urlListList.first
        let bgimage2 = chatMessage.backgroundImageV2.urlListList.first
        return ChatMessage(
            common:Common.create(common: chatMessage.common),
            user: User.create(user: chatMessage.user),
            content: chatMessage.content,
            visibleToSender: chatMessage.visibleToSender,
            backgroundImage: bgimage,
            fullScreenTextColor: chatMessage.fullScreenTextColor,
            backgroundImageV2: bgimage2,
            priorityLevel: Int(chatMessage.priorityLevel),
            eventTime: chatMessage.eventTime,
            sendReview: chatMessage.sendReview,
            fromIntercom: chatMessage.fromIntercom,
            intercomHideUserCard: chatMessage.intercomHideUserCard,
            chatBy: chatMessage.chatBy,
            individualChatPriority: Int(chatMessage.individualChatPriority),
            publicAreaCommon: PublicAreaCommon.create(publicAreaCommon: chatMessage.publicAreaCommon)
        )
    }
    
}

class GiftMessage: NSObject, Codable {
    var common: Common?
    var giftId: UInt64 = 0  //礼物的唯一标识符，用64位无符号整数存储，区分不同的礼物。
    var fanTicketCount: UInt64 = 0   //发送礼物后增加的粉丝票数，可能是用于粉丝排名或奖励系统的计数。
    var repeatCount: UInt64  = 0 //礼物重复发送的次数，表示该礼物在一次操作中被重复了多少次。
    var comboCount: UInt64  = 0 //礼物的连击次数，表示该礼物在一次发送操作中触发的连击数。
    var user: User?  //发送礼物的用户对象，User 类型存储发送者的详细信息。
    var toUser: User?  //接收礼物的用户对象，User 类型存储接收者的详细信息。
    var repeatEnd: Int = 0  //表示礼物发送的结束次数，可能是为了控制连击或多次发送的终止标识。
    var incomeTaskgifts: UInt64 = 0   //用于记录礼物的任务收益，可能是礼物带来的特定收益值。
    var roomFanTicketCount: UInt64  = 0 //直播房间的粉丝票数总数，礼物发送后累计到房间的粉丝票数。
    var selfQueuePriority: UInt64 = 0 //用户自身队列的优先级。这个字段用64位无符号整数表示，可能用于决定发送者自己礼物消息在队列中的优先级。
    var priority: UInt64 = 0 //礼物消息的全局优先级。这个字段用64位无符号整数存储，决定该礼物消息在整个系统中的处理优先级。数值越高，优先级越高，消息可能会更快被处理或显示。
    var gift: GiftStruct?  //礼物的结构体，使用之前定义的 GiftStruct 类型，表示礼物的详细信息。
    var sendType: UInt64 = 0 //发送类型，可能表示不同的发送方式或场景（如普通发送、批量发送等）。
    //        var trayDisplayText:String?   //礼物托盘显示的文本信息，可能是礼物的简短描述或展示文案。
    var bannedDisplayEffects: UInt64 = 0 //禁止显示的特效，表示该礼物的某些特效在当前场景下被禁用。
    var displayForSelfvar: Bool =  false//表示礼物是否在自己发送后也显示给自己，true 表示发送者也能看到礼物的显示效果。
    var interactGiftInfo: String?  //交互礼物信息，表示礼物的交互式信息（如送礼时的互动特效）。
    var diyItemInfo: String?  //自定义物品信息，可能是礼物的个性化或定制信息。
    var totalCount: UInt64 =  0//礼物的总数，表示在一次操作中总共发送的礼物数量。
    var clientGiftSource: Int = 0 //客户端礼物来源，表示礼物从哪个客户端或系统来源发送。
    var sendTime: UInt64 = 0 //发送时间，表示礼物发送的时间戳。
    var effectDisplayTs: UInt64 = 0 //特效显示时间戳，表示礼物的特效何时开始展示。
    var publicAreaCommon: PublicAreaCommon// 公共区域信息，PublicAreaCommon 用于存储一些与消息展示相关的公共区域信息，可能涉及到直播间或互动区域的展示。
    
    init(common: Common? = nil, giftId: UInt64, fanTicketCount: UInt64, repeatCount: UInt64, comboCount: UInt64, user: User? = nil, toUser: User? = nil, repeatEnd: Int, incomeTaskgifts: UInt64, roomFanTicketCount: UInt64, selfQueuePriority: UInt64, priority: UInt64, gift: GiftStruct? = nil, sendType: UInt64, bannedDisplayEffects: UInt64, displayForSelfvar: Bool, interactGiftInfo: String? = nil, diyItemInfo: String? = nil, totalCount: UInt64, clientGiftSource: Int, sendTime: UInt64, effectDisplayTs: UInt64, publicAreaCommon: PublicAreaCommon) {
        self.common = common
        self.giftId = giftId
        self.fanTicketCount = fanTicketCount
        self.repeatCount = repeatCount
        self.comboCount = comboCount
        self.user = user
        self.toUser = toUser
        self.repeatEnd = repeatEnd
        self.incomeTaskgifts = incomeTaskgifts
        self.roomFanTicketCount = roomFanTicketCount
        self.selfQueuePriority = selfQueuePriority
        self.priority = priority
        self.gift = gift
        self.sendType = sendType
        self.bannedDisplayEffects = bannedDisplayEffects
        self.displayForSelfvar = displayForSelfvar
        self.interactGiftInfo = interactGiftInfo
        self.diyItemInfo = diyItemInfo
        self.totalCount = totalCount
        self.clientGiftSource = clientGiftSource
        self.sendTime = sendTime
        self.effectDisplayTs = effectDisplayTs
        self.publicAreaCommon = publicAreaCommon
    }
    
    static  func create(giftMessage: Douyin_GiftMessage)->GiftMessage {
        return GiftMessage(
            common: Common.create(common: giftMessage.common),
            giftId : giftMessage.giftID,
            fanTicketCount : giftMessage.fanTicketCount,
            repeatCount : giftMessage.repeatCount,
            comboCount : giftMessage.comboCount,
            user : User.create(user: giftMessage.user),
            toUser : User.create(user: giftMessage.toUser),
            repeatEnd :Int(giftMessage.repeatEnd),
            incomeTaskgifts : giftMessage.incomeTaskgifts,
            roomFanTicketCount : giftMessage.roomFanTicketCount,
            selfQueuePriority : giftMessage.priority.selfQueuePriority,  // 从 GiftIMPriority 中获取
            priority : giftMessage.priority.priority,  // 从 GiftIMPriority 中获取
            gift : GiftStruct.create(giftStruct: giftMessage.gift),
            sendType : giftMessage.sendType,
            //                    trayDisplayText = giftMessage.trayDisplayText?.text,  // 假设 Text 类型有 text 字段
            bannedDisplayEffects : giftMessage.bannedDisplayEffects,
            displayForSelfvar : giftMessage.displayForSelf,
            interactGiftInfo : giftMessage.interactGiftInfo,
            diyItemInfo : giftMessage.diyItemInfo,
            totalCount : giftMessage.totalCount,
            clientGiftSource : Int(giftMessage.clientGiftSource),
            sendTime : giftMessage.sendTime,
            effectDisplayTs : giftMessage.effectDisplayTs,
            publicAreaCommon : PublicAreaCommon.create(publicAreaCommon: giftMessage.publicAreaCommon)
        )
    }
    
    
    func toString()-> String {
        return "GiftInfo(giftId=$giftId, fanTicketCount=$fanTicketCount, repeatCount=$repeatCount, comboCount=$comboCount, user=$user, toUser=$toUser, repeatEnd=$repeatEnd, incomeTaskgifts=$incomeTaskgifts, roomFanTicketCount=$roomFanTicketCount, selfQueuePriority=$selfQueuePriority, priority=$priority, gift=$gift, sendType=$sendType, bannedDisplayEffects=$bannedDisplayEffects, displayForSelfvar=$displayForSelfvar, interactGiftInfo=$interactGiftInfo, diyItemInfo=$diyItemInfo, totalCount=$totalCount, clientGiftSource=$clientGiftSource, sendTime=$sendTime, effectDisplayTs=$effectDisplayTs)"
    }
}

class GiftStruct: NSObject, Codable {
    var image: String?  // 这个字段表示礼物的图片，可能是礼物的外观或相关的视觉元素。
    var describe: String?  //礼物的描述信息，以字符串形式存储，可能是对礼物的简单说明或展示文本。
    var notify: Bool =  false//布尔类型字段，表示是否需要通知或提醒。true 可能意味着收到该礼物时要通知用户。
    var duration: UInt64 = 0//表示礼物的持续时间，可能是某些特效或礼物展示的时长，以64位无符号整数表示。
    var id: UInt64 = 0//礼物的唯一标识符，使用64位无符号整数存储，用于在系统中区分不同的礼物。
    var forLinkmic: Bool = false//表示礼物是否用于连麦场景，true 可能意味着这个礼物专为连麦互动设计。
    var doodle: Bool = false //布尔字段，表示礼物是否涉及涂鸦功能，true 可能意味着可以进行互动涂鸦。
    var forFansclub: Bool = false//是否为粉丝团专属礼物，true 表示该礼物是粉丝俱乐部专属的。
    var combo: Bool = false//表示是否为连击（combo）礼物。连击礼物通常意味着可以连续多次发送，产生连击效果。
    var type: Int = 0//礼物的类型，用32位无符号整数表示，可能表示不同类型的礼物（如虚拟物品、特效等）。
    var diamondCount: Int = 0 //礼物的价值，表示该礼物消耗的钻石数量。
    var isDisplayedOnPanel: Bool =  false//是否在面板上展示，true 表示该礼物会出现在礼物选择面板中。
    var primaryEffectId: UInt64 = 0 //礼物的主要特效ID，指向一个特效资源，用于展示礼物的主要动画效果。
    var name: String?  //礼物的名称，以字符串形式存储。
    var region: String?  //表示礼物适用的区域或国家，可能用于限制该礼物在哪些地区可用。
    var manual: String?  //礼物的手动说明，可能用于提供关于该礼物的使用说明或背景信息。
    var forCustom: Bool =  false//是否为定制礼物，true 表示该礼物是为某些场景或用户定制的。
    
    init(image: String? = nil, describe: String? = nil, notify: Bool, duration: UInt64, id: UInt64, forLinkmic: Bool, doodle: Bool, forFansclub: Bool, combo: Bool, type: Int, diamondCount: Int, isDisplayedOnPanel: Bool, primaryEffectId: UInt64, name: String? = nil, region: String? = nil, manual: String? = nil, forCustom: Bool) {
        self.image = image
        self.describe = describe
        self.notify = notify
        self.duration = duration
        self.id = id
        self.forLinkmic = forLinkmic
        self.doodle = doodle
        self.forFansclub = forFansclub
        self.combo = combo
        self.type = type
        self.diamondCount = diamondCount
        self.isDisplayedOnPanel = isDisplayedOnPanel
        self.primaryEffectId = primaryEffectId
        self.name = name
        self.region = region
        self.manual = manual
        self.forCustom = forCustom
    }
    
    static   func create(giftStruct: Douyin_GiftStruct)-> GiftStruct {
        let img = giftStruct.image.urlListList.first
        return GiftStruct(
            image: img,
            describe : giftStruct.describe,
            notify : giftStruct.notify,
            duration : giftStruct.duration,
            id : giftStruct.id,
            forLinkmic : giftStruct.forLinkmic,
            doodle : giftStruct.doodle,
            forFansclub : giftStruct.forFansclub,
            combo : giftStruct.combo,
            type : Int(giftStruct.type),
            diamondCount : Int(giftStruct.diamondCount),
            isDisplayedOnPanel : giftStruct.isDisplayedOnPanel,
            primaryEffectId : giftStruct.primaryEffectID,
            name : giftStruct.name,
            region : giftStruct.region,
            manual : giftStruct.manual,
            forCustom : giftStruct.forCustom
        )
    }
    
    
    func toString()-> String {
        return "GiftStruct(image=$image, describe=$describe, notify=$notify, duration=$duration, id=$id, forLinkmic=$forLinkmic, doodle=$doodle, forFansclub=$forFansclub, combo=$combo, type=$type, diamondCount=$diamondCount, isDisplayedOnPanel=$isDisplayedOnPanel, primaryEffectId=$primaryEffectId, name=$name, region=$region, manual=$manual, forCustom=$forCustom)"
    }
}

// 成员消息
class MemberMessage: NSObject, Codable{
    // 通用字段，包含消息的基础信息（例如消息 ID、房间 ID 等）。
    var common: Common
    // 用户对象，表示触发成员消息的用户，存储了用户的详细信息。
    var user: User
    // 当前房间的成员总数，用 64 位无符号整数表示。
    var memberCount: UInt64
    // 操作者，表示执行了某个操作（如设置为管理员）的用户。
    // 布尔类型字段，表示该用户是否被设置为管理员。
    var isSetToAdmin: Bool
    // 布尔类型字段，表示该用户是否是顶级用户（例如特权用户或贡献最高的用户）。
    var isTopUser: Bool
    // 用户的排名分数，通常用于贡献排行榜或类似功能。
    var rankScore: UInt64
    // 顶级用户的编号，可能用于展示用户的排行榜排名。
    var topUserNo: UInt64
    // 用户进入房间的类型，表示用户通过何种方式或状态进入房间（如 VIP 进入、普通用户进入等）。
    var enterType: UInt64
    // 行为类型，描述用户在房间中的某些特定操作或动作。
    var action: UInt64
    // 行为描述，以字符串形式存储，解释了 action 字段的具体含义。
    var actionDescription: String
    // 用户的 ID，通常用于系统中的用户唯一标识。
    var userId: UInt64
    // 弹出字符串，表示用户进入房间时可能显示的文本提示。
    var popStr: String
    // 背景图片，用户的展示背景图片。
    var backgroundImage: Image
    // 第二版本的背景图片，可能用于不同场景或版本的展示。
    var backgroundImageV2: Image
    // 主播展示文本，通常在用户进入房间时显示给主播的文本信息。
    //var anchorDisplayText: String,
    // 用户进入提示类型，表示系统如何提示用户进入房间。
    var userEnterTipType: UInt64
    // 主播进入提示类型，表示主播进入房间时的提示展示方式。
    var anchorEnterTipType: UInt64
    
    var publicAreaCommon: PublicAreaCommon// 公共区域信息，PublicAreaCommon 用于存储一些与消息展示相关的公共区域信息，可能涉及到直播间或互动区域的展示。
    
    init(common: Common, user: User, memberCount: UInt64, isSetToAdmin: Bool, isTopUser: Bool, rankScore: UInt64, topUserNo: UInt64, enterType: UInt64, action: UInt64, actionDescription: String, userId: UInt64, popStr: String, backgroundImage: Image, backgroundImageV2: Image, userEnterTipType: UInt64, anchorEnterTipType: UInt64, publicAreaCommon: PublicAreaCommon) {
        self.common = common
        self.user = user
        self.memberCount = memberCount
        self.isSetToAdmin = isSetToAdmin
        self.isTopUser = isTopUser
        self.rankScore = rankScore
        self.topUserNo = topUserNo
        self.enterType = enterType
        self.action = action
        self.actionDescription = actionDescription
        self.userId = userId
        self.popStr = popStr
        self.backgroundImage = backgroundImage
        self.backgroundImageV2 = backgroundImageV2
        self.userEnterTipType = userEnterTipType
        self.anchorEnterTipType = anchorEnterTipType
        self.publicAreaCommon = publicAreaCommon
    }
    
    static  func create(memberMessage: Douyin_MemberMessage)-> MemberMessage {
        return MemberMessage(
            common: Common.create(common: memberMessage.common),
            user: User.create(user: memberMessage.user),
            memberCount: memberMessage.memberCount,
            isSetToAdmin: memberMessage.isSetToAdmin,
            isTopUser: memberMessage.isTopUser,
            rankScore: memberMessage.rankScore,
            topUserNo: memberMessage.topUserNo,
            enterType: memberMessage.enterType,
            action: memberMessage.action,
            actionDescription: memberMessage.actionDescription,
            userId: memberMessage.userID,
            popStr: memberMessage.popStr,
            backgroundImage: Image.create(image: memberMessage.backgroundImage),
            backgroundImageV2: Image.create(image: memberMessage.backgroundImageV2),
            userEnterTipType: memberMessage.userEnterTipType,
            anchorEnterTipType: memberMessage.anchorEnterTipType,
            publicAreaCommon: PublicAreaCommon.create(publicAreaCommon: memberMessage.publicAreaCommon)
        )
    }
    
}

// 点赞消息
class LikeMessage: NSObject, Codable {
    // 与其他消息类型类似，Common 字段包含了该消息的一些通用属性，如消息 ID、房间 ID、创建时间等。
    var common: Common
    // 点赞次数，表示当前点赞的数量。
    var count: UInt64
    // 点赞总数，表示直播间或某个场景的累计点赞总数。
    var total: UInt64
    // 颜色信息，可能用于控制点赞图标或动画的显示颜色。
    var color: UInt64
    // 用户信息，表示发送点赞的用户的详细信息，包含 User 类型的字段。
    var user: User
    // 点赞图标，存储为字符串，可能是图标的 URL 或资源路径，用于展示点赞的图标。
    var icon: String
    // 双击点赞的详细信息，表示用户通过双击屏幕进行点赞的行为。
    var doubleLikeDetail: DoubleLikeDetail
    // 显示控制信息，用于控制点赞消息或图标在界面上的展示方式。
    var displayControlInfo: DisplayControlInfo
    // 连麦用户的 UID，表示如果该点赞来自连麦用户，存储该用户的唯一标识符。
    var linkmicGuestUid: UInt64
    // 场景，表示当前点赞发生的场景，可能用于区分不同的直播、活动或互动场景。
    var scene: String
    
    init(common: Common, count: UInt64, total: UInt64, color: UInt64, user: User, icon: String, doubleLikeDetail: DoubleLikeDetail, displayControlInfo: DisplayControlInfo, linkmicGuestUid: UInt64, scene: String) {
        self.common = common
        self.count = count
        self.total = total
        self.color = color
        self.user = user
        self.icon = icon
        self.doubleLikeDetail = doubleLikeDetail
        self.displayControlInfo = displayControlInfo
        self.linkmicGuestUid = linkmicGuestUid
        self.scene = scene
    }
    
    static func create(likeMessage: Douyin_LikeMessage)-> LikeMessage {
        return LikeMessage(
            common: Common.create(common: likeMessage.common),
            count: likeMessage.count,
            total: likeMessage.total,
            color: likeMessage.color,
            user: User.create(user: likeMessage.user),
            icon: likeMessage.icon,
            doubleLikeDetail: DoubleLikeDetail.create(doubleLikeDetail: likeMessage.doubleLikeDetail),
            displayControlInfo: DisplayControlInfo.create(displayControlInfo: likeMessage.displayControlInfo),
            linkmicGuestUid: likeMessage.linkmicGuestUid,
            scene: likeMessage.scene
        )
    }
    
}

// 双击点赞的详细信息
class DoubleLikeDetail: NSObject, Codable{
    // 表示是否为双击点赞。true 表示这是一个双击点赞行为，false 表示不是。
    var doubleFlag: Bool
    // 序列号，用于标识该双击点赞行为的顺序或唯一性。可能用于跟踪不同的双击点赞操作。
    var seqId: Int
    // 续订次数，表示用户在某个时段内多次触发双击点赞的次数。可以理解为重复的双击行为数。
    var renewalsNum: Int
    // 触发次数，表示该双击点赞行为已经触发的次数。可能用于统计一个用户在当前互动或直播中双击了多少次。
    var triggersNum: Int
    
    init(doubleFlag: Bool, seqId: Int, renewalsNum: Int, triggersNum: Int) {
        self.doubleFlag = doubleFlag
        self.seqId = seqId
        self.renewalsNum = renewalsNum
        self.triggersNum = triggersNum
    }
    
    static func create(doubleLikeDetail: Douyin_DoubleLikeDetail)->DoubleLikeDetail {
        return DoubleLikeDetail(
            doubleFlag: doubleLikeDetail.doubleFlag,
            seqId: Int(doubleLikeDetail.seqID),
            renewalsNum: Int(doubleLikeDetail.renewalsNum),
            triggersNum: Int(doubleLikeDetail.triggersNum)
        )
    }
    
}

// 显示控制信息
class DisplayControlInfo: NSObject, Codable {
    // 布尔类型字段，用于指示是否显示文本信息。true 表示显示点赞相关的文本信息，false 表示隐藏。
    var showText: Bool
    // 布尔类型字段，用于指示是否显示图标。true 表示显示点赞相关的图标或视觉元素，false 表示隐藏。
    var showIcons: Bool
    
    init(showText: Bool, showIcons: Bool) {
        self.showText = showText
        self.showIcons = showIcons
    }
    
    static  func create(displayControlInfo: Douyin_DisplayControlInfo)-> DisplayControlInfo {
        return DisplayControlInfo(
            showText: displayControlInfo.showText,
            showIcons: displayControlInfo.showIcons
        )
    }
    
}

// 社交消息
class SocialMessage: NSObject, Codable {
    // 通用字段，包含社交消息的一些基础信息，例如消息 ID、房间 ID、创建时间等。
    var common: Common
    // 用户对象，表示触发该社交行为的用户，User 类型存储了用户的详细信息。
    var user: User
    // 分享类型，使用 64 位无符号整数表示，可能用于区分不同的分享类型或场景（例如分享至社交平台、发送到好友等）。
    var shareType: UInt64
    // 行为类型，表示用户在社交场景中的具体操作行为，使用 64 位无符号整数存储。可能包括分享、关注、点赞等社交行为。
    var action: UInt64
    // 分享目标，表示分享的对象或平台，存储为字符串。例如，这可能是分享的社交平台、用户 ID、或是分享的具体内容（如视频、图片等）。
    var shareTarget: String
    // 关注数，表示用户的关注总数，可能在用户进行关注操作后更新这个值。
    var followCount: UInt64
    // 公共区域信息，PublicAreaCommon 用于存储一些与消息展示相关的公共区域信息，可能涉及到直播间或互动区域的展示。
    var publicAreaCommon: PublicAreaCommon
    
    init(common: Common,user: User, shareType: UInt64, action: UInt64, shareTarget: String, followCount: UInt64, publicAreaCommon: PublicAreaCommon) {
        self.common = common
        self.user = user
        self.shareType = shareType
        self.action = action
        self.shareTarget = shareTarget
        self.followCount = followCount
        self.publicAreaCommon = publicAreaCommon
    }
    
    static func create(socialMessage: Douyin_SocialMessage)-> SocialMessage {
        return SocialMessage(
            common: Common.create(common: socialMessage.common),
            user: User.create(user: socialMessage.user),
            shareType: socialMessage.shareType,
            action: socialMessage.action,
            shareTarget: socialMessage.shareTarget,
            followCount: socialMessage.followCount,
            publicAreaCommon: PublicAreaCommon.create(publicAreaCommon: socialMessage.publicAreaCommon)
        )
    }
    
}

// 粉丝团消息
class FansclubMessage: NSObject, Codable{
    // 通用信息，包含一些基础信息，如消息ID、房间ID等。
    var commonInfo: Common
    
    // 升级是1，加入是2。
    var type: Int
    
    // 消息内容，表示粉丝俱乐部的相关信息。
    var content: String
    
    // 用户对象，表示与该消息相关的用户信息。
    var user: User
    
    init(commonInfo: Common, type: Int, content: String, user: User) {
        self.commonInfo = commonInfo
        self.type = type
        self.content = content
        self.user = user
    }
    
    static func create(fansclubMessage: Douyin_FansclubMessage)-> FansclubMessage {
        return FansclubMessage(
            commonInfo: Common.create(common: fansclubMessage.commonInfo),
            type: Int(fansclubMessage.type),
            content: fansclubMessage.content,
            user: User.create(user: fansclubMessage.user)
        )
    }
    
}

class EmojiChatMessage: NSObject, Codable {
    // 包含消息的通用信息，如消息ID、房间ID、创建时间等。
    var common: Common
    
    // 发送表情的用户信息，存储在User类型中，包含发送者的详细信息。
    var user: User
    
    // 表情的唯一标识符，表示具体使用的表情。
    var emojiId: UInt64
    
    // 表情的默认文本内容，当无法显示自定义内容时使用。
    var defaultContent: String
    
    // 表情的背景图片，以Image类型表示，可能用于装饰表情消息的背景。
    var backgroundImage: Image
    
    // 表示消息是否来自对讲机功能，true表示消息来源于此。
    var fromIntercom: Bool
    
    // 当消息来自对讲机时，是否隐藏用户卡片信息，true表示隐藏。
    var intercomHideUserCard: Bool
    
    init(common: Common, user: User, emojiId: UInt64, defaultContent: String, backgroundImage: Image, fromIntercom: Bool, intercomHideUserCard: Bool) {
        self.common = common
        self.user = user
        self.emojiId = emojiId
        self.defaultContent = defaultContent
        self.backgroundImage = backgroundImage
        self.fromIntercom = fromIntercom
        self.intercomHideUserCard = intercomHideUserCard
    }
    
    static  func create(emojiChatMessage: Douyin_EmojiChatMessage)-> EmojiChatMessage {
        return EmojiChatMessage(
            common: Common.create(common: emojiChatMessage.common),
            user: User.create(user: emojiChatMessage.user),
            emojiId: UInt64(emojiChatMessage.emojiID),
            defaultContent: emojiChatMessage.defaultContent,
            backgroundImage: Image.create(image: emojiChatMessage.backgroundImage),
            fromIntercom: emojiChatMessage.fromIntercom,
            intercomHideUserCard: emojiChatMessage.intercomHideUserCard
        )
    }
    
}

class RoomUserSeqMessage: NSObject, Codable {
    // 包含消息的通用信息，如消息ID、房间ID、创建时间等。
    var common: Common
    
    // 房间中用户的排名列表，RoomUserSeqMessageContributor类型的数组，表示在房间中的用户排名信息。
    var ranksList: [RoomUserSeqMessageContributor]
    
    // 总计的用户数量，表示房间中的用户总数。
    var total: UInt64
    
    // 受欢迎程度的字符串表示，可能是一个用于显示人气的文案。
    var popStr: String
    
    // 座位列表，RoomUserSeqMessageContributor类型的数组，表示当前房间中有特殊席位的用户。
    var seatsList: [RoomUserSeqMessageContributor]
    
    // 房间的人气值，通常用于显示房间当前的热度。
    var popularity: UInt64
    
    // 房间中在线的总用户数，以64位整数表示。
    var totalUser: UInt64
    
    // 用户总数的字符串表示，用于展示在界面上。
    var totalUserStr: String
    
    // 总体数量的字符串表示，用于显示总体用户数量等数据。
    var totalStr: String
    
    // 为主播显示的在线用户数字符串，可能用于提供更简洁的在线用户信息。
    var onlineUserForAnchor: String
    
    // 为主播显示的总PV（页面浏览量）字符串，可能用于统计和显示直播的页面浏览量。
    var totalPvForAnchor: String
    
    // 右上角统计信息的字符串表示，用于显示在界面的右上角。
    var upRightStatsStr: String
    
    // 完整的右上角统计信息字符串，可能提供比upRightStatsStr更多的详细信息。
    var upRightStatsStrComplete: String
    
    init(common: Common, ranksList: [RoomUserSeqMessageContributor], total: UInt64, popStr: String, seatsList: [RoomUserSeqMessageContributor], popularity: UInt64, totalUser: UInt64, totalUserStr: String, totalStr: String, onlineUserForAnchor: String, totalPvForAnchor: String, upRightStatsStr: String, upRightStatsStrComplete: String) {
        self.common = common
        self.ranksList = ranksList
        self.total = total
        self.popStr = popStr
        self.seatsList = seatsList
        self.popularity = popularity
        self.totalUser = totalUser
        self.totalUserStr = totalUserStr
        self.totalStr = totalStr
        self.onlineUserForAnchor = onlineUserForAnchor
        self.totalPvForAnchor = totalPvForAnchor
        self.upRightStatsStr = upRightStatsStr
        self.upRightStatsStrComplete = upRightStatsStrComplete
    }
    
    static  func create(roomUserSeqMessage: Douyin_RoomUserSeqMessage)-> RoomUserSeqMessage {
        
        var ranksList: [RoomUserSeqMessageContributor] = []
        roomUserSeqMessage.ranksList.forEach { room in
            ranksList.append( RoomUserSeqMessageContributor.create(roomUserSeqMessageContributor: room))
        }
        
        var seatsList: [RoomUserSeqMessageContributor] = []
        roomUserSeqMessage.seatsList.forEach { room in
            seatsList.append( RoomUserSeqMessageContributor.create(roomUserSeqMessageContributor: room))
        }
        
        return RoomUserSeqMessage(
            common: Common.create(common: roomUserSeqMessage.common),
            ranksList: ranksList,
            total: UInt64(roomUserSeqMessage.total),
            popStr: roomUserSeqMessage.popStr,
            seatsList: seatsList,
            popularity: UInt64(roomUserSeqMessage.popularity),
            totalUser: UInt64(roomUserSeqMessage.totalUser),
            totalUserStr: roomUserSeqMessage.totalUserStr,
            totalStr: roomUserSeqMessage.totalStr,
            onlineUserForAnchor: roomUserSeqMessage.onlineUserForAnchor,
            totalPvForAnchor: roomUserSeqMessage.totalPvForAnchor,
            upRightStatsStr: roomUserSeqMessage.upRightStatsStr,
            upRightStatsStrComplete: roomUserSeqMessage.upRightStatsStrComplete
        )
    }
}

class RoomUserSeqMessageContributor: NSObject, Codable {
    // 用户的贡献分数，用64位无符号整数表示，通常用于表示用户对房间的贡献度。
    var score: UInt64
    
    // 该用户的详细信息，使用User结构体类型，包含用户的具体资料。
    var user: User
    
    // 用户的排名，表示用户在房间中的排名位置。
    var rank: UInt64
    
    // 分数的增量，表示用户贡献分数的变化。
    var delta: UInt64
    
    // 是否隐藏该用户，布尔类型，true表示该用户的排名或信息在界面上被隐藏。
    var isHidden: Bool
    
    // 分数描述，提供关于分数的文本描述，用于展示用户的贡献或排名信息。
    var scoreDescription: String
    
    // 用户的精确分数，用字符串形式表示，通常用于精确展示或计算用户的具体分数。
    var exactlyScore: String
    
    init(score: UInt64, user: User, rank: UInt64, delta: UInt64, isHidden: Bool, scoreDescription: String, exactlyScore: String) {
        self.score = score
        self.user = user
        self.rank = rank
        self.delta = delta
        self.isHidden = isHidden
        self.scoreDescription = scoreDescription
        self.exactlyScore = exactlyScore
    }
    
    static  func create(roomUserSeqMessageContributor: Douyin_RoomUserSeqMessageContributor)-> RoomUserSeqMessageContributor {
        return RoomUserSeqMessageContributor(
            score: roomUserSeqMessageContributor.score,
            user: User.create(user: roomUserSeqMessageContributor.user),
            rank: roomUserSeqMessageContributor.rank,
            delta: roomUserSeqMessageContributor.delta,
            isHidden: roomUserSeqMessageContributor.isHidden,
            scoreDescription: roomUserSeqMessageContributor.scoreDescription,
            exactlyScore: roomUserSeqMessageContributor.exactlyScore
        )
    }
    
}

