//
//  APIConfigrationManager.swift
//  liveplusexplore
//
//  Created by 郭炜 on 2024/12/23.
//

import UIKit

// MARK: - API配置模型
struct APIConfiguration: Codable {
    let version: String
    let apis: [String: APIEndpointConfig]
}

struct APIEndpointConfig: Codable {
    let url: String
    let method: String
    let params: APIParamsConfig
}

struct APIParamsConfig: Codable {
    let required: [String]
    let optional: [String]
    let `default`: [String: String]
}

// MARK: - API配置管理
class APIConfigurationManager {
    static let shared = APIConfigurationManager()
    
    private var configuration: APIConfiguration?
    
    private init() {
        loadConfiguration()
    }
    
    func loadConfiguration() {
        
        if let jsonString = JSSingleton.shared.apiConfigs,
           let data = jsonString.data(using: .utf8),
           let config = try? JSONDecoder().decode(APIConfiguration.self, from: data) {
            configuration = config
            print("成功加载加密配置===服务端")
            return
        }
        
        // 从加密文件加载配置
        if let jsonString = EncryptedConfigManager.shared.loadConfig(from: "api_config.json")
          {
            
            guard let data = jsonString.data(using: .utf8) else { return }
            
           do {
                let config = try JSONDecoder().decode(APIConfiguration.self, from: data)
                configuration = config
               return print("成功加载加密配置")
            
            } catch {
                print("error:\(error)")
            }
        }
        print("加载加密配置失败")
    }
    
    func getEndpoint(_ identifier: String) -> APIEndpointConfig? {
        return configuration?.apis[identifier]
    }
}
