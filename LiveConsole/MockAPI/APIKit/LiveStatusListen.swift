//
//  LiveStatusListen.swift
//  MockAnchor
//
//  Created by 郭炜 on 2024/11/22.
//

import UIKit

var roomID: String?
var cursor: String?
var last_rtt: String?
var internal_ext: String?
var nickName: String?

enum NotificationKey: String {
    
    case livingStatusChanged
    
    case LoginExpiration
    
    static func post(_ key: NotificationKey, object: Any? = nil) {
        NotificationCenter.default.post(name: NSNotification.Name(rawValue: key.rawValue), object: object)
    }
    
    static func add(_ key: NotificationKey, using block: @escaping (Notification) -> Void) {
        NotificationCenter.default.addObserver(forName: NSNotification.Name(rawValue: key.rawValue), object: nil, queue: OperationQueue.main, using: block)
    }
}

class LiveStatusListen {
    static let shared = LiveStatusListen()
    
    private var timer: Timer?
    
    // 开始轮询
    func startListening(interval: TimeInterval = 5.0) {
        stopListening() // 确保之前的timer被清除
        
        timer = Timer.scheduledTimer(withTimeInterval: interval, repeats: true) { [weak self] _ in
            guard let self = self else { return }
            if #available(iOS 13.0, *) {
                Task {
                    do {
                        let status: LiveStatusResponse = try await RequestManager.shared.requestApi(style: .liveStatus)
                        print("轮询获取直播间状态：\(status)")
                        if status.statusCode ==  20011 ||  status.statusCode == 20003 {
                            LCLog.d("用户未登录")
                            NotificationKey.post(.LoginExpiration, object: false)
                        }
                        roomID = status.data?.roomId
                        if let roomId = status.data?.roomId, !roomId.isEmpty {
                            /// 已开播
                            NotificationKey.post(.livingStatusChanged, object: true)
                        } else {
                            self.resetDatas()
                            /// 未开播
                            NotificationKey.post(.livingStatusChanged, object: false)
                        }
                    } catch {
                        self.resetDatas()
                        /// 未开播
                        NotificationKey.post(.livingStatusChanged, object: false)
                        print("轮询直播状态失败：\(error.localizedDescription)")
                    }
                }
            } else {
                // Fallback on earlier versions
            }
        }
        timer?.fire() // 立即执行一次
    }
    
    func resetDatas() {
        roomID = nil
        cursor = nil
        last_rtt = nil
        internal_ext = nil
    }
    
    // 停止轮询
    func stopListening() {
        timer?.invalidate()
        timer = nil
    }
}
