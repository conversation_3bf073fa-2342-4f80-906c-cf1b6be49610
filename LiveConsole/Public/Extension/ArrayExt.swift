////
////  ArrayExt.swift
////  LivePlus
////
////  Created by 郭炜 on 2021/6/8.
////
//
//import Foundation
//
//public extension Array {
//
//    /// SwifterSwift: Insert an element at the beginning of array.
//    ///
//    ///        [2, 3, 4, 5].prepend(1) -> [1, 2, 3, 4, 5]
//    ///        ["e", "l", "l", "o"].prepend("h") -> ["h", "e", "l", "l", "o"]
//    ///
//    /// - Parameter newElement: element to insert.
//    mutating func prepend(_ newElement: Element) {
//        insert(newElement, at: 0)
//    }
//
//    /// SwifterSwift: Safely swap values at given index positions.
//    ///
//    ///        [1, 2, 3, 4, 5].safeSwap(from: 3, to: 0) -> [4, 2, 3, 1, 5]
//    ///        ["h", "e", "l", "l", "o"].safeSwap(from: 1, to: 0) -> ["e", "h", "l", "l", "o"]
//    ///
//    /// - Parameters:
//    ///   - index: index of first element.
//    ///   - otherIndex: index of other element.
//    mutating func safeSwap(from index: Index, to otherIndex: Index) {
//        guard index != otherIndex else { return }
//        guard startIndex..<endIndex ~= index else { return }
//        guard startIndex..<endIndex ~= otherIndex else { return }
//        swapAt(index, otherIndex)
//    }
//
//    /// SwifterSwift: Sort an array like another array based on a key path. If the other array doesn't contain a certain value, it will be sorted last.
//    ///
//    ///        [MyStruct(x: 3), MyStruct(x: 1), MyStruct(x: 2)].sorted(like: [1, 2, 3], keyPath: \.x)
//    ///            -> [MyStruct(x: 1), MyStruct(x: 2), MyStruct(x: 3)]
//    ///
//    /// - Parameters:
//    ///   - otherArray: array containing elements in the desired order.
//    ///   - keyPath: keyPath indiciating the property that the array should be sorted by
//    /// - Returns: sorted array.
//    func sorted<T: Hashable>(like otherArray: [T], keyPath: KeyPath<Element, T>) -> [Element] {
//        let dict = otherArray.enumerated().reduce(into: [:]) { $0[$1.element] = $1.offset }
//        return sorted {
//            guard let thisIndex = dict[$0[keyPath: keyPath]] else { return false }
//            guard let otherIndex = dict[$1[keyPath: keyPath]] else { return true }
//            return thisIndex < otherIndex
//        }
//    }
//}
