//
//  LPOperators.swift
//  LivePlus
//
//  Created by 郭炜 on 2021/10/21.
//

import Foundation

/**
 edg...
 let int: Int? = 3
 print("\(int ??? "2")")
 
 replace: print("\(String(describing: int))")
 */
infix operator ???: NilCoalescingPrecedence

public func ???<T>(optional: T?,
                   defaultValue: @autoclosure () -> String) -> String {
    switch optional {
    case let value?: return String(describing: value)
    case nil: return defaultValue()
    }
}

/// 重载运算法模板
// 两个参数的传统函数进行柯里化
/**
 edg...
 func add(_ v1: Int, _ v2: Int) -> Int
 let curyingAdd = ~add
 curyingAdd(v1)(v2)
 */
prefix func ~<A, B, C>(_ fuc: @escaping (A, B) -> C) -> (B) -> (A) -> C {
    { b in { a in  fuc(a, b) } }
}

// 三个参数的传统函数进行柯里化
prefix func ~<A, B, C, D>(_ fuc: @escaping (A, B, C) -> D) -> (C) -> (B) -> (A) -> D {
    { c in { b in { a in fuc(a, b, c) } } }
}
