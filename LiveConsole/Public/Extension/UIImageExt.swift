//
//  UIImageExt.swift
//  LivePlus
//
//  Created by iclick on 2021/1/28.
//

import UIKit
import SwiftUI

extension UIImage {
    
    ///图片压缩到指定大小， 如 2M,maxLength=2
    func compressTolength(_ maxLength: Int) -> Data? {
        guard let imgData = jpegData(compressionQuality: 1) else {
            return nil
        }
        let maxL = maxLength * 1024 * 1024
        guard imgData.count > maxL  else {
            return imgData
        }
        var compress:CGFloat = 0.9
        let maxCompress:CGFloat = 0.1
        if var result = jpegData(compressionQuality: compress){
            while result.count > maxL, compress > maxCompress {
                compress -= 0.1
                if let tmpData = jpegData(compressionQuality: compress){
                    result = tmpData
                }
            }
            return result
        }
        return imgData
    }
    
    func compressImageQuality(toByte maxLength: Int) -> Data? {
        var compression: CGFloat = 1
        guard var data = self.jpegData(compressionQuality: compression),
              data.count > maxLength else { return self.jpegData(compressionQuality: 1) }
        print("Before compressing quality, image size =", data.count / 1024, "KB")

        var max: CGFloat = 1
        var min: CGFloat = 0
        for _ in 0..<6 {
            compression = (max + min) / 2
            data = self.jpegData(compressionQuality: compression)!
            print("Compression =", compression)
            print("In compressing quality loop, image size =", data.count / 1024, "KB")
            if CGFloat(data.count) < CGFloat(maxLength) * 0.9 {
                min = compression
            } else if data.count > maxLength {
                max = compression
            } else {
                break
            }
        }
        print("After compressing quality, image size =", data.count / 1024, "KB")
        return data
    }
    
    /// 裁剪图片
    func cropToSize(rect: CGRect) -> UIImage {
        var newRect = rect
        newRect.origin.x *= self.scale
        newRect.origin.y *= self.scale
        newRect.size.width *= self.scale
        newRect.size.height *= self.scale
        let cgimage = self.cgImage?.cropping(to: newRect)
        let resultImage = UIImage(cgImage: cgimage!, scale: self.scale, orientation: self.imageOrientation)
        return resultImage
    }
    
    //将图片裁剪成指定比例（多余部分自动删除）
    func crop(ratio: CGFloat) -> UIImage? {
        //计算最终尺寸
        var newSize:CGSize!
        if size.width/size.height > ratio {
            newSize = CGSize(width: size.height * ratio, height: size.height)
        }else{
            newSize = CGSize(width: size.width, height: size.width / ratio)
        }
        
        ////图片绘制区域
        var rect = CGRect.zero
        rect.size.width  = size.width
        rect.size.height = size.height
        rect.origin.x    = (newSize.width - size.width ) / 2.0
        rect.origin.y    = (newSize.height - size.height ) / 2.0
        
        //绘制并获取最终图片
        UIGraphicsBeginImageContext(newSize)
        draw(in: rect)
        let scaledImage = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()
        
        return scaledImage
    }
        
    /// 叠加图片
    /// - Parameters:
    ///   - botImage: 下面的图片
    ///   - topImage: 上面的图片
    ///   - topRect: 上面图片的区域
    /// - Returns: 叠加后的图片
    static func superimpose(botImage: UIImage, topImage: UIImage) -> UIImage? {
        let botImageRef = botImage.cgImage
//        let topImageRef = topImage.cgImage
        let botW = botImageRef?.width ?? 0
        let botH = botImageRef?.height ?? 0
        let point = 126 * UIScreen.main.scale
        let wh = 122 * UIScreen.main.scale
        // 以底部图片大小为画布创建上下文
        UIGraphicsBeginImageContext(CGSize(width: botW, height: botH))
        let botRect = CGRect(x: 0, y: 0, width: botW, height: botH)
        botImage.draw(in: botRect)
        let topRect = CGRect(x: point, y: point, width: wh, height: wh)
        topImage.draw(in: topRect)
        let result = UIGraphicsGetImageFromCurrentImageContext()
        
        return result
    }
    
    static func superimposeBack(botImage: UIImage, topImage: UIImage, botSize: CGSize, topFrame: CGRect) -> UIImage? {
        let botImageRef = botImage.cgImage
        let botW = botSize.width
        let botH = botSize.height
        // 以底部图片大小为画布创建上下文
        UIGraphicsBeginImageContext(CGSize(width: botW, height: botH))
        let botRect = CGRect(x: 0, y: 0, width: botW, height: botH)
        botImage.draw(in: botRect)
        topImage.draw(in: topFrame)
        let result = UIGraphicsGetImageFromCurrentImageContext()
        
        return result
    }
}
