//
//  String.swift
//  LivePlus
//
//  Created by iclick on 2020/12/24.
//
import Foundation

import CommonCrypto


extension String {
    func sha1() -> String {
        guard let data = self.data(using: String.Encoding.utf8) else { return "" }
        var digest = [UInt8](repeating: 0, count: Int(CC_SHA1_DIGEST_LENGTH))
        data.withUnsafeBytes {
            _ = CC_SHA1($0.baseAddress, CC_LONG(data.count), &digest)
        }
        let hexBytes = digest.map { String(format: "%02hhx", $0) }
        return hexBytes.joined()
    }
}

extension String {
    
    // 是否是空字符串
    var isBlank: Bool {
        let trimmedStr = self.trimmingCharacters(in: .whitespacesAndNewlines)
        return trimmedStr.isEmpty
    }
    
    ///移除后缀 eg: 123.zip -> 123
    func removePathExtension() -> String {
        return (self as NSString).deletingPathExtension
    }

    ///判断是否是纯数字
    func isPureNumberCharacters() -> Bool {
        let string = self
        let regex = "[0-9]*"
        let pred = NSPredicate(format: "SELF MATCHES %@", regex)
        return pred.evaluate(with: string)
    }
    ///去除空格、换行等
    func removeSpaceAndNewline() -> String {
        let aStr = self
        var tmpStr = aStr.replacingOccurrences(of: " ", with: "")
        tmpStr = tmpStr.replacingOccurrences(of: "\r", with: "")
        tmpStr = tmpStr.replacingOccurrences(of: "\n", with: "")
        tmpStr = tmpStr.replacingOccurrences(of: "+", with: "")
        return tmpStr
    }
    
    func isChinese() -> Bool {
        for char in self {
            if "\u{4E00}" <= char && char <= "\u{9FA5}" {
                return true
            }
        }
        return false
    }
//    ///手机号号码正则
//    func validateMobile() -> Bool {
//        let phoneRegex: String = "^(\\+\\d+)?1[34578]\\d{9}$"
//        let phoneTest = NSPredicate(format: "SELF MATCHES %@", phoneRegex)
//        return phoneTest.evaluate(with: self)
//    }
//    ///秒数转00:00:02
//    static func int2HMS(second:Int) -> String {
//        let min:Int = second / 60
//        if second <= 60{
//            return String(format: "00:00:%02d",second)
//        }else if second <= 60*60 {
//            return String(format: "00:%02d:%02d",min,second-60*min)
//        }else {
//            let hour:Int = min / 60
//            return String(format: "%02d:%02d:%02d",hour,min-hour*60,second-min*60)
//        }
//    }
    
}

extension String {
    //根据开始位置和长度截取字符串
    func lpSubString(start: Int, length: Int = -1) -> String {
        var tempLength = length
        if tempLength == -1 { tempLength = self.count - start }
        let startOffset = self.index(startIndex, offsetBy: start)
        let endOffset = self.index(startOffset, offsetBy: tempLength)
        return String(self[startOffset ..< endOffset])
    }
}


/**
 CGFloat 类型的随机数
 */
public extension CGFloat {
    static func randomCGFloatNumber(lower: CGFloat = 0,upper: CGFloat = 1) -> CGFloat {
        return CGFloat(Float(arc4random()) / Float(UInt32.max)) * (upper - lower) + lower
    }
}

extension String {
    func textAutoWidth(height: CGFloat, font: UIFont) -> CGFloat {
        let string = self as NSString
        let origin = NSStringDrawingOptions.usesLineFragmentOrigin
        let lead = NSStringDrawingOptions.usesFontLeading
        let rect = string.boundingRect(with: CGSize(width: 0, height: height), options: [origin, lead], attributes: [NSAttributedString.Key.font: font], context: nil)
        return rect.width
    }
}

extension String {
    
    // des 加解密
    // operation 加密 解密
    // key 
    func decrypt_des(key: String) -> String? {
        
        let operation = CCOperation(kCCDecrypt)
       
        if let keyData = Data(base64Encoded: key) {
            
            let cryptData: NSData?  = NSData(base64Encoded: self) // Data(base64Encoded: self, options: .ignoreUnknownCharacters)
            
            if cryptData == nil {
                return nil
            }
           
            let algoritm: CCAlgorithm = CCAlgorithm(kCCAlgorithmDES)
            let option: CCOptions = CCOptions(kCCOptionECBMode + kCCOptionPKCS7Padding)
            
            let keyBytes = [UInt8](keyData)
            let keyLength = kCCKeySizeDES
            
//            let dataIn = [UInt8](cryptData!)
            let dataInlength = cryptData?.length ??  0
            
            let dataOutAvailable = Int(dataInlength + kCCBlockSizeDES)
           
            var num_bytes_encrypted: size_t = 0
            
            let  dataOut = UnsafeMutablePointer<NSData>.allocate(capacity: dataOutAvailable)
            let cryptStatus = CCCrypt(operation, algoritm, option, keyBytes, keyLength, keyBytes, cryptData?.bytes, dataInlength, dataOut, dataOutAvailable, &num_bytes_encrypted)
            
            var data: NSData?
            if CCStatus(cryptStatus) == CCStatus(kCCSuccess) {
                data = NSData(bytes: dataOut, length: num_bytes_encrypted)
            }
            
            dataOut.deallocate()
            
            if data == nil {
                return nil
            }
            
            let stringResult = NSString(data: data as! Data, encoding:String.Encoding.utf8.rawValue) as String?
//            print("解密之后数据",stringResult)
 
            return stringResult
        }
        
        return nil

    }
    
}

extension Data {
    var html2AttributedString: NSAttributedString {
        do {
            return try NSAttributedString(data: self, options: [.documentType: NSAttributedString.DocumentType.html, .characterEncoding: String.Encoding.utf8.rawValue], documentAttributes: nil)
        } catch {
            print("error:", error)
            return NSAttributedString(string: "")
        }
    }
    var html2String: String {
        return html2AttributedString.string
    }
}

extension String {
    var html2AttributedString: NSAttributedString? {
        return Data(utf8).html2AttributedString
    }
    var html2String: String {
        return html2AttributedString?.string ?? ""
    }
    func html2Unicode() -> String {
        let appendString = self.replacingOccurrences(of: "</p>", with: "&#10;")
        return appendString.replacingOccurrences(of: "<[^>]+>", with: "", options: .regularExpression, range: nil)
    }
}

extension String {
    /**
     计算文字高度，允许换行计算

     @param fontSize 文字大小
     @param widht 文字宽度
     @return 返回文字的高度
     */
    public func sizeLineFeedWithFont(font: UIFont, width: CGFloat) -> CGFloat {
        let constraintRect = CGSize(width: width, height: .greatestFiniteMagnitude)
        let boundingBox = self.boundingRect(with: constraintRect,
                                              options: [.usesLineFragmentOrigin, .usesFontLeading],
                                              attributes: [NSAttributedString.Key.font: font], context: nil)
        return ceil(boundingBox.height) + 6
    }
}


