//
//  NSURL+Hook.m
//  LivePlus
//
//  Created by 郭炜 on 2022/1/21.
//

#import "NSURL+Hook.h"

@implementation NSURL (Hook)

//+ (void)load {
//    static dispatch_once_t onceToken;
//    dispatch_once(&onceToken, ^{
//        Method methodOld = class_getClassMethod([self class], @selector(URLWithString:));
//        Method methodNew = class_getClassMethod([self class], @selector(LPURLWithString:));
//        method_exchangeImplementations(methodOld, methodNew);
//    });
//}
//
//+ (instancetype)LPURLWithString:(NSString *)URLString {
//    NSString *endString = [URLString stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]];
//    return [self LPURLWithString:endString];
//}

@end
