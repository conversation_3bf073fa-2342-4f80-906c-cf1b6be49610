//
//  UIResponderExtensions.swift
//  LivePlus
//
//  Created by 郭炜 on 2022/2/10.
//

import UIKit

struct ResponderRoutFaster {
    
    // 行业版本的操作
    enum TradeViewOperation: String {
        /// 点击去链接
        case go_connect
        ///  绑定抖音
        case go_binddy
        /// 点击更多
        case more
        
        /// 解绑
        case go_unbind
    }
}



extension UIResponder {
    @objc func routerEvent(with name: String, userInfo: Dictionary<String, Any>) {
        self.next?.routerEvent(with: name, userInfo: userInfo)
    }
}
