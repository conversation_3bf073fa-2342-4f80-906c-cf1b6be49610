//
//  AVAssetExt.swift
//  LivePlus
//
//  Created by iclick on 2021/5/14.
//

import Photos



extension AVAsset {
    
    /// 是否HDR视频
    var isHDRVideo: Bool {
        
        if #available(iOS 14.0, *) {
            let hdrTrack = self.tracks(withMediaCharacteristic: .containsHDRVideo)
            return !hdrTrack.isEmpty
        }
        
        for track in self.tracks(withMediaType: .video) {
            guard  let cmFormatDescription = track.formatDescriptions.map({ $0 as! CMFormatDescription }).first else {
                continue
            }
            guard let transferFunction = CMFormatDescriptionGetExtension(cmFormatDescription,
                                                                         extensionKey: kCVImageBufferTransferFunctionKey) else {
                continue
            }
            if #available(iOS 11.0, *) {
                return [
                    kCVImageBufferTransferFunction_ITU_R_2020,
                    kCVImageBufferTransferFunction_ITU_R_2100_HLG,
                    kCVImageBufferTransferFunction_SMPTE_ST_2084_PQ
                ].contains(transferFunction as! CFString)
            }
        }
        
        return false
    }
    
    /// 是否4K视频
    var is4KVideo :Bool {
        let videoTracks = self.tracks(withMediaType: .video)
        if videoTracks.count > 0 {
            let track = videoTracks[0]
            let size = track.naturalSize
            if size.width > 3000 || size.height > 3000 {
                return true
            }
        }
        return false
    }
    
    var is2KVideo: Bool {
        let videoTracks = self.tracks(withMediaType: .video)
        if videoTracks.count > 0 {
            let track = videoTracks[0]
            let size = track.naturalSize
            if size.width > 1000 || size.height > 1000 {
                return true
            }
        }
        return false
    }
    
}

extension PHAsset {
    
    /// 判断文件大小是否合法
    func isLegalSize(_ size:Int) -> Bool {
        let fileSize = self.fileSize
        guard fileSize != 0 else {
            return false
        }
        return fileSize < size
    }
    
    /// 获取当前文件大小
    var fileSize : Int64 {
        guard let resource = PHAssetResource.assetResources(for: self).first else {
            return 0
        }
        guard let fileSize = resource.value(forKey: "fileSize") else {
            return 0
        }
        let result = fileSize as! Int64
        return result
    }
    
    /// 判断是否9:16的宽高比
    var is9_16 :Bool {
        
        let curW = CGFloat(self.pixelWidth)
        let curH = CGFloat(self.pixelHeight)
        guard curW < curH else {
            LCLog.d("素材宽 > 高，不可用")
            return false
        }
        let gap : CGFloat = 0.05
        let value = curW/curH * 16.0
        if abs(value-9.0) <= gap {
            return true
        }
        return false
    }
    
    /// 判断是否9:16的宽高比
    var is16_9 :Bool {
        
        let curW = CGFloat(self.pixelWidth)
        let curH = CGFloat(self.pixelHeight)
        guard curW > curH else {
            LCLog.d("素材宽 > 高，不可用")
            return false
        }
        let gap : CGFloat = 0.05
        let value = curW/curH * 9.0
        if abs(value-16.0) <= gap {
            return true
        }
        return false
    }

}
