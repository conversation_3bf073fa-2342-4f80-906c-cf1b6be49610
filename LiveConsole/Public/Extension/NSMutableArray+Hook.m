//
//  NSMutableArray+Hook.m
//  LivePlus
//
//  Created by 郭炜 on 2022/1/17.
//

#import "NSMutableArray+Hook.h"
#import <objc/runtime.h>

@implementation NSMutableArray (Hook)

+ (void)load {
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        // 类簇：NSString、NSArray、NSDictionary，真实类型是其他类型
        Class cls = NSClassFromString(@"__NSArrayM");
        Method methodOld = class_getInstanceMethod(cls, @selector(insertObject:atIndex:));
        Method methodNew = class_getInstanceMethod(cls, @selector(lp_insertObject:atIndex:));
        method_exchangeImplementations(methodOld, methodNew);
    });
}

- (void)lp_insertObject:(id)anObject atIndex:(NSUInteger)index {
    NSAssert(anObject != NULL, @"Invalid NSMutableArray Object");
    [self lp_insertObject:anObject atIndex:index];
}

@end
