//
//  UIViewExt.swift
//  LivePlus
//
//  Created by iclick on 2021/1/15.
//

import UIKit

extension UIView {
    
    /// 占位图
    static func placeHolderImg() -> UIImage {
        return UIImage(color: UIColor.brown.alpha(value: 0.2), size: CGSize(width: 10, height: 10))!
    }
}

// MARK: - 添加虚线边框
fileprivate let kDotLineName = "my_dot_line"
extension UIView {
    /// 添加虚线边框
    public func drawDottedBorderLine(_ color:UIColor = .white, hasShadow:Bool = false) {
        let curFrame = self.realFrame()
        let shapeLayer = CAShapeLayer()
        shapeLayer.name = kDotLineName
        shapeLayer.bounds = CGRect(x: 0, y: 0, width: curFrame.width, height: curFrame.height)
        shapeLayer.position = CGPoint(x: bounds.midX, y: bounds.midY)
        shapeLayer.path = UIBezierPath(roundedRect: shapeLayer.bounds, cornerRadius: 0).cgPath
        shapeLayer.lineWidth = 1.0/UIScreen.main.scale
        shapeLayer.lineDashPattern = [4, 4]
        shapeLayer.fillColor = UIColor.clear.cgColor
        shapeLayer.strokeColor = color.cgColor
        if hasShadow {
            shapeLayer.setLayerShadow(.gray, offset: CGSize(width: 2, height: 2), radius: 2)
        }
        layer.insertSublayer(shapeLayer, at: 0)
    }
    
    /// 移除虚线边框
    public func removeDottedBorderLine() {
        guard let layers = layer.sublayers else { return }
        for aLayer in layers {
            if aLayer.name == kDotLineName {
                aLayer.removeFromSuperlayer()
            }
        }
    }

    ///view尺寸改变时对应刷新边框
    public func refreshDottedLine() {
        if let borderLayer = self.layer.sublayers?.first, borderLayer.isKind(of: CAShapeLayer.self), borderLayer.name == kDotLineName {
            let border = borderLayer as! CAShapeLayer
            let path = UIBezierPath(roundedRect: self.bounds, cornerRadius: 0)
            border.path = path.cgPath
            border.frame = self.bounds
        }
    }
}

// MARK: - 添加边框
extension UIView {

    //画线
    private func drawBorder(rect: CGRect, color: UIColor) {
        let line = UIBezierPath(rect: rect)
        let lineShape = CAShapeLayer()
        lineShape.path = line.cgPath
        lineShape.fillColor = color.cgColor
        self.layer.addSublayer(lineShape)
    }

    //设置右边框
    public func rightBorder(width: CGFloat, borderColor: UIColor) {
        let rect = CGRect(x: 0, y: self.frame.size.width - width, width: width, height: self.frame.size.height)
        drawBorder(rect: rect, color: borderColor)
    }
    //设置左边框
    public func leftBorder(width: CGFloat, borderColor: UIColor) {
        let rect = CGRect(x: 0, y: 0, width: width, height: self.frame.size.height)
        drawBorder(rect: rect, color: borderColor)
    }
    //设置上边框
    public func topBorder(width: CGFloat, borderColor: UIColor) {
        let rect = CGRect(x: 0, y: 0, width: self.frame.size.width, height: width)
        drawBorder(rect: rect, color: borderColor)
    }

    //设置底边框
    public func bottomBorder(width: CGFloat, borderColor: UIColor) {
        let rect = CGRect(x: 0, y: self.frame.size.height-width, width: self.frame.size.width, height: width)
        drawBorder(rect: rect, color: borderColor)
    }
    
    // 切圆角
    public func roundedRect( byRoundingCorners corners: UIRectCorner, cornerRadii: CGSize) {
        let path = UIBezierPath(roundedRect: bounds,
                                byRoundingCorners: corners,
                                cornerRadii: cornerRadii)
        let masklayer = CAShapeLayer()
        masklayer.frame = bounds
        masklayer.path = path.cgPath
        layer.mask = masklayer
    }
    
    // 切圆角+边框
    public func rounderBoard(corners: UIRectCorner, cornerRadius: CGFloat, boardWidth: CGFloat, boardColor: UIColor) {
        if let layer = self.layer.sublayers?.first as? CAShapeLayer {
            layer.removeFromSuperlayer()
            self.layer.mask = nil
        }
        let maskLayer = CAShapeLayer()
        maskLayer.frame = CGRect(x: 0, y: 0, width: self.width, height: self.height)
        let borderLayer = CAShapeLayer()
        borderLayer.frame = CGRect(x: 0, y: 0, width: self.width, height: self.height)
        borderLayer.lineWidth = boardWidth
        borderLayer.strokeColor = boardColor.cgColor
        borderLayer.fillColor = UIColor.clear.cgColor
        let bezierPath = UIBezierPath(roundedRect: self.bounds, byRoundingCorners: corners, cornerRadii: CGSize(width: cornerRadius, height: cornerRadius))
        maskLayer.path = bezierPath.cgPath
        borderLayer.path = bezierPath.cgPath
        
        self.layer.insertSublayer(borderLayer, at: 0)
        self.layer.mask = maskLayer
    }
}

//extension UIView{
//    /// This is a function to get subViews of a particular type from view recursively. It would look recursively in all subviews and return back the subviews of the type T
//    /// https://stackoverflow.com/a/45297466/5321670
//    func allSubViewsOf<T : UIView>(type : T.Type) -> [T]{
//        var all = [T]()
//        func getSubview(view: UIView) {
//            if let aView = view as? T{
//                all.append(aView)
//            }
//            guard view.subviews.count>0 else { return }
//            view.subviews.forEach{ getSubview(view: $0) }
//        }
//        getSubview(view: self)
//        return all
//    }
//}

// MARK: - view添加菊花
extension UIView {
    struct ActivityIndicator {
        static var isEnabled = false
        static var style = _style
        static var view = _view
        
        static var _style: UIActivityIndicatorView.Style {
            if #available(iOS 13.0, *) {
                return .medium
            } else {
                return .white
            }
        }
        
        static var _view: UIActivityIndicatorView {
            if #available(iOS 13.0, *) {
                return UIActivityIndicatorView(style: .medium)
            } else {
                return UIActivityIndicatorView(style: .white)
            }
        }
    }
    
    // MARK: 配置菊花样式以及是否显示
    public var isActivityEnabled: Bool {
        get {
            guard let value = objc_getAssociatedObject(self, &ActivityIndicator.isEnabled) as? Bool else {
                return false
            }
            return value
        }
        set(newValue) {
            objc_setAssociatedObject(self, &ActivityIndicator.isEnabled, newValue, objc_AssociationPolicy.OBJC_ASSOCIATION_RETAIN_NONATOMIC)
        }
    }
    
    public var activityStyle: UIActivityIndicatorView.Style {
        get {
            guard let value = objc_getAssociatedObject(self, &ActivityIndicator.style) as? UIActivityIndicatorView.Style else {
                if #available(iOS 13.0, *) {
                    return .medium
                }else {
                    return .white
                }
            }
            return value
        }
        set(newValue) {
            objc_setAssociatedObject(self, &ActivityIndicator.style, newValue, objc_AssociationPolicy.OBJC_ASSOCIATION_RETAIN_NONATOMIC)
        }
    }
    
    public var activityIndicator: UIActivityIndicatorView {
        get {
            guard let value = objc_getAssociatedObject(self, &ActivityIndicator.view) as? UIActivityIndicatorView else {
                if #available(iOS 13.0, *) {
                    return UIActivityIndicatorView(style: .medium)
                } else {
                    return UIActivityIndicatorView(style: .white)
                }
            }
            return value
        }
        set(newValue) {
            let activityView = newValue
            activityView.hidesWhenStopped = true
            objc_setAssociatedObject(self, &ActivityIndicator.view, activityView, objc_AssociationPolicy.OBJC_ASSOCIATION_RETAIN_NONATOMIC)
        }
    }
        
    // MARK: - 调用方法
    // 在当前view上显示菊花
    public func showActivityIndicator() {
        guard isActivityEnabled else { return }
        var isActivityIndicatorFound = false
        DispatchQueue.main.async {
            self.activityIndicator = UIActivityIndicatorView(style: self.activityStyle)
            self.activityIndicator.translatesAutoresizingMaskIntoConstraints = false
            self.activityIndicator.color = .white
            if self.subviews.isEmpty {
                isActivityIndicatorFound = false
                self.addSubview(self.activityIndicator)
                
            } else {
                for view in self.subviews {
                    if !view.isKind(of: UIActivityIndicatorView.self) {
                        isActivityIndicatorFound = false
                        self.addSubview(self.activityIndicator)
                        break
                    } else {
                        isActivityIndicatorFound = true
                    }
                }
            }
            if !isActivityIndicatorFound {
                NSLayoutConstraint.activate([
                    self.activityIndicator.igCenterXAnchor.constraint(equalTo: self.igCenterXAnchor),
                    self.activityIndicator.igCenterYAnchor.constraint(equalTo: self.igCenterYAnchor)
                    ])
            }
            self.activityIndicator.startAnimating()
        }
    }
    
    // 隐藏当前view的菊花
    public func hideActivityIndicator() {
        guard isActivityEnabled else { return }
        DispatchQueue.main.async {
            self.subviews.forEach({ (view) in
                if let av = view as? UIActivityIndicatorView { av.stopAnimating() }
            })
        }
    }
}

extension UIView {
    var igCenterXAnchor: NSLayoutXAxisAnchor {
        if #available(iOS 11.0, *) { return self.safeAreaLayoutGuide.centerXAnchor }
        return self.centerXAnchor
    }
    
    var igCenterYAnchor: NSLayoutYAxisAnchor {
        if #available(iOS 11.0, *) { return self.safeAreaLayoutGuide.centerYAnchor }
        return self.centerYAnchor
    }
}

/// 渐变色的设置方向
public enum GradientBackgroundDirection: Int {
    case horizontal = 0 //水平的,从左到右
    case vertical //垂直的，从上到下
    case topLeftToBottomRight // 从左上角到右下角
}

// MARK: - 设置渐变色
extension UIView {
    /// 多颜色渐变，设置2～5个颜色值
    func setGradientBackgroud(withColors colors: [UIColor], direction: GradientBackgroundDirection) {
        guard colors.count > 1 else { return }
        var middleXs = [CGFloat]()
        let key = CGFloat(1.0/CGFloat(colors.count-1))
        switch colors.count {
        case 3:// 0->0.5->1
            middleXs = [key] // 0.5
            
        case 4:// 0->0.333->0.66->1
            middleXs = [key, key*2]
            
        case 5:// 0->0.25->0.5->0.75->1
            middleXs = [key, key*2, key*3]
            
        default:
            break
        }
        
        var subPoint = [CGPoint(x: 0, y: 0)] // 起点
        if middleXs.isEmpty {//只有两个颜色，加上终点
            switch direction {
            case .horizontal:
                    subPoint.append(CGPoint(x: 1, y: 0))
                
            case .vertical:
                subPoint.append(CGPoint(x: 0, y: 1))
                
            case .topLeftToBottomRight:
                subPoint.append(CGPoint(x: 1, y: 1))
                
            }
        } else {
            var endPoint = CGPoint(x: 1, y: 1) // 终点
            //加上中间点
            middleXs.forEach({
                var middlePoint = CGPoint.zero
                switch direction {
                case .horizontal:
                    middlePoint = CGPoint(x: $0, y: 0)
                    endPoint = CGPoint(x: 1, y: 0)
                    
                case .vertical:
                    middlePoint = CGPoint(x: 0, y: $0)
                    endPoint = CGPoint(x: 0, y: 1)
                    
                case .topLeftToBottomRight:
                    middlePoint = CGPoint(x: $0, y: $0)
                    endPoint = CGPoint(x: 1, y: 1)
                    
                }
                subPoint.append(middlePoint)
            })
            subPoint.append(endPoint)
        }
        var cgColors = [CGColor]()
        colors.forEach({ cgColors.append($0.cgColor) })
        
        setGradient(with: subPoint, colors: cgColors)
    }

    /// 颜色渐变：起始位置、颜色值
    private func setGradient(with points: [CGPoint], colors:[CGColor]) {
        let gradient = CAGradientLayer()
        gradient.frame = self.bounds
        gradient.startPoint = points.first!
        gradient.endPoint = points.last!
        gradient.colors = colors
        self.layer.insertSublayer(gradient, at: 0)
    }
    
    /// 移除渐变色背景
    func removeGradient() {
        guard layer.sublayers != nil else {
            return
        }
        for subLayer in layer.sublayers! {
            if subLayer.isKind(of: CAGradientLayer.self){
                subLayer.removeFromSuperlayer()
            }
        }
    }
    
}

// MARK: - 获取真实frame
extension UIView {
    
    ///获取view当前真实的frame
    public func realFrame() -> CGRect {
        let transform = self.transform
        self.transform = .identity
        let realFrame = self.frame
        self.transform = transform
        return realFrame
    }

}

// MARK: - 添加闪烁状态
extension UIView {
    public func flash() {
        let animation = CABasicAnimation(keyPath: "opacity")
        animation.fromValue = 1.0
        animation.toValue = 0.5
        animation.autoreverses = true
        animation.duration = 0.15
        animation.repeatCount = 1
        animation.isRemovedOnCompletion = true
        animation.fillMode = .forwards
        animation.timingFunction = CAMediaTimingFunction(name: .easeIn)
        self.layer.add(animation, forKey: "\(self.hashValue)")
    }
    
    public func rotation() {
        let animation = CABasicAnimation(keyPath: "transform.rotation.z")
        animation.fromValue = 0.0
        animation.toValue = Double.pi * 2
        animation.autoreverses = false
        animation.duration = 1
        animation.repeatCount = 1
        animation.isRemovedOnCompletion = true
        animation.fillMode = .forwards
        animation.timingFunction = CAMediaTimingFunction(name: .easeIn)
        self.layer.add(animation, forKey: "\(self.hashValue)")
    }
}

extension UIImageView {
    func loadImageInDisk(with url: URL?) {
        guard let url = url else { return }
        if let cacheImage = SDImageCache.shared.imageFromDiskCache(forKey: url.absoluteString) {
            self.image = cacheImage
            return
        }
        /// 查本地缓存： SDWebImage的磁盘缓存在app手动开启的时候不生效 没查到原因:) 感觉是library文件夹变化了
        /// 所以这里缓存一份到document中
        let localKey = LCTools.getMaterialSourceDownloadFolder() + "/" + url.absoluteString
        guard let md5LocalKey = (localKey as NSString).md5() else {
            self.sd_setImage(with: url, placeholderImage: UIView.placeHolderImg(), options: [.progressiveLoad], completed: nil)
            return
        }
        
        let localPath = LCTools.getMaterialSourceDownloadFolder() + "/" + md5LocalKey
        if let localImage = UIImage(contentsOfFile: localPath) {
            self.image = localImage
            return
        }
        
        self.sd_setImage(with: url, placeholderImage: UIView.placeHolderImg(), options: [.progressiveLoad]) { image, _, _, _ in
            guard let image = image else { return }
            LCTools.storeImage(image, isGif: false, to: localPath)
        }
    }
    
    static func loadDiskImages(urls: [String]) {
        for url in urls {
            let localKey = LCTools.getMaterialSourceDownloadFolder() + "/" + url
            guard let md5LocalKey = (localKey as NSString).md5() else {
                continue
            }
            let localPath = LCTools.getMaterialSourceDownloadFolder() + "/" + md5LocalKey
            
            if FileManager.default.fileExists(atPath: localPath) { continue }
            SDWebImageDownloader.shared.downloadImage(with: URL(string: url)) { image, _, _, _ in
                guard let image = image else { return }
                LCTools.storeImage(image, isGif: false, to: localPath)
            }
        }
    }
}

extension UIViewController {
    func showPop(view: UIView, dismissOnBackgroundTouch: Bool = true) {
        let ffpop = FFPopup(contentView: view, showType: .bounceIn, dismissType: .fadeOut, maskType: .dimmed, dismissOnBackgroundTouch: dismissOnBackgroundTouch, dismissOnContentTouch: false)
        let layout = FFPopupLayout(horizontal: .center, vertical: .center)
        ffpop.show(layout: layout)
    }
    
    
    func dismissPop(view: UIView, animated: Bool = true) {
        FFPopup.dismiss(contentView: view, animated: animated)
    }
    
    @discardableResult
    func showPopBot(view: UIView, maskType: FFPopup.MaskType = .clear, dismissOnBackgroundTouch: Bool = true, dismissBlock: (() -> Void)? = nil) -> FFPopup {
        let ffpop = FFPopup(contentView: view, showType: .slideInFromBottom, dismissType: .slideOutToBottom, maskType: maskType, dismissOnBackgroundTouch: dismissOnBackgroundTouch, dismissOnContentTouch: false)
        if let dismissBlock = dismissBlock {
            ffpop.didFinishDismissingBlock = dismissBlock
        }
        let layout = FFPopupLayout(horizontal: .center, vertical: .bottom)
        ffpop.show(layout: layout)
        return ffpop
    }
    
    func showPopBotRight(view: UIView, dismissOnBackgroundTouch: Bool = true, dismissBlock: (() -> Void)? = nil) {
        let ffpop = FFPopup(contentView: view, showType: .slideInFromBottom, dismissType: .slideOutToBottom, maskType: .clear, dismissOnBackgroundTouch: dismissOnBackgroundTouch, dismissOnContentTouch: false)
        if let dismissBlock = dismissBlock {
            ffpop.didFinishDismissingBlock = dismissBlock
        }
        let layout = FFPopupLayout(horizontal: .right, vertical: .bottom)
        ffpop.show(layout: layout)
    }
    
    func dismissAll() {
        FFPopup.dismissAll()
    }
    
}
