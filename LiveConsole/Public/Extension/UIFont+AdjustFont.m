//
//  UIFont+AdjustFont.m
//  LivePlus
//
//  Created by 郭炜 on 2021/12/29.
//

#import "UIFont+AdjustFont.h"
#import <objc/runtime.h>

#define MyUIScreen 375
#define MinScreenWidth MIN([UIScreen mainScreen].bounds.size.width, [UIScreen mainScreen].bounds.size.height)

@implementation UIFont (AdjustFont)

+ (void)load {
    Method newMethodSystem = class_getClassMethod([self class], @selector(adjustFont:));
    Method methodSystem = class_getClassMethod([self class], @selector(systemFontOfSize:));
    method_exchangeImplementations(newMethodSystem, methodSystem);
    
    Method newMethodNamed = class_getClassMethod([self class], @selector(adjustFontWithName:size:));
    Method methodNamed = class_getClassMethod([self class], @selector(fontWithName:size:));
    method_exchangeImplementations(newMethodNamed, methodNamed);
    
    Method newMeghodBold = class_getClassMethod([self class], @selector(boldSystemFontOfSize:));
    Method methodBold = class_getClassMethod([self class], @selector(adjustBoldOfSize:));
    method_exchangeImplementations(newMeghodBold, methodBold);
}


/// 系统font
+ (UIFont *)adjustFont:(CGFloat)fontSize {
    NSString *deviceType = [UIDevice currentDevice].model;
    if ( [deviceType isEqualToString:@"iPad"]) {
        return [UIFont adjustFont:fontSize];
    }
    UIFont *newFont = nil;
    newFont = [UIFont adjustFont:fontSize * MinScreenWidth / MyUIScreen];
    return newFont;
}

/// namedfont
+ (UIFont *)adjustFontWithName:(NSString *)name size:(CGFloat)size {
    NSString *deviceType = [UIDevice currentDevice].model;
    if ( [deviceType isEqualToString:@"iPad"]) {
        return [UIFont adjustFontWithName:name size:size];
    }
    UIFont *newFont = nil;
    newFont = [UIFont adjustFontWithName:name size:size * MinScreenWidth / MyUIScreen];
    return newFont;
}

/// 系统bold font
+ (UIFont *)adjustBoldOfSize:(CGFloat)size {
    NSString *deviceType = [UIDevice currentDevice].model;
    if ( [deviceType isEqualToString:@"iPad"]) {
        return [UIFont adjustBoldOfSize:size];
    }
    UIFont *newFont = nil;
    newFont = [UIFont adjustBoldOfSize:size * MinScreenWidth / MyUIScreen];
    return newFont;
}


@end
