//
//  LPSocketHelper.h
//  同屏demo
//
//  Created by admin on 2020/12/4.
//  Copyright © 2020 admin. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
#import <CoreImage/CoreImage.h>


#define screenW [UIScreen mainScreen].bounds.size.width
#define screenH [UIScreen mainScreen].bounds.size.height

NS_ASSUME_NONNULL_BEGIN

@interface LPSocketHelper : NSObject


+ (NSString *)getWifiName;


+ (NSString *)getIPAddresses;


/**
 * 判断字符串是否为IP地址
 * param iPAddress IP地址字符串
 * return BOOL 是返回YES，否返回NO
 */
+ (BOOL)isIPAddress:(NSString *)iPAddress;
    
/**
 *  生成二维码图片
 *
 *  @param QRString  二维码内容
 *  @param sizeWidth 图片size（正方形）
 *  @param color     填充色
 *
 *  @return  二维码图片
 */
+(nullable UIImage *)createQRimageString:(nullable NSString *)QRString sizeWidth:(CGFloat)sizeWidth fillColor:(UIColor *)color;

/**
 *  读取图片中二维码信息
 *
 *  @param image 图片
 *
 *  @return 二维码内容
 */
+(NSString *)readQRCodeFromImage:(UIImage *)image;


+ (long)getCurTimeSp;


+ (BOOL)isSameDay:(long)iTime1 Time2:(long)iTime2;

//+(LPRemoteModel *)getMessageModel:(LPRemoteActionType)action;

+ (NSDictionary *)dicFromObject:(NSObject *)object;


+ (UInt64)getUserId;


///以375*812作为基准的宽度
+ (CGFloat)DIN_WIDTH:(CGFloat )len;

///以375*812作为基准的高度
+ (CGFloat)DIN_HEIGHT:(CGFloat)len;

+ (void)setupAudionSession;

+ (CGPoint)getControlPointx0:(CGFloat)x0 andy0:(CGFloat)y0 x1:(CGFloat)x1 andy1:(CGFloat)y1 x2:(CGFloat)x2 andy2:(CGFloat)y2;

@end

NS_ASSUME_NONNULL_END
