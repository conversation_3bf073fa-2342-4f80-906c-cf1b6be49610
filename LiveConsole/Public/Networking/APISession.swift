//
//  APISession.swift
//  LivePlus
//
//  Created by iclick on 2020/12/23.
//

import Alamofire
import RxSwift
import YYKit


enum RequestHostType {
    /// 普通api请求
    case nomal
    /// 版本更新api请求
    case version
    // 文件下发
     case docs
}

/// 网络请求的管理类
class APISession {
    
    static let SecretKey = "2NlZdqN3TfH6scZZEHqj8nisGc2xSDvR"
    
//    http://iappsrestdev.optimix.cn/api/v1/home/<USER>
    //普通API请求域名 https://dev-iappsrest.quickleading.com/api/v1/vip/ckzs/function/3
    static let baseURLTest = "https://dev-klzkapi.quickleading.com/" //测试库
    static let baseURLOnline = "https://klzkapi.quickleading.com/" //线上库
    static let adapter = MyReqAdapter()
    static let uploadAdapter = MyUploadReqAdapter()
     
    // 加密的版本更新接口 以后就用这个
//    http://liveapidev.optimix.cn/api/v1/config/version
    
    ///版本更新的api请求域名 -- 测试库 4.9.5版本之后只给banner用
    static let version_baseURLTest = "https://dev-iappsrest.quickleading.com/"
    ///版本更新的api请求域名 -- 线上库 4.9.5版本之后只给banner用
    static let version_baseURLOnline = "https://iappsrest.quickleading.com/"
    static let versionAdapter = VersionReqAdapter()
    static let docsAdapter = DocsReqAdapter()
    
    ///微信域名
    static let baseWeChatApi = "https://api.weixin.qq.com"
    static let WeChatAdapter = WeChatReqAdapter()
    
    ///微信token
    static let wxTokenAdapter = TokenAdapter()
    
    static let yyCache = YYCache(name: "com.apisession")
    
    
    static let allSignatureUrls = [APIURL.POST_LOGIN,
                                   APIURL.GET_user_info,
                                   APIURL.GET_Purchasing_list,
//                                   APIURL.GET_Privilege_list,
                                   APIURL.GET_SMS_SEND,
                                   APIURL.GET_version_info,
                                   APIURL.GET_LIMIT,
                                   APIURL.Get_Subscribe_Verify,
                                   APIURL.GET_Qr_Scan,
                                   APIURL.GET_Qr_Info,
                                   APIURL.POST_Qr_Confirm,
                                   APIURL.GET_Qr_Checkout,
                                   APIURL.GET_Douyin_Gift,
                                   APIURL.GET_Audio_SPKID,
                                   APIURL.Post_Audio_Combine_Preset,
                                   APIURL.GET_Clone_Info,
                                   APIURL.Post_Audio_Combine_Clone,
                                   APIURL.POST_Clone_SpkId,
                                   APIURL.POST_Clone_Train,
                                   APIURL.POST_Train_Status,
                                   APIURL.GET_Clone_Texts,
                                   APIURL.POST_Clone_asyncPreTtsSynthesis,
                                   APIURL.POST_Clone_asyncCloneTtsSynthesis,
                                   APIURL.GET_Clone_Status,
                                   APIURL.GET_Cloneaudio_Statistics,
                                   APIURL.GET_LOG_OFF,
                                   APIURL.GET_products_audio_list,
                                   APIURL.POST_point_deduct_unit,
                                   APIURL.POST_PHONE_REBIND,
                                   APIURL.GET_Points_limit,
                                   APIURL.GET_Member_Order_limit
    ]
    
    // 指定的无规则排序标准
    static let  kSortArr = ["q", "z", "b", "t", "c", "v", "w", "p", " x", "s", "f", "g", "h", "u", "o", "r", "d", "m", "k", "n", "l", "a", "e", "j", "y", "i", "_"]
    
    ///get
    static func get<T: Codable>(_ url: String, hostType: RequestHostType = .nomal, parameters: Parameters = [:], cache: Bool = false, onlyCache: Bool = false,tmptoken: String? = nil, progressHandle: ((Progress) -> Void)? = nil) -> Observable<Status<T>> {
        var useCache = cache
        if parameters.keys.contains("page") {
            let page = parameters["page"] as! Int
            if page == 1 {
                useCache = true
            }
        } else if parameters.keys.contains("lastId") {
            let lastId = parameters["lastId"] as! String
            if lastId == "" || lastId == "0"{
                useCache = true
            }
        }
        
        var timestamp = LCTools.milliStamp()
        var sign = ""
        var encryption = false
        
        if self.whetherSecretSign(url: url), let sign1 = self.buildSecretSignForUrl(parameters: parameters, timestamp: "\(timestamp)") {
            sign = sign1
            encryption = true
        } else {
            timestamp = ""
        }
        
        return APISession.request(url, hostType: hostType, parameters: parameters, "GET", cache: useCache, timestamp: timestamp, sign: sign, encryption: encryption, onlyCache: onlyCache, tmptoken: tmptoken,progressHandle: progressHandle)
    }
    
    ///post
    static func post<T: Codable>(_ url: String, hostType: RequestHostType = .nomal, parameters: Parameters = [:], customParameters: String? = nil) -> Observable<Status<T>> {
        
        var timestamp = LCTools.milliStamp()
        var sign = ""
        var encryption = false
        if self.whetherSecretSign(url: url), let sign1 = self.buildSecretSignForUrl(parameters: parameters, timestamp: "\(timestamp)") {
            sign = sign1
            encryption = true

        } else {
            timestamp = ""
        }
        
        return APISession.request(url, hostType: hostType, parameters: parameters, "POST", timestamp: timestamp, sign: sign, encryption: encryption, customParameters: customParameters)
    }
    
    ///put
    static func put<T: Codable>(_ url: String, hostType: RequestHostType = .nomal, parameters: Parameters = [:]) -> Observable<Status<T>> {
        
        return APISession.request(url, hostType: hostType, parameters: parameters, "PUT")
    }
    
    ///patch
    static func patch<T: Codable>(_ url: String, hostType: RequestHostType = .nomal, parameters: Parameters = [:]) -> Observable<Status<T>> {
        
        return APISession.request(url, hostType: hostType, parameters: parameters, "PATCH")
    }
    
    /// delete
    static func delete<T: Codable>(_ url: String, hostType: RequestHostType = .nomal, parameters: Parameters = [:]) -> Observable<Status<T>> {

        return APISession.request(url, hostType: hostType, parameters: parameters, "DELETE")
    }
    
    public static var needStop: Bool = false
  
    
    /// upload
    static func upload<T: Codable>(_ url: String, fileUrl: URL, roomId: String, roomJson: String, progressHandle: @escaping (Progress) -> Void) -> Observable<T> {
        return Observable.create({ observer -> Disposable in
            let utilityQueue = DispatchQueue.global(qos: .utility)
            let mng = Alamofire.SessionManager.default
            mng.upload(multipartFormData: { multipartFormData in
                multipartFormData.append(fileUrl, withName: "file", fileName: roomId + ".zip", mimeType: "zip")
                multipartFormData.append(roomId.data(using: .utf8)!, withName: "roomId")
            }, to: url) { (result) in
                switch result {
                case .success(request: let upload, streamingFromDisk: _, streamFileURL: _):
                    print("request: \(upload)")
                    upload.uploadProgress(closure: progressHandle)
                    upload.responseString(queue: utilityQueue) { resp in
                        let res = resp.result
                        LCLog.d("api=\(resp.request?.url?.absoluteString ?? "")")
                        switch res {
                        case .success(let str):
                            print(str)
                            if let dict = try? JSONSerialization.jsonObject(with: str.data(using: .utf8)!, options: []) as? [String: Any] {
                                if dict != nil && dict["code"] != nil {
                                    //do something with json
                                    //....
                                }
                            }
                            if let m: T = JsonTool.string2Model(str) {
                                DispatchQueue.main.async {
                                    observer.onNext(m)
                                    observer.onCompleted()
                                }
                            } else {
                                /* 常见错误：
                                 1、类型： eg：Int写成string，反之亦然
                                 2、需要写成数组类型的，忘记"[]"
                                 */
                                LCLog.d("检查模型与数据格式匹配,或是请求方式错了")
                            }
                        case .failure(let er):
                            DispatchQueue.main.async {
                                observer.onError(er)
                                if !er.localizedDescription.isEmpty {
                                    HUD.showInfo("网络访问异常，请重试")
                                }
                            }
                            
                        }
                    }
                case .failure(let error):
                    print("error: \(error)")
                }
            }
            
            return Disposables.create()
        })
    }
    
    static func download(_ urlPath: String,
                         filePath: URL,
                         parameters: [String: Any],
                         progressHandle: @escaping (Progress) -> Void,
                         resultHandle: @escaping (String?) -> Void) {
        let utilityQueue = DispatchQueue.global(qos: .utility)
        let mng = Alamofire.SessionManager.default
        var urlRequest = URLRequest(url: URL(string: urlPath)!)
        urlRequest.httpMethod = "GET"
        let destination: DownloadRequest.DownloadFileDestination = { _, _ in
            return (filePath, [.removePreviousFile, .createIntermediateDirectories])
        }
        let getRequest = try! URLEncoding.queryString.encode(urlRequest, with: parameters)
        mng.download(getRequest, to: destination).downloadProgress(closure: progressHandle).response(queue: utilityQueue) { resp in
            if resp.error == nil, let filePath = resp.destinationURL?.path {
                resultHandle(filePath)
                return
            }
            resultHandle(nil)
        }
    }
    
    static func jsonToData(jsonDic: Dictionary<String, Any>) -> Data? {
        if (!JSONSerialization.isValidJSONObject(jsonDic)) {
            LCLog.d("is not a valid json object")
            return nil
        }
        //利用自带的json库转换成Data
        //如果设置options为JSONSerialization.WritingOptions.prettyPrinted，则打印格式更好阅读
        let data = try? JSONSerialization.data(withJSONObject: jsonDic, options: [])
        //Data转换成String打印输出
//        let str = String(data:data!, encoding: String.Encoding.utf8)
//        print("Json Str:\(str!)")
        return data
    }
    
    // onlyCache 表示如果有缓存只取缓存，不再更新服务器的数据
    // customParameters 是自定义参数 json格式
    static func request<T: Codable>(_ apiUrl: String, hostType: RequestHostType, parameters: Parameters, _ method: String, cache: Bool = false, timestamp: String = "", sign: String = "", encryption: Bool = false, onlyCache: Bool = false, customParameters: String? = nil , tmptoken: String? = nil,progressHandle: ((Progress) -> Void)? = nil) -> Observable<Status<T>> {
        return Observable.create({  observer -> Disposable in
            let utilityQueue = DispatchQueue.global(qos: .utility)
            var urlRequest = URLRequest(url: URL(string: apiUrl)!)
            urlRequest.httpMethod = method
            
            if let user = UserInfo.currentUser(), let token = user.token {
                urlRequest.setValue( token, forHTTPHeaderField: "token")
            }else {
                if let tmptoken = tmptoken {
                    urlRequest.setValue( tmptoken, forHTTPHeaderField: "token")
                }
            }
            
            if !sign.isEmpty {
                // 接口验证签名信息
                urlRequest.setValue(sign, forHTTPHeaderField: "sign")
            }

            if !timestamp.isEmpty {
                urlRequest.setValue(timestamp, forHTTPHeaderField: "ts")
            }
            
            var finalRequest: URLRequest?
            var getRequest: URLRequest?
            if method == "GET" {
                
                // 把参数加密之后 放到请求url
//                var newParams = parameters
                
//                if let jsonStr = parameters.toJsonString(), let enStr = buildSecretBodyData(sign, jsonStr: jsonStr, encrypt: true) {
//                    newParams = ["params": enStr]
//                }
                
                getRequest = try! URLEncoding.queryString.encode(urlRequest, with: parameters)
                finalRequest = getRequest
                
            } else if method == "PUT" {
                let putRequest = try! JSONEncoding.default.encode(urlRequest, with: parameters)
                finalRequest = putRequest
                
                let data = self.jsonToData(jsonDic: parameters)
                urlRequest.httpBody = data
            } else {
                if let customParameters = customParameters {
                    let postRequest = try! JSONEncoding.default.encode(urlRequest, with: parameters)
                    finalRequest = postRequest
                    if finalRequest?.value(forHTTPHeaderField: "Content-Type") == nil {
                        finalRequest?.setValue("application/json", forHTTPHeaderField: "Content-Type")
                    }
                    finalRequest?.httpBody = customParameters.data(using: .utf8)
                } else {
                    let postRequest = try! JSONEncoding.default.encode(urlRequest, with: parameters)
                    finalRequest = postRequest
                }
                
                
                // 把参数加密之后 放到请求体中
//                if let jsonStr = parameters.toJsonString(), let enStr = buildSecretBodyData(sign, jsonStr: jsonStr, encrypt: true), let newParams = ["params": enStr].toJsonString(), let enData = newParams.data(using: .utf8){
//                    // 加密 httpBody
//                    finalRequest?.httpBody = enData
//                }
                
            }
            var cachekey = ""
            var cacheJson: String! = ""
            //取出上次缓存的接口数据
            if let getRequest = getRequest { cachekey = getRequest.description }
            if let phone = UserInfo.currentUser()?.phone, let level = UserInfo.currentUser()?.vipLevelId {
                cachekey = cachekey + "\(phone)" + "\(level.rawValue)"
            }
            if cache {
                if let jsonData = yyCache?.diskCache.object(forKey: cachekey) {
                    cacheJson = String(data: jsonData as! Data, encoding: .utf8)
                    if validationCacheDataTime(jsonStr: cacheJson) {
                        if let res: T = JsonTool.string2Model(cacheJson!) {
                           
                            DispatchQueue.main.async {
                                observer.onNext(.success(res))
                            }
                            // 不需要刷新服务器数据了 直接返回
                            if onlyCache {
                                print("使用缓存数据，不在请求：\(res)")
                                DispatchQueue.main.async {
                                    observer.onCompleted()
                                    return
                                }
                                
                                return Disposables.create()
                            }
                        } else {
                            observer.onNext(.failure(BaseError("数据格式出错!")))
                            LCLog.d("cache检查模型与数据格式匹配,或是请求方式错了")
                        }
                        
                    }
                }
            }
            //
            
            let mng = Alamofire.SessionManager.default
            
            switch hostType {
           
            case .nomal: mng.adapter = adapter
            case .version: mng.adapter = versionAdapter
            case .docs: mng.adapter = docsAdapter
            }
            
            let request = mng.request(finalRequest!).responseString(queue: utilityQueue, encoding:.utf8) { resp in
                
                let res = resp.result
                LCLog.d("api=\(resp.request?.url?.absoluteString ?? "")")
                
                var sSign: String?
                
                if let response = resp.response {
                    response.allHeaderFields.filter { $0.0 as? String  == "sSign" }
                        .forEach { sSign = $0.1 as? String ?? nil}
                }
                
                switch res {
                case .success(var str):
                    print(str)
                    if let dict = try? JSONSerialization.jsonObject(with: str.data(using: .utf8)!, options: []) as? [String: Any] {
                        
                        // sign 验证
                        if let code = dict["code"] as? Int, let msg = dict["msg"] as? String {
                            //.... 单点登录
                            if code == 1002 {
                                DispatchQueue.main.async {
                                    HUD.showFail(msg, autoClear: true, autoClearTime: 2)?.isUserInteractionEnabled = false
                                    UserInfo.logout()
                                    NotificationCenter.default.post(name: LCKey.noti_logout, object: nil)
                                }
                            }
                            
                            // 签名和加密
                            if code == 0, encryption == true, let sSign = sSign {
                                // 校验sign
                                if  validationSignData(jsonStr: str, data: dict, sSign: sSign) {
                                    LCLog.d("签名校验通过 ")
                                    if let ts = dict["ts"] as? Int64, let dataStr = dict["data"] as? String {
                                        // 有签名 开始des 解密
                                        if let decoStr = buildSecretBodyData(String(ts), jsonStr: dataStr, encrypt: false) {
                                            // 解密成功了
                                            LCLog.d("解密成功 ")
                                            if decoStr.count == 0 {
                                                str = str.replacingOccurrences(of: "\(dataStr)", with: "\(decoStr)")
                                            } else {
                                                str = str.replacingOccurrences(of: "\"\(dataStr)\"", with: "\(decoStr)")
                                            }
                                            
                                        } else {
                                            LCLog.d("解密失败 ")
                                            DispatchQueue.main.async {
                                                observer.onNext(.failure(BaseError("软件不可用，建议您去应用商店重新下载安装")))
                                            }
                                            return
                                        }
                                    }
                                } else {
                                    LCLog.d("签名校验失败，本次请求无效")
                                    DispatchQueue.main.async {
                                        observer.onNext(.failure(BaseError("软件不可用，建议您去应用商店重新下载安装")))
                                    }
                                    return
                                }
                            }
                        }
                    }
                    
                    if hostType == .docs {
                        var encData: [String: Any] = [ "encStr": str ]
                        
                        do {
                            // 将字典转换为Data类型（这里相当于模拟JSON格式）
                            let jsonData = try JSONSerialization.data(withJSONObject: encData, options: [])
                            // 使用JSONDecoder解码Data为模型对象
                            let m = try JSONDecoder().decode(T.self, from: jsonData)

                            DispatchQueue.main.async {
                                observer.onNext(.success(m))
                                observer.onCompleted()
                            }
                        } catch {
                            print("转换出错: \(error)")
                            DispatchQueue.main.async {
                                observer.onNext(.failure(BaseError(error.localizedDescription)))
                            }
                        }
                        
                        return
                    }
                    
                    if let m: T = JsonTool.string2Model(str) {
                        if let yyCache = yyCache {
                            // 给保存的数据添加一个请求的时间戳
                            var timestamp = Date().string(withFormat: "yyyy-MM-dd")
                            var ss = "\"requestTime\":\""
                            ss.append(timestamp + "\",")
                            let idx = str.index(str.startIndex, offsetBy: 1)
                            str.insert(contentsOf: ss, at: idx)
                            yyCache.diskCache.setObject(str.data(using: .utf8)! as NSCoding, forKey: cachekey)
                        }
                        
                        DispatchQueue.main.async {
                            observer.onNext(.success(m))
                            observer.onCompleted()
                        }
                    } else {
                        /* 常见错误：
                         1、类型： eg：Int写成string，反之亦然
                         2、需要写成数组类型的，忘记"[]"
                         */
                        DispatchQueue.main.async {
                            observer.onNext(.failure(BaseError("数据格式出错!")))
                        }
                        LCLog.d("检查模型与数据格式匹配,或是请求方式错了")
                    }
                    
                case .failure(let error):
                    LCLog.d(error.localizedDescription)
                    DispatchQueue.main.async {
                        if LCDevice.checkNetRestrictedState() {
                            observer.onNext(.failure(BaseError("网络访问异常，请重试！")))
                        } else {
                            observer.onCompleted()
                        }
                    }
                    
                }
            }
            if let progress = progressHandle { request.downloadProgress(closure: progress) }
            
            return Disposables.create()
        })
    }
    
    // MARK:-  版本更新的api请求
    static func getCurrentVersionUpdateInfo<T: Codable>(_ apiUrl: String) -> Observable<Status<T>> {
        return APISession.get(apiUrl)

    }
    
    // MARK:- 微信二维码
    ///post
    static func postWeChatQrCode<T: Codable>(_ url: String, parameters: Parameters = [:]) -> Observable<T> {
        return APISession.WeChatRequest(url, parameters: parameters, "POST")
    }
    
    static func WeChatRequest<T: Codable>(_ apiUrl: String, parameters: Parameters,_ method: String) -> Observable<T> {
        return Observable.create({ observer -> Disposable in
            
            let utilityQueue = DispatchQueue.global(qos: .utility)
            var urlRequest = URLRequest(url: URL(string: apiUrl)!)
            urlRequest.httpMethod = method
            
            var finalRequest: URLRequest?
            
            let postRequest = try! JSONEncoding.default.encode(urlRequest, with: parameters)
            finalRequest = postRequest
            
            let mng = Alamofire.SessionManager.default
            mng.adapter = WeChatAdapter
            
            mng.request(finalRequest!).responseData(queue: utilityQueue) { (resp) in
                let res = resp.result
                LCLog.d("api=\(resp.request?.url?.absoluteString ?? "")")
                switch res {
                case .success(let data):
                    print(data)
                    
                    var mode: WeChatRespModel = WeChatRespModel()
                    mode.errcode = 0
                    mode.contentType = "jpeg"
                    mode.buffer = data
                    
                    DispatchQueue.main.async {
                        observer.onNext(mode as! T)
                        observer.onCompleted() 
                    }
                        
                case .failure(let er):
                    DispatchQueue.main.async {
                        observer.onError(er)
                    }
                }
            }
            return Disposables.create()
        })
    }

    // MARK:- 微信Token
    ///post
    static func getWechatToken<T: Codable>(_ url: String, parameters: Parameters = [:]) -> Observable<T> {
        return APISession.WeChatTokenRequest(url, parameters: parameters, "GET")
    }
    
    static func WeChatTokenRequest<T: Codable>(_ apiUrl: String, parameters: Parameters, _ method: String, cache: Bool = false) -> Observable<T> {
        return Observable.create({ observer -> Disposable in
            
            let utilityQueue = DispatchQueue.global(qos: .utility)
            var urlRequest = URLRequest(url: URL(string: apiUrl)!)
            urlRequest.httpMethod = method
            
            var finalRequest: URLRequest?
            var getRequest: URLRequest?
            getRequest = try! URLEncoding.queryString.encode(urlRequest, with: parameters)
            finalRequest = getRequest
            
            var cachekey = ""
            var cacheJson: String! = ""
            if cache {
                //取出上次缓存的接口数据
                // 缓存的时候增加用户的ID和会员等级
                cachekey = getRequest!.description
                if let phone = UserInfo.currentUser()?.phone, let level = UserInfo.currentUser()?.vipLevelId {
                    cachekey = cachekey + "\(phone)" + "\(level.rawValue)"
                }
                let yy = YYCache.init(name: "apiCache_"+LCDevice.KBundleShortVersionString)
                if let jsonData = yy!.diskCache.object(forKey: cachekey) {
                    cacheJson = String(data: jsonData as! Data, encoding: .utf8)
                    if let res: T = JsonTool.string2Model(cacheJson!) {
                        observer.onNext(res)
                    } else {
                        LCLog.d("cache检查模型与数据格式匹配,或是请求方式错了")
                    }
                }
            }
            //
            
            let mng = Alamofire.SessionManager.default
            mng.adapter = wxTokenAdapter
            mng.request(finalRequest!).responseString(queue: utilityQueue) { resp in
                
                let res = resp.result
                LCLog.d("api=\(resp.request?.url?.absoluteString ?? "")")
                switch res {
                case .success(let str):
//                    print(str)
                    if cache && str == cacheJson {
                        observer.onCompleted()
                        
                    } else {
                        if let dict = try? JSONSerialization.jsonObject(with: str.data(using: .utf8)!, options: []) as? [String: Any] {
                            
                            if dict != nil && dict["code"] != nil {
                                //do something with json
                                //....
                            }
                        }
                        
                        if let m: T = JsonTool.string2Model(str) {
                            if cache {
//                                let yy = YYCache.init(name: "apiCache_"+LCDevice.KBundleShortVersionString)
//                                yy!.diskCache.setObject(str.data(using: .utf8)! as NSCoding, forKey:cachekey)
                            }
                            
                            DispatchQueue.main.async {
                                observer.onNext(m)
                                observer.onCompleted()
                            }
                        } else {
                            /* 常见错误：
                             1、类型： eg：Int写成string，反之亦然
                             2、需要写成数组类型的，忘记"[]"
                             */
                            LCLog.d("检查模型与数据格式匹配,或是请求方式错了")
                        }
                    }
                    
                case .failure(let er):
                    DispatchQueue.main.async {
                        observer.onError(er)
                        if !er.localizedDescription.isEmpty {
                            HUD.showInfo("网络访问异常，请重试")
                        }
                    }
                    
                }
            }
            return Disposables.create()
        })
    }
   
    
}

extension SessionManager {
    public static let `default`: SessionManager = {
        let configuration = URLSessionConfiguration.default
        configuration.httpAdditionalHeaders = SessionManager.defaultHTTPHeaders
        configuration.timeoutIntervalForRequest = 60
        configuration.timeoutIntervalForResource = 60
        configuration.requestCachePolicy = .reloadIgnoringLocalCacheData
        // 禁止代理
        configuration.connectionProxyDictionary = [:]
        return SessionManager(configuration: configuration)
    }()
}

extension String: Comparable {
    static func > (lhs: String, rhs: String) -> Bool {
        rhs.compare(lhs, options: .literal) == .orderedDescending
    }
}

//public struct APIURLEncoding: ParameterEncoding {
//    public func encode(_ urlRequest: URLRequestConvertible, with parameters: Parameters?) throws -> URLRequest {
//        URLEncoding.en
//    }
//
    
//    public func encode(_ urlRequest: URLRequestConvertible, with parameters: Parameters?) throws -> URLRequest {
//        let urlRequest = URLRequest(url: URL.init(string: ""))
//
//
//        return urlRequest
//    }
//}

  // MARK: -  安全签名
extension APISession {
    
    // 根据 URL 判断是否需要安全签名
    static func whetherSecretSign(url: String) -> Bool {
        
        return allSignatureUrls.contains(where: { url.hasPrefix($0) })
    }
    
//    secret：秘钥
//    singn: 参数加密摘要
//    ts：当前时间timestamp
//    u：用户ID
    static func buildSecretSignForUrl(parameters: Parameters, timestamp: String ) -> String? {
        
        var u = ""
        if let userid = UserInfo.currentUser()?.userId {
           u = "\(userid)"
        }
        // 取出来参数
        var allKeys = [String]()
        allKeys.append("ts")
        allKeys.append("u")
        
        // 加密的秘钥
        allKeys.append("s")
        
        for key in parameters.keys {
            allKeys.append(key)
        }
        
        allKeys = bubbleSort(allKeys)
        
       // 排序 升序
//        allKeys.sort(by: <)
        
        var newPam = ""
        for (i, key) in allKeys.enumerated() {
            newPam = newPam.appendingFormat("%@=", key)
            if key == "ts" {
                newPam = newPam.appendingFormat("%@", timestamp)
            } else if key == "u" {
                newPam = newPam.appendingFormat("%@", u)
            } else if key == "s" {
                newPam = newPam.appendingFormat("%@", SecretKey)
            } else {
                newPam.append("\( parameters[key] ??? "")")
            }
            if i <= allKeys.count - 2 {
                newPam.append("&")
            }
        }
        
//        LCLog.d("BCrypt加密前：\(newPam)")
        
        let md5str = md5AndUppercased(str: newPam)
        
        let bCryptStr = JFBCrypt.hashPassword(md5str, withSalt: JFBCrypt.generateSalt(withNumberOfRounds: 10))

//        LCLog.d("BCrypt加密后：\(bCryptStr)")

        return bCryptStr
        
    }
    
  
    static func bubbleSort(_ array: [String]) -> [String] {
        
        var list = array
        //swap变量用来标记循环里最后一次交换的位置
        var swap = 0
        var k = list.count - 1
        //记录循环次数
        for i in 0..<list.count {
            var flag = true
            for j in 0..<k {
                if compareStr(str1: list[j], str2: list[j+1]) == 1 {
                    list.swapAt(j, j+1)
                    flag = false
                    swap = j
                }
            }
            k = swap
            if flag {
                break
            }
        }
        return list
    }
    
    static func compareStr(str1: String, str2: String) -> Int {
        
        let min = min(str1.count, str2.count)
        
        for i in 0..<min {
            
            let index1 = getIndex(str: str1.lpSubString(start: i, length: 1))
            let index2 = getIndex(str: str2.lpSubString(start: i, length: 1))
            
            if index1 == index2 {
                // TODO: 这里的改动需要充分验证
                // 如果已经到达较短字符串的末尾，让长字符串优先
                if i == min - 1 && str1.count != str2.count {
                    return str1.count > str2.count ? -1 : 1
                }
                continue
            } else if index1 < index2 {
                return -1
            } else {
                return 1
            }
        }
        return 0
    }
    
    static func getIndex(str: String) -> Int {
        
        return APISession.kSortArr.index(of: str.lowercased()) ?? -1

    }

    static func md5AndUppercased(str: String) -> String {
       
        var newPam = (str as NSString).md5()!
         // 转大写
        var  str1 = String()
        var j = 0
        while j != newPam.count {
            
            let s = newPam[newPam.index(newPam.startIndex, offsetBy: j)]
            // 把每个字符转换为整型的数值（用ASCII码值判断大小写）
            let s1 = String(s)
            var num: UInt32 = 0
            for code in s1.unicodeScalars {
                num = code.value
            }
            // 判断ASCII码值 ，进行大小写转换
            if num >= 97 && num <= 122 {
                num -= 32
            }
            // 把ASCII码值转换为字符
            let ch: Character = Character(UnicodeScalar(num)!)
            // 接收字符
            let s2 = String(ch)

            str1 += s2
            
            j += 1
        }
        newPam = str1
        return newPam
    }
    
    static func capitalAndMd5(str: String) -> String {
        
        var newPam = "\(SecretKey)&\(str)"
        LCLog.d("签名前字符串：\(newPam)")
        newPam = (newPam as NSString).md5()!
        //            // 转大写
        var  str1 = String()
        var j = 0
        while j != newPam.count {
            
            let s = newPam[newPam.index(newPam.startIndex, offsetBy: j)]
            // 把每个字符转换为整型的数值（用ASCII码值判断大小写）
            let s1 = String(s)
            var num: UInt32 = 0
            for code in s1.unicodeScalars {
                num = code.value
            }
            // 判断ASCII码值 ，进行大小写转换
            if num >= 97 && num <= 122 {
                num -= 32
            }
            // 把ASCII码值转换为字符
            let ch: Character = Character(UnicodeScalar(num)!)
            // 接收字符
            let s2 = String(ch)
            
            str1 += s2
            
            j += 1
        }
        newPam = str1
        return newPam
    }
    
    //seawer    se
    
    // 加密请求体数据
    static func buildSecretBodyData(_ sign: String, jsonStr: String, encrypt: Bool) -> String? {
        
        // 拼接 之后Md5 转大写
        let md5Str: String =  self.capitalAndMd5(str: sign)
        // des key 16位 不够补0
        let lenth = md5Str.count > 16 ?  16 :md5Str.count
        
        var deLCKey = md5Str.prefix(lenth)
        
        var zero: String = "0000000000000000"
        if deLCKey.count < 16 {
            zero.replaceSubrange(deLCKey.startIndex...deLCKey.endIndex, with: deLCKey)
            deLCKey = "\(zero)"
        }
        
        LCLog.d("签名解密前 jsonStr：\(jsonStr) key：\(String(deLCKey))")
        if let enStr = jsonStr.decrypt_des( key: String(deLCKey)) {
            LCLog.d("签名解密后 enStr：\(enStr)")
            return enStr
        }
        
        return nil
        
    }
    
    // 校验服务端的下行数据 sign
    static func validationSignData(jsonStr: String, data: Dictionary<String, Any>, sSign: String) -> Bool {
        //  A、取下行的数据字段：reqId、code、sign、ts、s按照特定的字母进行排序、拼接字符串；
       // B、对通过1拼接的字符串，通过BCrypt进行加密，生成clientSign
       // C、通过取Header中的sSign与clientSign比较，校验
        
        if  let code = data["code"] as? Int64, let sign = data["sign"] as? String, let ts = data["ts"] as? Int64 {
            
            var keys: [String] =  ["code", "sign", "ts", "s"]
            
            keys = bubbleSort(keys)
            
            var newPam = ""
            for (i, key) in keys.enumerated() {
                newPam = newPam + "\(key)" + "="
                if key == "ts" {
                    newPam = newPam + "\(ts)"
                } else if key == "s" {
                    newPam = newPam  + "\(SecretKey)"
                } else if key == "code" {
                    newPam = newPam + "\(code)"
                } else if key == "sign" {
                    newPam = newPam + "\(sign)"
                }
                
                if i <= keys.count - 2 {
                    newPam.append("&")
                }
            }
            
//            LCLog.d("BCrypt解密前：\(newPam)")
            let md5str = md5AndUppercased(str: newPam)
            let bCryptStr = JFBCrypt.hashPassword(md5str, withSalt: sSign)
            LCLog.d("BCrypt解密：\(bCryptStr == sSign ? "成功":"失败")")
            return bCryptStr == sSign
            
        }
            
        // 先把 返回的 sign 替换成空 然后进行加密 和 sign进行对比 一样就说明通过
//        if let sign = data["sign"] as? String {
//            let oldSign: String = jsonStr.replacingOccurrences(of: sign, with: "")
//            let newSign = self.capitalAndMd5(str: oldSign)
//            return newSign == sign
//        }
        return false
    }
    
    // 如果小于1天 就可以用缓存
    static func validationCacheDataTime(jsonStr: String) -> Bool {
        
        if let dict = try? JSONSerialization.jsonObject(with: jsonStr.data(using: .utf8)!, options: []) as? [String: Any] {
            if let timestamp = dict["requestTime"] as? String {
                let curtimestamp = Date().string(withFormat: "yyyy-MM-dd")
                if timestamp == curtimestamp {
                    return true
                }
            } else {
                // 如果之前没保存时间戳 还是执行之前的逻辑不判断
                return true
            }
        }
        return false
    }
}

// MARK: 字典转字符串
extension Dictionary {
    
    func toJsonString() -> String? {
        guard let data = try? JSONSerialization.data(withJSONObject: self,
                                                     options: []) else {
            return nil
        }
        guard let str = String(data: data, encoding: .utf8) else {
            return nil
        }
        return str
     }
    
}

/// 普通的Ap 请求header
class MyReqAdapter: RequestAdapter {
    
    ///修改header配置后需要同步到AliOSSHelper的取token的task配置
    func adapt(_ urlRequest: URLRequest) throws -> URLRequest {
        let isRelease = UserDefaults.standard.bool(forKey: LCKey.UD_ReleaseEnv)
        var req = urlRequest
        let urlstr = req.url?.absoluteString
        if !urlstr!.starts(with: "https") {
            if isRelease {
                req.url = URL(string: APISession.baseURLOnline + urlstr!)!
            } else {
                req.url = URL(string: APISession.baseURLTest + urlstr!)!
            }
        }
        req.appendNarmalHeader()
        return req
    }
}

class MyUploadReqAdapter: RequestAdapter {
    func adapt(_ urlRequest: URLRequest) throws -> URLRequest {
        let isRelease = UserDefaults.standard.bool(forKey: LCKey.UD_ReleaseEnv)
        var req = urlRequest
        let urlstr = req.url?.absoluteString
        if !urlstr!.starts(with: "https") {
            if isRelease {
                req.url = URL(string: APISession.baseURLOnline + urlstr!)!
            } else {
                req.url = URL(string: APISession.baseURLTest + urlstr!)!
            }
        }
        req.appendUploadNarmalHeader()
        return req
    }
}

/// 版本更新的Api 请求header
class VersionReqAdapter: RequestAdapter {
    
    ///修改header配置后需要同步到AliOSSHelper的取token的task配置
    func adapt(_ urlRequest: URLRequest) throws -> URLRequest {
        let isRelease = UserDefaults.standard.bool(forKey: LCKey.UD_ReleaseEnv)
        var req = urlRequest
        let urlstr = req.url?.absoluteString
        if !urlstr!.starts(with: "https") {
            if isRelease {
                req.url = URL(string: APISession.version_baseURLOnline + urlstr!)!
            } else {
                req.url = URL(string: APISession.version_baseURLTest + urlstr!)!
            }
        }
        req.appendNarmalHeader()
        
        return req
    }
}

class DocsReqAdapter: RequestAdapter {
    
    ///修改header配置后需要同步到AliOSSHelper的取token的task配置
    func adapt(_ urlRequest: URLRequest) throws -> URLRequest {
        let isRelease = UserDefaults.standard.bool(forKey: LCKey.UD_ReleaseEnv)
        var req = urlRequest
        let urlstr = req.url?.absoluteString
        if !urlstr!.starts(with: "https") {
            if isRelease {// https://docs.quickleading.com/docs/ckzs/ios/api_config.enc
                req.url = URL(string: "https://docs.quickleading.com/" + urlstr!)!
            } else {
                req.url = URL(string: "https://docs.quickleading.com/" + urlstr!)!
            }
        }
        req.appendNarmalHeader()
        
        return req
    }
}

class WeChatReqAdapter: RequestAdapter {
    
    func adapt(_ urlRequest: URLRequest) throws -> URLRequest {
        var req = urlRequest
        let urlstr = req.url?.absoluteString
        req.url = URL(string: APISession.baseWeChatApi + urlstr!)!
        req.setValue("application/json; charset=utf-8", forHTTPHeaderField: "Content-Type")
        return req
    }
}

class TokenAdapter: RequestAdapter {
    
    func adapt(_ urlRequest: URLRequest) throws -> URLRequest {
        var req = urlRequest
        let urlstr = req.url?.absoluteString
        req.url = URL(string: APISession.baseURLOnline + urlstr!)!
        return req
    }
}

fileprivate extension URLRequest {
    
    /// 添加api请求header字段
    mutating func appendNarmalHeader() {
        self.setValue("AppStore", forHTTPHeaderField: "channel")
        self.setValue(LCDevice.KBundleShortVersionString, forHTTPHeaderField: "version")
        self.setValue(UIDevice.current.machineModel, forHTTPHeaderField: "deviceId")
        self.setValue(LCDevice.udid, forHTTPHeaderField: "udid")
        self.setValue("iOS", forHTTPHeaderField: "os")

        if let user = UserInfo.currentUser(), let u = user.userId {
            self.setValue( "\(u)", forHTTPHeaderField: "u")
        } else {
            self.setValue( "", forHTTPHeaderField: "u")
        }
        
        if let user = UserInfo.currentUser(), let token = user.token {
            self.setValue( "\(token)", forHTTPHeaderField: "token")
        } else {
            self.setValue( "", forHTTPHeaderField: "token")
        }
        
                
        self.setValue(LCDevice.KFBundleId, forHTTPHeaderField: "appId")
        
        LCLog.d("appendNarmalHeader:\(self.allHTTPHeaderFields)")
    }
    
    mutating func appendUploadNarmalHeader() {
        self.setValue("AppStore", forHTTPHeaderField: "channel")
        self.setValue(LCDevice.KBundleShortVersionString, forHTTPHeaderField: "version")
        self.setValue(UIDevice.current.machineModel, forHTTPHeaderField: "deviceinfo")
        self.setValue("iOS", forHTTPHeaderField: "os")
        if let user = UserInfo.currentUser(), let token = user.token {
            self.setValue( token, forHTTPHeaderField: "live-token")
        }
        self.setValue("multipart/form-data;boundary=------\(LCDevice.udid)", forHTTPHeaderField: "Content-Type")
        
        self.setValue(LCDevice.KFBundleId, forHTTPHeaderField: "appId")
        

    }
}

