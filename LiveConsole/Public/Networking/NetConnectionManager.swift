//
//  NetConnectionManager.swift
//  LivePlus
//
//  Created by iclick on 2020/12/23.
//

import Alamofire
import Reachability

/// 监听网络状态
/// Alamofire的网络监听不靠谱 首次安装弹窗之后拿不到正确的权限
/// 但是里面有业务 不知道是干啥的 所以接入Reachability 用两层判断
class NetConnectionManager {

    public static let shared = NetConnectionManager()
    
    var currentNetStatus: NetworkReachabilityManager.NetworkReachabilityStatus {
        switch NetstatManager.shared.status {
        case .none: return .notReachable
        case .mobile: return .reachable(.wwan)
        case .wifi: return .reachable(.ethernetOrWiFi)
        }
        if netStatus != .notReachable, netStatus != .unknown { // 如果非未连接状态 就直接返回
            return netStatus
        }
    }
    private var netStatus = NetworkReachabilityManager.NetworkReachabilityStatus.unknown

    let reachabilityManager = Alamofire.NetworkReachabilityManager(host: "www.baidu.com")
    
    func startNetworkReachabilityObserver() {
        reachabilityManager?.listener = { status in
            self.netStatus = status
            
            switch status {
            case .notReachable:
                LCLog.d("The network is not reachable")
                if LCDevice.checkNetRestrictedState() {
                    HUD.showInfo("当前网络连接异常，请检查后重试")
                }
                NotificationCenter.default.post(name: LCKey.noti_NetworkNotReachable, object: nil, userInfo: nil)
            case .unknown :
                LCLog.d("It is unknown whether the network is reachable")
            case .reachable(.ethernetOrWiFi):

                NotificationCenter.default.post(name: LCKey.noti_NetworkReachable, object: ["type":"wifi"], userInfo: nil)
                
                MiddleRequestNet.getVersionApiInfo()
                MiddleRequestNet.getConfigApiData()
                guard let userInfo = UserInfo.currentUser(), let mphone = userInfo.phone else {
                    return
                }
               
                //查看有没有服务器验证的订单
                InPurchasingModel.queryInPurchasingModels(phone: mphone) { array in
                    guard let models = array else { return }
                    for model in models {
                        LCLog.d("---本地获取支付凭证去服务端验证---")
                        MiddleRequestNet.purchasingValidation(param: ["code": model.code, "type": model.type, "phone": model.phone]) { code, _ in
                            print("---code--:\(code)")
                            if code == 0 {
                                InPurchasingModel.deleteInPurchasingModels(models: [model])
                            }
                        }
                    }
                    
                }
                LCLog.d("The network is reachable over the WiFi connection")
            case .reachable(.wwan):
                LCLog.d("The network is reachable over the WWAN connection")
                NotificationCenter.default.post(name: LCKey.noti_NetworkReachable, object: ["type":"wwan"], userInfo: nil)
                
            }
        }
        
        // start listening
        reachabilityManager?.startListening()
        NetstatManager.shared.networkStatusListener()
    }
}

enum NetStatus {
    case wifi
    case mobile
    case none
}
class NetstatManager: UIViewController {
    static let shared = NetstatManager()
    let reachability = Reachability(hostName: "www.baidu.com")
    var status: NetStatus = .none
}
 
extension NetstatManager {
    
    /***** 网络状态监听部分（开始） *****/
    func networkStatusListener() {
        // 1、设置网络状态消息监听 2、获得网络Reachability对象
        NotificationCenter.default.addObserver(self, selector: #selector(self.reachabilityChanged),name: Notification.Name.reachabilityChanged,object: reachability)
        reachability?.startNotifier()
    }
    
    // 主动检测网络状态
    @objc func reachabilityChanged(note: NSNotification) {
        guard let reachability = note.object as? Reachability else { return }
        if reachability.isReachable() { // 判断网络连接状态
            if reachability.isReachableViaWiFi() {
                print("连接类型：WiFi")
                self.status = .wifi
                NotificationCenter.default.post(name: LCKey.noti_NetworkReachable, object: ["type":"wifi"], userInfo: nil)
            } else {
                print("连接类型：移动网络")
                self.status = .mobile
                NotificationCenter.default.post(name: LCKey.noti_NetworkReachable, object: ["type":"wifi"], userInfo: nil)
            }
        } else {
            print("网络连接：不可用")
            self.status = .none
        }
    }
}
