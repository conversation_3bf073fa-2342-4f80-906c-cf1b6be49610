//  Respository.swift
//  Created on 2018/3/7
//  Description <#文件描述#>

//  Copyright © 2018年 guowei inc. All rights reserved.
//  <AUTHOR>

import Foundation
import RxSwift

public struct BaseError: Error {
    private var desc = ""
    var localizedDescription: String {
        return desc
    }
    init(_ desc: String) {
        self.desc = desc
    }
}

public enum Status<T> {
    case success(T)
    case failure(BaseError)
    case pending

    init(error: BaseError) {
        self = .failure(error)
    }

    init(data: T) {
       self = .success(data)
    }

    init(error: BaseError?) {
        if let e = error {
            self = .failure(e)
        } else {
            self = .success(() as! T)
        }
    }

    init(data: T?, error: BaseError?) {
        if let e = error {
            self = .failure(e)
        } else if let content = data {
            self = .success(content)
        } else {
            //异常情况按错误处理
            self = .failure(BaseError.init("wrong interface data"))
        }
    }
}

public extension Status {
    var isSuccess: Bool {
        if case .success(_) = self { return true }
        return false
    }
    
    var isCompleted: Bool {
        if case .pending = self { return false }
        return true
    }

    var data: Observable<T> {
        switch self {
        case .success(let data):    return .just(data)
        default:                    return .empty()
        }
    }

    func successData() -> T? {
        switch self {
        case .success(let data):    return data
        default:                    return nil
        }
    }

    var error: Observable<BaseError> {
        switch self {
        case .failure(let error):   return .just(error)
        default:                    return .empty()
        }
    }
}

public extension Status where T: Collection {
    var isEmpty: Bool {
        switch self {
        case .success(let data):
            return data.count == 0
        default:
            return false
        }
    }
}
