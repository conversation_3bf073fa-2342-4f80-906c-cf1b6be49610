//
//  APIURL.swift
//  LivePlus
//
//  Created by iclick on 2020/12/23.
//

import UIKit

/// 存放API接口地址
class APIURL {
    
    // MARK: - --------- 全局配置
        
    ///版本更新信息
    static let GET_version_info = "api/v1/config/version"
        
    ///轮播图
    static let GET_cycle_images = "api/v1/home/<USER>"
    ///全局配置
    static let GET_CONFIG = "api/v1/config/"
    
    ///会员信息
    static let GET_user_info = "api/v1/user/info"
                                    
    //MARK: - --------- 内购
    /// 会员购买列表
    static let GET_Purchasing_list = "api/v1/products/member/ios"
    
    // 积分购买列表
    static let GET_products_audio_list = "api/v1/products/audio/ios"
    
    
    static let Purchasing_validation = "api/v1/pay/apple"
    
    static let Get_Subscribe_Verify = "api/v1/order/ios/verify"
    
    static let GET_Member_Order_limit = "api/v1/pay/member/orders/limit"
    
    static let GET_Points_limit = "api/v1/pay/points/orders/limit"

    
    /// 获取基础版本数据
    static let GET_LIMIT = "api/v1/vip/ckzs/function/"
        
    static let POST_Upload_Log = "api/v1/config/upload/log"
    
    // MARK: - 扫码登录
    // GET /api/v1/qr/scan?uuid=17712341234asdfgasfasad&userId=1231
    static let GET_Qr_Scan = "api/v1/qr/scan"
  // 确认登录 /api/v1/qr/confirm 确认登录
    static let POST_Qr_Confirm = "api/v1/qr/confirm"
    
    static let GET_Qr_Info = "api/v1/qr/info"
    
    static let GET_Qr_Checkout = "api/v1/qr/checkout"
        
    static let POST_WeChat_Code = "/wxa/getwxacodeunlimit?access_token=" //wxa/getwxacodeunlimit?access_token=ACCESS_TOKEN
    
    // MARK: - --------- 登录
    
    // 初始用户唯一码接口
    static var GET_USER_CODE       = "api/v1/user/initUserCode"
    
    ///发验证码
    static var GET_SMS_SEND        = "api/v1/sms/verifycode"
    
    ///手机号登录
    static var POST_LOGIN         = "api/v1/user/login/sms"
    
    ///手机号绑定
    static var POST_PHONE_BIND    = "api/v1/user/sms/bind"
    
    ///手机号换绑
    static var POST_PHONE_REBIND    = "api/v1/user/rebind/phone"

    ///微信登录
    static let POST_WeiXin_LOGIN = "api/v1/user/login/wechat"
    
    ///微信 绑定
    static let POST_WeiXin_BIND = "api/v1/user/wechat/bind"
    
    /// 微信 换绑
    static let POST_Weixin_REBind = "api/v1/user/wechat/rebind"
    
    ///微信 解绑
    static let GET_WeiXin_UNBIND = "api/v1/user/wechat/unbind"
    
    /// 注销
    static let GET_LOG_OFF = "api/v1/user/cancel"
    
    // 积分统计
    static let GET_Cloneaudio_Statistics  = "api/v1/cloneaudio/statistics"
    
    
//    测试: https://liveapidev.optimix.cn/api/v1/wechat/mini/token
//    正式(等上线): https://liveapi.optimix.cn/api/v1/wechat/mini/token
    
    static let GET_WECHAT_Token = "api/v1/wechat/liveplusmini/token"
            
    
    /// 轮询订阅状态
    static let GET_SUBSCRIBE = "api/v1/user/user/official"
    
    // MARK: - 备份
    
    
    static let GET_JS_Config = "docs/ckzs/ios/js_config.enc"
    
    static let GET_API_Config = "docs/ckzs/ios/api_config.enc"
    
    static let GET_Douyin_Gift = "api/v1/cloneaudio/douyin/gift"
    
    /// 获取克隆音色的id
    static let GET_Audio_SPKID = "api/v1/cloneaudio/spkid"
    static let Post_Audio_Combine_Preset = "api/v1/cloneaudio/preTtsSynthesis"
    static let Post_Audio_Combine_Clone = "api/v1/cloneaudio/cloneTtsSynthesis"
    static let GET_Clone_Info = "api/v1/cloneaudio/info"
    static let POST_Clone_SpkId = "api/v1/cloneaudio/spkid"
    static let POST_Clone_Train = "api/v1/cloneaudio/trainAudio"
    static let POST_Train_Status = "api/v1/cloneaudio/trainStatus"
    static let GET_Clone_Texts = "api/v1/cloneaudio/preText"
//    新版TTS
    // 轮询接口
    static let GET_Clone_Status  = "api/v1/cloneaudio/ttsStatus"
    static let POST_Clone_asyncPreTtsSynthesis = "api/v1/cloneaudio/asyncPreTtsSynthesis"
    static let POST_Clone_asyncCloneTtsSynthesis = "api/v1/cloneaudio/asyncCloneTtsSynthesis"
    
    // MARK: - 积分
    // 实时朗读积分扣除
    static let POST_point_deduct_unit  = "api/v1/point/deduct/unit"
    
}
