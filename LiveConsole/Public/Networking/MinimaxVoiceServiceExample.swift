import Foundation
import AVFoundation

// MARK: - MinimaxVoiceService 使用示例
class MinimaxVoiceServiceExample {
    
    private let voiceService = MinimaxVoiceService.shared
    private var audioPlayer: AVAudioPlayer?
    
    // MARK: - 示例1: 获取所有音色
    func exampleGetAllVoices() {
        voiceService.getAllVoices { result in
            switch result {
            case .success(let voices):
                print("获取到 \(voices.count) 个音色:")
                for voice in voices {
                    print("- 音色ID: \(voice.voiceId), 名称: \(voice.voiceName)")
                    if let description = voice.description {
                        print("  描述: \(description)")
                    }
                }
            case .failure(let error):
                print("获取音色失败: \(error.localizedDescription)")
            }
        }
    }
    
    // MARK: - 示例2: 生成语音并播放
    func exampleGenerateSpeech() {
        let text = "今天天气也很不错啊,完成个人用户认证或企业用户认证，以确保可以正常使用本功能。"
        let voiceId = "female-tianmei"

        voiceService.generateSpeech(text: text, voiceId: voiceId) { [weak self] result in
            switch result {
            case .success(let response):
                print("语音生成成功!")
                print("- 文件名: \(response.data.tosFileName)")
                print("- 音频大小: \(response.data.extraInfo.audioSize) bytes")
                print("- 音频长度: \(response.data.extraInfo.audioLength) ms")
                print("- 字符数: \(response.data.extraInfo.usageCharacters)")
                print("- 词数: \(response.data.extraInfo.wordCount)")

                // 注意：这里返回的是文件信息，不是音频数据
                // 如果需要播放，需要根据tosFileName下载音频文件
                print("音频文件已生成，文件名: \(response.data.tosFileName)")

            case .failure(let error):
                print("语音生成失败: \(error.localizedDescription)")
            }
        }
    }
    
    // MARK: - 示例3: 克隆语音
    func exampleCloneVoice() {
        let tosFileName = "tts_sync_d1f57180cd83.mp3"
        let voiceId = "a_1234567890"

        voiceService.cloneVoice(tosFileName: tosFileName, voiceId: voiceId) { result in
            switch result {
            case .success(let response):
                print("语音克隆成功!")
                print("- 音色ID: \(response.data.voiceId)")
                print("- 源文件: \(response.data.tosFileName)")
                print("- 试听音频: \(response.data.cloneResult.demoAudio)")
                print("- 上传文件ID: \(response.data.uploadResult.file.fileId)")
                print("- 文件大小: \(response.data.uploadResult.file.bytes) bytes")

                if response.data.cloneResult.inputSensitive {
                    print("⚠️ 检测到敏感内容")
                }

            case .failure(let error):
                print("语音克隆失败: \(error.localizedDescription)")
            }
        }
    }
    
    // MARK: - 示例4: 自定义参数的语音生成
    func exampleGenerateSpeechWithCustomParams() {
        let text = "这是一个自定义参数的语音合成示例"
        let model = "speech-02-hd"
        let voiceId = "male-qn-jingying"

        voiceService.generateSpeech(text: text, model: model, voiceId: voiceId) { result in
            switch result {
            case .success(let response):
                print("自定义语音生成成功")
                print("- 使用模型: \(model)")
                print("- 音色ID: \(voiceId)")
                print("- 生成文件: \(response.data.tosFileName)")
                print("- 音频格式: \(response.data.extraInfo.audioFormat)")
                print("- 采样率: \(response.data.extraInfo.audioSampleRate) Hz")
                print("- 比特率: \(response.data.extraInfo.bitrate) bps")

            case .failure(let error):
                print("自定义语音生成失败: \(error.localizedDescription)")
            }
        }
    }
    
    // MARK: - 示例5: 自定义参数的语音克隆
    func exampleCloneVoiceWithCustomParams() {
        let tosFileName = "my_voice_sample.mp3"
        let voiceId = "my_custom_voice"
        let demoText = "这是我的专属音色，听起来怎么样？"
        let demoModel = "speech-02-hd"
        let needNoiseReduction = false

        voiceService.cloneVoice(
            tosFileName: tosFileName,
            voiceId: voiceId,
            demoText: demoText,
            demoModel: demoModel,
            needNoiseReduction: needNoiseReduction
        ) { result in
            switch result {
            case .success(let response):
                print("自定义语音克隆成功:")
                print("- 音色ID: \(response.data.voiceId)")
                print("- 试听地址: \(response.data.cloneResult.demoAudio)")
                print("- 降噪设置: \(needNoiseReduction ? "开启" : "关闭")")
                print("- 克隆状态: \(response.data.cloneResult.baseResp.statusMsg)")
                print("- 上传状态: \(response.data.uploadResult.baseResp.statusMsg)")

            case .failure(let error):
                print("自定义语音克隆失败: \(error.localizedDescription)")
            }
        }
    }

    // MARK: - 示例6: 使用便利方法
    func exampleUsingConvenienceMethods() {
        // 获取女性音色
        voiceService.getVoicesByCategory("female") { result in
            switch result {
            case .success(let femaleVoices):
                print("女性音色列表 (\(femaleVoices.count) 个):")
                for voice in femaleVoices.prefix(5) { // 只显示前5个
                    print("- \(voice.voiceName) (ID: \(voice.voiceId))")
                }
            case .failure(let error):
                print("获取女性音色失败: \(error.localizedDescription)")
            }
        }

        // 查找特定音色信息
        voiceService.getVoiceById("female-tianmei") { result in
            switch result {
            case .success(let voice):
                if let voice = voice {
                    print("找到音色: \(voice.voiceName), 分类: \(voice.category ?? "未知")")
                } else {
                    print("未找到指定音色")
                }
            case .failure(let error):
                print("查找音色失败: \(error.localizedDescription)")
            }
        }

        // 生成语音并获取文件信息
        voiceService.generateSpeechWithFileInfo(text: "便利方法测试", voiceId: "female-tianmei") { result in
            switch result {
            case .success(let (response, fileName)):
                print("语音生成成功，文件名: \(fileName)")
                print("音频信息: \(response.data.extraInfo.audioSize) bytes, \(response.data.extraInfo.audioLength) ms")
            case .failure(let error):
                print("生成语音失败: \(error.localizedDescription)")
            }
        }

        // 克隆语音并获取试听URL
        voiceService.cloneVoiceWithDemoUrl(tosFileName: "test.mp3", voiceId: "test_voice") { result in
            switch result {
            case .success(let (response, demoUrl)):
                print("语音克隆成功!")
                print("试听URL: \(demoUrl)")
                print("文件ID: \(response.data.uploadResult.file.fileId)")
            case .failure(let error):
                print("克隆语音失败: \(error.localizedDescription)")
            }
        }
    }

    // MARK: - 辅助方法
    
    /// 播放音频数据
    private func playAudio(data: Data) {
        do {
            audioPlayer = try AVAudioPlayer(data: data)
            audioPlayer?.play()
            print("开始播放音频")
        } catch {
            print("播放音频失败: \(error.localizedDescription)")
        }
    }
    
    /// 保存音频数据到文件
    private func saveAudioToFile(data: Data, fileName: String) {
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
        let audioURL = documentsPath.appendingPathComponent(fileName)
        
        do {
            try data.write(to: audioURL)
            print("音频文件已保存到: \(audioURL.path)")
        } catch {
            print("保存音频文件失败: \(error.localizedDescription)")
        }
    }
    
    // MARK: - 综合示例：完整的语音处理流程
    func exampleCompleteWorkflow() {
        print("开始完整的语音处理流程...")

        // 1. 首先获取可用的音色
        voiceService.getAllVoices { [weak self] result in
            switch result {
            case .success(let voices):
                guard let firstVoice = voices.first else {
                    print("没有可用的音色")
                    return
                }

                print("使用音色: \(firstVoice.voiceName) (ID: \(firstVoice.voiceId))")
                if let category = firstVoice.category {
                    print("音色分类: \(category)")
                }

                // 2. 使用第一个音色生成语音
                let text = "欢迎使用Minimax语音服务！"
                self?.voiceService.generateSpeech(text: text, voiceId: firstVoice.voiceId) { result in
                    switch result {
                    case .success(let response):
                        print("语音生成成功!")
                        print("- 生成文件: \(response.data.tosFileName)")
                        print("- 音频大小: \(response.data.extraInfo.audioSize) bytes")
                        print("- 处理字符数: \(response.data.extraInfo.usageCharacters)")

                        // 注意：实际项目中需要根据tosFileName下载音频文件
                        print("✅ 语音文件已生成，可以通过文件名获取音频")

                    case .failure(let error):
                        print("语音生成失败: \(error.localizedDescription)")
                    }
                }

            case .failure(let error):
                print("获取音色列表失败: \(error.localizedDescription)")
            }
        }
    }
}

// MARK: - 如何在ViewController中使用
/*
class YourViewController: UIViewController {
    
    private let voiceExample = MinimaxVoiceServiceExample()
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        // 使用示例
        voiceExample.exampleGetAllVoices()
        voiceExample.exampleGenerateSpeech()
        voiceExample.exampleCloneVoice()
        voiceExample.exampleGenerateSpeechWithCustomParams()
        voiceExample.exampleCloneVoiceWithCustomParams()
        voiceExample.exampleUsingConvenienceMethods()
        voiceExample.exampleCompleteWorkflow()
    }
}
*/
