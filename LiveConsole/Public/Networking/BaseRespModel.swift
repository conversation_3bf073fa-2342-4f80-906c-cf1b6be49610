//
//  BaseRespModel.swift
//  LivePlus
//
//  Created by iclick on 2020/12/23.
//

import UIKit

enum LsqError: Error {
    case message(String)
}

// 获取验证码的类型
enum VerifyCodeType: String {
    case  login // 登录
    case  bindPhone // 绑定（换绑）手机
    case  setPwd // 设置（重置）密码
}

struct BaseRespModel<T: Codable>: Codable {
    var requestTime: String? // 接口请求的时间戳， 如果接口需要1天更新一次 可以用这个时间戳进行比较
    var sign: String? // 双重验证的加密签名
    var ts: Int64? // 双重验证的时间戳
    var code: Int? //0 表示成功 ，非0表示出错
    var msg: String?
    var reqId: String? //请求id
    var data: T?   //data的内容是个泛型，可以是数组：[BannerModel], 是对象：BannerModel, 基本数据类型：Bool
    
}

struct BaseNullData: Codable {
    var msg: String?
}


struct BaseNomalRespModel: Codable {
    var code: Int?
    var msg: String?
    var reqId: String?
}

class JsonTool {
    
    ///  例如  let push = try! JsonTool.decode(PushDataModel.self, param: userInfo)
    static func decode<T>(_ type: T.Type, param: [String: Any]) throws -> T where T: Decodable {
        guard let jsonData = self.getJsonData(with: param) else {
            throw LsqError.message("转换data失败")
        }
        guard let model = try? JSONDecoder().decode(type, from: jsonData) else {
            throw LsqError.message("转换模型失败")
        }
        return model
    }
    static func getJsonData(with param: Any) -> Data? {
        if !JSONSerialization.isValidJSONObject(param) {
            return nil
        }
        guard let data = try? JSONSerialization.data(withJSONObject: param, options: []) else {
            return nil
        }
        return data
    }
    
    /// json转model工具，外部根据泛型来指定转成什么类型
    static func string2Model<T: Codable>(_ str: String) -> T? {
        let jsonData = Data(str.utf8)
        let decoder = JSONDecoder()
        do {
            let model = try decoder.decode(T.self, from: jsonData)
            return model
        } catch {
            print("数据解析失败: \(error)")
            return nil
        }
    }
    
    static func string2ModelList<T: Codable>(_ str: String) -> [T]? {
        let jsonData = Data(str.utf8)
        let decoder = JSONDecoder()
        do {
            let model = try decoder.decode([T].self, from: jsonData)
            return model
        } catch {
            print("数据解析失败: \(error)")
            return nil
        }
    }
    
    /// model转json
    static func model2String<T: Codable>(_ model: T) -> String {
        
        let encoder = JSONEncoder()
        
        do {
            let data = try encoder.encode(model)
            guard let json = String(data: data, encoding: .utf8) else {
                return ""
            }
            return json
        } catch {
            LCLog.d(error.localizedDescription)
            return ""
        }
    }
}
