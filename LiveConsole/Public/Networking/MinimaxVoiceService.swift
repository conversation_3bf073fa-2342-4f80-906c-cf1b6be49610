import Foundation

// MARK: - 数据模型

// 获取音色列表的响应模型
struct MinimaxVoicesResponse: Codable {
    let data: VoicesData
    let success: Bool
}

struct VoicesData: Codable {
    let allVoices: [String: String]
    let categorizedVoices: CategorizedVoices
    let totalCount: Int

    enum CodingKeys: String, CodingKey {
        case allVoices = "all_voices"
        case categorizedVoices = "categorized_voices"
        case totalCount = "total_count"
    }
}

struct CategorizedVoices: Codable {
    let children: [String: String]?
    let english: [String: String]?
    let female: [String: String]?
    let male: [String: String]?
    let special: [String: String]?
}

// 音色信息模型
struct MinimaxVoice {
    let voiceId: String
    let voiceName: String
    let category: String?
}

// TTS请求模型
struct MinimaxTTSRequest: Codable {
    let text: String
    let model: String
    let voiceId: String

    enum CodingKeys: String, CodingKey {
        case text
        case model
        case voiceId = "voice_id"
    }
}

// TTS响应模型
struct MinimaxTTSResponse: Codable {
    let data: TTSData
    let success: Bool
}

struct TTSData: Codable {
    let extraInfo: AudioExtraInfo
    let success: Bool
    let tosFileName: String
    let traceId: String

    enum CodingKeys: String, CodingKey {
        case extraInfo = "extra_info"
        case success
        case tosFileName = "tos_file_name"
        case traceId = "trace_id"
    }
}

struct AudioExtraInfo: Codable {
    let audioChannel: Int
    let audioFormat: String
    let audioLength: Int
    let audioSampleRate: Int
    let audioSize: Int
    let bitrate: Int
    let invisibleCharacterRatio: Double
    let usageCharacters: Int
    let wordCount: Int

    enum CodingKeys: String, CodingKey {
        case audioChannel = "audio_channel"
        case audioFormat = "audio_format"
        case audioLength = "audio_length"
        case audioSampleRate = "audio_sample_rate"
        case audioSize = "audio_size"
        case bitrate
        case invisibleCharacterRatio = "invisible_character_ratio"
        case usageCharacters = "usage_characters"
        case wordCount = "word_count"
    }
}

// 语音克隆请求模型
struct MinimaxVoiceCloneRequest: Codable {
    let tosFileName: String
    let voiceId: String
    let demoText: String
    let demoModel: String
    let needNoiseReduction: Bool

    enum CodingKeys: String, CodingKey {
        case tosFileName = "tos_file_name"
        case voiceId = "voice_id"
        case demoText = "demo_text"
        case demoModel = "demo_model"
        case needNoiseReduction = "need_noise_reduction"
    }
}

// 语音克隆响应模型
struct MinimaxVoiceCloneResponse: Codable {
    let data: VoiceCloneData
    let success: Bool
}

struct VoiceCloneData: Codable {
    let cloneResult: CloneResult
    let success: Bool
    let tosFileName: String
    let uploadResult: UploadResult
    let voiceId: String

    enum CodingKeys: String, CodingKey {
        case cloneResult = "clone_result"
        case success
        case tosFileName = "tos_file_name"
        case uploadResult = "upload_result"
        case voiceId = "voice_id"
    }
}

struct CloneResult: Codable {
    let baseResp: BaseResp
    let demoAudio: String
    let inputSensitive: Bool
    let inputSensitiveType: Int

    enum CodingKeys: String, CodingKey {
        case baseResp = "base_resp"
        case demoAudio = "demo_audio"
        case inputSensitive = "input_sensitive"
        case inputSensitiveType = "input_sensitive_type"
    }
}

struct UploadResult: Codable {
    let baseResp: BaseResp
    let file: FileInfo

    enum CodingKeys: String, CodingKey {
        case baseResp = "base_resp"
        case file
    }
}

struct BaseResp: Codable {
    let statusCode: Int
    let statusMsg: String

    enum CodingKeys: String, CodingKey {
        case statusCode = "status_code"
        case statusMsg = "status_msg"
    }
}

struct FileInfo: Codable {
    let bytes: Int
    let createdAt: Int
    let fileId: Int
    let filename: String
    let purpose: String

    enum CodingKeys: String, CodingKey {
        case bytes
        case createdAt = "created_at"
        case fileId = "file_id"
        case filename
        case purpose
    }
}

// MARK: - Minimax语音服务类
class MinimaxVoiceService {
    
    // MARK: - 属性
    private let baseURL = "http://192.168.3.135:5005/api/minimax"
    private let session = URLSession.shared
    
    // MARK: - 单例
    static let shared = MinimaxVoiceService()
    
    private init() {}
    
    // MARK: - 公共方法
    
    /// 获取所有系统音色
    /// - Parameter completion: 完成回调，返回音色列表或错误
    func getAllVoices(completion: @escaping (Result<[MinimaxVoice], Error>) -> Void) {
        let urlString = "\(baseURL)/voices"

        guard let url = URL(string: urlString) else {
            completion(.failure(MinimaxError.invalidURL))
            return
        }

        var request = URLRequest(url: url)
        request.httpMethod = "GET"

        session.dataTask(with: request) { data, response, error in
            DispatchQueue.main.async {
                if let error = error {
                    completion(.failure(error))
                    return
                }

                guard let data = data else {
                    completion(.failure(MinimaxError.noData))
                    return
                }

                do {
                    let voicesResponse = try JSONDecoder().decode(MinimaxVoicesResponse.self, from: data)
                    let voices = self.convertToVoiceArray(from: voicesResponse.data)
                    completion(.success(voices))
                } catch {
                    completion(.failure(error))
                }
            }
        }.resume()
    }
    
    /// 生成语音（同步TTS）
    /// - Parameters:
    ///   - text: 要转换的文本
    ///   - model: 模型名称，默认为 "speech-02-hd"
    ///   - voiceId: 音色ID
    ///   - completion: 完成回调，返回TTS响应信息或错误
    func generateSpeech(text: String,
                       model: String = "speech-02-hd",
                       voiceId: String,
                       completion: @escaping (Result<MinimaxTTSResponse, Error>) -> Void) {
        let urlString = "\(baseURL)/tts/sync/tos"

        guard let url = URL(string: urlString) else {
            completion(.failure(MinimaxError.invalidURL))
            return
        }

        let requestBody = MinimaxTTSRequest(text: text, model: model, voiceId: voiceId)

        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")

        do {
            request.httpBody = try JSONEncoder().encode(requestBody)
        } catch {
            completion(.failure(error))
            return
        }

        session.dataTask(with: request) { data, response, error in
            DispatchQueue.main.async {
                if let error = error {
                    completion(.failure(error))
                    return
                }

                guard let data = data else {
                    completion(.failure(MinimaxError.noData))
                    return
                }

                do {
                    let ttsResponse = try JSONDecoder().decode(MinimaxTTSResponse.self, from: data)
                    completion(.success(ttsResponse))
                } catch {
                    completion(.failure(error))
                }
            }
        }.resume()
    }
    
    /// 克隆语音
    /// - Parameters:
    ///   - tosFileName: TOS文件名
    ///   - voiceId: 音色ID
    ///   - demoText: 试听文本，默认为 "这是您合成的音色，您可以试听一下"
    ///   - demoModel: 试听模型，默认为 "speech-02-hd"
    ///   - needNoiseReduction: 是否需要降噪，默认为 true
    ///   - completion: 完成回调，返回克隆结果或错误
    func cloneVoice(tosFileName: String,
                   voiceId: String,
                   demoText: String = "这是您合成的音色，您可以试听一下",
                   demoModel: String = "speech-02-hd",
                   needNoiseReduction: Bool = true,
                   completion: @escaping (Result<MinimaxVoiceCloneResponse, Error>) -> Void) {
        let urlString = "\(baseURL)/voice/clone/tos"

        guard let url = URL(string: urlString) else {
            completion(.failure(MinimaxError.invalidURL))
            return
        }

        let requestBody = MinimaxVoiceCloneRequest(
            tosFileName: tosFileName,
            voiceId: voiceId,
            demoText: demoText,
            demoModel: demoModel,
            needNoiseReduction: needNoiseReduction
        )

        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")

        do {
            request.httpBody = try JSONEncoder().encode(requestBody)
        } catch {
            completion(.failure(error))
            return
        }

        session.dataTask(with: request) { data, response, error in
            DispatchQueue.main.async {
                if let error = error {
                    completion(.failure(error))
                    return
                }

                guard let data = data else {
                    completion(.failure(MinimaxError.noData))
                    return
                }

                do {
                    let cloneResponse = try JSONDecoder().decode(MinimaxVoiceCloneResponse.self, from: data)
                    completion(.success(cloneResponse))
                } catch {
                    completion(.failure(error))
                }
            }
        }.resume()
    }

    // MARK: - 便利方法

    /// 根据分类获取音色列表
    /// - Parameters:
    ///   - category: 音色分类 ("children", "english", "female", "male", "special")
    ///   - completion: 完成回调
    func getVoicesByCategory(_ category: String, completion: @escaping (Result<[MinimaxVoice], Error>) -> Void) {
        getAllVoices { result in
            switch result {
            case .success(let voices):
                let filteredVoices = voices.filter { $0.category == category }
                completion(.success(filteredVoices))
            case .failure(let error):
                completion(.failure(error))
            }
        }
    }

    /// 根据音色ID查找音色信息
    /// - Parameters:
    ///   - voiceId: 音色ID
    ///   - completion: 完成回调
    func getVoiceById(_ voiceId: String, completion: @escaping (Result<MinimaxVoice?, Error>) -> Void) {
        getAllVoices { result in
            switch result {
            case .success(let voices):
                let voice = voices.first { $0.voiceId == voiceId }
                completion(.success(voice))
            case .failure(let error):
                completion(.failure(error))
            }
        }
    }

    /// 生成语音并获取音频文件URL（如果API返回文件URL）
    /// - Parameters:
    ///   - text: 要转换的文本
    ///   - voiceId: 音色ID
    ///   - completion: 完成回调，返回音频文件信息
    func generateSpeechWithFileInfo(text: String,
                                   voiceId: String,
                                   completion: @escaping (Result<(response: MinimaxTTSResponse, audioFileName: String), Error>) -> Void) {
        generateSpeech(text: text, voiceId: voiceId) { result in
            switch result {
            case .success(let response):
                let audioFileName = response.data.tosFileName
                completion(.success((response, audioFileName)))
            case .failure(let error):
                completion(.failure(error))
            }
        }
    }

    /// 克隆语音并获取试听音频URL
    /// - Parameters:
    ///   - tosFileName: TOS文件名
    ///   - voiceId: 音色ID
    ///   - completion: 完成回调，返回克隆结果和试听URL
    func cloneVoiceWithDemoUrl(tosFileName: String,
                              voiceId: String,
                              completion: @escaping (Result<(response: MinimaxVoiceCloneResponse, demoUrl: String), Error>) -> Void) {
        cloneVoice(tosFileName: tosFileName, voiceId: voiceId) { result in
            switch result {
            case .success(let response):
                let demoUrl = response.data.cloneResult.demoAudio
                completion(.success((response, demoUrl)))
            case .failure(let error):
                completion(.failure(error))
            }
        }
    }
}

// MARK: - 私有方法
private extension MinimaxVoiceService {

    /// 将API返回的音色数据转换为MinimaxVoice数组
    func convertToVoiceArray(from voicesData: VoicesData) -> [MinimaxVoice] {
        var voices: [MinimaxVoice] = []

        // 添加所有音色
        for (voiceId, voiceName) in voicesData.allVoices {
            let category = findCategory(for: voiceId, in: voicesData.categorizedVoices)
            let voice = MinimaxVoice(voiceId: voiceId, voiceName: voiceName, category: category)
            voices.append(voice)
        }

        return voices.sorted { $0.voiceName < $1.voiceName }
    }

    /// 查找音色所属的分类
    func findCategory(for voiceId: String, in categorizedVoices: CategorizedVoices) -> String? {
        if categorizedVoices.children?[voiceId] != nil {
            return "children"
        } else if categorizedVoices.english?[voiceId] != nil {
            return "english"
        } else if categorizedVoices.female?[voiceId] != nil {
            return "female"
        } else if categorizedVoices.male?[voiceId] != nil {
            return "male"
        } else if categorizedVoices.special?[voiceId] != nil {
            return "special"
        }
        return nil
    }
}

// MARK: - 错误定义
enum MinimaxError: Error, LocalizedError {
    case invalidURL
    case noData
    case invalidResponse

    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "无效的URL"
        case .noData:
            return "没有返回数据"
        case .invalidResponse:
            return "无效的响应格式"
        }
    }
}
