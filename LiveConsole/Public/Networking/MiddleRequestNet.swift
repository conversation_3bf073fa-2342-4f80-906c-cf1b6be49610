//
//  MiddleRequestNet.swift
//  LivePlus
//
//  Created by Brian<PERSON><PERSON> on 2021/9/15.
//

import UIKit
import RxSwift
import Alamofire
private let disposeBag = DisposeBag()
let UserDefault_expired = "UserDefault_expired_vip_3"

class MiddleRequestNet: NSObject {
   
    /// 获取版本更新信息
    static func getVersionApiInfo() {

        let req: Observable<Status<BaseRespModel<VersionUpdateInfoModel>>> = APISession.get(APIURL.GET_version_info, parameters: ["channel":"AppStore"], cache: false).asObservable()

        req.subscribe(onNext: {   status in
            switch status {
            case .success(let res):
                if res.code == 0 {
                    guard let data = res.data else { return }
                    LCLog.d("--取得版本更新信息 ok--")
                    hanleUpdateAlert(with: data)
                    data.saveVersionUpdateInfo()
                } else {
                    if let meg = res.msg {
                        HUD.showFail(meg)
                    }
                }
            case .failure(let error):
                LCLog.d(error.localizedDescription)
                HUD.showFail(error.localizedDescription)
            case .pending: break
            }
            
        }).disposed(by: disposeBag)
    }
    
    static func hanleUpdateAlert(with data:VersionUpdateInfoModel) {
        if let forceUpdate = data.forceUpdate, forceUpdate { // 强制更新 必须弹
            realShowUpdatePop(data: data)
            return
        }
        guard let versionLocal = VersionUpdateInfoModel.currentVersionUpdateInfo() else { // 本地无内容
            realShowUpdatePop(data: data)
            return
        }
        if versionLocal.newVersion != data.newVersion { // 本地版本号与新版本号不对应
            realShowUpdatePop(data: data)
        }
    }
    
    static func realShowUpdatePop(data: VersionUpdateInfoModel) {
        if let isUpdate = data.isUpdate, isUpdate, !VersionUpdateAlert.isHasShowVersionAlert {
            let versionAlert = VersionUpdateAlert(frame: UIScreen.main.bounds)
            versionAlert.show(with: data)
        }
    }
    
    /// 获取全局配置接口
    static func getConfigApiData(callback: ((_ isPassValite: Bool) -> Void)? = nil) {
        let curVersion = LCDevice.KBundleShortVersionString
        let param = ["channel": "20000", "currentVersion": curVersion]
        let req: Observable<Status<BaseRespModel<ConfigModel>>> = APISession.get(APIURL.GET_CONFIG, parameters: param, cache: false).asObservable()
        req.subscribe(onNext: { status in
            var isPass = true
            switch status {
            case .success(let model):
                if model.code == 0 {
                    guard let data = model.data else { return }
                    LCLog.d("--取得公共配置 ok--")
                    data.saveUserConfig()
    
                    isPass = false
                } else {
                    if model.code == 1002 {
                        //登录状态过期
                        UserInfo.logout()
                    }
                    if let meg = model.msg {
                        HUD.showFail(meg)
                    }
                }
            case .failure(let error):
                LCLog.d(error.localizedDescription)
                HUD.showFail(error.localizedDescription)
            case .pending: break
            }
            if callback != nil {
                callback!(isPass)
            }
            
        }).disposed(by: disposeBag)
    }
    
    /// 获取基础版本数据
    static func requestLimit() {
//        if !UserInfo.isMember, !UserInfo.isTrialVip {
//            return
//        }
//        guard let userInfo = UserInfo.currentUser(),
//              let vipLevelId = userInfo.vipLevelId?.rawValue else { return }
//        let vipLevel = String(vipLevelId)
//        let limitRequest = APIURL.GET_LIMIT + vipLevel
//        let req: Observable<Status<BaseRespModel<LimitModel>>> = APISession.get(limitRequest, hostType: .version, parameters: [:]).asObservable()
//        req.subscribe(onNext: {  status in
//            switch status {
//            case .success(let result):
//                if let model = result.data {
//                    print("获取基础配置成功:\(model)")
//                    LimitManager.limits = model
//                    model.saveLocal()
//                }
//            case .failure(let error):
//                LCLog.d(error.localizedDescription)
//            case .pending: break
//            }
//            
//        }).disposed(by: disposeBag)
        
        // 获取
    }
    
    enum CycleImageOption: String {
        case system = "1"
        case home = "2"
        case homeVideo = "3"
    }
    // MARK:-  轮播图
    static func getCycleImages(option: CycleImageOption = .system, completion: @escaping ((_ models: CycleImages) -> Void)) {
        var modelList: CycleImages = CycleImages(slideshows: [])

        let req: Observable<Status<BaseRespModel<CycleImages>>> = APISession.get(APIURL.GET_cycle_images, hostType: .version, parameters: ["scene": option.rawValue], cache: true).asObservable()
        req.subscribe(onNext: {  status in
            switch status {
            case .success(let res):
                if res.code == 0 {
                    guard let list = res.data else { return }
                    modelList = list
                }
            case .failure(let error):
                LCLog.d(error.localizedDescription)
                HUD.showFail(error.localizedDescription)
            case .pending: break
            }
            completion(modelList)
        }).disposed(by: disposeBag)
    }
    
    /// 获取会员信息  网络  检查会员是否过期。
    ///  isPass：true 没有过期 false 过期或者没有获取到
    ///  isShowTip 是否弹窗提示
    class func getUserInfoApiData(callback: ((_ isPassValite: Bool) -> Void)? = nil, isShowTip: Bool = true) {
        
        if var usrModel = UserInfo.currentUser() {
            let req: Observable<Status<BaseRespModel<UserInfo>>> = APISession.get(APIURL.GET_user_info, cache: false).asObservable()
            req.subscribe(onNext: { status in
                var isPass = false
                switch status {
                case .success(let res):
                    switch res.code {
                    case 0:
                        if let user = res.data {
                            // 先判断手机号是否是真正的手机号 ，如果不是 就强制退出
                            if let phone = user.phone, let old = usrModel.phone {
                                if phone != old {
                                    HUD.showFail("当前监测您的账号有安全风险，请用您的会员手机号重新登录。")
                                    UserInfo.logout()
                                    NotificationCenter.default.post(name: LCKey.noti_logout, object: nil)
                                    DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
                                        exit(0)
                                    }
                                    return
                                }
                            }
                            
                            //这里这样设置是因为没有返回token等其他值，后面优化遍历属性设置
                            usrModel.expireFlag = user.expireFlag
                            usrModel.offiSubscribe = user.offiSubscribe
                            usrModel.needBindWechat = user.needBindWechat
                            usrModel.vipLevelId = user.vipLevelId
                            usrModel.memberInfoList = user.memberInfoList
                            usrModel.staffService = user.staffService
                            usrModel.staffServiceDays = user.staffServiceDays
                            usrModel.audioInfo = user.audioInfo
                            usrModel.nickName = user.nickName
                            usrModel.phone = user.phone
                            //....   新增试用会员信息
                            usrModel.trialVipExpireFlag = user.trialVipExpireFlag // 试用会员是否过期
                            usrModel.trialVipLevelId = user.trialVipLevelId // // 试用会员等级
                            usrModel.trialVipLevel = user.trialVipLevel // 试用会员名称
                            usrModel.trialVipLevels = user.trialVipLevels  // 试用会员详情]
                            usrModel.trialExpireDays = user.trialExpireDays // 试用会员剩余天数
                            usrModel.trialExpireTime = user.trialExpireTime  // 试用
                            
                            usrModel.saveUser()
                            if let expireFlag = user.expireFlag, expireFlag, isShowTip {
//                                LCLog.d("----lllllll")
//                                let tipView = ActivationCodeTip.shared
//                                tipView.present()
//                                self.alertExpired()
                                isPass = true
                            } else {
                                isPass = true
                            }
                        } else {
                            if let mesg = res.msg {
                                HUD.showFail(mesg)
                            }
                        }
                    case 1002:
                        HUD.showFail(res.msg ?? "登录失效，请重新登录")
                        UserInfo.logout()
                        NotificationCenter.default.post(name: LCKey.noti_logout, object: nil)
                    case 1003, 1004:
                        HUD.showFail("登录失效，请重新登录")
                        UserInfo.logout()
                        NotificationCenter.default.post(name: LCKey.noti_logout, object: nil)
                    case 7004:
                        // 非法请求时间
                        DispatchQueue.main.async {
                            HUD.showFail(res.msg ?? "非法请求时间")
                        }
                        
                    default:
                        HUD.showFail(res.msg ?? "用户信息获取失败，请重新登录")
                        
                    }
                   
                case .failure(let error):
                    LCLog.d(error.localizedDescription)
                    HUD.showFail(error.localizedDescription)
                case .pending: break
                }
                if callback != nil {
                    callback!(isPass)
                }
            }).disposed(by: disposeBag)
        }
    }
    
    class func getUserInfo(token: String, callback: @escaping ((_ isPassValite: Bool) -> Void)) {
        
        let req: Observable<Status<BaseRespModel<UserInfo>>> = APISession.get(APIURL.GET_user_info, cache: false, tmptoken: token).asObservable()
        req.subscribe(onNext: { status in
            switch status {
            case .success(let res):
                switch res.code {
                case 0:
                    if var user = res.data {
                        user.token = token
                        user.saveUser()
                       
                    } else {
                        if let mesg = res.msg {
                            HUD.showFail(mesg)
                        }
                    }
                    callback(true)
                case 1002:
                    
                    UserInfo.logout()
                    NotificationCenter.default.post(name: LCKey.noti_logout, object: nil)
                    callback(false)
                case 1003, 1004:
                    HUD.showFail("登录失效，请重新登录")
                    UserInfo.logout()
                    NotificationCenter.default.post(name: LCKey.noti_logout, object: nil)
                    callback(false)
                case 7004:
                    // 非法请求时间
                    DispatchQueue.main.async {
                        HUD.showFail(res.msg ?? "非法请求时间")
                    }
                    callback(false)
                default:
                    HUD.showFail(res.msg ?? "用户信息获取失败，请重新登录")
                    callback(false)
                }
                
            case .failure(let error):
                LCLog.d(error.localizedDescription)
                HUD.showFail(error.localizedDescription)
                callback(false)
            case .pending: break
            }
            
        }).disposed(by: disposeBag)
        
    }
    /**
     {
         "reqId": "",
         "code": 0,
         "msg": "操作成功",
         "data": [{
             "originalPrice": 30.00,
             "name": "1个月",
             "discountPrice": 28.00,
             "mainPic": "",
             "sku": "zbjj_vip_month_001"
         }, {
             "originalPrice": 60.00,
             "name": "3个月",
             "discountPrice": 58.00,
             "mainPic": "",
             "sku": "zbjj_vip_season_001"
         }, {
             "originalPrice": 90.00,
             "name": "6个月",
             "discountPrice": 88.00,
             "mainPic": "",
             "sku": "zbjj_vip_half_year_001"
         }, {
             "originalPrice": 180.00,
             "name": "12个月",
             "discountPrice": 178.00,
             "mainPic": "",
             "sku": "zbjj_vip_year_001"
         }]

     */
    // MARK:-  获取购买列表
    static func getPurchasingList(completion: @escaping ((_ models: [PurchasingModel]) -> Void)) {

        let req: Observable<Status<BaseRespModel<[PurchasingModel]>>> = APISession.get(APIURL.GET_Purchasing_list).asObservable()
        req.subscribe(onNext: {  status in
            switch status {
            case .success(let res):
                if res.code == 0 {
                    guard let list = res.data else {
                        completion([])
                        return
                    }
                    completion(list)
                }
            case .failure(let error):
                completion([])
                LCLog.d(error.localizedDescription)
                HUD.showFail(error.localizedDescription)
            case .pending: break
            }
        }, onError: { error in
            completion([])
            LCLog.d(error.localizedDescription)
            HUD.showFail(error.localizedDescription)
        }).disposed(by: disposeBag)
    }
    
    
    // MARK:-  获取购买列表 getPurchasingList
    static func getPointsProductsList(completion: @escaping ((_ models: [PointsProductsModel]) -> Void)) {

        let req: Observable<Status<BaseRespModel<[PointsProductsModel]>>> = APISession.get(APIURL.GET_products_audio_list, parameters: ["type": "points"]).asObservable()
        req.subscribe(onNext: {  status in
            switch status {
            case .success(let res):
                if res.code == 0 {
                    guard let list = res.data else {
                        completion([])
                        return
                    }
                    completion(list)
                }
            case .failure(let error):
                completion([])
                LCLog.d(error.localizedDescription)
                HUD.showFail(error.localizedDescription)
            case .pending: break
            }

        }, onError: { error in
            completion([])
            LCLog.d(error.localizedDescription)
            HUD.showFail(error.localizedDescription)
        }).disposed(by: disposeBag)
    }
    
    /**
     {
     "code": "",
     "type": 1
     }
     type = 1 是正式, type = 0 测试
     */
    // MARK: - 购买验证
    static func purchasingValidation(param: [String: Any] = [:], completion: ((_ status: Int, _ phone: String) -> Void)? = nil) {
        LCLog.d("---服务端验证凭证---")
        let req: Observable<Status<BaseRespModel<String>>> = APISession.post(APIURL.Purchasing_validation, parameters: param).asObservable()
        req.subscribe(onNext: {  status in
            switch status {
            case .success(let res):
                if res.code == 0 {
//                    guard let list = res.data else { return }
                }
                guard let cp = completion else {
                    return
                }
                cp(res.code ?? 0, res.data ?? "")
            case .failure(let error):
                LCLog.d(error.localizedDescription)
                HUD.showFail(error.localizedDescription)
                guard let cp = completion else {
                    return
                }
                cp(-1001, "")
            case .pending: break
            }
            
        }).disposed(by: disposeBag)
    }
    
    // MARK: - 服务器订阅验证
    static func serverSubscribeVerify() {
        let req: Observable<Status<BaseRespModel<SubscribeVerify>>> = APISession.get(APIURL.Get_Subscribe_Verify).asObservable()
        req.subscribe(onNext: {  status in
            switch status {
            case .success(let res):
                if res.code == 0 {
                    
                }
            case .failure(let error):
                LCLog.d(error.localizedDescription)
                
            case .pending: break
            }
            
        }).disposed(by: disposeBag)
    }
    
//    /// 获取
//    static func getLiveRoomGuideImages(callback: ((_ models: [LiveRoomImagesModel]) -> Void)? = nil) {
//
//    }
    
    // MARK: - 是否需要限制购买
    static func orderLimit(isMember:Bool = true, completion: @escaping  ((_ result: Bool) -> Void)) {
        let url = isMember ? APIURL.GET_Member_Order_limit : APIURL.GET_Points_limit
        LCLog.d("---是否需要限制购买---")
        let req: Observable<Status<BaseRespModel<OrderLimitModel>>> = APISession.get(url).asObservable()
        req.subscribe(onNext: {  status in
            switch status {
            case .success(let res):
                if res.code == 0 {
                    completion(true)
                } else {
                    completion(false)
                    HUD.showFail(res.msg ?? "校验失败")
                }
               
            case .failure(let error):
                LCLog.d(error.localizedDescription)
                HUD.showFail(error.localizedDescription)
                completion(false)
            case .pending: break
            }
            
        },onError: { error in
            LCLog.d(error.localizedDescription)
            HUD.showFail(error.localizedDescription)
            completion(false)
        }).disposed(by: disposeBag)
    }
    
    
    // MARK: - 上报用户内购支付信息
    static func reportToSeverPayLog(log: String, completion: (() -> Void)? = nil) {
        LCLog.d("---内购日志上报---")
        let req: Observable<Status<BaseRespModel<String?>>> = APISession.post(APIURL.POST_Upload_Log, parameters: ["payLog": log]).asObservable()
        req.subscribe(onNext: {  status in
            switch status {
            case .success( _):
                DispatchQueue.main.async {
//                    LCLog.d("---内购日志上报成功---")
                    HUD.showInfo("---内购日志上报成功---")
                }
               
            case .failure(let error):
                DispatchQueue.main.async {
//                    LCLog.d("---内购日志上报成功---")
                    HUD.showInfo("---内购日志上报失败---")
                }
                LCLog.d(error.localizedDescription)
                HUD.showFail(error.localizedDescription)
               
            case .pending: break
            }
            
        }).disposed(by: disposeBag)
    }
    
}


var expiredAlertShowing = false

extension MiddleRequestNet {
    
    // MARK: - 获取JS的配置
    static func getJSConfig(callback: @escaping ((ConfigEncModel) -> Void)) {
        // @陈世民(smchen) https://docs.quickleading.com/docs/ckzs/ios/js_config.enc
        
        let req: Observable<Status<ConfigEncModel>> = APISession.get(APIURL.GET_JS_Config, hostType: .docs).asObservable()
        req.subscribe(onNext: {  status in
            switch status {
            case .success(let result):
                callback(result)
            case .failure(let error):
                LCLog.d(error.localizedDescription)
            case .pending: break
            }
            
        }).disposed(by: disposeBag)
    }
    
    static func getApiConfig(callback: @escaping ((ConfigEncModel?) -> Void)) {
        // @陈世民(smchen) https://docs.quickleading.com/docs/ckzs/ios/api_config.enc
        
        let req: Observable<Status<ConfigEncModel>> = APISession.get(APIURL.GET_API_Config, hostType: .docs).asObservable()
        req.subscribe(onNext: {  status in
            switch status {
            case .success(let result):
                callback(result)
            case .failure(let error):
                LCLog.d(error.localizedDescription)
                callback(nil)
            case .pending: break
            }
            
        }, onError: { error in
            callback(nil)
            HUD.showFail(error.localizedDescription)
        }).disposed(by: disposeBag)
    }
    
}

// MARK: - 扫码登录
extension MiddleRequestNet {
    //
    static func getQRScan(uuid: String, userid: String, completion: @escaping ((_ mode: QRScanModel) -> Void)) {

        let req: Observable<Status<BaseRespModel<UserCode?>>> = APISession.get(APIURL.GET_Qr_Scan, parameters: ["uuid": uuid, "userId": userid ]).asObservable()
        req.subscribe(onNext: {  status in
            switch status {
            case .success( let res):
               completion(QRScanModel(code: res.code, msg: res.msg))
            case .failure(let error):
                completion(QRScanModel(code: -1, msg: error.localizedDescription))
                LCLog.d(error.localizedDescription)
            case .pending: break
            }
            
        }).disposed(by: disposeBag)
    }
    
    
    static func qrConfirm(uuid: String, userid: String, action: Qr_ConfirmAction,  completion: @escaping ((_ mode: QRScanModel) -> Void)) {

        let req: Observable<Status<BaseRespModel<UserCode?>>> = APISession.post(APIURL.POST_Qr_Confirm, parameters: ["uuid": uuid, "userId": userid , "action": action.rawValue]).asObservable()
        req.subscribe(onNext: {  status in
            switch status {
            case .success( let res):
               completion(QRScanModel(code: res.code, msg: res.msg))
            case .failure(let error):
                completion(QRScanModel(code: -1, msg: error.localizedDescription))
                LCLog.d(error.localizedDescription)
            case .pending: break
            }
            
        }).disposed(by: disposeBag)
    }
    
    //  GET /api/v1/qr/info 获取二维码内容
    static func getQRInfo(completion: @escaping ((_ code: String?) -> Void)) {

        let req: Observable<Status<BaseRespModel<QRInfoModel>>> = APISession.get(APIURL.GET_Qr_Info).asObservable()
        req.subscribe(onNext: {  status in
            switch status {
            case .success( let res):
                if res.code == 0 {
                    completion(res.data?.uuid)
                } else {
                    completion(nil)
                }
            case .failure(let error):
                completion(nil)
                LCLog.d(error.localizedDescription)
            case .pending: break
            }
            
        }).disposed(by: disposeBag)
    }
    
    // GET /api/v1/qr/checkout  轮询接口，用户判断扫码状态
    
    static func qrCheckout(uuid: String, completion: @escaping ((_ result: QRCheckout_Result) -> Void)) {

        let req: Observable<Status<BaseRespModel<QRCheckoutModel>>> = APISession.get(APIURL.GET_Qr_Checkout, parameters: ["uuid":uuid]).asObservable()
        req.subscribe(onNext: {  status in
            switch status {
            case .success( let res):
                completion(QRCheckout_Result(code: res.code, msg: res.msg, token: res.data?.token))
            case .failure(let error):
                completion(QRCheckout_Result())
                LCLog.d(error.localizedDescription)
            case .pending: break
            }
            
        }).disposed(by: disposeBag)
    }
    
}


extension MiddleRequestNet {
    //  礼物列表
    static func getDouyinGifts(completion: ((_ data: DouyinGiftModel?) -> Void)? = nil)  {
        
        let req: Observable<Status<BaseRespModel<DouyinGiftModel>>> = APISession.get(APIURL.GET_Douyin_Gift).asObservable()
        req.subscribe(onNext: {  status in
            switch status {
            case .success(let res):
                guard let data = res.data, let code = res.code else { return }
                if code == 0 {
                    completion?(data ?? nil)
                } else {
                    completion?(nil )
                }
            case .failure(let error):
                HUD.showFail(error.localizedDescription)
                completion?(nil )
            case .pending: break
            }
       
        }).disposed(by: disposeBag)
    }
}

let USER_SPEAK_ID_DEFAULT_KEY = "USER_SPEAK_ID_DEFAULT_KEY"

struct UserCloneMinimaxModle: Codable {
    var voiceId: String
    var demoUrl: String?
    
    static var getAllUserDefaultModels: [UserCloneMinimaxModle] {
        
    }
    
    static func appendNewModel(model: UserCloneMinimaxModle) {
        
    }
}

extension MiddleRequestNet {
    // 获取克隆音色列表
    static func getSpeakIDList(completion: ((_ data: AudioSpkIdModel?) -> Void)? = nil)  {
        MinimaxVoiceService.shared.getAllVoices { result in
            switch result {
            case .success(let voices):
                print("获取到 \(voices.count) 个音色")
                let preModels = voices.map {
                    AudioPreSpeakModel(voiceType: $0.voiceId,
                                       voiceName: $0.voiceName,
                                       voiceUrl: "")
                }
                var userModels: [AudioUserSpeakModel] = []
                if let spkIds = UserDefaults.standard.value(forKey: USER_SPEAK_ID_DEFAULT_KEY) as? [String] {
                    userModels = spkIds.map {
                        AudioUserSpeakModel(spkId: $0,
                                            spkName: "Minimax-克隆",
                                            spkStatus: 1,
                                            statusStr: "success")
                    }
                }
                completion?(AudioSpkIdModel(userSpk: userModels,
                                           preSpk: preModels))
            case .failure(let error):
                print("错误: \(error.localizedDescription)")
            }
        }
        
//        let req: Observable<Status<BaseRespModel<AudioSpkIdModel>>> = APISession.get(APIURL.GET_Audio_SPKID).asObservable()
//        req.subscribe(onNext: {  status in
//            switch status {
//            case .success(let res):
//                guard let data = res.data, let code = res.code else { return }
//                if code == 0 {
//                    completion?(data)
//                } else {
//                    completion?(nil)
//                    DispatchQueue.main.async {
//                        HUD.showFail(res.msg ?? "无法验证此压缩包")
//                    }
//                }
//            case .failure(let error):
//                HUD.showFail(error.localizedDescription)
//                completion?(nil)
//            case .pending: break
//            }
//       
//        }).disposed(by: disposeBag)
    }
    
    // MARK: - 合成语音 同步接口
    /// type: nick、timbre。nick为提前录制，timbre为实时合成
    static func combinePresetAudio(voiceType: String,
                                   text: String,
                                   completion: ((_ data: AudioPresetCombineModel?) -> Void)? = nil)  {
        MinimaxVoiceService.shared.generateSpeech(text: text,
                                                  voiceId: voiceType) { result in
            switch result {
            case .success(let response):
                let result = AudioPresetCombineModel(audioName: response.data.tosFileName,
                                                     points: 100000,
                                                     preLen: response.data.extraInfo.usageCharacters)
                completion?(result)
            case .failure(let error):
                completion?(nil)
                HUD.showFail(error.localizedDescription)
            }
        }
        
//        let parameters: [String: Any] = ["voiceType": voiceType,
//                                         "text": text,
//                                         "type": "nick",
//                                         "deduct": false]
//        
//        let req: Observable<Status<BaseRespModel<AudioPresetCombineModel>>> = APISession.post(APIURL.Post_Audio_Combine_Preset, parameters: parameters).asObservable()
//        req.subscribe(onNext: {  status in
//            switch status {
//            case .success(let res):
//                guard let data = res.data, let code = res.code else {
//                    completion?(nil)
//                    return
//                }
//                if code == 0 {
//                    completion?(data)
//                } else {
//                    completion?(nil)
//                    DispatchQueue.main.async {
//                        HUD.showFail(res.msg ?? "无法验证此压缩包")
//                    }
//                }
//            case .failure(let error):
//                completion?(nil)
//                HUD.showFail(error.localizedDescription)
//            case .pending: break
//            }
//       
//        }, onError: { error in
//            completion?(nil)
//            HUD.showFail(error.localizedDescription)
//        }).disposed(by: disposeBag)
    }
    
    // MARK: - 合成语音 同步接口
    /// 合成克隆语音
    static func combineCloneAudio(spkId: String,
                                   text: String,
                                   completion: ((_ data: AudioCloneCombineModel?) -> Void)? = nil)  {
        MinimaxVoiceService.shared.generateSpeech(text: text,
                                                  voiceId: spkId) { result in
            switch result {
            case .success(let response):
                let result = AudioCloneCombineModel(audioName: response.data.tosFileName,
                                                     points: 100000,
                                                     preLen: response.data.extraInfo.usageCharacters)
                completion?(result)
            case .failure(let error):
                completion?(nil)
                HUD.showFail(error.localizedDescription)
            }
        }
//        let parameters: [String: Any] = ["spkId": spkId,
//                                         "text": text,
//                                         "type": "nick",
//                                         "deduct": false]
//        let req: Observable<Status<BaseRespModel<AudioCloneCombineModel>>> = APISession.post(APIURL.Post_Audio_Combine_Clone, parameters: parameters).asObservable()
//        req.subscribe(onNext: {  status in
//            switch status {
//            case .success(let res):
//                guard let data = res.data, let code = res.code else {
//                    completion?(nil)
//                    return
//                }
//                if code == 0 {
//                    completion?(data)
//                } else {
//                    completion?(nil)
//                    DispatchQueue.main.async {
//                        HUD.showFail(res.msg ?? "无法验证此压缩包")
//                    }
//                }
//            case .failure(let error):
//                completion?(nil)
//                HUD.showFail(error.localizedDescription)
//            case .pending: break
//            }
//       
//        }, onError: { error in
//            completion?(nil)
//            HUD.showFail(error.localizedDescription)
//        }).disposed(by: disposeBag)
    }
    
    // MARK: - 合成语音 异步接口
    // EmptyResponseModel
    static func submitTTSVoiceAsyn(model: TTSReqModel, deduct: Bool = true,  completion: ((_ data: AsyncCloneTtsModel?) -> Void)? = nil){
        
        MinimaxVoiceService.shared.generateSpeechAsync(text: model.text,
                                                       voiceId: model.spkId) { result in
            switch result {
            case .success(let response):
                let result = AsyncCloneTtsModel(taskId: response.taskId, balanceWord: 10000)
                completion?(result)
            case .failure(let error):
                completion?(nil)
                HUD.showFail(error.localizedDescription)
            }
        }
        
//        var parameters: [String: Any] = ["text": model.text,
//                                         "type": model.isUseForName ? "nick" : "timbre",
//                                         "deduct": deduct]
//        
//        if model.isPreTts {
//            parameters["voiceType"] = model.spkId
//        } else {
//            parameters["spkId"] = model.spkId
//        }
//        let url = model.isPreTts ? APIURL.POST_Clone_asyncPreTtsSynthesis : APIURL.POST_Clone_asyncCloneTtsSynthesis
//        
//        LCLog.d("请求参数：\(parameters)")
//        
//        let req: Observable<Status<BaseRespModel<AsyncCloneTtsModel>>> = APISession.post(url, parameters: parameters).asObservable()
//        req.subscribe(onNext: {  status in
//            switch status {
//            case .success(let res):
//                guard let code = res.code else {
//                    completion?(nil)
//                    return
//                }
//                if code == 0 {
//                    completion?(res.data)
//                } else {
//                    completion?(nil)
//                    DispatchQueue.main.async {
//                        HUD.showFail(res.msg ?? "无法验证此压缩包")
//                    }
//                }
//            case .failure(let error):
//                completion?(nil)
//                HUD.showFail(error.localizedDescription)
//                
//            case .pending: break
//            }
//       
//        }, onError: { error in
//            completion?(nil)
//            HUD.showFail(error.localizedDescription)
//            
//        }).disposed(by: disposeBag)
        
    }
    

    /// 更新音色名
    static func updateCloneName(name: String, spkId: String, completion: ((_ successed: Bool) -> Void)? = nil)  {
        let parameters: [String: Any] = ["type": "update",
                                         "spkName": name,
                                         "spkId": spkId]
        completion?(false)
        HUD.showFail("Demo未实现此功能")
//        let req: Observable<Status<BaseRespModel<EmptyResponseModel>>> = APISession.post(APIURL.POST_Clone_SpkId, parameters: parameters).asObservable()
//        req.subscribe(onNext: {  status in
//            switch status {
//            case .success(let res):
//                guard let code = res.code else { return }
//                if code == 0 {
//                    completion?(true)
//                } else {
//                    completion?(false)
//                    DispatchQueue.main.async {
//                        HUD.showFail(res.msg ?? "修改失败")
//                    }
//                }
//            case .failure(let error):
//                completion?(false )
//                HUD.showFail(error.localizedDescription)
//                
//            case .pending: break
//            }
//       
//        }, onError: { error in
//            completion?(false )
//            DispatchQueue.main.async {
//                HUD.showFail(error.localizedDescription)
//            }
//        }).disposed(by: disposeBag)
    }
    
    /// 删除音色
    static func deleteCloneName(name: String, spkId: String, completion: ((_ successed: Bool) -> Void)? = nil)  {
        completion?(false)
        HUD.showFail("Demo未实现此功能")

//        let parameters: [String: Any] = ["type": "delete",
//                                         "spkName": name,
//                                         "spkId": spkId]
//        let req: Observable<Status<BaseRespModel<EmptyResponseModel>>> = APISession.post(APIURL.POST_Clone_SpkId, parameters: parameters).asObservable()
//        req.subscribe(onNext: {  status in
//            switch status {
//            case .success(let res):
//                guard let code = res.code else { return }
//                if code == 0 {
//                    completion?(true)
//                } else {
//                    completion?(false)
//                    DispatchQueue.main.async {
//                        HUD.showFail(res.msg ?? "删除失败")
//                    }
//                }
//            case .failure(let error):
//                HUD.showFail(error.localizedDescription)
//                completion?(false )
//            case .pending: break
//            }
//       
//        }).disposed(by: disposeBag)
    }
    
    /// 上传音色
    static func uploadCloneAudio(name: String,
                                 spkId: Int,
                                 bucketFileName: String,
                                 text: String,
                                 completion: ((_ result: AudioTrainResultModel?) -> Void)? = nil)  {
        
        
        
//        let parameters: [String: Any] = ["spkName": name,
//                                         "preTextId": spkId,
//                                         "bucketFileName": bucketFileName,
//                                         "userText":text]
//        let req: Observable<Status<BaseRespModel<AudioTrainResultModel>>> = APISession.post(APIURL.POST_Clone_Train, parameters: parameters).asObservable()
//        req.subscribe(onNext: {  status in
//            switch status {
//            case .success(let res):
//                guard let code = res.code else { return }
//                if code == 0 {
//                    completion?(res.data)
//                } else {
//                    completion?(nil)
//                    DispatchQueue.main.async {
//                        HUD.showFail(res.msg ?? "无法验证此压缩包")
//                    }
//                }
//            case .failure(let error):
//                completion?(nil)
//                HUD.showFail(error.localizedDescription)
//            case .pending: break
//            }
//       
//        }, onError: { error in
//            completion?(nil)
//            HUD.showFail(error.localizedDescription)
//        }).disposed(by: disposeBag)
    }
    
    /// 获取克隆进度
    static func getTrainAudioStatus(spkId: String,
                                 completion: ((_ result: AudioTrainResultModel?) -> Void)? = nil)  {
        let parameters: [String: Any] = ["spkId": spkId]
        let req: Observable<Status<BaseRespModel<AudioTrainResultModel>>> = APISession.post(APIURL.POST_Train_Status, parameters: parameters).asObservable()
        req.subscribe(onNext: {  status in
            switch status {
            case .success(let res):
                guard let data = res.data, let code = res.code else { return }
                if code == 0 {
                    completion?(data)
                } else {
                    completion?(nil)
                    DispatchQueue.main.async {
                        HUD.showFail(res.msg ?? "无法验证此压缩包")
                    }
                }
            case .failure(let error):
                HUD.showFail(error.localizedDescription)
                completion?(nil)
            case .pending: break
            }
       
        }).disposed(by: disposeBag)
    }

    /// 获取克隆文本的列表
    static func getCloneAudioTexts(completion: ((_ result: [AudioCloneTextModel]?) -> Void)? = nil)  {
        let req: Observable<Status<BaseRespModel<[AudioCloneTextModel]>>> = APISession.get(APIURL.GET_Clone_Texts, parameters: [:]).asObservable()
        req.subscribe(onNext: {  status in
            switch status {
            case .success(let res):
                guard let data = res.data, let code = res.code else { return }
                if code == 0 {
                    completion?(data)
                } else {
                    completion?(nil)
                    DispatchQueue.main.async {
                        HUD.showFail(res.msg ?? "无法验证此压缩包")
                    }
                }
            case .failure(let error):
                HUD.showFail(error.localizedDescription)
                completion?(nil)
            case .pending: break
            }
       
        }).disposed(by: disposeBag)
    }
    
    /// 获取合成进度
    static func getTTSStatusStatus(taskId: String,
                                   completion: ((_ result: AsyncTTSStatusModel?, _ code: Int) -> Void)? = nil)  {
        
        let parameters: [String: Any] = ["taskId": taskId]
        let req: Observable<Status<BaseRespModel<AsyncTTSStatusModel>>> = APISession.get(APIURL.GET_Clone_Status, parameters: parameters).asObservable()
        req.subscribe(onNext: {  status in
            switch status {
            case .success(let res):
                guard let data = res.data, let code = res.code else { return }
                if code == 0 {
                    completion?(data, 0)
                } else {
                    completion?(nil, code)
                    DispatchQueue.main.async {
                        HUD.showFail(res.msg ?? "无法验证此压缩包")
                    }
                }
            case .failure(let error):
                HUD.showFail(error.localizedDescription)
                completion?(nil, -1001)
            case .pending: break
            }
       
        }).disposed(by: disposeBag)
    }
}

// MARK: - 积分统计
extension MiddleRequestNet {
    
    /// 积分记录
    static func getPiontsStatistics(completion: @escaping ((_ result: [PointsInfo]?) -> Void))  {
        
        let req: Observable<Status<BaseRespModel<[PointsInfo]>>> = APISession.get(APIURL.GET_Cloneaudio_Statistics).asObservable()
        req.subscribe(onNext: {  status in
            switch status {
            case .success(let res):
                guard let code = res.code else { return }
                if code == 0 {
                    completion(res.data)
                } else {
                    completion(nil)
                    DispatchQueue.main.async {
                        HUD.showFail(res.msg ?? "无法验证此压缩包")
                    }
                }
            case .failure(let error):
                HUD.showFail(error.localizedDescription)
                completion(nil)
            case .pending: break
            }
       
        }, onError: { error in
            HUD.showFail(error.localizedDescription)
            completion(nil)
        }).disposed(by: disposeBag)
    }
    
    /// 实时朗读用户积分
    static func ttsPiontsDeduct(completion: @escaping ((_ result: PointsDeduct?) -> Void))  {
        
        let req: Observable<Status<BaseRespModel<PointsDeduct>>> = APISession.post(APIURL.POST_point_deduct_unit).asObservable()
        req.subscribe(onNext: {  status in
            switch status {
            case .success(let res):
                guard  let code = res.code else { return }
                if code == 0 {
                    completion(res.data)
                } else {
                    completion(nil)
//                    DispatchQueue.main.async {
//                        HUD.showFail(res.msg ?? "无法验证此压缩包")
//                    }
                }
            case .failure(let error):
                completion(nil)
                HUD.showFail(error.localizedDescription)
            case .pending: break
            }
       
        }, onError: { error in
            completion(nil)
            HUD.showFail(error.localizedDescription)
        }).disposed(by: disposeBag)
    }
}
