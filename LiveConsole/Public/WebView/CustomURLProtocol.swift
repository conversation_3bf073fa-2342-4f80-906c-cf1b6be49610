//
//  CustomURLProtocol.swift
//  LivePlus
//
//  Created by iclick on 2021/9/8.
//

import UIKit


class CustomURLProtocol: URLProtocol {
    static let  URLProtocolHandledKey = "URLProtocolHandledKey"
    var connection: NSURLConnection?
    let customUrl = ""
    
    
   class func registerCustomerProtocol(){
        
        //注册自己的protocol
//        [NSURLProtocol registerClass:[CustomProtocol class]];
    URLProtocol.registerClass(CustomURLProtocol.self);
    
//    let cls: AnyClass? = NSClassFromString("WKBrowsingContextController")
//    let sel = NSSelectorFromString("registerSchemeForCustomProtocol:")
//    if cls?.responds(to: sel) != nil {
//        // 通过http和https的请求，同理可通过其他的Scheme 但是要满足ULR Loading System
//        cls?.perform(sel, with: "http");
//        cls?.perform(sel, with: "https");
//    }
    }
    
    override class func canInit(with request: URLRequest) -> Bool {
        //只处理http和https请求
        let scheme = request.url?.scheme
        if scheme?.caseInsensitiveCompare("http") == ComparisonResult.orderedSame,scheme?.caseInsensitiveCompare("https") == ComparisonResult.orderedSame {
            //看看是否已经处理过了，防止无限循环
            if URLProtocol.property(forKey: URLProtocolHandledKey, in: request) != nil {
                return false
            }
            guard let url = request.url else {
                return false
            }
//            if url == "ds" {
//                return true
//            }else{
//                return false
//            }

//            Log(@"relativePath -- %@",request.URL.relativePath);
//            LCLog.d("relativePath---\(String(request.url?.relativePath))")
            // 可以做一些网络请求的需要的事情(如:带cookie等等)
        }
          
           return true
    }
    
    //This method returns a canonical version of the given request.
    override class func canonicalRequest(for request: URLRequest) -> URLRequest {
        return request
    }
    
    //Compares two requests for equivalence with regard to caching.
    override class func requestIsCacheEquivalent(_ a: URLRequest, to b: URLRequest) -> Bool {
        return super.requestIsCacheEquivalent(a, to: b)
    }
  
    //Starts protocol-specific loading of a request.
    override func startLoading() {
//        let mutableReqeust = self.request;

        //打标签，防止无限循环
        
        print("Serving response from NSURLConnection")
      
        self.connection = NSURLConnection(request: self.request, delegate: self)
    }

    //tops protocol-specific loading of a request.
    override func stopLoading() {
        if self.connection != nil {
            self.connection?.cancel()
        }
        self.connection = nil
    }

    
}

