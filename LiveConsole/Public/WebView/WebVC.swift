//
//  WebVC.swift
//  LivePlus
//
//  Created by iclick on 2020/12/23.
//

import WebKit
//import WebViewJavascriptBridge

private let copyPcUpLoadUrl: String = "copyPcUpLoadUrl"
private let showLiveGuide: String = "showLiveGuide"

class WebVC: BaseVC, WKUIDelegate, WKNavigationDelegate, Routable, WKScriptMessageHandler {
    
//    var bridge:WebViewJavascriptBridge?
    var webView: WKWebView!
    var webTitleLabel: UILabel?
    var baseURl: String?
    var baseTitle: String?
    var baseRequest: URLRequest?
    var progressView: WebProgressLine!
    var reloadBtn: UIButton!
    var isShowProgress = true
    var titleView :UIView?
    
    // 下载隐私协议
    public var downloadBtn: UIButton!
    public var downloadUrl: String!
    
    private var showCloseButton = true
    private var showBackButton = true

    var isNeedStartNewWeb = true//web点击后是否需要跳转到新的controller (如果是，次级页面的标题从web里面取)
    var isHomeWeb = true //是否web主页面
    
    typealias injectCallback = () -> Void
    var copyCallback: injectCallback?
    var showCallback: injectCallback?
    var startTime: Double = 0.0

    var cachePolicy: URLRequest.CachePolicy = .reloadIgnoringLocalCacheData
    
    var cacheKey: String?
    var cacheVersion: String?
    
    // MARK: - Init
    required init?(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    @objc init(title: String?, url: String?,
               withProgressLine: Bool,
               needStartNewWeb: Bool,
               showCloseButton: Bool = false,
               showBackButton: Bool = true,
               showDownloadBtn: Bool = false
    ) {
        super.init(nibName: nil, bundle: nil)
        guard let url = url else {
            LCLog.d("错误：web加载的url为空")
            return
        }
        LCLog.d("web加载的url--"+url)
        baseTitle = title
        if baseTitle == nil {
            baseTitle = ""
        }
        baseURl = url
        isShowProgress = withProgressLine
        isNeedStartNewWeb = needStartNewWeb
        self.showCloseButton = showCloseButton
        self.showBackButton = showBackButton
        
        
        if showDownloadBtn {
            let navBar = getNavViewWithItem(titleStr: title, leftImageName: "icon_nav_back_gray", rightImageName: nil)
            downloadBtn = UIButton()
            downloadBtn.frame = CGRect(x: LCDevice.screenW - 80, y: 0, width: LCDevice.DIN_WIDTH(60), height: LCDevice.DIN_WIDTH(36))
            downloadBtn.bottom = LCDevice.Nav_H - 4
            downloadBtn.setTitle("下载", for: .normal)
            downloadBtn.setTitleColor(UIColor("#4D4E52"), for: .normal)
            downloadBtn.titleLabel?.font = LCDevice.DIN_Font_PF_M(14)
            downloadBtn.addTarget(self, action: #selector(downloadBtnAction), for: .touchUpInside)
            navBar.addSubview(downloadBtn)
            view.addSubview(navBar)
        }
    }

    static func initRouteParams(params: [String: Any]?) -> UIViewController {
        var vc: WebVC?
        if let urlStr = params?["url"] as? String {
            var title = ""
            let aTitle = params?["title"]
            if aTitle != nil {
                title = (aTitle as! NSString).byURLDecode()
            }
            vc = WebVC.init(title: title, url: urlStr, withProgressLine: true, needStartNewWeb: false)
        }
        return vc!
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        edgesForExtendedLayout = []
        automaticallyAdjustsScrollViewInsets = false

        fd_interactivePopDisabled = true
        
        setupWebView()
        setupTitleView()

        loadWebView(url: baseURl!)
        setupProgressView()
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        webView.configuration.userContentController.removeScriptMessageHandler(forName: copyPcUpLoadUrl)
        webView.configuration.userContentController.removeScriptMessageHandler(forName: showLiveGuide)
    }

    override func leftButtonAction() {
        if webView.canGoBack {
            webView.goBack()
        } else {
            navigationController?.popViewController(animated: true)
        }
    }
    
    override func closeButtonAction() {
        navigationController?.popViewController(animated: true)
    }
    
    // MARK:- UI
    func setupTitleView() {
        titleView = getNavViewWithItem(titleStr: baseTitle!, leftImageName: showBackButton ? "icon_nav_back_gray" : nil, rightImageName: nil, showClose: showCloseButton)
        webTitleLabel = titleView?.viewWithTag(kNaviTitleLabTag) as? UILabel
        view.addSubview(titleView!)
    }

    func setupWebView() {

        if webView != nil {
            return
        }

        let userContentController: WKUserContentController = WKUserContentController()
        userContentController.add(self, name: copyPcUpLoadUrl)
        userContentController.add(self, name: showLiveGuide)
        let config = WKWebViewConfiguration()
        config.preferences.minimumFontSize = 0.0
        config.userContentController = userContentController
        
        config.allowsInlineMediaPlayback = true
        config.allowsAirPlayForMediaPlayback = true
        config.allowsPictureInPictureMediaPlayback = true
        config.mediaTypesRequiringUserActionForPlayback = WKAudiovisualMediaTypes.all



        view.height = LCDevice.screenH - LCDevice.X_BOTTOM_INSET
        let webViewFrame = CGRect(x: 0, y: LCDevice.Nav_H, width: view.width, height: view.height - LCDevice.Nav_H)
        webView = WKWebView.init(frame: webViewFrame, configuration: config)
        webView.navigationDelegate = self
        webView.uiDelegate = self
        view.addSubview(webView)
        
        for subView in webView!.subviews {
            ///隐藏滚动条
            let v = subView
            if v.isKind(of: UIScrollView.self) == true {
                (v as! UIScrollView).showsHorizontalScrollIndicator = false
                (v as! UIScrollView).showsVerticalScrollIndicator = false
            }
        }

//        bridge = WebViewJavascriptBridge.init(forWebView: webView)
//        bridge?.setWebViewDelegate(self)
//
//        ///打开新页面
//        bridge?.registerHandler("callNativeStartNewWeb", handler: { (_ data:Any?, nil) in
//            if self.isNeedStartNewWeb {
//                let dic = data as! [String:String]
//                let newWeb = WebVC.init(title:"",url:dic["url"]!,withProgressLine:self.isShowProgress,needStartNewWeb:true)
//                newWeb.isHomeWeb = false
//                self.navigationController?.pushViewController(newWeb, animated: true)
//            }
//        })
//        bridge?.registerHandler("testObjcCallback", handler: { (_ data:Any?, nil) in
////            print(data)
//        })

        webView.addObserver(self, forKeyPath: "title", options: .new, context: nil)
        webView.addObserver(self, forKeyPath: "estimatedProgress", options: .new, context: nil)
    }

    func setupProgressView() {
        progressView = WebProgressLine.init(frame: CGRect(x: 0, y: LCDevice.Nav_H, width: view.width, height: 1))
        progressView.backgroundColor = UIColor("#6974F2")
        progressView.isHidden = true
        view.addSubview(progressView)
    }

    // MARK:- KVO
    override func observeValue(forKeyPath keyPath: String?, of object: Any?, change: [NSKeyValueChangeKey: Any]?, context: UnsafeMutableRawPointer?) {

        if keyPath == "title" {

            if !isNeedStartNewWeb {
                return
            }
//            if isHomeWeb {
//                return
//            }

            if webView.isEqual(object) {
                webTitleLabel?.text = webView.title
            } else {
                super.observeValue(forKeyPath: keyPath, of: object, change: change, context: context)
            }
        } else if keyPath == "estimatedProgress" {
            if webView.isEqual(object) {
                //                webProgressView.loadProgress(webView.estimatedProgress)

                if webView.estimatedProgress == 1.0 {
                    print("web--- end" + "\(NSDate())")
                    //                    webProgressView.isHidden = true
                    webView.isHidden = false
                } else {

                    //                    webProgressView.isHidden = false
                    webView.isHidden = true
                }

            } else {
                super.observeValue(forKeyPath: keyPath, of: object, change: change, context: context)
            }
        } else {
            super.observeValue(forKeyPath: keyPath, of: object, change: change, context: context)
        }

    }
    
    // MARK:- Private methods
    private func loadWebView(url: String) {

        if webView == nil {
            setupWebView()
        }
        let urlString = url
        if let reqURL = URL.init(string: urlString) {
            let request = URLRequest.init(url: reqURL, cachePolicy: cachePolicy, timeoutInterval: 15.0)
            baseRequest = request
            self.webView.load(baseRequest!)
        } else {
            DispatchQueue.main.asyncAfter(deadline: DispatchTime.now()+1.2) {[weak self] in
                self?.navigationController?.popViewController(animated: true)
            }
        }
    }
    
    // MARK:- Public methods
    func scrollToTop() {
        webView.scrollView.scrollToTop(animated: true)
    }

    // MARK:- WKNavigationDelegate
    func webView(_ webView: WKWebView, didFinish navigation: WKNavigation!) {
        if let key = cacheKey, let v = cacheVersion {
            UserDefaults.standard.setValue(v, forKey: key)
            LCLog.d("web： 完成加载保存：\(key),\(v)")
        }
      
        let endTime: Double = CFAbsoluteTimeGetCurrent()
        let time = Int(endTime * 1000 - startTime * 1000)
        LCLog.d("web： 完成加载:\(time)")
        if isShowProgress {
            progressView.endLoading()
        } else {
//            HUD.hideAllHUD()
        }
//        webView.evaluateJavaScript("document.documentElement.style.webkitTouchCallout='none';", completionHandler: nil)
//        webView.evaluateJavaScript("document.documentElement.style.webkitUserSelect='none';", completionHandler: nil)
    }

    func webView(_ webView: WKWebView, didFailProvisionalNavigation navigation: WKNavigation!, withError error: Error) {
        if isShowProgress {
            progressView.endLoading()
        } else {
//            HUD.showInfo("加载失败")
        }

        let aError = error as NSError
        if aError.code == NSURLErrorCancelled {
            return
        }

        if reloadBtn == nil {
            reloadBtn = UIButton.init(type: .custom)
            reloadBtn.frame = CGRect(x: (LCDevice.screenW - 300)/2, y: 200, width: 300, height: 50)
            reloadBtn.setTitle("点击重试", for: .normal)
            reloadBtn.setTitleColor(UIColor.black, for: .normal)
            reloadBtn.addTarget(self, action: #selector(reloadBtnAction), for: .touchUpInside)
        }
        view.addSubview(reloadBtn)
    }

    func webView(_ webView: WKWebView, didStartProvisionalNavigation navigation: WKNavigation!) {
        startTime = CFAbsoluteTimeGetCurrent()
        LCLog.d("web： 开始加载")
        if isShowProgress {
            progressView.startLoading()
        } else {
//            HUD.showWait()
        }
        
    }
    
   
    func webView(_ webView: WKWebView, decidePolicyFor navigationAction: WKNavigationAction, decisionHandler: @escaping (WKNavigationActionPolicy) -> Void) {
        guard let url =  navigationAction.request.url?.absoluteString, let sc =  navigationAction.request.url?.scheme  else {
            decisionHandler(.allow)
            return
        }
        LCLog.d("web加载的url:\(url)")
        
        // 微信的推广
        if url.contains("weixin"), sc == "weixin" {
            if !WXApi.isWXAppInstalled() {
                HUD.showInfo("请先安装微信后使用")
                decisionHandler(.cancel)
                return
                
            }
            if #available(iOS 10, *) {
                UIApplication.shared.open(URL.init(string: url)!, options: [:], completionHandler: nil)
            } else {
                UIApplication.shared.openURL(URL.init(string: url)!)
            }
            decisionHandler(.cancel)
            return
        } else if url.contains("snssdk1128") || url.contains("dingtalk://") {
            if #available(iOS 10, *) {
                UIApplication.shared.open(URL.init(string: url)!, options: [:], completionHandler: nil)
            } else {
                UIApplication.shared.openURL(URL.init(string: url)!)
            }
            decisionHandler(.cancel)
            return
        } else if url.contains("bilibili"), sc == "bilibili" {
            if #available(iOS 10, *) {
                UIApplication.shared.open(URL.init(string: url)!, options: [:], completionHandler: nil)
            } else {
                UIApplication.shared.openURL(URL.init(string: url)!)
            }
            decisionHandler(.cancel)
            return
        }
       
        decisionHandler(.allow)
    }
    
    func webView(_ webView: WKWebView, didReceive challenge: URLAuthenticationChallenge, completionHandler: @escaping (URLSession.AuthChallengeDisposition, URLCredential?) -> Void) {
        // 判断服务器采用的验证方法
        if challenge.protectionSpace.authenticationMethod == NSURLAuthenticationMethodServerTrust {
            if challenge.previousFailureCount == 0 {
                // 如果没有错误的情况下 创建一个凭证，并使用证书
                let credential = URLCredential(trust: challenge.protectionSpace.serverTrust!)
                completionHandler(.useCredential, credential)
            } else {
                // 验证失败，取消本次验证
                completionHandler(.cancelAuthenticationChallenge, nil)
            }
        } else {
            completionHandler(.cancelAuthenticationChallenge, nil)
        }
    }
    
    func webView(_ webView: WKWebView, createWebViewWith configuration: WKWebViewConfiguration, for navigationAction: WKNavigationAction, windowFeatures: WKWindowFeatures) -> WKWebView? {

        webView.load(navigationAction.request)
        return nil
//        let  newWebView =  WKWebView.init(frame: webView.bounds, configuration: configuration)
//
//        guard let frame =  navigationAction.targetFrame else {
//            newWebView.load(navigationAction.request);
//            newWebView.navigationDelegate = self;
//            newWebView.uiDelegate = self;
//            self.view .addSubview(newWebView)
//            return newWebView;
//        }
//
//        if (!frame.isMainFrame) {
//            newWebView.load(navigationAction.request);
//        }
//
//        newWebView.navigationDelegate = self;
//        newWebView.uiDelegate = self;
//        self.view .addSubview(newWebView)
//        return newWebView;
    }

    // MARK:- WKUIDelegate
    func webView(_ webView: WKWebView, runJavaScriptAlertPanelWithMessage message: String, initiatedByFrame frame: WKFrameInfo, completionHandler: @escaping () -> Void) {
//        HUD.showInfo(message)
        completionHandler()
    }

    func webView(_ webView: WKWebView, runJavaScriptConfirmPanelWithMessage message: String, initiatedByFrame frame: WKFrameInfo, completionHandler: @escaping (Bool) -> Void) {

        let alertVC = UIAlertController.init(title: "", message: message, preferredStyle: .alert)
        let okAction = UIAlertAction.init(title: "确定", style: .default, handler: { (action:UIAlertAction) in
            completionHandler(true)
        })
        alertVC.addAction(okAction)
        let cancelAction = UIAlertAction.init(title: "取消", style: .default, handler: { (action:UIAlertAction) in
            completionHandler(false)
        })
        alertVC.addAction(cancelAction)
        present(alertVC, animated: true, completion: nil)
    }

    func webView(_ webView: WKWebView, runJavaScriptTextInputPanelWithPrompt prompt: String, defaultText: String?, initiatedByFrame frame: WKFrameInfo, completionHandler: @escaping (String?) -> Void) {

    }

    /// JS拦截OC方法
    func userContentController(_ userContentController: WKUserContentController, didReceive message: WKScriptMessage) {
        print("name: \(message.name)")
        print("body: \(message.body)")
        
        switch message.name {
        case copyPcUpLoadUrl:
            if let copyCallback = copyCallback { copyCallback() }
        case showLiveGuide:
            if let showCallback = showCallback { showCallback() }
        default: break
        }
    }

    // MARK:- Btn action
    @objc func reloadBtnAction() {
        reloadBtn.removeFromSuperview()
        self.webView.load(baseRequest!)
    }

    // MARK:- Btn action
    @objc func downloadBtnAction() {
        Router.openPrivacyDown()
        
    }
    
    // MARK:- deinit
    deinit {
       
        guard let wev = webView else {
            return
        }
        wev.stopLoading()
        wev.removeObserver(self, forKeyPath: "title", context: nil)
        wev.removeObserver(self, forKeyPath: "estimatedProgress", context: nil)
        webView = nil
    }

}
