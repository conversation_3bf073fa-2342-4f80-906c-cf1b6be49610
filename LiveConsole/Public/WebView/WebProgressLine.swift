//
//  WebProgressLine.swift
//  LivePlus
//
//  Created by iclick on 2020/12/23.
//

import UIKit

class WebProgressLine: UIView {

    override init(frame: CGRect) {
        super.init(frame: frame)
        isHidden = true
        backgroundColor = UIColor.white
    }
    
    required init?(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    /// 开始加载动画
    func startLoading() {
        isHidden = false
        self.width = 0
        UIView.animate(withDuration: 0.4, animations: {
            self.width = LCDevice.screenW * 0.6
        }) { (finished) in
            UIView.animate(withDuration: 0.4, animations: {
                self.width = LCDevice.screenW * 0.8
            })
        }
    }
    
    /// 结束加载动画
    func endLoading() {
        UIView.animate(withDuration: 0.2, animations: {
            self.width = LCDevice.screenW
        }) { (finished) in
            self.isHidden = true
        }
    }
    
    /// 加载进度
    func progress(prog: CGFloat) {
        isHidden = false
        UIView.animate(withDuration: 0.08) {
            self.width = LCDevice.screenW * prog
        }
    }
}
