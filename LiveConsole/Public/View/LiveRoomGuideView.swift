//
//  LiveRoomGuideView.swift
//  LivePlus
//
//  Created by Brian<PERSON><PERSON> on 2021/12/17.
//

import UIKit
import BetterSegmentedControl

class LiveRoomGuideView: UIView {
    
    // MARK: - 属性
    public var bgView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        return view
    }()
    
    public var indexLbl: UILabel = {
        var lbl = UILabel()
        lbl.textColor = UIColor("#797B7D")
        lbl.isUserInteractionEnabled = true
        lbl.font = LCDevice.DIN_Font_PF_M(14)
        return lbl
    }()
    
    /// 背景的透明视图
    private var maskBaseView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor("#000000").alpha(value: 0.4)
        return view
    }()
    
    private lazy var nextButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitle("下一步", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.titleLabel?.font = LCDevice.DIN_Font_PF_M(14)
        button.backgroundColor = UIColor("#6974F2")
        button.cornerRadius = 13
        return button
    }()
    
    private lazy var imgView: UIImageView = {
        let view = UIImageView()
        view.isUserInteractionEnabled = true
        view.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(imgBtnAction)))
        return view
    }()
    
    // 完成按钮
    lazy var completeBtn: UIButton = {
        let button = commonButton()
        return button
    }()
    
    public var complted: (() -> Void)?
    
    private var isShowInView: Bool = false
    
    // 当前索引
    private var currentIndex: Int = 0 {
        didSet {
            indexLbl.text = "跳过(\(currentIndex+1)/\(imgArray.count))"
        }
    }
    
    public var imgArray: [LiveRoomImagesModel] = [] {
        didSet {
            if imgArray.count > 0 {
                indexLbl.text = "跳过(1/\(imgArray.count))"
                if let url = URL(string: imgArray[0].img) {
                    imgView.setImageWith(url, placeholder: nil)
                }
            }
        }
    }
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupBaseView()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func hitTest(_ point: CGPoint, with event: UIEvent?) -> UIView? {
        return super.hitTest(point, with: event)
    }
    
    override func point(inside point: CGPoint, with event: UIEvent?) -> Bool {
        if self.maskBaseView.frame.contains(point) {
            return true
        }
        return false
    }

    private func setupBaseView() {
        let maskVW: CGFloat = 300
        let maskVH: CGFloat = 520 + 60
        maskBaseView.frame = CGRect(x: 0, y: 0, width: LCDevice.screenW, height: LCDevice.screenH)
        addSubview(maskBaseView)
        bgView.frame = CGRect(x: (LCDevice.screenW - maskVW) / 2, y: (LCDevice.screenH - maskVH) / 2, width: maskVW, height: maskVH)
        bgView.cornerRadius = 15
        addSubview(bgView)
        let btnW: CGFloat = 82
        let btnH: CGFloat = 28
        nextButton.frame = CGRect(x: bgView.width - 24 - btnW, y: bgView.height - 16 - btnH, width: btnW, height: btnH)
        nextButton.addTarget(self, action: #selector(completeBtnAction(sender:)), for: .touchUpInside)
        bgView.addSubview(nextButton)
        let imgW: CGFloat = 300
        let imgH: CGFloat = 520
        imgView.frame = CGRect(x: 0, y: 0, width: imgW, height: imgH)
        bgView.addSubview(imgView)
        indexLbl.frame = CGRect(x: 24, y: maskVH - (14 + 23), width: 80, height: 14)
        bgView.addSubview(indexLbl)
        indexLbl.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(indexLblGesture(gesture:))))
        imgArray =  [LiveRoomImagesModel(imgUrl: "guide_img_add_bg", jumpUrl: ""),
                     LiveRoomImagesModel(imgUrl: "guide_img_ portrait", jumpUrl: ""),
                     LiveRoomImagesModel(imgUrl: "guide_ prospects_effect", jumpUrl: ""),
                     LiveRoomImagesModel(imgUrl: "guide_ area_list", jumpUrl: ""),
                     LiveRoomImagesModel(imgUrl: "guide_ scene", jumpUrl: ""),
                     LiveRoomImagesModel(imgUrl: "guide_ gesture_op", jumpUrl: ""),
                     LiveRoomImagesModel(imgUrl: "guide_ more", jumpUrl: ""),
                     LiveRoomImagesModel(imgUrl: "guide_dy_ premiere", jumpUrl: ""),
                     LiveRoomImagesModel(imgUrl: "guide_ shooting", jumpUrl: "")]
        
        let swipeLeft = UISwipeGestureRecognizer(target: self, action: #selector(swipeLeftAction))
        swipeLeft.direction = .left
        let swipeRight = UISwipeGestureRecognizer(target: self, action: #selector(swipeRightAction))
        swipeRight.direction = .right
        imgView.addGestureRecognizers([swipeLeft, swipeRight])
    }
    
    @objc func swipeLeftAction() {
        guard currentIndex < imgArray.count - 1 else { return }
        currentIndex = currentIndex + 1
        if currentIndex == imgArray.count - 1 {
            nextButton.setTitle("完成", for: .normal)
            indexLbl.text = "再看一遍"
        } else {
            nextButton.setTitle("下一步", for: .normal)
        }
        if currentIndex == imgArray.count {
            dismiss()
            if let completed = complted { completed() }
            return
        }
        if let url = URL(string: imgArray[currentIndex].img) {
            imgView.setImageWith(url, placeholder: nil)
        }
    }
    
    @objc func swipeRightAction() {
        guard currentIndex > 0 else { return }
        currentIndex = currentIndex - 1
        if let url = URL(string: imgArray[currentIndex].img) {
            imgView.setImageWith(url, placeholder: nil)
        }
        nextButton.setTitle("下一步", for: .normal)
    }
    
    private func commonButton() -> UIButton {
        let button = UIButton(type: .custom)
        button.setBackgroundImage(UIImage(color: UIColor("#000000").alpha(value: 0.2)), for: .normal)
        button.cornerRadius = 20
        button.borderWidth = 1
        button.borderColor = UIColor("#ffffff")
        return button
    }
    
    private func commonTextButton(text: String) -> UIButton {
        let button = UIButton(type: .custom)
        let attriStr = NSMutableAttributedString.init(string: text)
        attriStr.addAttributes([.underlineStyle: NSUnderlineStyle.single.rawValue, .foregroundColor: UIColor.white], range: NSRange(location: 0, length: attriStr.length))
        button.titleLabel?.attributedText  = attriStr
        button.setAttributedTitle(attriStr, for: .normal)
        return button
    }
    
    private func commonTextLable(text: String, frame: CGRect) -> UILabel {
        let textLabel = UILabel()
        textLabel.text = text
        textLabel.font = LCDevice.DIN_Font_PF_M(18)
        textLabel.textColor = UIColor.white
        textLabel.textAlignment = .center
        textLabel.numberOfLines = 0
        textLabel.frame = frame
        textLabel.tag = 1233
        return textLabel
    }
}

extension LiveRoomGuideView {
    public func show(superView: UIView) {
       
        
    }
    
    public func dismiss() {
        self.removeFromSuperview()
    }
    
    @objc func completeBtnAction(sender: UIButton) {
        currentIndex = currentIndex + 1
        if currentIndex == imgArray.count - 1 {
            sender.setTitle("完成", for: .normal)
            indexLbl.text = "再看一遍"
        }
        if currentIndex == imgArray.count {
            dismiss()
            if let completed = complted { completed() }
            return
        }
        if let url = URL(string: imgArray[currentIndex].img) {
            imgView.setImageWith(url, placeholder: nil)
        }
    }
    
    @objc func imgBtnAction() {
        guard let mod = imgArray[safe: currentIndex],
                let jumpUrl = mod.url,
                !jumpUrl.isEmpty else {
            return
        }
        let webCtrl = WebVC.init(title: "", url: jumpUrl, withProgressLine: true, needStartNewWeb: false)
        let vc = AppDelegate.curDisplayVC()
        vc.navigationController?.pushViewController(webCtrl, animated: true)
    }
    
    @objc private func indexLblGesture(gesture: UITapGestureRecognizer) {
        if indexLbl.text == "再看一遍" {
            currentIndex = 0
            indexLbl.text = "跳过(1/\(imgArray.count))"
            if let url = URL(string: imgArray[0].img) {
                imgView.setImageWith(url, placeholder: nil)
            }
            nextButton.setTitle("下一步", for: .normal)
        } else {
            if let completed = complted { completed() }
            dismiss()
        }
    }
}
