//
//  CustomLayoutButton.swift
//  LivePlus
//
//  Created by iclick on 2021/1/4.
//

import UIKit

/// Display type
public enum CustomLayoutButtonType {
    case normal
    /// 文本在左
    case left
    /// 文本在右
    case right
    /// 文本在下
    case bottom
}

class CustomLayoutButton: UIButton {

    private var _type: CustomLayoutButtonType = .normal
    ///图片大小
    var imageSize = CGSize.zero
    ///图片相对于 top/right 的 offset
    var offset: CGFloat = 0
    
    // bot 情况下image的上边界
    var topOffset: CGFloat = 0
    
    // MARK: - Getter && Setter
    open var type: CustomLayoutButtonType {
        get {
            return _type
        }
        set {
            self._type = newValue
            if newValue != .normal {
                titleLabel?.textAlignment = .center
            }
        }
    }
    
    // MARK: - overwriter
    
    override func imageRect(forContentRect contentRect: CGRect) -> CGRect {
        switch _type {
        case .left:
            let x = contentRect.width - offset - imageSize.width
            let y = (contentRect.height - imageSize.height)/2.0
            return CGRect(x: x, y: y, width: imageSize.width, height: imageSize.height)
        case .right:
            return CGRect(x: 0, y: 0, width: height, height: height)
        case .bottom:
            let x = (width-imageSize.width)/2.0
            return CGRect(x: x, y: topOffset, width: imageSize.width, height: imageSize.height)
        default:
            return super.titleRect(forContentRect: contentRect)
        }
    }
    
    override func titleRect(forContentRect contentRect: CGRect) -> CGRect {
        switch _type {
        case .left:
            return CGRect(x: 0, y: 0, width: contentRect.width-offset-imageSize.width, height: contentRect.height)
        case .right:
            return CGRect(x: offset + height, y: 0, width: width-height, height: height)
        case .bottom:
            return CGRect(x: 0, y: imageSize.height + offset, width: width, height: LCDevice.DIN_WIDTH(18))
        default:
            return super.titleRect(forContentRect: contentRect)
        }
    }
    
    override func setImage(_ image: UIImage?, for state: UIControl.State) {
        super.setImage(image, for: state)
        if let image = image {
            imageSize = image.size
        }
    }
    
}


@objc extension UIButton {
    /// Enum to determine the title position with respect to the button image
    ///
    /// - top: title above button image
    /// - bottom: title below button image
    /// - left: title to the left of button image
    /// - right: title to the right of button image
    @objc enum PositionInButton: Int {
        case top, bottom, left, right
    }
    
    /// This method sets an image and title for a UIButton and
    ///   repositions the titlePosition with respect to the button image.
    ///
    /// - Parameters:
    ///   - image: Button image
    ///   - title: Button title
    ///   - titlePosition: UIViewContentModeTop, UIViewContentModeBottom, UIViewContentModeLeft or UIViewContentModeRight
    ///   - additionalSpacing: Spacing between image and title
    ///   - state: State to apply this behaviour
    @objc func set(image: UIImage?, title: String, titlePosition: PositionInButton, additionalSpacing: CGFloat, state: UIControl.State){
        imageView?.contentMode = .center
        setImage(image, for: state)
        setTitle(title, for: state)
        titleLabel?.contentMode = .center

        adjust(title: title as NSString, at: titlePosition, with: additionalSpacing)
        
    }
    
    /// This method sets an image and an attributed title for a UIButton and
    ///   repositions the titlePosition with respect to the button image.
    ///
    /// - Parameters:
    ///   - image: Button image
    ///   - title: Button attributed title
    ///   - titlePosition: UIViewContentModeTop, UIViewContentModeBottom, UIViewContentModeLeft or UIViewContentModeRight
    ///   - additionalSpacing: Spacing between image and title
    ///   - state: State to apply this behaviour
    @objc func set(image: UIImage?, attributedTitle title: String, at position: PositionInButton, width spacing: CGFloat, state: UIControl.State){
        imageView?.contentMode = .center
        setImage(image, for: state)
        
        adjust(attributedTitle: title, at: position, with: spacing)
        
        titleLabel?.contentMode = .center
        setTitle(title, for: state)
    }
    
    
    // MARK: Private Methods
    
    @objc private func adjust(title: NSString, at position: PositionInButton, with spacing: CGFloat) {
        let imageRect: CGRect = self.imageRect(forContentRect: frame)
        
        // Use predefined font, otherwise use the default
        let titleFont: UIFont = titleLabel?.font ?? UIFont()
        let titleSize: CGSize = title.size(withAttributes: [NSAttributedString.Key.font: titleFont])
        
        arrange(titleSize: titleSize, imageRect: imageRect, atPosition: position, withSpacing: spacing)
    }
    
    @objc private func adjust(attributedTitle: String, at position: PositionInButton, with spacing: CGFloat) {
        let imageRect: CGRect = self.imageRect(forContentRect: frame)
        let titleSize = attributedTitle.size()
        
        arrange(titleSize: titleSize, imageRect: imageRect, atPosition: position, withSpacing: spacing)
    }

    @objc private func arrange(titleSize: CGSize, imageRect:CGRect, atPosition position: PositionInButton, withSpacing spacing: CGFloat) {
        switch (position) {
        case .top:
            titleEdgeInsets = UIEdgeInsets(top: -(imageRect.height + titleSize.height + spacing), left: -(imageRect.width), bottom: 0, right: 0)
            imageEdgeInsets = UIEdgeInsets(top: 0, left: 0, bottom: 0, right: -titleSize.width)
            contentEdgeInsets = UIEdgeInsets(top: spacing / 2 + titleSize.height, left: -imageRect.width/2, bottom: 0, right: -imageRect.width/2)
        case .bottom:
            titleEdgeInsets = UIEdgeInsets(top: (imageRect.height + titleSize.height + spacing), left: -(imageRect.width), bottom: 0, right: 0)
            imageEdgeInsets = UIEdgeInsets(top: 0, left: 0, bottom: 0, right: -titleSize.width)
            contentEdgeInsets = UIEdgeInsets(top: 0, left: -imageRect.width/2, bottom: spacing / 2 + titleSize.height, right: -imageRect.width/2)
        case .left:
            titleEdgeInsets = UIEdgeInsets(top: 0, left: -(imageRect.width * 2), bottom: 0, right: 0)
            imageEdgeInsets = UIEdgeInsets(top: 0, left: 0, bottom: 0, right: -(titleSize.width * 2 + spacing))
            contentEdgeInsets = UIEdgeInsets(top: 0, left: 0, bottom: 0, right: spacing / 2)
        case .right:
            titleEdgeInsets = UIEdgeInsets(top: 0, left: 0, bottom: 0, right: -spacing)
            imageEdgeInsets = UIEdgeInsets(top: 0, left: 0, bottom: 0, right: 0)
            contentEdgeInsets = UIEdgeInsets(top: 0, left: 0, bottom: 0, right: spacing / 2)
        }
    }
}


@IBDesignable
class HLImagePositionButton: UIButton {
    public enum ImagePositionType: Int {
        case left
        case right
        case top
        case bottom
    }
    
    private(set) var type:ImagePositionType
    @IBInspectable
    let space:CGFloat
    
    @IBInspectable
    var typeRawValue:Int {
        set {
            if let type = ImagePositionType(rawValue: newValue) {
                self.type = type
            }
        }
        get {
            return type.rawValue
        }
    }
    
    init(type:ImagePositionType = .left,space:CGFloat = 5) {
        self.type = type
        self.space = space
        super.init(frame: CGRect.zero)
        
//        super.init(type: UIButton.ButtonType.custom)
//        super.init(type: .custom)
    }
    
    required init?(coder aDecoder: NSCoder) {
//        fatalError("init(coder:) has not been implemented")
        
        self.type = .left
        self.space = 5
        super.init(coder: aDecoder)
    }
    
    
    @available(*,unavailable)
    override var titleEdgeInsets: UIEdgeInsets {
        set {
            super.titleEdgeInsets = newValue
        }
        get {
            return super.titleEdgeInsets
        }
    }
    
    @available(*,unavailable)
    override var imageEdgeInsets: UIEdgeInsets {
        set {
            super.imageEdgeInsets = newValue
        }
        get {
            return super.imageEdgeInsets
        }
    }
    
    
    
    /// 如果buttonType 不为systme imageView 与titleLabel 始终存在
    override func layoutSubviews() {
        super.layoutSubviews()
        
        guard let imageView = imageView,
        let titleLabel = titleLabel else {
            return
        }
        
        let labelSize = titleLabel.intrinsicContentSize
        let imageSize = imageView.intrinsicContentSize
        
        switch type {
        case .left:
            if isSetTitle() && isSetImage() {
//                titleLabel.frame.origin.x = imageView.frame.maxX + space
                titleLabel.frame.origin.x = frame.width/2 - (labelSize.width + imageSize.width + space)/2
                imageView.frame.origin.x = titleLabel.frame.maxX + space
                
            }
        case .right:
            if isSetTitle() && isSetImage() {
//                titleLabel.frame.origin.x = contentEdgeInsets.left
//                imageView.frame.origin.x = titleLabel.frame.maxX + space
                
                imageView.frame.origin.x = frame.width/2 - (labelSize.width + imageSize.width + space)/2
                titleLabel.frame.origin.x = imageView.frame.maxX + space
            }
        case .top:
            if isSetTitle() && isSetImage() {
//                imageView.frame.origin.y = contentEdgeInsets.top
                imageView.frame.origin.y = frame.height/2 - (labelSize.height + imageSize.height + space)/2
                
                imageView.center.x = frame.width/2
                titleLabel.frame.size = titleLabel.intrinsicContentSize
                titleLabel.frame.origin.y = imageView.frame.maxY + space
                titleLabel.center.x = frame.width/2
            }
        case .bottom:
            if isSetTitle() && isSetImage() {
                titleLabel.frame.size = titleLabel.intrinsicContentSize
//                titleLabel.frame.origin.y = contentEdgeInsets.top
                titleLabel.frame.origin.y = frame.height/2 - (labelSize.height + imageSize.height + space)/2
                
                titleLabel.center.x = frame.width/2
                imageView.frame.origin.y = titleLabel.frame.maxY + space
                imageView.center.x = frame.width/2
            }
        }
    }
    
    override var intrinsicContentSize: CGSize {
        guard let imageView = imageView,
            let titleLabel = titleLabel else {
                return .zero
        }
        
        switch type {
        case .left,.right:
            
            return CGSize(width: super.intrinsicContentSize.width + getRealSpace(), height: super.intrinsicContentSize.height)
        case .top,.bottom:
            if !isSetImage() && !isSetTitle() {
//                backgroundImage(for: <#T##UIControl.State#>)
                // 只设置了背景图片
                return super.intrinsicContentSize
            }
            
            let titleW = titleLabel.intrinsicContentSize.width
            let imageW = imageView.intrinsicContentSize.width
            let titleH = titleLabel.intrinsicContentSize.height
            let imageH = imageView.intrinsicContentSize.height
            
            let contentEdgeWidth = contentEdgeInsets.left + contentEdgeInsets.right
            let contentEdgeHeight = contentEdgeInsets.top + contentEdgeInsets.bottom
            
            return CGSize(width: CGFloat.maximum(titleW, imageW) + contentEdgeWidth, height: titleH + imageH + getRealSpace() + contentEdgeHeight)
        }
    }
    
    
    private func getRealSpace() -> CGFloat {
        if isSetTitle() && isSetImage() {
            return space
        }
        return 0
    }
    
    private func isSetTitle() -> Bool {
        guard let titleLabel = titleLabel else {
                return false
        }
        return titleLabel.text != nil && !titleLabel.text!.isEmpty
    }
    private func isSetImage() -> Bool {
        guard let imageView = imageView else {
            return false
        }
        return imageView.image != nil
    }

}
