//
//  GestureGuideInfoView.swift
//  LivePlus
//
//  Created by Brian<PERSON><PERSON> on 2021/11/26.
//

import UIKit
import RxSwift

enum GestureGuideType {
    /// 点击切换区域素材
    case areaMaterialSwi
    /// 点击切换内容区域
    case areaContentSwi
    /// 调整区域大小
    case areaSizeAdjustSwi
    /// 区域旋转
    case areaRotationSwi
    /// 长按调整区域位置
    case areaMovePostionSwi
    /// 切换不同场景
    case sceneSwi
    /// 音量调节
    case volumeAdjustSwi
    /// 视频播放暂停
    case videoPlayOrStopSwi
    /// 清屏
    case clearScreenSwi
    ///  开始或暂停视频录制
    case startOrStopRecord
}

class GestureGuideInfoView: UIView {
    // MARK: - 属性
    public let bag = DisposeBag()

    /// 当前视图类型
    public var guideType: GestureGuideType = .areaMaterialSwi
    // 当前索引
    public var currentIndex: Int = 0
    public var viewsArray: [UIView] = []
    /// 背景的透明视图
    private var maskBaseView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor("#000000").alpha(value: 0.4)
        view.isHidden = true
        return view
    }()
    
    // 完成按钮
    lazy var completeBtn: UIButton = {
        let button = commonButton()
        return button
    }()
    
    private var isShowInView: Bool = false
    
    private var layerCount = 3
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupBaseView()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
//    override func hitTest(_ point: CGPoint, with event: UIEvent?) -> UIView? {
////        dismiss()
//        return super.hitTest(point, with: event)
//    }
//
    private func setupBaseView() {
        maskBaseView.frame = CGRect(x: 0, y: 0, width: LCDevice.screenW, height: LCDevice.screenH)
        addSubview(maskBaseView)
    }
    
    private func commonButton() -> UIButton {
        let button = UIButton(type: .custom)
        button.setBackgroundImage(UIImage(color: UIColor("#000000").alpha(value: 0.2)), for: .normal)
        button.cornerRadius = 20
        button.borderWidth = 1
        button.borderColor = UIColor("#ffffff")
        return button
    }
    
    private func commonTextButton(text: String) -> UIButton {
        let button = UIButton(type: .custom)
        let attriStr = NSMutableAttributedString.init(string: text)
        attriStr.addAttributes([.underlineStyle: NSUnderlineStyle.single.rawValue, .foregroundColor:UIColor.white], range: NSRange(location: 0, length: attriStr.length))
        button.titleLabel?.attributedText  = attriStr
        button.setAttributedTitle(attriStr, for: .normal)
        return button
    }
    
    private func commonTextLable(text: String, frame: CGRect) -> UILabel {
        let textLabel = UILabel()
        textLabel.text = text
        textLabel.font = LCDevice.DIN_Font_PF_M(18)
        textLabel.textColor = UIColor.white
        textLabel.textAlignment = .center
        textLabel.numberOfLines = 0
        textLabel.frame = frame
        textLabel.tag = 1233
        return textLabel
    }
    
    private func commonFingerView(named: String, frame: CGRect) -> UIImageView {
        let fingerView = UIImageView()
        fingerView.image = UIImage(named: named)
        fingerView.frame = frame
        return fingerView
    }
    
    private func setupAreaMaterialGuide(maskView: UIView) {
        let fW: CGFloat = 299
        let fH: CGFloat = 154
        let fingerView = commonFingerView(named: "gesture_single_left_right", frame: CGRect(x: (LCDevice.screenW - fW) / 2, y: (LCDevice.screenH - fH) / 2, width: fW, height: fH))
        let textLabel = commonTextLable(text: "左划、右划可切换区域内素材", frame: CGRect(x: (LCDevice.screenW - 234) / 2, y: fingerView.bottom, width: 234, height: 25))
        maskView.addSubview(fingerView)
        maskView.addSubview(textLabel)
    }
    
    private func setupAreaContentGuide(maskView: UIView) {
        let fW: CGFloat = 148
        let fH: CGFloat = 164
        let fingerView = commonFingerView(named: "gesture_single_up_down", frame: CGRect(x: (LCDevice.screenW - fW) / 2 , y: (LCDevice.screenH - fH) / 2, width: fW, height: fH))
        
        let textLabel = commonTextLable(text: "上划、下划可切换内容区域", frame: CGRect(x: (LCDevice.screenW - 234) / 2, y: fingerView.bottom + 15, width: 234, height: 25))
        maskView.addSubview(fingerView)
        maskView.addSubview(textLabel)
    }
    
    private func setupAreaRotationGuide(maskView: UIView) {
        let fW: CGFloat = 76
        let fH: CGFloat = 108
        let fingerView = commonFingerView(named: "gesture_double_rotation", frame: CGRect(x: (LCDevice.screenW - fW) / 2, y: (LCDevice.screenH - fH) / 2, width: fW, height: fH))
        let textLabel = commonTextLable(text: "双指同时左右旋转可调整当前区域旋转角度", frame: CGRect(x: (LCDevice.screenW - 234) / 2, y: fingerView.bottom + 15, width: 234, height: 55))
        maskView.addSubview(fingerView)
        maskView.addSubview(textLabel)
    }
    
    private func setupAreaSizeAdjustGuide(maskView: UIView) {
        let fW: CGFloat = 274
        let fH: CGFloat = 110
        let fingerView = commonFingerView(named: "gesture_double_zoom", frame: CGRect(x: (LCDevice.screenW - fW) / 2, y: (LCDevice.screenH - fH) / 2, width: fW, height: fH))
        let textLabel = commonTextLable(text: "双指向外、向内划动可调整当前区域大小", frame: CGRect(x: (LCDevice.screenW - 234) / 2, y: fingerView.bottom + 15, width: 234, height: 55))
        maskView.addSubview(fingerView)
        maskView.addSubview(textLabel)
    }
    
    private func setupAreaMovePostionGuide(maskView: UIView) {
        let fW: CGFloat = 91
        let fH: CGFloat = 159
        let fingerView = commonFingerView(named: "gesture_long_press_move", frame: CGRect(x: (LCDevice.screenW - fW) / 2, y: (LCDevice.screenH - fH) / 2, width: fW, height: fH))
        let textLabel = commonTextLable(text: "长按屏幕后进行拖动可调整当前区域的位置", frame: CGRect(x: (LCDevice.screenW - 234) / 2, y: fingerView.bottom + 15, width: 234, height: 25))
        maskView.addSubview(fingerView)
        maskView.addSubview(textLabel)
    }
    
    private func setupSceneSwiGuide(maskView: UIView) {
        let fW: CGFloat = 299
        let fH: CGFloat = 153
        let fingerView = commonFingerView(named: "gesture_double_left_right", frame: CGRect(x: (LCDevice.screenW - fW) / 2, y: (LCDevice.screenH - fH) / 2, width: fW, height: fH))
        let textLabel = commonTextLable(text: "双指左划、右划可切换不同场景", frame: CGRect(x: (LCDevice.screenW - 234) / 2, y: fingerView.bottom + 15, width: 234, height: 25))
        maskView.addSubview(fingerView)
        maskView.addSubview(textLabel)
    }
    
    private func setupClearScreenGuide(maskView: UIView) {
        let fW: CGFloat = 206
        let fH: CGFloat = 146
        let fingerView = commonFingerView(named: "gesture_single_double_click", frame: CGRect(x: (LCDevice.screenW - fW) / 2, y: (LCDevice.screenH - fH) / 2, width: fW, height: fH))
        let textLabel = commonTextLable(text: "双击屏幕可切换/关闭清屏模式", frame: CGRect(x: (LCDevice.screenW - 234) / 2, y: fingerView.bottom + 15, width: 234, height: 25))
        maskView.addSubview(fingerView)
        maskView.addSubview(textLabel)
    }
    
    private func setupVideoPlayOrStopGuide(maskView: UIView) {
        let fW: CGFloat = 206
        let fH: CGFloat = 146
        let fingerView = commonFingerView(named: "gesture_double_click", frame: CGRect(x: (LCDevice.screenW - fW) / 2, y: (LCDevice.screenH - fH) / 2, width: fW, height: fH))
        let textLabel = commonTextLable(text: "双指单击屏幕开始或暂停视频播放", frame: CGRect(x: (LCDevice.screenW - 234) / 2, y: fingerView.bottom + 15, width: 234, height: 25))
        maskView.addSubview(fingerView)
        maskView.addSubview(textLabel)
   
    }
    
    private func setupStartOrStopRecordGuide(maskView: UIView) {
        let fW: CGFloat = 206
        let fH: CGFloat = 146
        let fingerView = commonFingerView(named: "gesture_double_double_click", frame: CGRect(x: (LCDevice.screenW - fW) / 2, y: (LCDevice.screenH - fH) / 2, width: fW, height: fH))
        let textLabel = commonTextLable(text: "双指双击屏幕开始或暂停视频录制", frame: CGRect(x: (LCDevice.screenW - 234) / 2, y: fingerView.bottom + 15, width: 234, height: 25))
        maskView.addSubview(fingerView)
        maskView.addSubview(textLabel)
    }
    
    private func setupVolumeAdjustGuide(maskView: UIView) {
        let fW: CGFloat = 162
        let fH: CGFloat = 173
        let fingerView = commonFingerView(named: "gesture_double_up_down", frame: CGRect(x: (LCDevice.screenW - fW) / 2, y: (LCDevice.screenH - fH) / 2, width: fW, height: fH))
        let textLabel = commonTextLable(text: "双指上划、下划可调整音量大小", frame: CGRect(x: (LCDevice.screenW - 234) / 2, y: fingerView.bottom + 15, width: 234, height: 25))
        maskView.addSubview(fingerView)
        maskView.addSubview(textLabel)
    }
    
}


extension GestureGuideInfoView {
    // MARK: - 单个使用
    public func  singleGuideTypeDisplay(guideType: GestureGuideType) {
        self.guideType = guideType
        setGuideViewForType(guideType: guideType, maskView: maskBaseView)
        let textLabel = maskBaseView.viewWithTag(1233)
        let commonBtn: UIButton = commonButton()
        commonBtn.frame = CGRect(x: (LCDevice.screenW - 133) / 2, y: (textLabel?.bottom ?? 0) + 92, width: 133, height: 40)
        let commonAgainBtn: UIButton = commonTextButton(text: "")
        commonAgainBtn.frame = CGRect(x: (LCDevice.screenW - 133) / 2, y: commonBtn.bottom + 12, width: 133, height: 40)
        maskBaseView.addSubview(commonBtn)
        maskBaseView.addSubview(commonAgainBtn)
        commonBtn.setTitle("完成", for: .normal)
//        commonBtn.addTarget(self, action: #selector(completeBtnOriginAction(sender:)), for: .touchUpOutside)
//        commonAgainBtn.addTarget(self, action: #selector(againBtnOriginAction(sender:)), for: .touchUpOutside)
        commonBtn.rx.tap
            .subscribe(onNext: { _ in
                self.dismiss()
            })
            .disposed(by: bag)
        commonAgainBtn.rx.tap
            .subscribe(onNext: { _ in
                self.dismiss()
            })
            .disposed(by: bag)
        maskBaseView.isHidden = false
    }
    
    // MARK: - 组合使用
    func combinedDisplay(typeArray: [GestureGuideType]) {
        for (idx, typeItem) in typeArray.enumerated() {
            let maskview = UIView()
            maskview.backgroundColor = UIColor("#000000").alpha(value: 0.4)
            if idx != 0 {
                maskview.isHidden = true
            }
            let mx: CGFloat = CGFloat(idx) * LCDevice.screenW
            maskview.frame = CGRect(x: mx, y: 0, width: LCDevice.screenW, height: LCDevice.screenH)
            self.addSubview(maskview)
            setGuideViewForType(guideType: typeItem, maskView: maskview)
            let textLabel = maskview.viewWithTag(1233)
            let commonBtn: UIButton = commonButton()
            commonBtn.frame = CGRect(x: (LCDevice.screenW - 133) / 2, y: (textLabel?.bottom ?? 0) + 92, width: 133, height: 40)
            let commonAgainBtn: UIButton = commonTextButton(text: (idx == typeArray.count - 1 ? "再看一遍" : "跳过教程"))
            commonAgainBtn.frame = CGRect(x: (LCDevice.screenW - 133) / 2, y: commonBtn.bottom + 12, width: 133, height: 40)
            maskview.addSubview(commonBtn)
            maskview.addSubview(commonAgainBtn)
            if idx == typeArray.count - 1 {
                commonBtn.setTitle("完成", for: .normal)
//                commonBtn.addTarget(self, action: #selector(completeBtnAction(sender:)), for: .touchUpOutside)
//                commonAgainBtn.addTarget(self, action: #selector(lookAgainBtnAction(sender:)), for: .touchUpOutside)
//                commonAgainBtn.setTitle("再看一遍", for: .normal)
                commonBtn.rx.tap
                    .subscribe(onNext: { _ in
                        self.completeBtnAction(sender: commonBtn)
                    })
                    .disposed(by: bag)
                commonAgainBtn.rx.tap
                    .subscribe(onNext: { _ in
                        self.lookAgainBtnAction(sender: commonBtn)
                    })
                    .disposed(by: bag)

            } else {
                commonBtn.setTitle("下一页", for: .normal)
//                commonBtn.addTarget(self, action: #selector(nextBtnAction(sender:)), for: .touchUpOutside)
//                commonAgainBtn.addTarget(self, action: #selector(completeBtnAction(sender:)), for: .touchUpOutside)

//                commonAgainBtn.setTitle("跳过教程", for: .normal)
                commonBtn.rx.tap
                    .subscribe(onNext: { _ in
                        self.nextBtnAction(sender: commonBtn)
                    })
                    .disposed(by: bag)
                commonAgainBtn.rx.tap
                    .subscribe(onNext: { _ in
                        self.completeBtnAction(sender: commonAgainBtn)
                    })
                    .disposed(by: bag)
            }
            
            viewsArray.append(maskview)
            
        }
        self.frame = CGRect(x: 0, y: 0, width: LCDevice.screenW * CGFloat(typeArray.count) , height: LCDevice.screenH)
    }
    
    func setGuideViewForType(guideType: GestureGuideType, maskView: UIView) {
        switch guideType {
        case .areaMaterialSwi:
            setupAreaMaterialGuide(maskView: maskView)
        case .areaContentSwi:
            setupAreaContentGuide(maskView: maskView)
        case .areaRotationSwi:
            setupAreaRotationGuide(maskView: maskView)
        case .areaSizeAdjustSwi:
            setupAreaSizeAdjustGuide(maskView: maskView)
        case .areaMovePostionSwi:
            setupAreaMovePostionGuide(maskView: maskView)
        case .sceneSwi:
            setupSceneSwiGuide(maskView: maskView)
        case .clearScreenSwi:
            setupClearScreenGuide(maskView: maskView)
        case .videoPlayOrStopSwi:
            setupVideoPlayOrStopGuide(maskView: maskView)
        case .startOrStopRecord:
            setupStartOrStopRecordGuide(maskView: maskView)
        case .volumeAdjustSwi:
            setupVolumeAdjustGuide(maskView: maskView)
        }
    }
}

extension GestureGuideInfoView {
//    public func show() {
//        maskBaseView.isHidden = false
//    }
    
    public func dismiss() {
        maskBaseView.isHidden = true
        self.removeFromSuperview()
    }
    
    
    @objc func nextBtnAction(sender: UIButton) {
        currentIndex += 1
        let view: UIView = viewsArray[currentIndex]
        view.isHidden = false
        UIView.animate(withDuration: 0.3) {

            self.x = -(LCDevice.screenW * CGFloat(self.currentIndex))
        }
    }
    @objc func lookAgainBtnAction(sender: UIButton) {
        currentIndex = 0
        self.x = 0
    }
    
    @objc func completeBtnAction(sender: UIButton) {
        dismiss()
    }
    
}
