//
//  PolicyAlert.swift
//  LivePlus
//
//  Created by iclick on 2020/12/25.
//

import UIKit

/// 首次打开app，隐私政策弹窗
class PolicyAlert: UIView {

    ///当前是否正在显示协议弹窗
    var isShowing = false

    private let alert = UIView()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupAlert()
    }
    
    required init?(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Public methods
    func show() {
        backgroundColor = UIColor.black.alpha(value: 0.3)
        
        guard let appDel = UIApplication.shared.delegate as? AppDelegate else { return }
        
        appDel.wd.addSubview(self)
        
        isShowing = true
    }
    
    // MARK: - UI
    private func setupAlert() {
        alert.frame = CGRect(x: 0, y: 0, width: LCDevice.screenW - 84, height: 0)
        alert.center = self.center
        alert.backgroundColor = .white
        alert.layer.cornerRadius = 20
        alert.layer.masksToBounds = true
        addSubview(alert)
        
        //title
        let title = UILabel(frame: CGRect(x: 0, y: LCDevice.DIN_WIDTH(24), width: alert.width, height: LCDevice.DIN_WIDTH(30)))
        title.text = "《服务协议》及《隐私政策》"
        title.font = LCDevice.DIN_Font_PF_M(18)
        title.textColor = UIColor("#1E1F20")
        title.textAlignment = .center
        alert.addSubview(title)
        
        //content
        let xMargin = LCDevice.DIN_WIDTH(23)
        let contentLab = YYLabel(frame: CGRect(x: xMargin, y: title.bottom + LCDevice.DIN_WIDTH(15), width: alert.width-xMargin*2, height: 0))
        let contentStr = " 欢迎使用快瓴中控台APP!\n1.为了更好的为您提供产品服务，请您仔细阅读《用户使用协议》以及《隐私政策》。\n2.在您开始使用产品之前，请务必仔细阅读并充分理解本协议。如您未满18周岁，请在您的法定监护人陪同下，仔细阅读并充分理解本协议。\n3.为给你提供克隆音色功能，我们可能会申请录音权限。\n4.请点击“同意并继续”并开始使用我们的产品及服务。若点击“不同意”，则相关服务不可使用。\n"
        let contentAttString = NSMutableAttributedString(string: contentStr)
        let pStyle = NSMutableParagraphStyle()
        pStyle.lineSpacing = LCDevice.DIN_WIDTH(4)
        contentAttString.addAttribute(NSAttributedString.Key.paragraphStyle, value: pStyle, range: (contentStr as NSString).rangeOfAll())
        contentAttString.font = LCDevice.DIN_Font_PF_R(14)
        contentAttString.color = UIColor("#4D4E52")
        
        //link
        let agreementFS = "《用户使用协议》"
        let agreementRange = contentStr.range(of: agreementFS)!
        let agreementLinkRange = NSRange(agreementRange, in: contentStr)
        let privacyFS = "《隐私政策》"
        let privacyRange = contentStr.range(of: privacyFS)!
        let privacyLinkRange = NSRange(privacyRange, in: contentStr)
        contentAttString.setTextHighlight(agreementLinkRange, color: UIColor("#6974F2"), backgroundColor: nil) { (_ containerView, _ attText, _ range, _ rect) in
            LPStatistics.logEvent(.PROTOCOL_REMAIND(.seeProtocol), eventAction: nil)
            Router.openUserAgreement()
        }
        contentAttString.setTextHighlight(privacyLinkRange, color: UIColor("#6974F2"), backgroundColor: nil) { (_ containerView, _ attText, _ range, _ rect) in
            LPStatistics.logEvent(.PROTOCOL_REMAIND(.seePrivacy), eventAction: nil)
            Router.openPrivacyAgreement()
        }
        
        //计算文本实际高度
        let contentTextLayout = YYTextLayout(containerSize: CGSize(width: contentLab.width, height: 1000), text: contentAttString)
        let contentRealHeight = (contentTextLayout?.textBoundingSize.height)!
        contentLab.height = contentRealHeight
        contentLab.numberOfLines = 0
        contentLab.attributedText = contentAttString
        contentLab.textVerticalAlignment = .top
        alert.addSubview(contentLab)
        
        //计算最终高度
        let bottomBtnH = LCDevice.DIN_WIDTH(45)
        alert.height = title.top + title.height + contentLab.height + bottomBtnH + LCDevice.DIN_WIDTH(26+26)
        alert.center = self.center
        
        let buttonWidth: CGFloat = (LCDevice.screenW - 84 - 49) / 2.0
        let noButton = UIButton(type: .custom)
        noButton.frame = CGRect(x: 20, y: 0, width: buttonWidth, height: 44)
        noButton.top = contentLab.bottom + 12
        noButton.setTitle("不同意", for: .normal)
        noButton.backgroundColor = UIColor("#F5F5F8")
        noButton.titleLabel?.font = LCDevice.DIN_Font_PF_M(14)
        noButton.setTitleColor(UIColor("#4D4E52"), for: .normal)
        noButton.cornerRadius = 22
        noButton.addTarget(self, action: #selector(quitBtnAction), for: .touchUpInside)
        alert.addSubview(noButton)
        
        let sureButton = UIButton(frame: CGRect(x: 20, y: 0, width: buttonWidth, height: 44))
        sureButton.top = contentLab.bottom + 12
        sureButton.left = noButton.right + 9
        sureButton.setTitle("同意并继续", for: .normal)
        sureButton.backgroundColor = UIColor("#6974F2")
        sureButton.titleLabel?.font = LCDevice.DIN_Font_PF_M(14)
        sureButton.setTitleColor(UIColor.white, for: .normal)
        sureButton.cornerRadius = 22
        sureButton.addTarget(self, action: #selector(sureBtnAction), for: .touchUpInside)
        alert.addSubview(sureButton)
        
    }
    
    // MARK: - Btn action
    @objc private func quitBtnAction() {
        LPStatistics.logEvent(.PROTOCOL_REMAIND(.clickProtocol), eventAction: .zero)
        exit(0)
    }
    
    @objc private func sureBtnAction() {
        LPStatistics.logEvent(.PROTOCOL_REMAIND(.clickProtocol), eventAction: .one)
        removeFromSuperview()
        UserDefaults.standard.set(true, forKey: LCKey.UD_PolicyAlertDone)
        isShowing = false
    }

}
