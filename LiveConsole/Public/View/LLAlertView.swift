//
//  AlertView.swift
//  fastword
//
//  Created by 郭炜 on 2021/4/3.
//

// MARK: - 封装的alertView 后续如果弹窗样式要变 统一修改此处即可 在代码中请不要直接使用系统弹窗

import UIKit


// kvc修改系统属性
public let titleKVC = "attributedTitle"
public let messageKVC = "attributedMessage"
public let buttonColorKVC = "titleTextColor"

enum AlertViewActionColor: String {
    case defaultColor = "#898B8F" // 默认#898B8F灰色
    case mainColor = "#6974F2" // 6863F7蓝色
}

struct LLAlertView {

    typealias LLAlertViewAction = (() -> Void)
    typealias LLActionShow = (title: String, color: AlertViewActionColor)
    
    private var alertView: UIAlertController?

    public init(title: String,
                message: String,
                leftShow: LLActionShow,
                rightShow: LLActionShow,
                leftAction: LLAlertViewAction?,
                rightAction: LLAlertViewAction?) {
        if let alertView = alertView, alertView.isVisible {
            return
        }
        alertView = UIAlertController(title: title, message: message, preferredStyle: .alert)
        let cancelAction = UIAlertAction(title: leftShow.title, style: .cancel) { _ in
            if let closure = leftAction {
                closure()
            }
        }
        let defaultAction = UIAlertAction(title: rightShow.title, style: .default) { _ in
            if let closure = rightAction {
                closure()
            }
        }
        
        // 设置标题颜色和字体
        let titleAttr = NSMutableAttributedString(string: title)
        titleAttr.addAttributes([.font: LCDevice.DIN_Font_PF_M(16),
                                   .foregroundColor: UIColor("#797B7D")], range: titleAttr.rangeOfAll())
        alertView?.setValue(titleAttr, forKey: titleKVC)
        // 设置message颜色和字体
        let messageAttr = NSMutableAttributedString(string: message + "\n")
        messageAttr.addAttributes([.font: LCDevice.DIN_Font_PF_M(15),
                                   .foregroundColor: UIColor("#4D4E52")], range: messageAttr.rangeOfAll())
        alertView?.setValue(messageAttr, forKey: messageKVC)
        // 设置按钮颜色
        cancelAction.setValue(UIColor(leftShow.color.rawValue), forKey: buttonColorKVC)
        defaultAction.setValue(UIColor(rightShow.color.rawValue), forKey: buttonColorKVC)
        alertView?.addAction(cancelAction)
        alertView?.addAction(defaultAction)
        alertView?.preferredAction = defaultAction // 设置默认按钮加粗
    }
    
    public init(title: String,
                message: String,
                show: LLActionShow,
                action: LLAlertViewAction?) {
        alertView = UIAlertController(title: title, message: message, preferredStyle: .alert)
        let cancelAction = UIAlertAction(title: show.title, style: .default) { _ in
            if let closure = action {
                closure()
            }
        }
        
        // 设置标题颜色和字体
        let titleAttr = NSMutableAttributedString(string: title)
        titleAttr.addAttributes([.font: LCDevice.DIN_Font_Bold(16),
                                 .foregroundColor: UIColor.black], range: titleAttr.rangeOfAll())
        alertView?.setValue(titleAttr, forKey: titleKVC)
        // 设置message颜色和字体
        let messageAttr = NSMutableAttributedString(string: message + "\n")
        messageAttr.addAttributes([.font: LCDevice.DIN_Font_PF_M(15),
                                   .foregroundColor: UIColor("#4D4E52")], range: messageAttr.rangeOfAll())
        alertView?.setValue(messageAttr, forKey: messageKVC)
        // 设置按钮颜色
        cancelAction.setValue(UIColor(show.color.rawValue), forKey: buttonColorKVC)
        alertView?.addAction(cancelAction)
    }
    
    public init(title: String,
                messageAttr: NSMutableAttributedString,
                show: LLActionShow,
                action: LLAlertViewAction?) {
        alertView = UIAlertController(title: title, message: messageAttr.string, preferredStyle: .alert)
        let cancelAction = UIAlertAction(title: show.title, style: .default) { _ in
            if let closure = action {
                closure()
            }
        }
        
        // 设置标题颜色和字体
        let titleAttr = NSMutableAttributedString(string: title)
        titleAttr.addAttributes([.font: LCDevice.DIN_Font_Bold(16),
                                 .foregroundColor: UIColor.black], range: titleAttr.rangeOfAll())
        alertView?.setValue(titleAttr, forKey: titleKVC)
        // 设置message颜色和字体
//        let messageAttr = NSMutableAttributedString(string: message + "\n")
//        messageAttr.addAttributes([.font: LCDevice.DIN_Font_PF_M(15),
//                                   .foregroundColor: UIColor("#4D4E52")], range: messageAttr.rangeOfAll())
        alertView?.setValue(messageAttr, forKey: messageKVC)
        // 设置按钮颜色
        cancelAction.setValue(UIColor(show.color.rawValue), forKey: buttonColorKVC)
        alertView?.addAction(cancelAction)
    }
}

// MARK: - public method
extension LLAlertView {
    public func show(with viewController: UIViewController) {
        guard let popupView = alertView else { return }
        DispatchQueue.main.async {
            viewController.present(popupView, animated: true, completion: nil)
        }
    }
}
