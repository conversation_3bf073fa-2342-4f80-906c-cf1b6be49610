//
//  MyCustomSliderView.swift
//  LivePlus
//
//  Created by 郭炜 on 2022/8/24.
//

import UIKit
import SnapKit

enum MyDirection {
    case horizontal
    case vertical
}

class MyCustomSliderView: UIView {
    var currentProgressBlock:((_ currentProgress:Float)->Void)?
    
    var tempDirection : MyDirection = .horizontal
    var direction :MyDirection {
        get {
            return tempDirection
        }
        set {
            tempDirection = newValue
            foregroundView.snp.makeConstraints { make in
                make.leading.trailing.equalTo(self)
                make.bottom.equalTo(self.snp.bottom)
                make.top.equalTo(self.snp.bottom).offset(-self.progress*Float(self.frame.size.height))
            }
            
        }
    }
    
    var tempProgress:Float = 0.0
    var progress:Float {
        get {
            return tempProgress
        }
        
        set {
            tempProgress = newValue
            if tempProgress > 1.0 {
                tempProgress = 1.0
            } else if tempProgress<0.0 {
                tempProgress = 0.0
            }
            if self.direction == .horizontal {
                foregroundView.snp.remakeConstraints { make in
                    make.top.bottom.equalTo(self)
                    make.leading.equalTo(self)
                    make.trailing.equalTo(self.snp.leading).offset(tempProgress*Float(self.frame.size.width))
                }
            }else {
                foregroundView.snp.remakeConstraints { make in
                    make.leading.trailing.equalTo(self)
                    make.bottom.equalTo(self.snp.bottom)
                    make.top.equalTo(self.snp.bottom).offset(-tempProgress*Float(self.frame.size.height))
                }
                
            }
        }
    }
    
    var value: Float {
        set {
            progress = newValue / 100.0
        }
        get {
            return progress * 100.0
        }
    }
    
    var tempMyBackgroundColor: UIColor = .lightGray
    var myBackgroundColor: UIColor {
        get{
            return tempMyBackgroundColor
        }
        set{
            tempMyBackgroundColor = newValue
            backgroundView.backgroundColor = tempMyBackgroundColor
        }
    }
    
    var tempForegroundColor: UIColor = .orange
    var foregroundColor: UIColor {
        get{
            return tempForegroundColor
        }
        set{
            tempForegroundColor = newValue
            foregroundView.backgroundColor = tempForegroundColor
        }
    }
    
    var tempCornerRadius:CGFloat = 10.0
    var radius: CGFloat  {
        get {
            return tempCornerRadius
        }
        set {
            tempCornerRadius = newValue
            self.backgroundView.layer.cornerRadius = newValue
        }
    }
    private var backgroundView : UIView!
    private var foregroundView : UIView!
    override init(frame: CGRect) {
        super.init(frame: frame)
        setUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setUI() {
        self.layer.masksToBounds = true
        self.layer.cornerRadius = self.radius
        backgroundView = UIView()
        backgroundView.backgroundColor = self.myBackgroundColor
        backgroundView.layer.cornerRadius = self.radius
        backgroundView.layer.masksToBounds = true
        let tapSingle = UIPanGestureRecognizer(target: self, action: #selector(pan(_:)))
        backgroundView.addGestureRecognizer(tapSingle)
        self.addSubview(backgroundView)
        backgroundView.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.bottom.equalToSuperview()
            make.leading.equalToSuperview()
            make.trailing.equalToSuperview()
        }
        
        foregroundView = UIView()
        foregroundView.backgroundColor = self.foregroundColor
        foregroundView.isUserInteractionEnabled = false
        foregroundView.layer.masksToBounds = true
        self.addSubview(foregroundView)
        if self.direction == .horizontal {
            foregroundView.snp.makeConstraints { make in
                make.top.bottom.equalTo(self)
                make.leading.equalTo(self)
                make.trailing.equalTo(self.snp.leading).offset(self.progress*Float(self.frame.size.width))
            }
        } else {
            foregroundView.snp.makeConstraints { make in
                make.leading.trailing.equalTo(self)
                make.bottom.equalTo(self.snp.bottom)
                make.top.equalTo(self.snp.bottom).offset(-self.progress*Float(self.frame.size.height))
            }
        }
        
    }
    
    @objc func pan(_ cognizer:UIPanGestureRecognizer) {
        let location = cognizer.location(in: self)
        switch cognizer.state {
        case .began:
            
            break
            
        case .changed:
            
            let speed = cognizer.velocity(in: self)
            
            if self.direction == .horizontal {
                let detal = speed.x/100.0
                self.progress += Float(detal)/100.0
                if let progressBlock = self.currentProgressBlock {
                    progressBlock(value)
                }
            }else {
                let detal = speed.y/100.0
                self.progress -= Float(detal/100.0)
                if let progressBlock = self.currentProgressBlock {
                    progressBlock(value)
                }
            }
            break
        case .ended:
            break
        default:
            break
        }
    }
    
}
