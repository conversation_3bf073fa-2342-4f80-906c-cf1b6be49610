//
//  LPAlertView.swift
//  LivePlus
//
//  Created by 郭炜 on 2022/1/24.
//

import UIKit

enum AlertOption {
    typealias AlertAction = () -> Void
    
    case gray(title: String, action: AlertAction?)
    case main(title: String, action: AlertAction?)
    case custom(title: String,
                action: AlertAction?,
                titleColor: UIColor,
                backColor: UIColor,
                borderColor: UIColor = .clear,
                font: UIFont = LCDevice.DIN_Font_PF_R(14))
    
    var backColor: UIColor {
        switch self {
        case .gray: return UIColor("#FFFFFF")
        case .main: return UIColor("#6863F7")
        case .custom( _, _, _, let backColor, _, _): return backColor
        }
    }
    
    var borderColor: UIColor {
        switch self {
        case .gray: return UIColor("#AC63F9")
        case .main: return UIColor.clear
        case .custom( _, _, _, _, let borderColor, _): return borderColor
        }
    }
    
    var titleColor: UIColor {
        switch self {
        case .gray: return UIColor("#19191A")
        case .main: return UIColor("#FFFFFF")
        case .custom( _, _, let titleColor, _, _, _): return titleColor
        }
    }
    
    var font: UIFont {
        switch self {
        case .gray: return LCDevice.DIN_Font_PF_R(14)
        case .main: return LCDevice.DIN_Font_PF_M(14)
        case .custom( _, _, _, _, _, let font): return font
        }
    }
    
    var rawValue: Int {
        switch self {
        case .gray: return 1000
        case .main: return 1001
        case .custom: return 1002
        }
    }
}

class AlertView: UIView {
    
    private lazy var backView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.white
        view.cornerRadius = 30
        return view
    }()
    
    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.textColor = UIColor("#1E1F20")
        label.font = LCDevice.DIN_Font_PF_M(18)
        label.textAlignment = .center
        label.numberOfLines = 0
        return label
    }()
    
    private lazy var messageLabel: UITextView = {
        let textview = UITextView()
        textview.font = LCDevice.DIN_Font_PF_M(14)
        textview.textColor = UIColor("#4D4E52")
        textview.isUserInteractionEnabled = false
        textview.textAlignment = .center
        textview.delegate = self
        textview.linkTextAttributes = [
            .foregroundColor: UIColor("#6A65FF"),  // 设置链接颜色
            .font: LCDevice.DIN_Font_PF_M(15)
        ]
        return textview
    }()
    
    private lazy var stackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .horizontal
        stackView.alignment = .fill
        stackView.spacing = 24
        stackView.distribution = .fillEqually
        return stackView
    }()
    
    private lazy var desLabel: UILabel = {
        let label = UILabel()
        label.textColor = UIColor("#999999")
        label.font = UIFont.systemFont(ofSize: 12, weight: .regular)
        label.textAlignment = .center
        label.isHidden = true
        return label
    }()
    
    private var leftOption: AlertOption? = .gray(title: "", action: nil)
    private var rightOption: AlertOption? = .main(title: "", action: nil)
    private var title: String = ""
    private var message: String = ""
    
    private var attMessage: NSAttributedString?
    private var titleAtt: NSAttributedString?
    
    // 描述的文本 显示在按钮的下面
    private var desText: String?
    
    override init(frame: CGRect) {
        super.init(frame: frame)
    }

    convenience init(leftOption: AlertOption?,
                     rightOption: AlertOption?,
                     title: String,
                     message: String) {
        self.init(frame: CGRect(x: 0, y: 0, width: LCDevice.screenW, height: LCDevice.screenH))
        self.leftOption = leftOption
        self.rightOption = rightOption
        self.title = title
        self.message = message
        makeUI()
    }
    
    
    convenience init(leftOption: AlertOption?,
                     rightOption: AlertOption?,
                     title: String,
                     attMessage: NSAttributedString?) {
        self.init(frame: CGRect(x: 0, y: 0, width: LCDevice.screenW, height: LCDevice.screenH))
        self.leftOption = leftOption
        self.rightOption = rightOption
        self.title = title
        self.attMessage = attMessage
        makeUI()
    }
    
    convenience init(leftOption: AlertOption?,
                     rightOption: AlertOption?,
                     titleAtt: NSAttributedString,
                     message: String,
                     desText: String? = nil) {
        self.init(frame: CGRect(x: 0, y: 0, width: LCDevice.screenW, height: LCDevice.screenH))
        self.leftOption = leftOption
        self.rightOption = rightOption
        self.titleAtt = titleAtt
        self.message = message
        self.desText = desText
        makeUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func makeUI() {
        backgroundColor = UIColor(white: 0.0, alpha: 0.5)
        let leftButton = factoryButton(with: self.leftOption)
        let rightButton = factoryButton(with: self.rightOption)
        addSubview(backView)
        backView.addSubviews([titleLabel, messageLabel, stackView, desLabel])
                
        if let attMessage = attMessage {
            messageLabel.attributedText = attMessage
            messageLabel.isUserInteractionEnabled = true
            messageLabel.isEditable = false // 非编辑状态才能点击链接
//            messageLabel.isSelectable = false
            messageLabel.dataDetectorTypes = [.link] // 自动检测链接
        } else {
            messageLabel.text = message
        }
        if let titleAtt = titleAtt {
            titleLabel.attributedText = titleAtt
        } else {
            titleLabel.text = title
        }
        
        var messageHeight = message.sizeLineFeedWithFont(font: messageLabel.font ?? LCDevice.DIN_Font_PF_R(14), width: LCDevice.screenW - 24)
        if let attMessage = attMessage {
            messageHeight = attMessage.string.sizeLineFeedWithFont(font: LCDevice.DIN_Font_PF_R(16), width: LCDevice.screenW - 24)
        }

        // 不需要设置高度，让子控件约束即可
        backView.snp.makeConstraints { make in
            make.width.equalTo(LCDevice.screenW - 48)
            make.center.equalToSuperview()
        }
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(30)
            make.leading.trailing.equalToSuperview().inset(30)
        }
        messageLabel.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview().inset(30)
            make.top.equalTo(titleLabel.snp.bottom).offset(16)
            make.height.equalTo(messageHeight + 20) // 因为messageLabel是textView，所以这里要加个高度约束
        }
        
        if let desText = desText {
            self.desLabel.text = desText
            self.desLabel.isHidden = false
            stackView.snp.makeConstraints { make in
                make.leading.trailing.equalToSuperview().inset(40)
                make.bottom.equalToSuperview().inset(46)
                make.top.equalTo(messageLabel.snp.bottom).offset(20)
                make.height.equalTo(44)
            }
            
            desLabel.snp.makeConstraints { make in
                make.leading.trailing.equalToSuperview().inset(40)
                make.bottom.equalToSuperview().inset(12)
                make.height.equalTo(17)
            }
            
        } else {
            self.desLabel.isHidden = true
            stackView.snp.makeConstraints { make in
                make.leading.trailing.equalToSuperview().inset(40)
                make.bottom.equalToSuperview().inset(28)
                make.top.equalTo(messageLabel.snp.bottom).offset(20)
                make.height.equalTo(44)
            }
            
        }
        
        if let leftButton = leftButton {
            stackView.addArrangedSubview(leftButton)
        }
        if let rightButton = rightButton {
            stackView.addArrangedSubview(rightButton)
        }
    }
    
    private func factoryButton(with option: AlertOption?) -> UIButton? {
        guard let option = option else {
            return nil
        }

        var button: UIButton
        switch option {
        case .gray(let title, let action):
            button = UIButton(action: {
                AlertView.dismiss()
                if let action = action {
                    action()
                }
            })
            button.setTitle(title, for: .normal)
        case .main(let title, let action):
            button = UIButton(action: {
                AlertView.dismiss()
                if let action = action {
                    action()
                }
            })
            button.setTitle(title, for: .normal)
            button.applyGradient()
        case .custom(let title, let action, _, _, _, _):
            button = UIButton(action: {
                AlertView.dismiss()
                if let action = action {
                    action()
                }
            })
            button.setTitle(title, for: .normal)
        }
        button.setTitleColor(option.titleColor, for: .normal)
        button.backgroundColor = option.backColor
        button.borderColor = option.borderColor
        button.borderWidth = 1
        button.titleLabel?.font = option.font
        button.tag = option.rawValue
        button.cornerRadius = 22
        return button
    }
    
    private static func dismiss() {
        showingView.removeFromSuperview()
        isAlertShowing = false
    }
}

extension AlertView: UITextViewDelegate {
    func textView(_ textView: UITextView, shouldInteractWith URL: URL, in characterRange: NSRange, interaction: UITextItemInteraction) -> Bool {
        if URL.absoluteString == "linkValueRemote" {
            print("跳转到遥控器链接")
            AlertView.dismiss()
            
        }
        return false
    }
}

extension AlertView {
    private static var isAlertShowing: Bool = false
    private static var showingView: AlertView = AlertView(frame: UIScreen.main.bounds)
    public static func show(leftOption: AlertOption?,
                            rightOption: AlertOption?,
                            title: String,
                            message: String) {
        if isAlertShowing { print("重复弹窗"); return }
        guard let appDel = UIApplication.shared.delegate as? AppDelegate else { return }
        isAlertShowing = true
        showingView = AlertView(leftOption: leftOption, rightOption: rightOption, title: title, message: message)
        appDel.wd.addSubview(showingView)
    }
    
    public static func show(leftOption: AlertOption?,
                            rightOption: AlertOption?,
                            title: String,
                            attMessage: NSAttributedString) {
        if isAlertShowing { print("重复弹窗"); return }
        guard let appDel = UIApplication.shared.delegate as? AppDelegate else { return }
        isAlertShowing = true
        showingView = AlertView(leftOption: leftOption, rightOption: rightOption, title: title, attMessage: attMessage)
        appDel.wd.addSubview(showingView)
    }
    
    public static func show(leftOption: AlertOption?,
                            rightOption: AlertOption?,
                            titleAtt: NSAttributedString,
                            message: String,
                            desText: String? = nil) {
        if isAlertShowing { print("重复弹窗"); return }
        guard let appDel = UIApplication.shared.delegate as? AppDelegate else { return }
        isAlertShowing = true
        showingView = AlertView(leftOption: leftOption, rightOption: rightOption, titleAtt: titleAtt, message: message, desText: desText)
        appDel.wd.addSubview(showingView)
    }
}



// MARK: - 带背景图的弹窗

class BGAlertView: UIView {
    
    private var attMessage: NSAttributedString?
    
    typealias AlertAction = (Bool) -> Void
    
    var callBack: AlertAction?
    
    private lazy var backView: UIImageView = {
        let view = UIImageView(image: UIImage(named: "alert_background"))
        view.contentMode = .scaleAspectFill
        view.cornerRadius = 20
        view.isUserInteractionEnabled = true
        return view
    }()
    
    private lazy var iconView: UIImageView = {
        let view = UIImageView(image: UIImage(named: "温馨提示"))
        view.contentMode = .scaleAspectFit
        return view
    }()
    
    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.textColor = UIColor("#19191A")
        label.font = UIFont.systemFont(ofSize: 18, weight: .medium)
        label.textAlignment = .center
        label.text = "温馨提示"
        return label
    }()
    
    private lazy var messageLabel: UILabel = {
        let textview = UILabel()
        textview.font = UIFont.systemFont(ofSize: 15, weight: .regular)
        
        textview.numberOfLines = 0
        return textview
    }()
    
    private var gradientLayer: CAGradientLayer = {
        let leftColor = UIColor("#6974F2")
        let rightColor = UIColor("#AC63F9")
        let gradientColors = [leftColor.cgColor, rightColor.cgColor]
        let gradientLocations: [NSNumber] = [0.0, 1.0]
        let gradientLayer = CAGradientLayer()
        gradientLayer.startPoint = CGPoint(x: 0, y: 0)
        gradientLayer.endPoint = CGPoint(x: 1, y: 0)
        gradientLayer.colors = gradientColors
        gradientLayer.locations = gradientLocations
        return gradientLayer
    }()
    
    lazy var okButton: UIButton = {
        let button = UIButton(frame: CGRect(x: 0, y: 0, width: 170, height: 40))
        button.setTitle("知道了", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 15, weight: .regular)
        button.cornerRadius = 20
        button.addTarget(self, action: #selector(okAction), for: .touchUpInside)
        return button
    }()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        makeUI()
    }

    convenience init(attMessage: NSAttributedString?) {
        self.init(frame: UIScreen.main.bounds)
        self.attMessage = attMessage
        makeUI()
        self.messageLabel.attributedText = attMessage
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func makeUI() {
        backgroundColor = UIColor(white: 0.0, alpha: 0.5)
        addSubview(backView)
        
        backView.addSubviews([titleLabel, iconView, messageLabel, okButton])
        
        backView.snp.makeConstraints { make in
            make.width.equalTo(252)
            make.height.equalTo(222)
            make.center.equalToSuperview()
        }
        
        iconView.snp.makeConstraints { make in
            make.leading.equalToSuperview().inset(66)
            make.top.equalToSuperview().inset(17)
            make.height.width.equalTo(30)
        }
        
        
        titleLabel.snp.makeConstraints { make in
            make.leading.equalToSuperview().inset(98)
            make.top.equalToSuperview().inset(17)
            make.height.equalTo(30)
        }
        
        messageLabel.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview().inset(22)
            make.top.equalTo(titleLabel.snp.bottom).offset(20)
        }
        
      
        okButton.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.bottom.equalToSuperview().inset(30)
            make.height.equalTo(40)
            make.width.equalTo(170)
        }
        
        okButton.layer.insertSublayer(gradientLayer, at: 0)
        gradientLayer.frame = okButton.bounds
    }
    
   
    @objc func okAction() {
        if let back = self.callBack {
            back(true)
        }
        removeFromSuperview()
    }
    
}
