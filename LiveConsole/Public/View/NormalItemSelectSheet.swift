//
//  NormalItemSelectSheet.swift
//  LivePlus
//
//  Created by iclick on 2021/2/20.
//

import UIKit

///单选弹窗
class NormalItemSelectSheet: UIView {

    ///当前是否正在显示
    var isShowing = false
    
    ///按钮点击回调
    @objc var selectItemActionBlock:((_ index:Int) -> Void)?

    private let alert = UIView()

    private let kBtnBaseTag : Int = 100
    private var lastSelectButtonTag : Int = 0
    
    private let k_BtnTitleColor_normal = UIColor("#4D4E52")
    private let k_BtnTitleColor_select = UIColor("#777AFF")
    private let k_BtnBgColor_normal = UIColor.clear
    private let k_BtnBgColor_select = UIColor("#F4F5F6")
    private let k_BtnTitleFont_normal = LCDevice.DIN_Font_PF_M(14)
    private let k_BtnTitleFont_select = LCDevice.DIN_Font_PF_S(16)

    private var title = ""
    private var items = [String]()
    private var selectedIdx = 0
    private var _shouldMakeSure = false
    private var currentSelectedIndex = -1
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        let tapGes = UITapGestureRecognizer(target: self, action: #selector(dismiss))
        self.isUserInteractionEnabled = true
        self.addGestureRecognizer(tapGes)
    }
    
    ///shouldMakeSure：是：显示“确定”按钮，需要点击确定后消失弹窗
    @objc init(withTitle:String, items:[String], shouldMakeSure:Bool=false) {
        super.init(frame: UIScreen.main.bounds)
        let tapGes = UITapGestureRecognizer(target: self, action: #selector(dismiss))
        self.isUserInteractionEnabled = true
        self.addGestureRecognizer(tapGes)
        self.title = withTitle
        self.items = items
        self._shouldMakeSure = shouldMakeSure
        setupAlert()
    }

    required init?(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    // MARK: - Public methods
    @objc func show(selectedIndex:Int) {
        self.selectedIdx = selectedIndex
        backgroundColor = UIColor.black.alpha(value: 0.3)
        UIApplication.shared.keyWindow?.addSubview(self)
        display()
    }

    
    /// 显示
    private func display() {
        isShowing = true
        UIView.animate(withDuration: 0.25) {
            self.alert.top = LCDevice.screenH - self.alert.height
        } completion: { (_) in
            let path = UIBezierPath(roundedRect: self.alert.bounds, byRoundingCorners: [.topLeft,.topRight], cornerRadii: CGSize(width: LCDevice.DIN_WIDTH(24), height: LCDevice.DIN_WIDTH(24)))
            let masklayer = CAShapeLayer()
            masklayer.frame = self.alert.bounds
            masklayer.path = path.cgPath
            self.alert.layer.mask = masklayer
        }
        let lastBtn = alert.viewWithTag(lastSelectButtonTag) as! UIButton
        setItemButtonStyle(sender: lastBtn, isSelected: false)
        if  let curView = alert.viewWithTag(kBtnBaseTag+self.selectedIdx), curView.isKind(of: UIButton.self){
            let curBtn = curView as! UIButton
            setItemButtonStyle(sender: curBtn, isSelected: true)
        }
    }

    /// 隐藏
     @objc private func dismiss() {
        isShowing = false
        UIView.animate(withDuration: 0.25) {
            self.alert.top = LCDevice.screenH
        } completion: { (_) in
            self.removeFromSuperview()
        }
    }


    // MARK: - UI
    private func setupAlert() {
        let alertH = LCDevice.DIN_WIDTH(68) + LCDevice.DIN_WIDTH(48+6) * CGFloat(self.items.count) + LCDevice.DIN_WIDTH(84)
        
        alert.frame = CGRect(x: 0, y: LCDevice.screenH, width: LCDevice.screenW, height: alertH+LCDevice.X_BOTTOM_INSET)
        alert.backgroundColor = .white
        alert.layer.masksToBounds = true
        addSubview(alert)

        //title
        let title = UILabel(frame: CGRect(x: 0, y: LCDevice.DIN_WIDTH(24), width: alert.width, height: LCDevice.DIN_WIDTH(30)))
        title.text = self.title
        title.font = LCDevice.DIN_Font_PF_M(18)
        title.textColor = UIColor("#1E1F20")
        title.textAlignment = .center
        alert.addSubview(title)
        
        let line = UIView(frame: CGRect(x: LCDevice.DIN_WIDTH(59), y: LCDevice.DIN_WIDTH(67), width: LCDevice.DIN_WIDTH(258), height: LCDevice.DIN_WIDTH(1)))
        line.backgroundColor = UIColor("#E5E5E5")
        alert.addSubview(line)
        
        let items = self.items
        var curY = line.bottom + LCDevice.DIN_WIDTH(9)
        for (index,value) in items.enumerated() {
            let btn = UIButton(frame: CGRect(x: LCDevice.DIN_WIDTH(59), y: curY, width: LCDevice.DIN_WIDTH(258), height: LCDevice.DIN_WIDTH(48)))
            btn.tag = kBtnBaseTag + index
            btn.titleLabel?.font = k_BtnTitleFont_normal
            btn.setTitle(value, for: .normal)
            btn.setTitleColor(k_BtnTitleColor_normal, for: .normal)
            btn.backgroundColor = k_BtnBgColor_normal
            btn.layer.cornerRadius = 8
            btn.layer.masksToBounds = true
            btn.addTarget(self, action: #selector(itemBtnAction(sender:)), for: .touchUpInside)
            
            if index == 0{
                setItemButtonStyle(sender: btn, isSelected: true)
            }
            alert.addSubview(btn)
            curY += btn.height + LCDevice.DIN_WIDTH(4)
        }
        
        //cancel
        let cancelBtn = UIButton(frame: CGRect(x: LCDevice.DIN_WIDTH(37), y: alertH - LCDevice.DIN_WIDTH(44+23), width: LCDevice.DIN_WIDTH(302), height: LCDevice.DIN_WIDTH(44)))
        cancelBtn.backgroundColor = UIColor("#8B87FF")
        if _shouldMakeSure {
            cancelBtn.setTitle("确定", for: .normal)
        }else{
            cancelBtn.setTitle("取消", for: .normal)
        }
        cancelBtn.titleLabel?.font = LCDevice.DIN_Font_PF_M(16)
        cancelBtn.setTitleColor(.white, for: .normal)
        cancelBtn.layer.masksToBounds = true
        cancelBtn.layer.cornerRadius = 8
        cancelBtn.addTarget(self, action: #selector(cancelBtnAction), for: .touchUpInside)
        alert.addSubview(cancelBtn)
    }

    // MARK: - Btn action
    @objc private func itemBtnAction(sender:UIButton) {
        let lastBtn = alert.viewWithTag(lastSelectButtonTag) as! UIButton
        setItemButtonStyle(sender: lastBtn, isSelected: false)
        setItemButtonStyle(sender: sender, isSelected: true)
        if self._shouldMakeSure {
            //需要确定，此处不返回结果,先把结果记录下
            currentSelectedIndex = sender.tag - kBtnBaseTag
        }else{
            //不需要确定，直接返回结果
            if let block = selectItemActionBlock {
                block(sender.tag - kBtnBaseTag)
            }
            dismiss()
        }
    }

    @objc private func cancelBtnAction() {
        if self._shouldMakeSure {
            //需要把选择结果回调出去
            if let block = selectItemActionBlock,currentSelectedIndex>=0 {
                block(currentSelectedIndex)
            }
            dismiss()
        }else{
            dismiss()
        }
    }

    
    // MARK: - Private methods
    
    private func setItemButtonStyle(sender:UIButton,isSelected:Bool) {
        if isSelected {
            lastSelectButtonTag = sender.tag
            sender.setTitleColor(k_BtnTitleColor_select, for: .normal)
            sender.titleLabel?.font = k_BtnTitleFont_select
            sender.backgroundColor = k_BtnBgColor_select
        }else{
            sender.setTitleColor(k_BtnTitleColor_normal, for: .normal)
            sender.titleLabel?.font = k_BtnTitleFont_normal
            sender.backgroundColor = k_BtnBgColor_normal
        }
        
    }
    
}
