//
//  VersionUpdateAlert.swift
//  LivePlus
//
//  Created by iclick on 2021/1/20.
//

import UIKit

///版本更新弹窗
class VersionUpdateAlert: UIView {
    static var isHasShowVersionAlert: Bool = false
    ///确定按钮点击回调
    var sureBtnActionBlock:(() -> Void)?
    
//    private var isShowing = false

    private let alert = UIView()
    private let sureBtn = UIButton()
    private var updateTitle: String?
    private var contentStr = ""
    private var isForceUpdate = false
    private var versionStr = ""
    
    override init(frame: CGRect) {
        super.init(frame: frame)
    }
    
    required init?(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Public methods
    func show(with model: VersionUpdateInfoModel) {
        VersionUpdateAlert.isHasShowVersionAlert = true
        backgroundColor = UIColor.black.alpha(value: 0.6)
        guard let appDel = UIApplication.shared.delegate as? AppDelegate else { return }
        appDel.wd.addSubview(self)
//        isShowing = true
        updateTitle = model.title
        if let content = model.updateDescription {
            contentStr = content
        }
        isForceUpdate = model.forceUpdate ?? false
        versionStr = "V\(model.newVersion ?? "")"
        setupAlert()
    }

    // MARK: - UI
    private func setupAlert() {
        alert.frame = CGRect(x: (LCDevice.screenW - LCDevice.DIN_WIDTH(333))/2, y: LCDevice.DIN_WIDTH(150), width: LCDevice.DIN_WIDTH(333), height: LCDevice.DIN_HEIGHT(416))
//        alert.frame = CGRect(x: 21, y: LCDevice.DIN_WIDTH(150), width: LCDevice.screenW - 42, height: 0)
        alert.backgroundColor = .clear
        alert.layer.cornerRadius = LCDevice.DIN_WIDTH(20)
        alert.layer.masksToBounds = true
        addSubview(alert)
        
//        let imgV = UIImageView(frame: CGRect(x: 0, y: 0, width: alert.width, height: LCDevice.DIN_WIDTH(149)))
//        imgV.image = UIImage(named: "Icon_update_rocker")
//        alert.addSubview(imgV)
        
        //version
//        let versionLbl = UILabel(frame: CGRect(x: LCDevice.DIN_WIDTH(98), y: LCDevice.DIN_WIDTH(64), width: LCDevice.DIN_WIDTH(77), height: LCDevice.DIN_WIDTH(26)))
//        versionLbl.text = versionStr
//        versionLbl.font = LCDevice.DIN_Font_PF_S(16)
//        versionLbl.textColor = UIColor("#6974F2")
//        versionLbl.textAlignment = .center
//        versionLbl.backgroundColor = UIColor.white
//        versionLbl.layer.cornerRadius = LCDevice.DIN_WIDTH(13)
//        versionLbl.layer.masksToBounds = true
//        alert.addSubview(versionLbl)
        
        
        //白色背景
        let bgView = UIView(frame: CGRect(x: 0, y: 0, width: alert.width, height: 0))
        bgView.backgroundColor = .white
        alert.addSubview(bgView)
        
        //title
        let title = UILabel(frame: CGRect(x: 0, y: LCDevice.DIN_WIDTH(26), width: alert.width, height: LCDevice.DIN_WIDTH(30)))
        if let titleStr = updateTitle {
            title.text = titleStr
        } else {
            title.text = "新版本更新\(versionStr)"
        }
        title.font = LCDevice.DIN_Font_PF_M(20)
        title.textColor = UIColor("#1E1F20")
        title.textAlignment = .center
        alert.addSubview(title)
        
        //content
        
//        let contentLab = YYLabel(frame: CGRect(x: xMargin, y: title.bottom + LCDevice.DIN_WIDTH(8), width: alert.width-xMargin*2, height: 0))
//        let contentAttString = NSMutableAttributedString(string: contentStr)
//        let pStyle = NSMutableParagraphStyle()
//        pStyle.lineSpacing = LCDevice.DIN_WIDTH(4)
//        contentAttString.addAttribute(NSAttributedString.Key.paragraphStyle, value: pStyle, range: (contentStr as NSString).rangeOfAll())
//        contentAttString.font = LCDevice.DIN_Font_PF_R(14)
//        contentAttString.color = UIColor("#83858A")
        
//        //计算文本实际高度
//        let contentTextLayout = YYTextLayout(containerSize: CGSize(width: contentLab.width, height: 1000), text: contentAttString)
//        let contentRealHeight = (contentTextLayout?.textBoundingSize.height)!
//        contentLab.height = contentRealHeight
//        contentLab.numberOfLines = 0
//        contentLab.attributedText = contentAttString
//        contentLab.textVerticalAlignment = .top
//        alert.addSubview(contentLab)
        
        let xMargin = LCDevice.DIN_WIDTH(40)
        let contentTxt = UITextView(frame: CGRect(x: xMargin, y: title.bottom + LCDevice.DIN_WIDTH(5), width: alert.width - xMargin * 2, height: LCDevice.DIN_HEIGHT(265)))
        contentTxt.text = contentStr
        contentTxt.textColor = UIColor("#4D4E52")
        contentTxt.font = LCDevice.DIN_Font_PF_R(14)
        contentTxt.isUserInteractionEnabled = true
        contentTxt.isEditable = false
        contentTxt.isSelectable = false
        alert.addSubview(contentTxt)
        
        //计算最终高度
//        let bottomBtnH = isForceUpdate ? LCDevice.DIN_WIDTH(36+10) : LCDevice.DIN_WIDTH(36+48)
        alert.center = self.center
        
        bgView.height = alert.height
        
        let btnW = LCDevice.DIN_WIDTH(142)
        if !isForceUpdate {
            let afterBtn = UIButton(frame: CGRect(x: LCDevice.DIN_WIDTH(20), y: alert.height-LCDevice.DIN_WIDTH(24+44), width: btnW, height: LCDevice.DIN_WIDTH(44)))
            afterBtn.setTitle("暂不升级", for: .normal)
            afterBtn.setTitleColor(UIColor("#919499"), for: .normal)
            afterBtn.titleLabel?.font = LCDevice.DIN_Font_PF_M(14)
            afterBtn.addTarget(self, action: #selector(afterBtnAction), for: .touchUpInside)
//            afterBtn.borderWidth = 1
//            afterBtn.borderColor = UIColor("#6974F2")
            afterBtn.backgroundColor = UIColor("#F6F5F8")
            afterBtn.layer.masksToBounds = true
            afterBtn.layer.cornerRadius = afterBtn.height/2
            alert.addSubview(afterBtn)
        }
        
        let afX = !isForceUpdate ? LCDevice.DIN_WIDTH(20 + 142 + 9) : LCDevice.DIN_WIDTH(50)
        let bW =  !isForceUpdate ? LCDevice.DIN_WIDTH(142) : LCDevice.DIN_WIDTH(233)
        //立即更新
        let sureBtn = UIButton(frame: CGRect(x: afX, y: alert.height-LCDevice.DIN_WIDTH(24+44), width: bW, height: LCDevice.DIN_WIDTH(44)))
        sureBtn.setTitle("立即升级", for: .normal)
        sureBtn.backgroundColor = UIColor("#6974F2")
        sureBtn.setTitleColor(.white, for: .normal)
        sureBtn.titleLabel?.font = LCDevice.DIN_Font_PF_M(14)
        sureBtn.layer.masksToBounds = true
        sureBtn.layer.cornerRadius = sureBtn.height/2
        sureBtn.addTarget(self, action: #selector(sureBtnAction), for: .touchUpInside)
        alert.addSubview(sureBtn)
        
      
    }
    
    // MARK: - Btn action
    @objc private func afterBtnAction() {
        removeFromSuperview()
//        isShowing = false
        VersionUpdateAlert.isHasShowVersionAlert = false
    }
    
    @objc private func sureBtnAction() {
        Router.openAppStore()
    }

}
