//
//  HUD.swift
//  LivePlus
//
//  Created by iclick on 2020/12/28.
//
//  来自Github地址: https://github.com/stackhou/SwiftProgressHUD

import UIKit

private let yj_topBarTag: Int = 1001
private let yj_showHUDBackColor = UIColor(red: 0, green: 0, blue: 0, alpha: 0.6)
private let yj_showHUDWhitColor = UIColor("#F5F5F8")
/// Display type
public enum HUDType {
    case success
    case fail
    case info
}

//------------- API START -------------
@objc class HUD: NSObject {
    
    /// Set keyWindow Mask background color
    static public var hudBackgroundColor: UIColor = UIColor.clear {
        didSet {
            SwiftProgress.hudBackgroundColor = hudBackgroundColor
        }
    }
    
    /// Setting the number of manual hides
    static public var hideHUDTaps: Int = 2 {
        didSet {
            SwiftProgress.hideHUDTaps = hideHUDTaps
        }
    }
    
    /// Wait for loading...
    @discardableResult
    @objc public class func showWait() -> UIWindow? {
        if let _ = UIApplication.shared.keyWindow {
            return SwiftProgress.wait()
        }
        return nil
    }
    
    /// Success
    @discardableResult
    @objc static func showSuccess(_ text: String, autoClear: Bool = true, autoClearTime: Int = 2) -> UIWindow? {
        if let _ = UIApplication.shared.keyWindow {
            return SwiftProgress.showNoticeWithText(HUDType.success, text: text, autoClear: autoClear, autoClearTime: autoClearTime)
        }
        return nil
    }
    
    /// Success
    @discardableResult
    @objc static func showSaveText(_ text: String, autoClear: Bool = true, autoClearTime: Int = 2) -> UIWindow? {
        if let _ = UIApplication.shared.keyWindow {
            return SwiftProgress.showNoticeWithText(HUDType.success, text: text, autoClear: autoClear, autoClearTime: autoClearTime, font: 20)
        }
        return nil
    }
    
    /// Fail
    @discardableResult
    @objc static func showFail(_ text: String, autoClear: Bool = true, autoClearTime: Int = 2) -> UIWindow? {
        if let _ = UIApplication.shared.keyWindow {
            return SwiftProgress.showNoticeWithText(HUDType.fail, text: text, autoClear: autoClear, autoClearTime: autoClearTime)
        }
        return nil
    }
    
    /// Hint information
    @discardableResult
    @objc public class func showInfo(_ text: String, autoClear: Bool = true, autoClearTime: Int = 2, animateTime: Double = 0.2) -> UIWindow? {
        if let _ = UIApplication.shared.keyWindow {
            return SwiftProgress.showNoticeWithText(HUDType.info, text: text, autoClear: autoClear, autoClearTime: autoClearTime, animateTime: animateTime)
        }
        return nil
    }
    
    /// Prompt free type
    @discardableResult
    public class func show(_ text: String, type: HUDType, autoClear: Bool, autoClearTime: Int = 2) -> UIWindow? {
        if let _ = UIApplication.shared.keyWindow {
            return SwiftProgress.showNoticeWithText(type, text: text, autoClear: autoClear, autoClearTime: autoClearTime)
        }
        return nil
    }
    
    /// Only display text
    @discardableResult
    public class func showOnlyText(_ text: String) -> UIWindow? {
        if let _ = UIApplication.shared.keyWindow {
            return SwiftProgress.showText(text)
        }
        return nil
    }
    
    /// Status bar prompt
    @discardableResult
    public class func showOnNavigation(_ text: String, autoClear: Bool = true, autoClearTime: Int = 1, textColor: UIColor = UIColor.black, fontSize: CGFloat = 13, backgroundColor: UIColor = UIColor.white) -> UIWindow? {
        if let _ = UIApplication.shared.keyWindow {
            
            return SwiftProgress.noticeOnNavigationBar(text, autoClear: autoClear, autoClearTime: autoClearTime, textColor: textColor, fontSize: fontSize, backgroundColor: backgroundColor)
        }
        return nil
    }
    
    /// Animated picture array
    @discardableResult
    public class func showGif(timeMilliseconds: Int = 100, backgroundColor: UIColor = UIColor.clear, scale: Double = 1.0) -> UIWindow? {
        if let _ = UIApplication.shared.keyWindow {
            return SwiftProgress.wait(timeMilliseconds: timeMilliseconds, backgroundColor: yj_showHUDBackColor, scale: scale)
        }
        return nil
    }
    
    /// Clear all
    public class func hideAllHUD() {
        SwiftProgress.clear()
    }
    
    @discardableResult
    @objc static func showCustomView() -> UIWindow? {
        if let _ = UIApplication.shared.keyWindow {
            return SwiftProgress.showCustomView()
        }
        return nil
    }
}
//----------------- API END -------------------

class SwiftProgress: NSObject {
    
    static var hudBackgroundColor: UIColor = UIColor.clear
    static var hideHUDTaps: Int = 2
    static var windows = [UIWindow]()
    static let rv = UIApplication.shared.keyWindow?.subviews.first //as UIView!
    static var timer: DispatchSource!
    static var timerTimes = 0
    
    /* just for iOS 8 */
    static var degree: Double {
        get {
            return [0, 0, 180, 270, 90][UIApplication.shared.statusBarOrientation.hashValue] as Double
        }
    }
    
    static func clear() {
        self.cancelPreviousPerformRequests(withTarget: self)
        if let _ = timer {
            timer.cancel()
            timer = nil
            timerTimes = 0
        }
        windows.removeAll(keepingCapacity: false)
    }
    
    @discardableResult
    static func noticeOnNavigationBar(_ text: String, autoClear: Bool, autoClearTime: Int, textColor: UIColor,
                                      fontSize: CGFloat, backgroundColor: UIColor) -> UIWindow{
        SwiftProgress.clear()
        let statusBarFrame = UIApplication.shared.statusBarFrame
        let frame = CGRect(x: 0, y: 0, width: statusBarFrame.width, height: (statusBarFrame.height + 44))
        let window = UIWindow()
        window.rootViewController = HUDVC()
        window.backgroundColor = UIColor.clear
        let view = UIView()
        view.backgroundColor = backgroundColor
        let label = UILabel(frame: CGRect(x: 0, y: statusBarFrame.height, width: frame.width, height: (frame.height - 44)))
        label.textAlignment = NSTextAlignment.center
        label.font = UIFont.systemFont(ofSize: fontSize)
        label.textColor = textColor
        label.text = text
        view.addSubview(label)
        
        window.frame = frame
        view.frame = frame
        
        if let version = Double(UIDevice.current.systemVersion),
            version < 9.0 {
            // change center
            var array = [UIScreen.main.bounds.width, UIScreen.main.bounds.height]
            array = array.sorted(by: <)
            let screenWidth = array[0]
            let screenHeight = array[1]
            let x = [0, screenWidth/2, screenWidth/2, 10, screenWidth-10][UIApplication.shared.statusBarOrientation.hashValue] as CGFloat
            let y = [0, 10, screenHeight-10, screenHeight/2, screenHeight/2][UIApplication.shared.statusBarOrientation.hashValue] as CGFloat
            window.center = CGPoint(x: x, y: y)
            
            // change direction
            window.transform = CGAffineTransform(rotationAngle: CGFloat(degree * Double.pi / 180))
        }
        
        window.windowLevel = UIWindow.Level.statusBar
        window.isHidden = false
        window.addSubview(view)
        windows.append(window)
        
        var origPoint = view.frame.origin
        origPoint.y = -(view.frame.size.height)
        let destPoint = view.frame.origin
        view.tag = yj_topBarTag
        
        view.frame = CGRect(origin: origPoint, size: view.frame.size)
        UIView.animate(withDuration: 0.3, animations: {
            view.frame = CGRect(origin: destPoint, size: view.frame.size)
        }, completion: { b in
            if autoClear {
                
                DispatchQueue.global().asyncAfter(deadline: DispatchTime.now() + .seconds(autoClearTime), execute: {
                    DispatchQueue.main.async {
                        UIView.animate(withDuration: 0.3, animations: {
                            /// Vanishing animation
                            view.frame = CGRect(origin: origPoint, size: view.frame.size)
                        }, completion: { (b) in
                            let selector = #selector(SwiftProgress.hideNotice(_:))
                            self.perform(selector, with: window, afterDelay: TimeInterval(autoClearTime))
                        })
                    }
                })
            }
        })
        return window
    }
    
    @discardableResult
    static func wait(timeMilliseconds: Int = 100, backgroundColor: UIColor = yj_showHUDBackColor, scale: Double = 1.0) -> UIWindow {
        SwiftProgress.clear()
        
//        let gifImgs = GifTool.getHUDImgs()
//        let frame = CGRect(x: 0, y: 0, width: 78, height: 78)
        let frame = CGRect(x: 0, y: 0, width: 44, height: 44)
        let window = UIWindow()
        window.backgroundColor = hudBackgroundColor
        window.rootViewController = HUDVC()
        let mainView = UIView()
        mainView.layer.cornerRadius = 12
        mainView.backgroundColor = backgroundColor
        
        /// add tapGesture
        let tapGesture = UITapGestureRecognizer.init(target: self, action: #selector(tapHideGesture(gesture:)))
        tapGesture.numberOfTapsRequired = hideHUDTaps
        window.addGestureRecognizer(tapGesture)
        
//        let imgViewFrame = CGRect(x: Double(frame.size.width) * (1 - scale) * 0.5, y: Double(frame.size.height) * (1 - scale) * 0.5,
//                                  width: Double(frame.size.width) * scale, height: Double(frame.size.height) * scale)
//
//        if gifImgs.count > 0 {
//            if gifImgs.count > timerTimes {
//                let iv = UIImageView(frame: imgViewFrame)
//                iv.image = gifImgs.first!
//                iv.contentMode = UIViewContentMode.scaleAspectFit
//                mainView.addSubview(iv)
//                timer = DispatchSource.makeTimerSource(flags: DispatchSource.TimerFlags(rawValue: UInt(0)), queue: DispatchQueue.main) as! DispatchSource
//                timer.scheduleRepeating(deadline: DispatchTime.now(), interval: DispatchTimeInterval.milliseconds(timeMilliseconds))
//                timer.setEventHandler(handler: { () -> Void in
//                    let img = gifImgs[timerTimes % gifImgs.count]
//                    iv.image = img
//                    timerTimes += 1
//                })
//                timer.resume()
//            }
//        } else {
        let ai = UIActivityIndicatorView(style: .white)
            ai.frame = CGRect(x: 0, y: 0, width: 20, height: 20)
            ai.startAnimating()
            mainView.addSubview(ai)
//        }
        
        window.frame = rv!.bounds
        mainView.frame = frame
        mainView.center = rv!.center
        ai.center = CGPoint(x: mainView.width/2, y: mainView.height/2)
        
        if #available(iOS 9.0, *) {
            
        } else {
            // change center
            window.center = getRealCenter()
            // change direction
            window.transform = CGAffineTransform(rotationAngle: CGFloat(degree * Double.pi / 180))
        }
        
        window.windowLevel = UIWindow.Level.alert
        window.isHidden = false
        window.addSubview(mainView)
        windows.append(window)
        
        mainView.alpha = 0.0
        UIView.animate(withDuration: 0.2, animations: {
            mainView.alpha = 1
        })
        return window
    }
    
    @discardableResult
    static func showText(_ text: String, autoClear: Bool=true, autoClearTime: Int = 2) -> UIWindow {
        SwiftProgress.clear()
        
        let window = UIWindow()
        window.backgroundColor = hudBackgroundColor
        window.rootViewController = HUDVC()
        let mainView = UIView()
        mainView.layer.cornerRadius = 12
        mainView.backgroundColor = yj_showHUDBackColor
        
        /// add tapGesture
        let tapGesture = UITapGestureRecognizer.init(target: self, action: #selector(tapHideGesture(gesture:)))
        tapGesture.numberOfTapsRequired = hideHUDTaps
        window.addGestureRecognizer(tapGesture)
        
        let label = UILabel()
        label.text = text
        label.numberOfLines = 0
        label.font = UIFont.systemFont(ofSize: 13)
        label.textAlignment = NSTextAlignment.center
        label.textColor = UIColor.white
        let size = label.sizeThatFits(CGSize(width: UIScreen.main.bounds.width-82, height: CGFloat.greatestFiniteMagnitude))
        label.bounds = CGRect(x: 0, y: 0, width: size.width, height: size.height)
        mainView.addSubview(label)
        
        let superFrame = CGRect(x: 0, y: 0, width: label.frame.width + 50, height: label.frame.height + 30)
        
        window.frame = rv!.bounds
        mainView.frame = superFrame
        
        label.center = mainView.center
        mainView.center = rv!.center
        
        if let version = Double(UIDevice.current.systemVersion),
            version < 9.0 {
            // change center
            window.center = getRealCenter()
            // change direction
            window.transform = CGAffineTransform(rotationAngle: CGFloat(degree * Double.pi / 180))
        }
        
        window.windowLevel = UIWindow.Level.alert
        window.isHidden = false
        window.addSubview(mainView)
        windows.append(window)
        
        if autoClear {
            let selector = #selector(SwiftProgress.hideNotice(_:))
            self.perform(selector, with: window, afterDelay: TimeInterval(autoClearTime))
        }
        return window
    }
    
//    //大的提示
//    @discardableResult
//    static func showNoticeWithText(_ type: HUDType,text: String, autoClear: Bool, autoClearTime: Int) -> UIWindow {
//        SwiftProgress.clear()
//
//        let font = UIFont.systemFont(ofSize: 13)
//        let txtH = (text as NSString).height(for: font, width: LCDevice.DIN_WIDTH(70))
//
//        let window = UIWindow()
//        window.backgroundColor = hudBackgroundColor
//        window.rootViewController = HUDVC()
//        let mainView = UIView()
//        mainView.layer.cornerRadius = 10
//        mainView.backgroundColor = .white
//
//        /// add tapGesture
//        let tapGesture = UITapGestureRecognizer.init(target: self, action: #selector(tapHideGesture(gesture:)))
//        tapGesture.numberOfTapsRequired = hideHUDTaps
//        window.addGestureRecognizer(tapGesture)
//
//        var image = UIImage()
//        switch type {
//        case .success:
//            image = UIImage.init(named: "icon_success")!
//        case .fail:
//            image = UIImage.init(named: "icon_fail")!
//        case .info:
//            image = UIImage.init(named: "icon_success")!
//        }
//        let checkmarkView = UIImageView(image: image)
//        checkmarkView.frame = CGRect(x: 0, y: 0, width: 100, height: 100)
//        mainView.addSubview(checkmarkView)
//
//        let label = UILabel(frame: CGRect(x: 15, y: 80, width: 70, height: txtH))
//        label.font = font
//        label.numberOfLines = 0
//        label.text = text
//        label.textColor = SColor.BASE_BLACK
//        label.textAlignment = .center
//        mainView.addSubview(label)
//
//        let frame = CGRect(x: 0, y: LCDevice.screenH/2-50, width: 100, height: max(100, label.bottom+7.5))
//
//        window.frame = rv!.bounds
//        mainView.frame = frame
//        mainView.center = rv!.center
//
//        if let version = Double(UIDevice.current.systemVersion),
//            version < 9.0 {
//            // change center
//            window.center = getRealCenter()
//            // change direction
//            window.transform = CGAffineTransform(rotationAngle: CGFloat(degree * Double.pi / 180))
//        }
//
//        window.windowLevel = UIWindowLevelAlert
//        window.center = rv!.center
//        window.isHidden = false
//        window.addSubview(mainView)
//        windows.append(window)
//
//        mainView.alpha = 0.0
//        UIView.animate(withDuration: 0.2, animations: {
//            mainView.alpha = 1
//        })
//
//        if autoClear {
//            let selector = #selector(SwiftProgress.hideNotice(_:))
//            self.perform(selector, with: window, afterDelay: TimeInterval(autoClearTime))
//        }
//        return window
//    }
    
    @discardableResult
    static func showCustomView() -> UIWindow {
        SwiftProgress.clear()
        let frame = CGRect(x: 0, y: LCDevice.screenH/2, width: 280, height: 40)

        let window = UIWindow()
        window.backgroundColor = .clear
        window.rootViewController = HUDVC()

        /// add tapGesture
        let tapGesture = UITapGestureRecognizer.init(target: self, action: #selector(tapHideGesture(gesture:)))
        tapGesture.numberOfTapsRequired = hideHUDTaps
        window.addGestureRecognizer(tapGesture)
        

        let label = BackupSaveTipView()
        label.frame = frame
        
        label.backgroundColor = yj_showHUDBackColor
        label.cornerRadius = 6

        window.frame = rv!.bounds
        label.center = rv!.center
        
        window.windowLevel = UIWindow.Level.alert
        window.center = rv!.center
        window.isHidden = false
        window.addSubview(label)
        windows.append(window)
        
        let selector = #selector(SwiftProgress.hideNotice(_:))
        self.perform(selector, with: window, afterDelay: TimeInterval(3.5))
        window.isUserInteractionEnabled = false
        label.startTimer()
        return window
    }

    
//    小的,toast样子的提示
    @discardableResult
    static func showNoticeWithText(_ type: HUDType, text: String, autoClear: Bool, autoClearTime: Int, animateTime: Double = 0.2, font: CGFloat = 14) -> UIWindow {
        SwiftProgress.clear()

        let font = LCDevice.DIN_Font_PF_M(font)
        let maxW =  (type == .success ? LCDevice.DIN_WIDTH(250) : LCDevice.DIN_WIDTH(200))
        
        let txtH = (text as NSString).height(for: font, width: maxW)
        let txtW = (text as NSString).size(for: font, size: CGSize(width: maxW, height: txtH), mode: .byWordWrapping).width

        let maxH = (type == .success ? max(txtH, LCDevice.DIN_WIDTH(56)) : txtH+LCDevice.DIN_WIDTH(12*2))
        let frame = CGRect(x: 0, y: LCDevice.screenH/2, width: txtW+LCDevice.DIN_WIDTH(24*2), height: maxH)
        let window = UIWindow()
        window.backgroundColor = hudBackgroundColor
        window.rootViewController = HUDVC()
        let mainView = UIView()
        mainView.layer.cornerRadius = LCDevice.DIN_WIDTH(8)
        mainView.backgroundColor = yj_showHUDWhitColor

        /// add tapGesture
        let tapGesture = UITapGestureRecognizer.init(target: self, action: #selector(tapHideGesture(gesture:)))
        tapGesture.numberOfTapsRequired = hideHUDTaps
        window.addGestureRecognizer(tapGesture)
        
//        let sucFlowerView = UIImageView(frame: CGRect(x: 0, y: LCDevice.DIN_WIDTH(5), width: LCDevice.DIN_WIDTH(86), height: LCDevice.DIN_WIDTH(51)))
////        sucFlowerView.image = UIImage(named: "icon_tips_suc_flower")
//        sucFlowerView.isHidden = true
//        mainView.addSubview(sucFlowerView)
//
//        let checkmarkView = UIImageView()
//        checkmarkView.frame = CGRect(x: LCDevice.DIN_WIDTH(37), y: 0, width: LCDevice.DIN_WIDTH(20), height: LCDevice.DIN_WIDTH(14))
//        checkmarkView.contentMode = .scaleAspectFill
//        checkmarkView.centerY = frame.height/2
//        mainView.addSubview(checkmarkView)

        let label = UILabel(frame: CGRect(x: (frame.width-txtW)/2, y: (frame.height-txtH)/2, width: txtW, height: txtH))
        label.font = font
        label.numberOfLines = 0
        label.text = text
        label.textColor = UIColor("#1E1F20")
        label.textAlignment = .center
        mainView.addSubview(label)
        
//        switch type {
//        case .success :
////            label.numberOfLines = 1
////            sucFlowerView.isHidden = false
////            checkmarkView.isHidden = false
////            label.textColor = SColor.baseHintGray
////            label.left = LCDevice.DIN_WIDTH(5)
////            checkmarkView.image = UIImage(named: "icon_tips_suc_yes")!
//            break
//        case .fail:
////            sucFlowerView.isHidden = true
////            checkmarkView.isHidden = false
////            checkmarkView.width = LCDevice.DIN_WIDTH(4)
////            checkmarkView.image = UIImage(named: "icon_tips_error")!
////            label.textColor = .white
////            label.left = LCDevice.DIN_WIDTH(5)
//            break
//        case .info:
////            sucFlowerView.isHidden = true
////            checkmarkView.isHidden = true
////            label.textColor = .white
////            label.left = 0
////            label.width = frame.width
////            label.textAlignment = .center
//            break
//        }

        window.frame = rv!.bounds
        mainView.frame = frame
        mainView.center = rv!.center

        if let version = Double(UIDevice.current.systemVersion),
            version < 9.0 {
            // change center
            window.center = getRealCenter()
            // change direction
            window.transform = CGAffineTransform(rotationAngle: CGFloat(degree * Double.pi / 180))
        }

        window.windowLevel = UIWindow.Level.alert
        window.center = rv!.center
        window.isHidden = false
        window.addSubview(mainView)
        windows.append(window)

        mainView.alpha = 0.0
        UIView.animate(withDuration: animateTime, animations: {
            mainView.alpha = 1
        })

        if autoClear {
            let selector = #selector(SwiftProgress.hideNotice(_:))
            self.perform(selector, with: window, afterDelay: TimeInterval(autoClearTime))
        }
        return window
    }
    
    /// Repair window has not been removed
    @objc static func hideNotice(_ sender: AnyObject) {
        if let window = sender as? UIWindow {
            
            if let v = window.subviews.first {
                UIView.animate(withDuration: 0.2, animations: {
                    
                    if v.tag == yj_topBarTag {
                        v.frame = CGRect(x: 0, y: -v.frame.height, width: v.frame.width, height: v.frame.height)
                    }
                    v.alpha = 0
                }, completion: { b in
                    if let index = windows.index(where: { (item) -> Bool in
                        return item == window
                    }) {
                        windows.remove(at: index)
                    }
                })
            }
        }
    }
    
    // just for iOS 8
    static func getRealCenter() -> CGPoint {
        if UIApplication.shared.statusBarOrientation.hashValue >= 3 {
            return CGPoint(x: rv!.center.y, y: rv!.center.x)
        } else {
            return rv!.center
        }
    }
    
    /// tap Hide HUD
    @objc
    static func tapHideGesture(gesture: UITapGestureRecognizer) {
        clear()
    }
}

class SwiftProgressSDK {
    struct Cache {
        static var imageOfCheckmark: UIImage?
        static var imageOfCross: UIImage?
        static var imageOfInfo: UIImage?
    }
    class func draw(_ type: HUDType) {
        let checkmarkShapePath = UIBezierPath()
        
        // draw circle
        checkmarkShapePath.move(to: CGPoint(x: 36, y: 18))
        checkmarkShapePath.addArc(withCenter: CGPoint(x: 18, y: 18), radius: 17.5, startAngle: 0, endAngle: CGFloat(Double.pi*2), clockwise: true)
        checkmarkShapePath.close()
        
        switch type {
        case .success: // draw checkmark
            checkmarkShapePath.move(to: CGPoint(x: 10, y: 18))
            checkmarkShapePath.addLine(to: CGPoint(x: 16, y: 24))
            checkmarkShapePath.addLine(to: CGPoint(x: 27, y: 13))
            checkmarkShapePath.move(to: CGPoint(x: 10, y: 18))
            checkmarkShapePath.close()
        case .fail: // draw X
            checkmarkShapePath.move(to: CGPoint(x: 10, y: 10))
            checkmarkShapePath.addLine(to: CGPoint(x: 26, y: 26))
            checkmarkShapePath.move(to: CGPoint(x: 10, y: 26))
            checkmarkShapePath.addLine(to: CGPoint(x: 26, y: 10))
            checkmarkShapePath.move(to: CGPoint(x: 10, y: 10))
            checkmarkShapePath.close()
        case .info:
            checkmarkShapePath.move(to: CGPoint(x: 18, y: 6))
            checkmarkShapePath.addLine(to: CGPoint(x: 18, y: 22))
            checkmarkShapePath.move(to: CGPoint(x: 18, y: 6))
            checkmarkShapePath.close()
            
            UIColor.white.setStroke()
            checkmarkShapePath.stroke()
            
            let checkmarkShapePath = UIBezierPath()
            checkmarkShapePath.move(to: CGPoint(x: 18, y: 27))
            checkmarkShapePath.addArc(withCenter: CGPoint(x: 18, y: 27), radius: 1, startAngle: 0, endAngle: CGFloat(Double.pi*2), clockwise: true)
            checkmarkShapePath.close()
            
            UIColor.white.setFill()
            checkmarkShapePath.fill()
        }
        
        UIColor.white.setStroke()
        checkmarkShapePath.stroke()
    }
    class var imageOfCheckmark: UIImage {
        if (Cache.imageOfCheckmark != nil) {
            return Cache.imageOfCheckmark!
        }
        UIGraphicsBeginImageContextWithOptions(CGSize(width: 36, height: 36), false, 0)
        
        SwiftProgressSDK.draw(HUDType.success)
        
        Cache.imageOfCheckmark = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()
        return Cache.imageOfCheckmark!
    }
    class var imageOfCross: UIImage {
        if (Cache.imageOfCross != nil) {
            return Cache.imageOfCross!
        }
        UIGraphicsBeginImageContextWithOptions(CGSize(width: 36, height: 36), false, 0)
        
        SwiftProgressSDK.draw(HUDType.fail)
        
        Cache.imageOfCross = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()
        return Cache.imageOfCross!
    }
    class var imageOfInfo: UIImage {
        if (Cache.imageOfInfo != nil) {
            return Cache.imageOfInfo!
        }
        UIGraphicsBeginImageContextWithOptions(CGSize(width: 36, height: 36), false, 0)
        
        SwiftProgressSDK.draw(HUDType.info)
        
        Cache.imageOfInfo = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()
        return Cache.imageOfInfo!
    }
}

extension UIWindow {
    func hide() {
        SwiftProgress.hideNotice(self)
    }
}
class HUDVC: UIViewController {
    // MARK: - 不隐藏状态栏
    override var prefersStatusBarHidden: Bool {
        return false
    }
}

