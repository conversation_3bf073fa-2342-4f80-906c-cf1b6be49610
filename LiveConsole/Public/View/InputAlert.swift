//
//  InputAlert.swift
//  LivePlus
//
//  Created by iclick on 2021/1/4.
//

import UIKit

/// 我的直播间 名称修改 - 带输入框的alert
class InputAlert: UIView {

    ///确定按钮点击回调
    var sureBtnActionBlock:((_ string:String) -> Void)?
    
    var isShowing = false

    private let alert = UIView()
    private let sureBtn = UIButton()
    private let inputTF = UITextField()
    ///最大输入字符
    private var _maxCount = Int(25)
    
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupAlert()
    }
    
    required init?(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Public methods
    func show(_ placeholder:String = "", _ maxCount:Int = 25, selectAll: Bool = true) {
        _maxCount = maxCount
        backgroundColor = UIColor.black.alpha(value: 0.6)
        UIApplication.shared.keyWindow?.addSubview(self)
        isShowing = true
        inputTF.becomeFirstResponder()
        inputTF.placeholder = placeholder == "" ? "直播间模板名字新修改" : placeholder
        inputTF.text = placeholder == "" ? "直播间模板名字新修改" : placeholder
        sureBtn.setTitleColor(UIColor("#48494C").alpha(value: 0.3), for: .normal)
        sureBtn.isEnabled = false
        if selectAll {
            inputTF.selectAllText()
        }
        
    }

    // MARK: - UI
    private func setupAlert() {
        alert.frame = CGRect(x: LCDevice.DIN_WIDTH(53), y: LCDevice.DIN_WIDTH(200), width: LCDevice.DIN_WIDTH(270), height: LCDevice.DIN_WIDTH(174))
        alert.backgroundColor = .white
        alert.layer.cornerRadius = LCDevice.DIN_WIDTH(10)
        alert.layer.masksToBounds = true
        addSubview(alert)
        
        //title
        let title = UILabel(frame: CGRect(x: 0, y: LCDevice.DIN_WIDTH(24), width: alert.width, height: LCDevice.DIN_WIDTH(30)))
        title.text = "重命名"
        title.font = LCDevice.DIN_Font_PF_M(16)
        title.textColor = UIColor("#1E1F20")
        title.textAlignment = .center
        alert.addSubview(title)
        
        //input
        
        let inputBg = UIView(frame: CGRect(x: LCDevice.DIN_WIDTH(22), y: LCDevice.DIN_WIDTH(67), width: LCDevice.DIN_WIDTH(225), height: LCDevice.DIN_WIDTH(36)))
        inputBg.layer.cornerRadius = inputBg.height/2
        inputBg.layer.masksToBounds = true
        inputBg.layer.borderColor = UIColor("#6974F2").cgColor
        inputBg.layer.borderWidth = LCDevice.DIN_WIDTH(1)
        alert.addSubview(inputBg)
        
        inputTF.frame = CGRect(x: LCDevice.DIN_WIDTH(22), y: 0, width:inputBg.width - LCDevice.DIN_WIDTH(22*2), height: inputBg.height)
        inputTF.font = LCDevice.DIN_Font_PF_M(14)
        inputTF.textColor = UIColor("#48494C")
        inputTF.placeholder = "直播间模板名字新修改"
        inputTF.delegate = self
        inputTF.addTarget(self, action: #selector(inputTFValueDidChanged(sender:)), for: .editingChanged)
        inputBg.addSubview(inputTF)
        
        //line
        let lineView = UIView(frame: CGRect(x: 0, y: alert.height-LCDevice.DIN_WIDTH(53), width: alert.width, height: LCDevice.DIN_WIDTH(1)))
        lineView.backgroundColor = UIColor("#EBEBEB")
        alert.addSubview(lineView)
        
        //取消
        let bottomBtnH = LCDevice.DIN_WIDTH(53)
        let quitBtn = UIButton(frame: CGRect(x: 0, y: alert.height-bottomBtnH, width: alert.width/2, height: bottomBtnH))
        quitBtn.setTitle("取消", for: .normal)
        quitBtn.setTitleColor(UIColor("#48494C"), for: .normal)
        quitBtn.titleLabel?.font = LCDevice.DIN_Font_PF_M(14)
        quitBtn.addTarget(self, action: #selector(quitBtnAction), for: .touchUpInside)
        alert.addSubview(quitBtn)
        
        //sepline
        let seplineView = UIView(frame: CGRect(x: LCDevice.DIN_WIDTH(134), y: alert.height-LCDevice.DIN_WIDTH(53), width: LCDevice.DIN_WIDTH(1), height: LCDevice.DIN_WIDTH(53)))
        seplineView.backgroundColor = UIColor("#EBEBEB")
        alert.addSubview(seplineView)
        
        //sure
        sureBtn.frame = CGRect(x: alert.width/2, y: alert.height-bottomBtnH, width: alert.width/2, height: bottomBtnH)
        sureBtn.setTitle("确定", for: .normal)
        sureBtn.titleLabel?.font = LCDevice.DIN_Font_PF_M(14)
        sureBtn.addTarget(self, action: #selector(sureBtnAction), for: .touchUpInside)
        sureBtn.setTitleColor(UIColor("#48494C").alpha(value: 0.3), for: .normal)
        sureBtn.isEnabled = false
        alert.addSubview(sureBtn)
    }
    
    // MARK: - Btn action
    
    @objc func inputTFValueDidChanged(sender:UITextField) {
        if sender.text!.count>0 {
            sureBtn.isEnabled = true
            sureBtn.setTitleColor(UIColor("#48494C"), for: .normal)
        } else {
            sureBtn.isEnabled = false
            sureBtn.setTitleColor(UIColor("#48494C").alpha(value: 0.3), for: .normal)
        }
    }
    
    @objc private func quitBtnAction() {
        removeFromSuperview()
        isShowing = false
        inputTF.text = nil
    }
    
    @objc private func sureBtnAction() {
        removeFromSuperview()
        isShowing = false
        
        if let block = sureBtnActionBlock,let title = inputTF.text {
            block(title)
        }
        inputTF.text = nil
    }
}

extension InputAlert: UITextFieldDelegate{
    func textField(_ textField: UITextField, shouldChangeCharactersIn range: NSRange, replacementString string: String) -> Bool {
        //最多输25位
        if textField == inputTF , let text = textField.text{
            let str = (text as NSString).replacingCharacters(in: range, with: string)
            if str.count > _maxCount {
                return false
            }
        }
        return true
    }
}
