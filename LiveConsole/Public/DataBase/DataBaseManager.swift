//
//  DataBaseManager.swift
//  fastword
//
//  Created by 郭炜 on 2021/4/6.
//

import Foundation
import Realm
import RealmSwift

private let docmentPath = NSSearchPathForDirectoriesInDomains(FileManager.SearchPathDirectory.documentDirectory, FileManager.SearchPathDomainMask.userDomainMask, true)[0] as String

///之前旧版本的数据库文件，已经不再支持，需要删除
private let dataBasePath0 = docmentPath.appending("/default.realm")

private let dataBasePath1 = docmentPath.appending("/defaultDB.realm")

/// 当前版本的数据库文件地址
private let dataBasePath = docmentPath.appending("/defaultDB4.realm")

/// 2021.8.11 直播间改版，之前的直播间数据库不兼容，需要删除
private let dataBasePath2 = docmentPath.appending("/defaultDB2.realm")

/// 2021.11.9 直播间改版 之前的直播间数据库不兼容，需要删除
private let dataBasePath3 = docmentPath.appending("/defaultDB3.realm")


struct DataBaseManager {
    
    /// 配置数据库
    public static func globalConfigRealm() {
        
        do {
            
            let oldPath0 = URL(fileURLWithPath: dataBasePath0)
            let oldPath0_lock = URL(fileURLWithPath: dataBasePath0 + ".lock")
            let oldPath0_management = URL(fileURLWithPath: dataBasePath0 + ".management")
            
            let oldPath1 = URL(fileURLWithPath: dataBasePath1)
            let oldPath1_lock = URL(fileURLWithPath: dataBasePath1 + ".lock")
            let oldPath1_management = URL(fileURLWithPath: dataBasePath1 + ".management")
            
            let oldPath2 = URL(fileURLWithPath: dataBasePath2)
            let oldPath2_lock = URL(fileURLWithPath: dataBasePath2 + ".lock")
            let oldPath2_management = URL(fileURLWithPath: dataBasePath2 + ".management")
            
            let oldPath3 = URL(fileURLWithPath: dataBasePath3)
            let oldPath3_lock = URL(fileURLWithPath: dataBasePath3 + ".lock")
            let oldPath3_management = URL(fileURLWithPath: dataBasePath3 + ".management")
            
            if FileManager.default.fileExists(atPath: dataBasePath0) {
                try FileManager.default.removeItem(at: oldPath0)
            }
            
            if FileManager.default.fileExists(atPath: dataBasePath0 + ".lock") {
                try FileManager.default.removeItem(at: oldPath0_lock)
            }
            
            if FileManager.default.fileExists(atPath: dataBasePath0 + ".management") {
                try FileManager.default.removeItem(at: oldPath0_management)
            }
            
            if FileManager.default.fileExists(atPath: dataBasePath1) {
                try FileManager.default.removeItem(at: oldPath1)
            }
            
            if FileManager.default.fileExists(atPath: dataBasePath1 + ".lock") {
                try FileManager.default.removeItem(at: oldPath1_lock)
            }
            
            if FileManager.default.fileExists(atPath: dataBasePath1 + ".management") {
                try FileManager.default.removeItem(at: oldPath1_management)
            }
            
            if FileManager.default.fileExists(atPath: dataBasePath2) {
                try FileManager.default.removeItem(at: oldPath2)
            }
            
            if FileManager.default.fileExists(atPath: dataBasePath2 + ".lock") {
                try FileManager.default.removeItem(at: oldPath2_lock)
            }
            
            if FileManager.default.fileExists(atPath: dataBasePath2 + ".management") {
                try FileManager.default.removeItem(at: oldPath2_management)
            }
            
            if FileManager.default.fileExists(atPath: dataBasePath3) {
                try FileManager.default.removeItem(at: oldPath3)
            }
            
            if FileManager.default.fileExists(atPath: dataBasePath3 + ".lock") {
                try FileManager.default.removeItem(at: oldPath3_lock)
            }
            
            if FileManager.default.fileExists(atPath: dataBasePath3 + ".management") {
                try FileManager.default.removeItem(at: oldPath3_management)
            }
           
        } catch {
            LCLog.d("删除旧的数据库出错：\(error.localizedDescription)")
        }
        
        /// 这个方法主要用于数据模型属性增加或删除时的数据迁移，每次模型属性变化时，将 dbVersion 加 1 即可，Realm 会自行检测新增和需要移除的属性，然后自动更新硬盘上的数据库架构，移除属性的数据将会被删除。
        let dbVersion: UInt64 = 22

        var config = Realm.Configuration.defaultConfiguration
        
        config.schemaVersion = dbVersion
        config.shouldCompactOnLaunch = { totalBytes, bytesUsed in /// 进行压缩
            let overSizeMB = 50 * 1024 * 1024
            return (totalBytes > overSizeMB) && ( Double(bytesUsed) / Double(totalBytes) < 0.5)
        }
        var deleteRealm: Bool = false
        config.migrationBlock = { migration, oldSchemaVersion in
            if oldSchemaVersion < dbVersion {
                deleteRealm = true
            }
        }
        
        Realm.Configuration.defaultConfiguration = config
        Realm.asyncOpen { result in
            switch result {
            case .success(_): print("******************Realm 数据库配置成功!******************")
            case .failure(let error): print("******************Realm 数据库配置失败\(error.localizedDescription)!******************")
            }
        }
        if deleteRealm {
            RLMRealm.default().beginWriteTransaction()
            RLMRealm.default().deleteAllObjects()
            do {
                try RLMRealm.default().commitWriteTransaction()
                deleteRealm = false
            } catch {
                print("romoving realm error: \(error)")
            }
        }
    }
    
    /// 获取数据库
    public static func getCurrentDB() -> Realm? {
        if let url = URL(string: dataBasePath), let defaultRealm = try? Realm(fileURL: url) {
            print("数据库地址为-> \(defaultRealm.configuration.fileURL?.absoluteString ?? "")")
            return defaultRealm
        }
        return nil
    }
}

// MARK: - 新增
extension DataBaseManager {
    
    /// 新增单条数据
    /// - Parameter object: 数据模型
    public static func addObject<T>(object: T) where T: Object {
        guard let defaultRealm = self.getCurrentDB() else {
            print("新增单条数据失败 getCurrentDB返回空")
            return
        }
        do {
            try defaultRealm.write { defaultRealm.add(object) }
            print("新增单条数据成功 \(defaultRealm.configuration.fileURL?.absoluteString ?? "")")
        } catch {
            print("新增单条数据失败 error: \(error)")
        }
    }
    
    /// 新增多条数据
    /// - Parameter objects: 数据模型集合
    public static func addObjets<T>(objects: [T]) where T: Object {
        guard let defaultRealm = self.getCurrentDB() else {
            print("新增多条数据失败 getCurrentDB返回空")
            return
        }
        do {
            try defaultRealm.write { defaultRealm.add(objects, update: .error) }
            print("新增多条数据成功 \(defaultRealm.configuration.fileURL?.absoluteString ?? "")")
        } catch {
            print("新增多条数据失败 error: \(error)")
        }
    }
}

// MARK: - 删除
extension DataBaseManager {
    
    /// 删除单条数据
    /// - Parameter object: 数据模型
    public static func deleteObject<T>(object: T) where T: Object {
        guard let defaultRealm = self.getCurrentDB() else {
            print("删除单条数据失败 getCurrentDB返回空")
            return
        }
        do {
            try defaultRealm.write { defaultRealm.delete(object) }
            print("删除单条数据成功 \(defaultRealm.configuration.fileURL?.absoluteString ?? "")")
        } catch {
            print("删除单条数据失败 error: \(error)")
        }
    }
    
//    public static func deleteAll() {
//        guard let defaultRealm = self.getCurrentDB() else {
//            print("删除单条数据失败 getCurrentDB返回空")
//            return
//        }
//        do {
//            try FileManager.default.removeItem(at: Realm.Configuration.defaultConfiguration.fileURL!)
////        NSFileManager.defaultManager().removeItemAtURL(Realm.Configuration.defaultConfiguration.fileURL!)
//            defaultRealm.deleteAll();
//        } catch {
////
//        }
//    }
    
    /// 删除多条数据
    /// - Parameter objects: 数据模型集合
    public static func deleteObjects<T>(objects: [T]) where T: Object {
        guard let defaultRealm = self.getCurrentDB() else {
            print("删除多条数据失败 getCurrentDB返回空")
            return
        }
        do {
            try defaultRealm.write { defaultRealm.delete(objects) }
            print("删除多条数据成功 \(defaultRealm.configuration.fileURL?.absoluteString ?? "")")
        } catch {
            print("删除多条数据失败 error: \(error)")
        }
    }
    
    /// 根据条件删除单条/多条数据
    /// - Parameters:
    ///   - objectClass: 数据类型
    ///   - filter: 条件
    public static func deleteObjectFilter<T>(objectClass: T, filter: NSPredicate?) where T: Object {
        let objects = DataBaseManager.queryObject(objectClass: objectClass, filter: filter)
        DataBaseManager.deleteObjects(objects: objects)
    }
    
    /// 删除某张表
    /// - Parameter objectClass: 删除对象
    public static func clearTableClass<T>(objectClass: T) where T: Object {
        guard let defaultRealm = self.getCurrentDB() else {
            print("删除某张表失败 getCurrentDB返回空")
            return
        }
        do {
          try defaultRealm.write { defaultRealm.delete(defaultRealm.objects((T.self as Object.Type).self)) }
          print("删除某张表成功")
        } catch {
          print("删除某张表失败 error: \(error)")
        }
    }
    
}

// MARK: - 查询
extension DataBaseManager {
    
    /// 查询过滤数据、所有数据
    /// - Parameters:
    ///   - objectClass: 当前查询对象
    ///   - filter: 查询条件
    /// - Returns: 查询结果
    public static func queryObject <T> (objectClass: T, filter: NSPredicate? = nil) -> [T] where T: Object {
        guard let defaultRealm = self.getCurrentDB() else {
            fatalError("查询表数据失败 getCurrentDB返回空")
        }
        var results: Results<Object>
        if let filter = filter {
            results = defaultRealm.objects((T.self as Object.Type).self).filter(filter)
        } else {
            results = defaultRealm.objects((T.self as Object.Type).self)
        }
        
        guard results.count > 0 else { return [] }
        var objectArray = [T]()
        for model in results {
           objectArray.append(model as! T)
        }
        return objectArray
    }
    
    /// 查询过滤数据、所有数据
    /// - Parameters:
    ///   - objectClass: 当前查询对象
    ///   - filter: 查询条件
    ///   - sortedKey: 排序的key
    ///   - sortedAscending: 是否升序
    /// - Returns: 查询结果
    public static func queryObject <T> (objectClass: T, filter: String? = nil,sortedKey:String? = nil, sortedAscending: Bool = true) -> [T] where T: Object {
        guard let defaultRealm = self.getCurrentDB() else {
            return []
            fatalError("查询表数据失败 getCurrentDB返回空")
        }
        var results: Results<Object>
        if let filter = filter {
            if let sortedKey = sortedKey {
                results = defaultRealm.objects((T.self as Object.Type).self).filter(filter).sorted(byKeyPath: sortedKey,ascending: sortedAscending)
            } else {
                results = defaultRealm.objects((T.self as Object.Type).self).filter(filter)
            }
        } else {
            results = defaultRealm.objects((T.self as Object.Type).self)
        }
        
        guard results.count > 0 else { return [] }
        var objectArray = [T]()
        for model in results {
           objectArray.append(model as! T)
        }
        return objectArray
    }

}

// MARK: - 更新
extension DataBaseManager {
    ///更新单条数据
    public static func updateObject<T>(object: T) where T: Object {
        guard let defaultRealm = self.getCurrentDB() else {
            print("更新单条数据失败 getCurrentDB返回空")
            return
        }
        do {
            try defaultRealm.write { defaultRealm.add(object, update: .error) }
            print("更新单条数据成功")
        } catch {
            print("更新单条数据失败 error: \(error)")
        }
    }
    
    /// 更新 单条数据的某一个属性
    public static func updateObjectAttribute<T>(object: T ,attribute:[String:Any]) where T: Object {
        guard let defaultRealm = self.getCurrentDB() else {
            print("更新单条数据的某一个属性失败 getCurrentDB返回空")
            return
        }
        do {
            try defaultRealm.write {
                let keys = attribute.keys
                 for keyString in keys {
                    object.setValue(attribute[keyString], forKey: keyString)
                }
            }
            print("更新单条数据的某一个属性成功")
        } catch {
            print("更新单条数据的某一个属性失败 error: \(error)")
        }
    }
    
    /// 更新多条数据
    public static func updateObjects<T>(objects: [T]) where T: Object {
        guard let defaultRealm = self.getCurrentDB() else {
            print("更新多条数据失败 getCurrentDB返回空")
            return
        }
        do {
            try defaultRealm.write { defaultRealm.add(objects, update: .error) }
            print("更新多条数据成功")
        } catch {
            print("更新多条数据失败 error: \(error)")
        }
    }
    
    /// 更新多条数据的某一个属性
    public static func updateObjectsAttribute<T>(objectClass: T ,attribute:[String:Any]) where T: Object {
        guard let defaultRealm = self.getCurrentDB() else {
            print("更新多条数据的某一个属性失败 getCurrentDB返回空")
            return
        }
        do {
            try defaultRealm.write {
                let objects = defaultRealm.objects((T.self as Object.Type).self)
                let keys = attribute.keys
                 for keyString in keys {
                    objects.setValue(attribute[keyString], forKey: keyString)
                }
            }
            print("更新多条数据的某一个属性成功")
        } catch {
            print("更新多条数据的某一个属性失败 error: \(error)")
        }
    }
}

extension DataBaseManager {
    //查询所有数据
    static func getAll<Element: Object>(type: Element.Type, onResult: (_ results: Results<Element>) -> ()) {
        guard let defaultRealm = self.getCurrentDB() else {
            print("更新多条数据的某一个属性失败 getCurrentDB返回空")
            return
        }
        try! defaultRealm.write {
            //realm.objects(type).filter().sorted(byKeyPath: "name", ascending: false)//可以按条件筛选
            onResult(defaultRealm.objects(type))
        }
    }
    
    //根据主键查询数据
    static func getByPrimaryKey<Element: Object>(type: Element.Type, key:String) -> Element? {
        guard let defaultRealm = self.getCurrentDB() else {
            print("更新多条数据的某一个属性失败 getCurrentDB返回空")
            return nil
        }

        let ele = defaultRealm.object(ofType: type, forPrimaryKey: key)
        if let e = ele {
            if e.isInvalidated {
                return nil
            }
        }
        return ele
    }
    
    //根据主键查询数据
    static func getByPrimaryKey<Element: Object>(type: Element.Type, key:String, onResult: (_ result:Element?) -> ()){
        guard let defaultRealm = self.getCurrentDB() else {
            print("更新多条数据的某一个属性失败 getCurrentDB返回空")
            return
        }
        try! defaultRealm.write {
            onResult(defaultRealm.object(ofType: type, forPrimaryKey: key))
        }
    }
    
    //添加数据
    static func add(object: Object) {
        guard let defaultRealm = self.getCurrentDB() else {
            print("更新多条数据的某一个属性失败 getCurrentDB返回空")
            return
        }
        if object.isInvalidated {
            return
        }
        try! defaultRealm.write {
            defaultRealm.add(object)
        }
    }
    
    //删除数据
    static func delete(object: Object) {
        guard let defaultRealm = self.getCurrentDB() else {
            print("更新多条数据的某一个属性失败 getCurrentDB返回空")
            return
        }

        try! defaultRealm.write {
            if object.isInvalidated {
                return
            }
            defaultRealm.delete(object)
        }
    }
    
    //删除数据
    static func deleteInTransaction(object: Object) {
        guard let defaultRealm = self.getCurrentDB() else {
            print("更新多条数据的某一个属性失败 getCurrentDB返回空")
            return
        }
        if object.isInvalidated {
            return
        }
        defaultRealm.delete(object)
    }
    
    //修改数据
    static func update<Element: Object>(object: Element, onUpdate: (_ obj: Element) -> ()){
        guard let defaultRealm = self.getCurrentDB() else {
            print("更新多条数据的某一个属性失败 getCurrentDB返回空")
            return
        }
        try! defaultRealm.write {
            onUpdate(object)
            defaultRealm.add(object, update: .error)
        }
    }
    
    //修改数据
    static func update(onUpdate: () -> ()) {
        guard let defaultRealm = self.getCurrentDB() else {
            print("更新多条数据的某一个属性失败 getCurrentDB返回空")
            return
        }
        try! defaultRealm.write {
            onUpdate()
        }
    }
    
    //修改数据
    static func updateInTransaction<Element: Object>(object: Element) {
        guard let defaultRealm = self.getCurrentDB() else {
            print("更新多条数据的某一个属性失败 getCurrentDB返回空")
            return
        }
        if object.isInvalidated {
            return
        }
        defaultRealm.add(object, update: .error)
    }
    
}
