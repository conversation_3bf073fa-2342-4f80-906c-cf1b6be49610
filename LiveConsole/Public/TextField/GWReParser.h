//
//  GWReParser.h
//  UnionPayCard
//
//  Created by guo<PERSON> on 2018/11/30.
//  Copyright © 2018年 <EMAIL>. All rights reserved.
//

#import <Foundation/Foundation.h>
@class GWReGroup;

NS_ASSUME_NONNULL_BEGIN

@interface GWReParser : NSObject
{
    NSString *_pattern;
    BOOL _ignoreCase;
    GWReGroup *_node;
    BOOL _finished;
    NSRegularExpression *_exactQuantifierRegex;
    NSRegularExpression *_rangeQuantifierRegex;
}

- (id)initWithPattern:(NSString*)pattern;
- (id)initWithPattern:(NSString*)pattern ignoreCase:(BOOL)ignoreCase;
- (NSString*)reformatString:(NSString*)input;

@property (readonly, nonatomic) NSString *pattern;

@end

NS_ASSUME_NONNULL_END
