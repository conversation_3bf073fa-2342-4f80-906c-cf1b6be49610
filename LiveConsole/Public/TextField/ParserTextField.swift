//
//  UnionCardTextField.swift
//  UnionPayCard
//
//  Created by guo<PERSON> on 2018/11/30.
//  Copyright © 2018年 <EMAIL>. All rights reserved.
//

import UIKit

class ParserTextField: UITextField {

    public var pattern: String = "" {
        didSet {
            if !pattern.isEmpty {
                parser = GWReParser.init(pattern: pattern)
            }
        }
    }
    private var lastAcceptedValue: String?
    private var parser: GWReParser?

    override init(frame: CGRect) {
        super.init(frame: frame)
        lastAcceptedValue = nil
        parser = nil
        self.addTarget(self, action: #selector(formatInput(textField:)), for: .editingChanged)
    }

    required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
        lastAcceptedValue = nil
        parser = nil
        self.addTarget(self, action: #selector(formatInput(textField:)), for: .editingChanged)
    }

    @objc func formatInput(textField: UITextField) {
        guard let _ = parser else { print("请传入正则"); return }

        DispatchQueue.main.async {
            var formatted = self.parser?.reformatString(textField.text ?? "")
            if formatted == nil {
                formatted = self.lastAcceptedValue
            } else {
                if formatted == "" && textField.text != "" {
                    textField.text = self.lastAcceptedValue
                    self.sendActions(for: .valueChanged)
                } else {
                    self.lastAcceptedValue = formatted
                    let newText = formatted
                    if textField.text != newText {
                        textField.text = formatted
                        self.sendActions(for: .valueChanged)
                    }
                }
            }
        }
    }

}
