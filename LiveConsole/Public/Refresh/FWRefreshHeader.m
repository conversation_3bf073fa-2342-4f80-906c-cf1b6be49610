//
//  MJDIYHeader.m
//  MJRefreshExample
//
//  Created by <PERSON><PERSON> on 15/6/13.
//  Copyright © 2015年 小码哥. All rights reserved.
//

#import "FWRefreshHeader.h"
#import "LiveConsole-Swift.h"

static CGFloat layerWH = 24.0;
@interface FWRefreshHeader()
@property (nonatomic, strong) UILabel *endLabel;

@property (nonatomic, strong) UIImageView *animationImageView;
@end

@implementation FWRefreshHeader
#pragma mark - 重写方法
#pragma mark 在这里做一些初始化配置（比如添加子控件）
- (void)prepare
{
    [super prepare];
    // 设置控件的高度
    self.mj_h = 80;
    
    [self addSubview:self.endLabel];
    [self addSubview:self.animationImageView];
}

#pragma mark 在这里设置子控件的位置和尺寸
- (void)placeSubviews
{
    [super placeSubviews];
    
}

#pragma mark 监听scrollView的contentOffset改变
- (void)scrollViewContentOffsetDidChange:(NSDictionary *)change
{
    [super scrollViewContentOffsetDidChange:change];
}

- (void)endRefreshing:(NSString *)endText {
    self.endLabel.text = endText;
    [self endAnimation];
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.7 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [super endRefreshing];
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            self.endLabel.hidden = YES;
            self.animationImageView.hidden = NO;
        });
    });
}

- (void)beginRefreshing {
    [self startAnimation];
    [super beginRefreshing];
}

#pragma mark 监听scrollView的contentSize改变
- (void)scrollViewContentSizeDidChange:(NSDictionary *)change
{
    [super scrollViewContentSizeDidChange:change];
    
}

#pragma mark 监听scrollView的拖拽状态改变
- (void)scrollViewPanStateDidChange:(NSDictionary *)change
{
    [super scrollViewPanStateDidChange:change];

}

#pragma mark 监听控件的刷新状态
- (void)setState:(MJRefreshState)state
{
    MJRefreshCheckState;
    switch (state) {
        case MJRefreshStateIdle:
            break;
        case MJRefreshStatePulling:
            break;
        case MJRefreshStateRefreshing:
            [self startAnimation];
            break;
        default:
            break;
    }
}

- (void)startAnimation
{
    [self startViews];
    
    [self.animationImageView.layer removeAllAnimations];

    CABasicAnimation *rotationAnimation = [CABasicAnimation animationWithKeyPath:@"transform.rotation.z"];
    rotationAnimation.toValue = @(M_PI * 2 * 0.75);
    rotationAnimation.timingFunction = [CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionLinear];
    rotationAnimation.duration = 1.3;
    rotationAnimation.repeatCount = HUGE;
    rotationAnimation.fillMode = kCAFillModeForwards;
    rotationAnimation.removedOnCompletion = NO;
    [self.animationImageView.layer addAnimation:rotationAnimation forKey:@"rotationAnimation"];
}

- (void)endAnimation
{
    [self.animationImageView.layer removeAllAnimations];
    [self endViews];
}

- (void)startViews {
    self.animationImageView.hidden = NO;
    self.endLabel.hidden = YES;
}

- (void)endViews {
    self.animationImageView.hidden = YES;
    self.endLabel.hidden = NO;
}

- (UILabel *)endLabel {
    if (!_endLabel) {
        _endLabel = [[UILabel alloc] init];
        _endLabel.text = @"您已成功上传10个素材";
        _endLabel.textColor = [UIColor colorWithRGB:0xC1C1FF];
        _endLabel.backgroundColor = [UIColor clearColor];
        _endLabel.font = [UIFont systemFontOfSize:14];
        _endLabel.layer.masksToBounds = YES;
        _endLabel.textAlignment = NSTextAlignmentCenter;
        _endLabel.hidden = YES;
        _endLabel.frame = CGRectMake(0, (self.mj_h - 24) / 2.0, 200, layerWH);
        _endLabel.centerX = [UIScreen mainScreen].bounds.size.width / 2.0;
    }
    return _endLabel;
}

- (UIImageView *)animationImageView {
    if (!_animationImageView) {
        _animationImageView = [[UIImageView alloc] initWithFrame:CGRectMake(0, (self.mj_h - 24) / 2.0, 24, 24)];
        _animationImageView.centerX = [UIScreen mainScreen].bounds.size.width / 2.0;
        _animationImageView.image = [UIImage imageNamed:@"sc_刷新_云端素材"];
    }
    return _animationImageView;
}

@end
