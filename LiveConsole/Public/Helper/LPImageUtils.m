//
//  LPImageUtils.m
//  LivePlus
//
//  Created by iclick on 2020/12/9.
//

#import "LPImageUtils.h"
#import <SDCycleScrollView/TAPageControl.h>

@implementation LPImageUtils

+ (UIImage *)imageFromCVPixelBuffer:(CVPixelBufferRef)buffer{
    if (!buffer) {
        return nil;
    }
    CGImageRef videofore = [self CGImageRefFromCVpixel:buffer];
    UIImage *rImage = [UIImage imageWithCGImage:videofore];
    CGImageRelease(videofore);
    return rImage;
}

+(CGImageRef)CGImageRefFromCVpixel:(CVPixelBufferRef)samplePixel{
    CGImageRef videoImage;
    CreateCGImageFromCVPixelBuffer(samplePixel, &videoImage);
    return videoImage;
}


static OSStatus CreateCGImageFromCVPixelBuffer(CVPixelBufferRef pixelBuffer, CGImageRef *imageOut){
    OSStatus err = noErr;
    OSType sourcePixelFormat;
    size_t width, height, sourceRowBytes;
    void *sourceBaseAddr = NULL;
    CGBitmapInfo bitmapInfo;
    CGColorSpaceRef colorspace = NULL;
    CGDataProviderRef provider = NULL;
    CGImageRef image = NULL;
    
    sourcePixelFormat = CVPixelBufferGetPixelFormatType( pixelBuffer );
    if ( kCVPixelFormatType_32ARGB == sourcePixelFormat )
        bitmapInfo = kCGBitmapByteOrder32Big | kCGImageAlphaNoneSkipFirst;
    else if ( kCVPixelFormatType_32BGRA == sourcePixelFormat )
        bitmapInfo = kCGBitmapByteOrder32Little | kCGImageAlphaNoneSkipFirst;
    else
        return -95014; // only uncompressed pixel formats
    
    sourceRowBytes = CVPixelBufferGetBytesPerRow( pixelBuffer );
    width = CVPixelBufferGetWidth( pixelBuffer )  ;
    height = CVPixelBufferGetHeight( pixelBuffer ) ;
    
    CVPixelBufferLockBaseAddress( pixelBuffer, 0 );
    sourceBaseAddr = CVPixelBufferGetBaseAddress( pixelBuffer );
    
    colorspace = CGColorSpaceCreateDeviceRGB();
    
    CVPixelBufferRetain( pixelBuffer );
    provider = CGDataProviderCreateWithData( (void *)pixelBuffer, sourceBaseAddr, sourceRowBytes * height, ReleaseCVPixelBuffer);
    image = CGImageCreate(width, height, 8, 32, sourceRowBytes, colorspace, bitmapInfo, provider, NULL, true, kCGRenderingIntentDefault);
    
    if ( err && image ) {
        CGImageRelease( image );
        image = NULL;
    }
    if ( provider ) CGDataProviderRelease( provider );
    if ( colorspace ) CGColorSpaceRelease( colorspace );
    *imageOut = image;
    return err;
}

static void ReleaseCVPixelBuffer(void *pixel, const void *data, size_t size){
    CVPixelBufferRef pixelBuffer = (CVPixelBufferRef)pixel;
    CVPixelBufferUnlockBaseAddress( pixelBuffer, 0 );
    CVPixelBufferRelease( pixelBuffer );
}

+ (void)getVideoThumbnailImage:(NSURL *)aUrl atTime:(CMTime)aTime maxSize:(CGSize)size completeBlock:(void (^)(CGImageRef _Nullable imgRef))completeBlock{
    AVURLAsset *asset = [AVURLAsset URLAssetWithURL:aUrl options:nil];
    if (CMTimeGetSeconds(aTime) > CMTimeGetSeconds(asset.duration)) {
        aTime = asset.duration;
    }
    AVAssetImageGenerator *imageGenerator = [[AVAssetImageGenerator alloc] initWithAsset:asset];
    imageGenerator.maximumSize = size;
    imageGenerator.appliesPreferredTrackTransform = YES;
    NSMutableArray *times = [NSMutableArray array];
    NSValue *timeValue = [NSValue valueWithCMTime:aTime];
    [times addObject:timeValue];
    [imageGenerator generateCGImagesAsynchronouslyForTimes:times completionHandler:^(CMTime requestedTime, CGImageRef  _Nullable image, CMTime actualTime, AVAssetImageGeneratorResult result, NSError * _Nullable error) {
        if (image) {
            completeBlock(image);
        }else{
            completeBlock(nil);
        }
    }];
}

+ (UIImage *)getVideoThumbnailImage:(NSURL *)aUrl atTime:(CMTime)aTime maxSize:(CGSize)size{
    AVURLAsset *asset = [AVURLAsset URLAssetWithURL:aUrl options:nil];
    return [LPImageUtils getVideoThumbnailImageFromAvAsset:asset atTime:aTime maxSize:size];
}

+ (UIImage *)getVideoThumbnailImageFromAvAsset:(AVAsset *)asset atTime:(CMTime)aTime maxSize:(CGSize)size{
    if (CMTimeGetSeconds(aTime) > CMTimeGetSeconds(asset.duration)) {
        aTime = asset.duration;
    }
    AVAssetImageGenerator *imageGenerator = [[AVAssetImageGenerator alloc] initWithAsset:asset];
    imageGenerator.maximumSize = size;
    imageGenerator.appliesPreferredTrackTransform = YES;
    NSError *error;
    CGImageRef imageRef = [imageGenerator copyCGImageAtTime:aTime actualTime:nil error:&error];
    
    
    NSMutableArray *times = [NSMutableArray array];
    NSValue *timeValue = [NSValue valueWithCMTime:kCMTimeZero];
    [times addObject:timeValue];
    [imageGenerator generateCGImagesAsynchronouslyForTimes:times completionHandler:^(CMTime requestedTime, CGImageRef  _Nullable image, CMTime actualTime, AVAssetImageGeneratorResult result, NSError * _Nullable error) {
        
    }];
    
    if (error) {
        NSLog(@"getVideoThumbnailImage error: --- %@",error);
        return nil;
    }
    UIImage *thumbnailImage = [[UIImage alloc] initWithCGImage:imageRef];
    CGImageRelease(imageRef);
    return thumbnailImage;
}

+ (int)getSourceDegressFromAsset:(AVAsset*)asset{
    NSArray *tracks = [asset tracksWithMediaType:AVMediaTypeVideo];
    if(tracks.count == 0) {
        return 0;
    }
    AVAssetTrack *videoTrack = [tracks objectAtIndex:0];
    CGAffineTransform t = videoTrack.preferredTransform;
    if(t.a == 0 && t.b == 1.0 && t.c == -1.0 && t.d == 0){
        // Portrait degress = 90;
        return 90;
    }else if(t.a == 0 && t.b == -1.0 && t.c == 1.0 && t.d == 0){
        // PortraitUpsideDown degress = 270;

    }else if(t.a == 1.0 && t.b == 0 && t.c == 0 && t.d == 1.0){
        // LandscapeRight degress = 0;
        return 0;
    }else if(t.a == -1.0 && t.b == 0 && t.c == 0 && t.d == -1.0){
        // LandscapeLeft  degress = 180;
        return 180;
    }
    return 0;
}


/**
获取点击的颜色
@param point 点击的位置
@return 返回点击地方的颜色
*/
+(UIColor*)getPixelColorScreenWindowAtLocation:(CGPoint)point{
    UIColor* color = nil;
    UIImage *image = [self fullScreenshots];
    CGImageRef inImage = image.CGImage;
    // Create off screen bitmap context to draw the image into. Format ARGB is 4 bytes for each pixel: Alpa, Red, Green, Blue
    CGContextRef cgctx = [self createARGBBitmapContextFromImage:inImage];
    if (cgctx == NULL) { return nil;  }
    size_t w = CGImageGetWidth(inImage);
    size_t h = CGImageGetHeight(inImage);
    CGRect rect = {{0,0},{w,h}};
    // Draw the image to the bitmap context. Once we draw, the memory
    // allocated for the context for rendering will then contain the
    // raw image data in the specified color space.
    CGContextDrawImage(cgctx, rect, inImage);
    // Now we can get a pointer to the image data associated with the bitmap
    // context.
    unsigned char* data = CGBitmapContextGetData (cgctx);
    CGFloat scale = [UIScreen mainScreen].scale;
    if (data != NULL) {
        //offset locates the pixel in the data from x,y.
        //4 for 4 bytes of data per pixel, w is width of one row of data.
        @try {
            int offset = 4*((w*round(point.y * scale))+round(point.x * scale));
            int alpha =  (int)data[offset];
            int red = (int)data[offset+1];
            int green = (int)data[offset+2];
            int blue = (int)data[offset+3];
            color = [UIColor colorWithRed:(red/255.0f) green:(green/255.0f) blue:(blue/255.0f) alpha:(alpha/255.0f)];
            
            free(data);
        }
        @catch (NSException * e) {
            NSLog(@"%@",[e reason]);
        }
        @finally {
        }
        

    }
    
    
    return color;
}

+ (UIImage *)fullScreenshots
{
    CGSize imageSize = CGSizeZero;

    UIInterfaceOrientation orientation = [UIApplication sharedApplication].statusBarOrientation;
    if (UIInterfaceOrientationIsPortrait(orientation)) {
        imageSize = [UIScreen mainScreen].bounds.size;
    } else {
        imageSize = CGSizeMake([UIScreen mainScreen].bounds.size.height, [UIScreen mainScreen].bounds.size.width);
    }

    UIGraphicsBeginImageContextWithOptions(imageSize, NO, 0);
    CGContextRef context = UIGraphicsGetCurrentContext();
    for (UIWindow *window in [[UIApplication sharedApplication] windows]) {
        CGContextSaveGState(context);
        CGContextTranslateCTM(context, window.center.x, window.center.y);
        CGContextConcatCTM(context, window.transform);
        CGContextTranslateCTM(context, -window.bounds.size.width * window.layer.anchorPoint.x, -window.bounds.size.height * window.layer.anchorPoint.y);
        if (orientation == UIInterfaceOrientationLandscapeLeft) {
            CGContextRotateCTM(context, M_PI_2);
            CGContextTranslateCTM(context, 0, -imageSize.width);
        } else if (orientation == UIInterfaceOrientationLandscapeRight) {
            CGContextRotateCTM(context, -M_PI_2);
            CGContextTranslateCTM(context, -imageSize.height, 0);
        } else if (orientation == UIInterfaceOrientationPortraitUpsideDown) {
            CGContextRotateCTM(context, M_PI);
            CGContextTranslateCTM(context, -imageSize.width, -imageSize.height);
        }
        if ([window respondsToSelector:@selector(drawViewHierarchyInRect:afterScreenUpdates:)]) {
            [window drawViewHierarchyInRect:window.bounds afterScreenUpdates:YES];
        } else {
            [window.layer renderInContext:context];
        }
        CGContextRestoreGState(context);
    }

    UIImage *image = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    return image;
}

+(CGContextRef) createARGBBitmapContextFromImage:(CGImageRef) inImage {
    CGContextRef    context = NULL;
    CGColorSpaceRef colorSpace;
    void *          bitmapData;
    int            bitmapByteCount;
    int            bitmapBytesPerRow;
    // Get image width, height. We'll use the entire image.
    size_t pixelsWide = CGImageGetWidth(inImage);
    size_t pixelsHigh = CGImageGetHeight(inImage);
    // Declare the number of bytes per row. Each pixel in the bitmap in this
    // example is represented by 4 bytes; 8 bits each of red, green, blue, and
    // alpha.
    bitmapBytesPerRow  = (pixelsWide * 4);
    bitmapByteCount    = (bitmapBytesPerRow * pixelsHigh);
    // Use the generic RGB color space.
    colorSpace = CGColorSpaceCreateDeviceRGB();
    if (colorSpace == NULL) {
        fprintf(stderr, "Error allocating color spacen");
        return NULL;
    }
    // Allocate memory for image data. This is the destination in memory
    // where any drawing to the bitmap context will be rendered.
    bitmapData = malloc( bitmapByteCount );
    if (bitmapData == NULL) {
        fprintf (stderr, "Memory not allocated!");
        CGColorSpaceRelease( colorSpace );
        return NULL;
    }
    // Create the bitmap context. We want pre-multiplied ARGB, 8-bits
    // per component. Regardless of what the source image format is
    // (CMYK, Grayscale, and so on) it will be converted over to the format
    // specified here by CGBitmapContextCreate.
    context = CGBitmapContextCreate (bitmapData,
                                    pixelsWide,
                                    pixelsHigh,
                                    8,      // bits per component
                                    bitmapBytesPerRow,
                                    colorSpace,
                                    kCGImageAlphaPremultipliedFirst);
    if (context == NULL) {
        free (bitmapData);
        fprintf (stderr, "Context not created!");
    }
    // Make sure and release colorspace before returning
    CGColorSpaceRelease( colorSpace );
    return context;
}




//+ (CGPoint)convertToPointOfInterestFromViewCoordinates:(CGPoint)viewCoordinates inFrame:(CGRect)frame withOrientation:(UIDeviceOrientation)orientation andFillMode:(GPUImageFillModeType)fillMode mirrored:(BOOL)mirrored
//{
//    CGSize frameSize = frame.size;
//    CGPoint pointOfInterest = CGPointMake(0.5, 0.5);
//
//    if (mirrored){
//        viewCoordinates.x = frameSize.width - viewCoordinates.x;
//    }
//
//    if (fillMode == kGPUImageFillModeStretch) {
//        pointOfInterest = CGPointMake(viewCoordinates.y / frameSize.height, 1.f - (viewCoordinates.x / frameSize.width));
//    } else {
//        CGSize apertureSize = CGSizeMake(CGRectGetHeight(frame), CGRectGetWidth(frame));
//        if (!CGSizeEqualToSize(apertureSize, CGSizeZero)) {
//            CGPoint point = viewCoordinates;
//            CGFloat apertureRatio = apertureSize.height / apertureSize.width;
//            CGFloat viewRatio = frameSize.width / frameSize.height;
//            CGFloat xc = .5f;
//            CGFloat yc = .5f;
//
//            if (fillMode == kGPUImageFillModePreserveAspectRatio) {
//                if (viewRatio > apertureRatio) {
//                    CGFloat y2 = frameSize.height;
//                    CGFloat x2 = frameSize.height * apertureRatio;
//                    CGFloat x1 = frameSize.width;
//                    CGFloat blackBar = (x1 - x2) / 2;
//                    if (point.x >= blackBar && point.x <= blackBar + x2) {
//                        xc = point.y / y2;
//                        yc = 1.f - ((point.x - blackBar) / x2);
//                    }
//                } else {
//                    CGFloat y2 = frameSize.width / apertureRatio;
//                    CGFloat y1 = frameSize.height;
//                    CGFloat x2 = frameSize.width;
//                    CGFloat blackBar = (y1 - y2) / 2;
//                    if (point.y >= blackBar && point.y <= blackBar + y2) {
//                        xc = ((point.y - blackBar) / y2);
//                        yc = 1.f - (point.x / x2);
//                    }
//                }
//            } else if (fillMode == kGPUImageFillModePreserveAspectRatioAndFill) {
//                if (viewRatio > apertureRatio) {
//                    CGFloat y2 = apertureSize.width * (frameSize.width / apertureSize.height);
//                    xc = (point.y + ((y2 - frameSize.height) / 2.f)) / y2;
//                    yc = (frameSize.width - point.x) / frameSize.width;
//                } else {
//                    CGFloat x2 = apertureSize.height * (frameSize.height / apertureSize.width);
//                    yc = 1.f - ((point.x + ((x2 - frameSize.width) / 2)) / x2);
//                    xc = point.y / frameSize.height;
//                }
//            }
//
//            pointOfInterest = CGPointMake(xc, yc);
//        }
//    }
//
//    return pointOfInterest;
//}

+ (CVPixelBufferRef) pixelBufferFromImage:(UIImage *)image {
    
    NSDictionary *options = @{
                              (NSString*)kCVPixelBufferCGImageCompatibilityKey : @YES,
                              (NSString*)kCVPixelBufferCGBitmapContextCompatibilityKey : @YES,
                              (NSString*)kCVPixelBufferIOSurfacePropertiesKey: [NSDictionary dictionary]
                              };
    
    CVPixelBufferRef pxbuffer = NULL;
    CGFloat frameWidth = CGImageGetWidth(image.CGImage);
    CGFloat frameHeight = CGImageGetHeight(image.CGImage);
    CVReturn status = CVPixelBufferCreate(
                                          kCFAllocatorDefault,
                                          frameWidth,
                                          frameHeight,
                                          kCVPixelFormatType_32BGRA,
                                          (__bridge CFDictionaryRef)options,
                                          &pxbuffer);
    
    NSParameterAssert(status == kCVReturnSuccess && pxbuffer != NULL);
    
    CVPixelBufferLockBaseAddress(pxbuffer, 0);
    
    void *pxdata = CVPixelBufferGetBaseAddress(pxbuffer);
    
    NSParameterAssert(pxdata != NULL);
    
    CGColorSpaceRef rgbColorSpace = CGColorSpaceCreateDeviceRGB();
    
    CGContextRef context = CGBitmapContextCreate(
                                                 pxdata,
                                                 frameWidth,
                                                 frameHeight,
                                                 8,
                                                 CVPixelBufferGetBytesPerRow(pxbuffer),
                                                 rgbColorSpace, kCGImageAlphaNoneSkipFirst | kCGBitmapByteOrder32Little);
    NSParameterAssert(context);
    
    CGContextConcatCTM(context, CGAffineTransformIdentity);
    
    CGContextDrawImage(context, CGRectMake(0, 0, frameWidth, frameHeight), image.CGImage);
    
    CGColorSpaceRelease(rgbColorSpace);
    CGContextRelease(context);
    CVPixelBufferUnlockBaseAddress(pxbuffer, 0);
    //    CFRelease(rgbColorSpace) ;
    
    return pxbuffer;
}




+ (UIImage *)fixOrientation:(UIImage *)image {
    
    // No-op if the orientation is already correct
    if (image.imageOrientation == UIImageOrientationUp) return image;
    
    // We need to calculate the proper transformation to make the image upright.
    // We do it in 2 steps: Rotate if Left/Right/Down, and then flip if Mirrored.
    CGAffineTransform transform = CGAffineTransformIdentity;
    
    switch (image.imageOrientation) {
        case UIImageOrientationDown:
        case UIImageOrientationDownMirrored:
            transform = CGAffineTransformTranslate(transform, image.size.width, image.size.height);
            transform = CGAffineTransformRotate(transform, M_PI);
            break;
            
        case UIImageOrientationLeft:
        case UIImageOrientationLeftMirrored:
            transform = CGAffineTransformTranslate(transform, image.size.width, 0);
            transform = CGAffineTransformRotate(transform, M_PI_2);
            break;
            
        case UIImageOrientationRight:
        case UIImageOrientationRightMirrored:
            transform = CGAffineTransformTranslate(transform, 0, image.size.height);
            transform = CGAffineTransformRotate(transform, -M_PI_2);
            break;
        case UIImageOrientationUp:
        case UIImageOrientationUpMirrored:
            break;
    }
    
    switch (image.imageOrientation) {
        case UIImageOrientationUpMirrored:
        case UIImageOrientationDownMirrored:
            transform = CGAffineTransformTranslate(transform, image.size.width, 0);
            transform = CGAffineTransformScale(transform, -1, 1);
            break;
            
        case UIImageOrientationLeftMirrored:
        case UIImageOrientationRightMirrored:
            transform = CGAffineTransformTranslate(transform, image.size.height, 0);
            transform = CGAffineTransformScale(transform, -1, 1);
            break;
        case UIImageOrientationUp:
        case UIImageOrientationDown:
        case UIImageOrientationLeft:
        case UIImageOrientationRight:
            break;
    }
    
    // Now we draw the underlying CGImage into a new context, applying the transform
    // calculated above.
    CGContextRef ctx = CGBitmapContextCreate(NULL, image.size.width, image.size.height,
                                             CGImageGetBitsPerComponent(image.CGImage), 0,
                                             CGImageGetColorSpace(image.CGImage),
                                             CGImageGetBitmapInfo(image.CGImage));
    CGContextConcatCTM(ctx, transform);
    switch (image.imageOrientation) {
        case UIImageOrientationLeft:
        case UIImageOrientationLeftMirrored:
        case UIImageOrientationRight:
        case UIImageOrientationRightMirrored:
            // Grr...
            CGContextDrawImage(ctx, CGRectMake(0,0,image.size.height,image.size.width), image.CGImage);
            break;
            
        default:
            CGContextDrawImage(ctx, CGRectMake(0,0,image.size.width,image.size.height), image.CGImage);
            break;
    }
    
    // And now we just create a new UIImage from the drawing context
    CGImageRef cgimg = CGBitmapContextCreateImage(ctx);
    UIImage *img = [UIImage imageWithCGImage:cgimg];
    CGContextRelease(ctx);
    CGImageRelease(cgimg);
    return img;
}

@end
