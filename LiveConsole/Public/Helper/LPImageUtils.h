//
//  LPImageUtils.h
//  LivePlus
//
//  Created by iclick on 2020/12/9.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
#import <AVFoundation/AVFoundation.h>
//#import "GPUImageView.h"

NS_ASSUME_NONNULL_BEGIN

@interface LPImageUtils : NSObject

+ (UIImage *)imageFromCVPixelBuffer:(CVPixelBufferRef)buffer;
+ (CVPixelBufferRef) pixelBufferFromImage:(UIImage *)image ;

+ (UIImage *)getVideoThumbnailImage:(NSURL *)aUrl atTime:(CMTime)aTime maxSize:(CGSize)size;
+ (UIImage *)getVideoThumbnailImageFromAvAsset:(AVAsset *)asset atTime:(CMTime)aTime maxSize:(CGSize)size;
+ (void)getVideoThumbnailImage:(NSURL *)aUrl atTime:(CMTime)aTime maxSize:(CGSize)size completeBlock:(void (^)(CGImageRef _Nullable imgRef))completeBlock;

+ (int)getSourceDegressFromAsset:(AVAsset*)asset;

/**
获取点击的颜色
@param point 点击的位置
@return 返回点击地方的颜色
*/
+(UIColor*)getPixelColorScreenWindowAtLocation:(CGPoint)point;

//+ (CGPoint)convertToPointOfInterestFromViewCoordinates:(CGPoint)viewCoordinates inFrame:(CGRect)frame withOrientation:(UIDeviceOrientation)orientation andFillMode:(GPUImageFillModeType)fillMode mirrored:(BOOL)mirrored;

+ (UIImage *)fixOrientation:(UIImage *)image ;

@end

NS_ASSUME_NONNULL_END
