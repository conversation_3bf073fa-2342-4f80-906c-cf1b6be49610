//
//  GCDServices.swift
//  LivePlus
//
//  Created by 郭炜 on 2021/11/1.
//

import Foundation

public struct GCDServices {
    
    public typealias Task = () -> Void
    
    /// 异步任务
    /// - Parameter task: 异步任务块
    public static func async(_ task: @escaping Task) { _async(task) }

    /// 异步任务
    /// - Parameters:
    ///   - task: 异步任务块
    ///   - mainTask: 主线程任务块
    public static func async(_ task: @escaping Task,
                             _ mainTask: @escaping Task) { _async(task, mainTask) }

    /// 延迟任务
    /// - Parameters:
    ///   - seconds: 延迟秒数
    ///   - block: 延迟任务块
    /// - Returns: 任务Item 可用于取消等操作
    @discardableResult
    public static func delay(_ seconds: Double,
                             _ block: @escaping Task) -> DispatchWorkItem {
        let item = DispatchWorkItem(block: block)
        DispatchQueue.main.asyncAfter(deadline: DispatchTime.now() + seconds, execute: item)
        return item
    }
    
    /// 异步延迟任务
    /// - Parameters:
    ///   - seconds: 延迟秒数
    ///   - task: 异步任务块
    /// - Returns: 任务Item 可用于取消等操作
    @discardableResult
    public static func asyncDelay(_ seconds: Double,
                                  _ task: @escaping Task) -> DispatchWorkItem { _asyncDelay(seconds, task) }

    /// 异步延迟任务
    /// - Parameters:
    ///   - seconds: 延迟秒数
    ///   - task: 异步任务块
    ///   - mainTask: 主线程任务块
    /// - Returns: 任务Item 可用于取消等操作
    @discardableResult
    public static func asyncDelay(_ seconds: Double,
                                  _ task: @escaping Task,
                                  _ mainTask: @escaping Task) -> DispatchWorkItem { _asyncDelay(seconds, task, mainTask) }

    private static func _asyncDelay(_ seconds: Double,
                                    _ task: @escaping Task,
                                    _ mainTask: Task? = nil) -> DispatchWorkItem {
        let item = DispatchWorkItem(block: task)
        DispatchQueue.global().asyncAfter(deadline: DispatchTime.now() + seconds, execute: item)
        if let main = mainTask { item.notify(queue: DispatchQueue.main, execute: main) }
        return item
    }
    
    private static func _async(_ task: @escaping Task,
                               _ mainTask: Task? = nil) {
        let item = DispatchWorkItem(block: task)
        DispatchQueue.global().async(execute: item)
        if let main = mainTask { item.notify(queue: DispatchQueue.main, execute: main) }
    }
}
