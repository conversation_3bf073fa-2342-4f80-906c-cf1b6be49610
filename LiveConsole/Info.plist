<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleLocalizations</key>
	<array>
		<string>zh_CN</string>
	</array>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string></string>
			<key>CFBundleURLSchemes</key>
			<array/>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>wechat</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>wxa509873c882b3449</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleIdentifier</key>
			<string></string>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>com.quickleading.ailivecontrol</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>ailivecontrol</string>
			</array>
		</dict>
	</array>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>weixinULAPI</string>
		<string>weixin</string>
		<string>wechat</string>
		<string>fastword5CD12938</string>
		<string>taobao</string>
		<string>kwai</string>
		<string>kwaiAuth2</string>
		<string>kwaiopenapi</string>
		<string>KwaiBundleToken</string>
		<string>kwai.clip.multi</string>
		<string>KwaiSDKMediaV2</string>
		<string>ksnebula</string>
		<string>wechat</string>
		<string>weixinULAPI</string>
		<string>weixin</string>
		<string>bilibili</string>
	</array>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
		<key>NSExceptionDomains</key>
		<dict>
			<key>************</key>
			<dict>
				<key>NSIncludesSubdomains</key>
				<true/>
				<key>NSTemporaryExceptionAllowsInsecureHTTPLoads</key>
				<true/>
				<key>NSTemporaryExceptionMinimumTLSVersion</key>
				<string>TLSv1.1</string>
			</dict>
		</dict>
	</dict>
	<key>NSBonjourServices</key>
	<array>
		<string>_adhp._tcp</string>
		<string>_wsraop._tcp</string>
		<string>_http.tcp</string>
		<string>_airplay._tcp.</string>
		<string>_apowermirror._tcp.</string>
		<string>_rdlink._tcp.</string>
		<string>_nearByContent._tcp</string>
	</array>
	<key>UIBackgroundModes</key>
	<array/>
</dict>
</plist>
