//
//  AppDelegate.swift
//  LiveConsole
//
//  Created by <PERSON><PERSON><PERSON>-HF-BJB-081 on 2024/1/29.
//

import UIKit
import SDWebImage
import Alamofire
import RealmSwift
import SwiftyStoreKit
import NSObject_Rx


var isFromBackThenEnterFroient = false

@main
class AppDelegate: UIResponder, UIApplicationDelegate, WXApiDelegate {

    public lazy var window: UIWindow? = {
        return self.wd
    }()
    
    public lazy var fpsLabel: YYFPSLabel = {
      let lab =  YYFPSLabel()
        return lab
    }()
    
    public  lazy var wd: UIWindow = {
        return UIWindow.init(frame: UIScreen.main.bounds)
    }()
    
    /// 强制横竖屏
    public var allowRotation: Bool = false

    func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?) -> Bool {
        // Override point for customization after application launch.

        
        // MARK: - 打点
        LPStatistics.logEvent(.ENTER_APP(.enterApp), eventAction: .one)
        
        NetConnectionManager.shared.startNetworkReachabilityObserver()
        
        APIConfigurationManager.shared.loadConfiguration()
        
#if DEBUG
        UserDefaults.standard.set(false, forKey: LCKey.UD_ReleaseEnv) // 测试库
#else
        UserDefaults.standard.set(false, forKey: LCKey.UD_ReleaseEnv) // 测试库
#endif
        
#if TEST
        UserDefaults.standard.set(true, forKey: LCKey.UD_ReleaseEnv)
        print("现网环境")
#endif
        wd.backgroundColor  = .clear
        wd.rootViewController =  BaseTabBarViewController()
        wd.makeKeyAndVisible()
        
        // 初始化数据库配置
        DataBaseManager.globalConfigRealm()
        
        doInBackground(launchOptions)
        removeCacheFiles()
        
        WXApi.startLog(by: .detail) { log in
            LCLog.d("微信日志--------\(log)")
        }
        
        // 注册微信
        if !WXApi.registerApp(LCKey.Wechat_AppID, universalLink: LCKey.App_universalLink) {
            print("---微信注册失败-")
        }
       
        
        LCLog.d("微信版本号--------\(WXApi.getVersion())")
        
//        WXApi.checkUniversalLinkReady { Step, result in
//            print("--universalLink--\(Step.rawValue)--\(result.success) ---\(result.errorInfo) ---\(result.suggestion)")
//        }
//
      

        #if DEBUG
       
        fpsLabel.bottom = wd.height - 12
        fpsLabel.right = wd.width - 12
        fpsLabel.alpha = 1
        UIApplication.shared.keyWindow?.addSubview(fpsLabel)
        #endif
        
        
#if TEST
        fpsLabel.bottom = wd.height - 12
        fpsLabel.right = wd.width - 12
        fpsLabel.alpha = 1
        UIApplication.shared.keyWindow?.addSubview(fpsLabel)
        
        if let ison =  UserDefaults.standard.value(forKey: LCKey.UD_CPU) as? Bool {
            fpsLabel.isHidden = ison
//            print("CPU：\(ison)")
        } else {
            fpsLabel.isHidden = false
//            print("CPU：显示")
        }
        
#endif
        
        return true
    }

    /// 不紧急的初始化操作
    func doInBackground(_ launchOptions: [UIApplication.LaunchOptionsKey: Any]?)  {
        DispatchQueue.global().async {
//            #if DEBUG
//
//            #else
//            Bugly.start(withAppId: LCKey.Bugly_AppKey)
//            #endif
            
//            SDImageCodersManager.shared.addCoder(SDImageGIFCoder.shared)
//            SDImageCodersManager.shared.addCoder(SDImageWebPCoder.shared)
            SDImageCacheConfig.default.maxDiskAge = 60 * 60 * 24 * 100  // 100 day
            SDImageCacheConfig.default.maxDiskSize = 200 * 1024 * 1024// 200MB
            SDImageCache.shared.config.shouldCacheImagesInMemory = true
            
            
            // 注册友盟
            #if DEBUG
            UMConfigure.initWithAppkey(LCKey.UM_AppKey, channel: "test")
            UMConfigure.setLogEnabled(true)
            // 开发者需要显式的调用此函数，日志系统才能工作
            UMCommonLogManager.setUp()
            #else
            UMConfigure.initWithAppkey(LCKey.UM_AppKey, channel: "App Store")
            UMConfigure.setLogEnabled(false)
            // 友盟崩溃
            UMCrashConfigure.setCrashCBBlock { "com.quickleading.ailivecontrol" }
            #endif


            self.inPurchasing()
            
            
            // 清空一下
            LCTools.removeReceiveBackupFolderAll()
            LCTools.removeBackupRoomFolderAll()
            
           
        }
    }
    
    
    
    func applicationDidBecomeActive(_ application: UIApplication) {
        print("进入前台")
        isFromBackThenEnterFroient = true

    }
    
    /// 内购
    func inPurchasing() {
        //监听从appstore 购买 调起app
        LPPayTool.shared.shouldAddStorePaymentHandler()
        
        // updatedTransactions
        LPPayTool.shared.completeTransactions()
        
        LPPayTool.shared.validationForServer()
    }
        
    func application(_ application: UIApplication, handleOpen url: URL) -> Bool {
        return handleOpenUrl(url)
    }
    
    func application(_ application: UIApplication, open url: URL, sourceApplication: String?, annotation: Any) -> Bool {
        return handleOpenUrl(url)
    }
    
    func application(_ app: UIApplication, open url: URL, options: [UIApplication.LaunchOptionsKey : Any] = [:]) -> Bool {
        return handleOpenUrl(url)
    }

    
    func application(_ application: UIApplication, supportedInterfaceOrientationsFor window: UIWindow?) -> UIInterfaceOrientationMask {
        if allowRotation {
            return .landscapeRight
        }
        return .portrait
    }
    
    func handleOpenUrl(_ url: URL) -> Bool {
//        print(url)
        let urlString = url.absoluteString
        if urlString.hasPrefix("wx") {
            return WXApi.handleOpen(url, delegate: self)
        } else if urlString.hasPrefix("ks") {
            return true
        } else if urlString.hasPrefix("file") {
//            handleFile(url)
            return true
        } else if urlString.hasPrefix("livepp"){
            return true
        } else {
            return false
        }
    }
    
    func handleOpenUniversalLinkFor(userActivity: NSUserActivity) -> Bool {
        guard let url = userActivity.webpageURL else {
            return false
        }
        if url.path.contains(LCKey.Wechat_AppID) {
            return WXApi.handleOpenUniversalLink(userActivity, delegate: self)
        } else if url.path.contains("ks653303422638428748") {
            return true
        } else {
           
            return true
        }
    }
    
    
    // MARK: - 处理app退出时清空缓存文件
    
    func applicationWillTerminate(_ application: UIApplication) {
        
        LCLog.d("程序被主动退出--")
        removeCacheFiles()
    }
    
    func application(_ application: UIApplication, continue userActivity: NSUserActivity, restorationHandler: @escaping ([UIUserActivityRestoring]?) -> Void) -> Bool {
        return handleOpenUniversalLinkFor(userActivity: userActivity)
    }
    
    func applicationDidReceiveMemoryWarning(_ application: UIApplication) {
        
    }
    
    /// 清空需要删除的数据
    private func removeCacheFiles() {
//        LCTools.removeTempDownloadFolder()
        LCTools.removeTemporaryCacheFolder()
//        LCTools.removeMaterialSourceDownloadFolder()
    }
    
    // MARK: - WXApiDelegate
    func onResp(_ resp: BaseResp) {
        if resp.isKind(of: SendAuthResp.self) {
            guard let resp2 = resp as? SendAuthResp else { return }
            NotificationCenter.default.post(name: LCKey.noti_didGetWeiXinResponse, object: resp2, userInfo: nil)
            NotificationCenter.default.post(name: LCKey.noti_wechatResponse, object: resp2, userInfo: nil)
            return
        }
        if resp.isKind(of: WXLaunchMiniProgramResp.self) {
            guard let resp = resp as? WXLaunchMiniProgramResp else { return }
            print("微信调起小程序结果：\(String(describing: resp.extMsg))")
            return
        }
        print("微信授权失败")
    }
    
    func onReq(_ req: BaseReq) {
        print("微信发送请求：\(req)")
    }
    
    @objc
    class func curDisplayVC() -> UIViewController {
        guard let appDel = UIApplication.shared.delegate as? AppDelegate,
              let nav = appDel.wd.rootViewController as? BaseTabBarViewController,
              let nav1 = nav.selectedViewController as? UINavigationController,
              let visibleController = nav1.visibleViewController else {
            return UINavigationController()
        }
        return visibleController
    }
}
