
//
//  LCTools.swift
//  livePlus
//
//  Created by iclick on 2020/11/28.
//  Copyright © 2020年 www.iclick.com. All rights reserved.
//

import UIKit
import Lottie
import RxSwift
import AVFoundation
import Photos

/// 存储所有用户的直播间（待废弃）
fileprivate let k_allRoom_floder = "AllRoom"

/// 存储所有的系统素材库资源下载
fileprivate let k_sourceDownload_floder = "sourceDownloadMaterailFloder"

// 本地的PCM文件
fileprivate let k_LocalPCM_floder = "LocalPCM"

/// 临时目录
fileprivate let k_temp_floder = "tempCache"

/// 下载资源的存储文件夹
fileprivate let k_temp_Download_floder = "tempDownloadCache"

//public let  k_default_bg_portraitImage = "default_bg_portraitImage"
//
public let  k_default_PayLogInfo = "PayLogInfo"

/// 所有的备份直播间zip 自己生成的ZIP 包保存的文件夹
let k_backup_all_floder = "backupRooms"

///  APP收到的ZIP包 ，不是自己生成的
let k_backup_receive_floder = "backupReceiveRooms"

fileprivate let k_SmartAudioRecord_floder = "SmartAudioRecord"


/*整个App的沙盒文件存储路径：
 一、realm数据库：直接放documents .如： /documents/defaultDB2.realm
 二、直播间：
 1、直播间json：转model存realm
 2、直播间素材：跟用户不绑定，放置到统一的文件夹（getMaterialSourceDownloadFolder）.如：/documents/sourceDownload
 3、直播间封面：因需要直播间上传同步，放置到统一的文件夹（getMaterialSourceDownloadFolder）.如：/documents/sourceDownload/xxx.png
 4、快手的直播封面图：因需要直播间上传同步，放置到统一的文件夹（getMaterialSourceDownloadFolder）.如：/documents/sourceDownload/xxx.png
 5、直播间素材封面（仅本地相册资源）：直播间同步时不需要，放置到某个临时目录，可以随意清除.如：/documents/tempCache/xxx.png
 */

@objcMembers class LCTools: NSObject {
    
    static let shared = LCTools()
    
    override init() {
        super.init()
        NotificationCenter.default.addObserver(self, selector: #selector(didGetWeiXinResp(_:)), name: LCKey.noti_wechatResponse, object: nil)
    }
    
    class func userIsMember() -> Bool {
        return UserInfo.isMember
    }
    
   
    
    // 用户内购记录保存及上报 用户的手机号作为记录
    class func savePayLogInfo( message: String) {
        
        guard let user = UserInfo.currentUser(), let phone = user.phone else {
            return
        }
        
        let timeFormatter = DateFormatter.init()
        timeFormatter.locale = Locale(identifier: "en_US_POSIX")
        timeFormatter.timeZone = TimeZone(abbreviation: "GMT")
        timeFormatter.dateFormat = "yyyy-MM-dd HH:mm:ss.SSS"
        let strNowTime = timeFormatter.string(from: Date()) as String
        
        var info = "\n phone: \(phone) + 时间:\(strNowTime) message:\(message)"
        if let logs = UserDefaults.standard.string(forKey: k_default_PayLogInfo + phone) {
            info = logs + info
            UserDefaults.standard.set(info, forKey: k_default_PayLogInfo + phone)
            return
        }
        
        UserDefaults.standard.set(info, forKey: k_default_PayLogInfo + phone)
        
    }
    
    class func repaortPayLogInfo() {
        guard let user = UserInfo.currentUser(), let phone = user.phone else {
            return
        }
        
        if let logs = UserDefaults.standard.string(forKey: k_default_PayLogInfo + phone) {
            MiddleRequestNet.reportToSeverPayLog(log: logs)
            UserDefaults.standard.removeObject(forKey: k_default_PayLogInfo + phone)
            return
        }
    }
    
    // MARK: - 用户信息 - usercode
    
    // MARK: - 临时文件存储
    /// 获取临时文件的存储路径
    class func getTemporaryCacheFolder() -> String {
        let documentsPath = NSSearchPathForDirectoriesInDomains(FileManager.SearchPathDirectory.documentDirectory, FileManager.SearchPathDomainMask.userDomainMask, true).first
        let rootFolder = documentsPath! + "/" + k_temp_floder
        if !FileManager.default.fileExists(atPath: rootFolder){
            do {
                try FileManager.default.createDirectory(atPath: rootFolder, withIntermediateDirectories: true, attributes: nil)
            } catch {
                LCLog.d("something error when create root doct path")
            }
        }
        return rootFolder
    }
    
    class func getMP4CacheFolderPath() -> String {
        let documentsPath = NSSearchPathForDirectoriesInDomains(FileManager.SearchPathDirectory.documentDirectory, FileManager.SearchPathDomainMask.userDomainMask, true).first
        let rootFolder = documentsPath! + "/" + "MP4"
        
        return rootFolder
    }
    
    /// 清除临时文件目录
    class func removeTemporaryCacheFolder() {
        let rootFolder = getTemporaryCacheFolder()
        if FileManager.default.fileExists(atPath: rootFolder) {
            do {
                try FileManager.default.removeItem(atPath: rootFolder)
            } catch {
                LCLog.d("something error when remove path")
            }
        }
        
        let mp4 = getMP4CacheFolderPath()
        if FileManager.default.fileExists(atPath: mp4) {
            do {
                try FileManager.default.removeItem(atPath: mp4)
            } catch {
                LCLog.d("something error when remove path")
            }
        }
        
    }
    

    
    // MARK: - 直播间相关的本地数据存储
    ///图片保存到本地
    class func storeImage(_ image: UIImage, isGif: Bool, to path: String) {
        var imageData = image.pngData()
        if isGif, image.isKind(of: SDAnimatedImage.self) {
            let sdImg = image as! SDAnimatedImage
            imageData = sdImg.animatedImageData
        }
        do {
            try imageData?.write(to: URL(fileURLWithPath: path))
        } catch {
            LCLog.d("保存失败:\(error)")
        }
    }
    
}

// MARK: - 直播间上传
extension LCTools {
    /*直播间上传时的素材处理：
     1、创建直播间id同名的文件夹(此文件夹位于getTemporaryCacheFolder内，下次打开app会清空)
     2、扫描当前直播间用到的所有素材，从k_sourceDownload_floder复制到新建的直播间文件夹
     3、直播间封面：从k_sourceDownload_floder复制到新建的直播间文件夹
     4、快手开播封面：从k_sourceDownload_floder复制到新建的直播间文件夹
     */
    
    // 保存相册的图片到res 文件下 name 是素材的ID
    class func copyPhotoImage(_ name: String, image: UIImage, toRoom id: String) {
        print("copy item \(name)")
        let floder = getRoomTempStoreFloder(withRoomId: id)
        let sourcePath = getMaterialSourceLocalFullPathFromFolder(name)
        let resPath = floder + "/" + "res/"
        let newPath = resPath + name
        guard !FileManager.default.fileExists(atPath: newPath) else {
            LCLog.d("素材：\(name)已经存储过：\(newPath)")
            return
        }
        if !FileManager.default.fileExists(atPath: floder) {
            do {
                try FileManager.default.createDirectory(atPath: floder, withIntermediateDirectories: true, attributes: nil)
            } catch {
                LCLog.d("something error when create root doct path")
            }
        }
        if !FileManager.default.fileExists(atPath: resPath) {
            do {
                try FileManager.default.createDirectory(atPath: resPath, withIntermediateDirectories: true, attributes: nil)
            } catch {
                LCLog.d("something error when create root doct path")
            }
        }
      
        do {
            print("sourcePath:\(sourcePath) newPath:\(newPath)")
            try? image.pngData()?.write(to: URL(fileURLWithPath: newPath))
        } catch {
            LCLog.d(error.localizedDescription)
        }
    }
    
    
    // 保存相册的gif到res 文件下 name 是素材的ID
    class func copyPhotoGif(_ name: String, data: Data, toRoom id: String) {
        print("copy item \(name)")
        let floder = getRoomTempStoreFloder(withRoomId: id)
        let sourcePath = getMaterialSourceLocalFullPathFromFolder(name)
        let resPath = floder + "/" + "res/"
        let newPath = resPath + name
        guard !FileManager.default.fileExists(atPath: newPath) else {
            LCLog.d("素材：\(name)已经存储过：\(newPath)")
            return
        }
        if !FileManager.default.fileExists(atPath: floder) {
            do {
                try FileManager.default.createDirectory(atPath: floder, withIntermediateDirectories: true, attributes: nil)
            } catch {
                LCLog.d("something error when create root doct path")
            }
        }
        if !FileManager.default.fileExists(atPath: resPath) {
            do {
                try FileManager.default.createDirectory(atPath: resPath, withIntermediateDirectories: true, attributes: nil)
            } catch {
                LCLog.d("something error when create root doct path")
            }
        }
       
        do {
            print("sourcePath:\(sourcePath) newPath:\(newPath)")

            try? data.write(to: URL(fileURLWithPath: newPath))
        } catch {
            LCLog.d(error.localizedDescription)
        }
    }
    
    
    // 保存相册的gif到res 文件下 name 是素材的ID
    class func copyPhotoVideo(_ name: String, path: URL, toRoom id: String) {
        print("copy item \(name)")
        let floder = getRoomTempStoreFloder(withRoomId: id)

        let resPath = floder + "/" + "res/"
        let newPath = resPath + name
        guard !FileManager.default.fileExists(atPath: newPath) else {
            LCLog.d("素材：\(name)已经存储过：\(newPath)")
            return
        }
        if !FileManager.default.fileExists(atPath: floder) {
            do {
                try FileManager.default.createDirectory(atPath: floder, withIntermediateDirectories: true, attributes: nil)
            } catch {
                LCLog.d("something error when create root doct path")
            }
        }
        if !FileManager.default.fileExists(atPath: resPath) {
            do {
                try FileManager.default.createDirectory(atPath: resPath, withIntermediateDirectories: true, attributes: nil)
            } catch {
                LCLog.d("something error when create root doct path")
            }
        }
        
        
        do {
            print("sourcePath:\(path) newPath:\(newPath)")

            try FileManager.default.copyItem(at: path, to: URL(fileURLWithPath: newPath))
        } catch {
            LCLog.d(error.localizedDescription)
        }
    }
    
    /// (从k_sourceDownload_floder)复制素材到与直播间id同名的文件夹
    class func copyLocalMaterialSource(_ name: String, toRoom id: String, isAudio: Bool = false) {
        print("copy item \(name)")
        let floder = getRoomTempStoreFloder(withRoomId: id)
        
        var sourcePath = getMaterialSourceLocalFullPathFromFolder(name)
        if isAudio {
            let Documents = NSSearchPathForDirectoriesInDomains(.documentDirectory, .userDomainMask, true)[0]
            sourcePath = Documents.appendingPathComponent("SmartAudioRecord") + "/" + name
        }
        
        let resPath = floder + "/" + "res/"
        let newPath = resPath + name
        guard !FileManager.default.fileExists(atPath: newPath) else {
            LCLog.d("素材：\(name)已经存储过：\(newPath)")
            return
        }
        if !FileManager.default.fileExists(atPath: floder) {
            do {
                try FileManager.default.createDirectory(atPath: floder, withIntermediateDirectories: true, attributes: nil)
            } catch {
                LCLog.d("something error when create root doct path")
            }
        }
        if !FileManager.default.fileExists(atPath: resPath) {
            do {
                try FileManager.default.createDirectory(atPath: resPath, withIntermediateDirectories: true, attributes: nil)
            } catch {
                LCLog.d("something error when create root doct path")
            }
        }
        if !FileManager.default.fileExists(atPath: sourcePath) {
            LCLog.d("资源不存在: \(sourcePath)")
            return
        }
        do {
            print("sourcePath:\(sourcePath) newPath:\(newPath)")
            try FileManager.default.copyItem(atPath: sourcePath, toPath: newPath)
        } catch {
            LCLog.d(error.localizedDescription)
        }
    }
    
    /// 保存直播间json串到直播间id同名的文件夹
    class func saveRoomJson(with roomId: String, and jsonString: String) {
        let folder = getRoomTempStoreFloder(withRoomId: roomId)
        let path = folder + "/" + "room.json"
        do {
            try jsonString.write(toFile: path, atomically: true, encoding: .utf8)
        } catch {
            LCLog.d("保存json串失败：error: \(error)")
        }
    }
    
    /// 保存配置信息
    class func saveConfigJson(jsonString: String) {
        let folder = getBackupSourceDownloadFolder()
        let path = folder + "/" + "config.json"
        do {
            try jsonString.write(toFile: path, atomically: true, encoding: .utf8)
        } catch {
            LCLog.d("保存json串失败：error: \(error)")
        }
    }
    
    /// 获取与直播间id同名的文件夹路径
    class func getRoomTempStoreFloder(withRoomId: String) -> String {
        return getBackupSourceDownloadFolderFullpath("room")
    }
    
}

// MARK: - 直播间的数据存储  【待废弃】
extension LCTools {
    
    /** 待废弃 ---- 这个是跟我的直播间绑定的
     * 获取文件的本地存储路径
     * 1、png、mp4 类型，存储为 文件名+类型 ，如 123.png
     * 2、lottie文件为zip解压后的文件夹，存储为 以lottie文件(json)名命名的文件夹，如 :123 ; 加载文件时需要拼接文件 如：xxx/xxx/123/123.json
     */
    class func getLocalFullPath(withName name: String) -> String {
        let path = createMyRoomSourceBaseDoctPath()
        return path + "/" + name
    }
    
    ///我的直播间 素材存储的根目录
    class func createMyRoomSourceBaseDoctPath() -> String {
        let rootFolder = getCurrentUserRoomCacheFolder()
        if !FileManager.default.fileExists(atPath: rootFolder) {
            do {
                try FileManager.default.createDirectory(atPath: rootFolder, withIntermediateDirectories: true, attributes: nil)
            } catch {
                LCLog.d("something error when create root doct path")
            }
        }
        return rootFolder
    }
    
    ///清空当前用户的所有直播间模板
    class func removeCurrentUserRoomCacheSource() {
        let rootFolder = getCurrentUserRoomCacheFolder()
        if FileManager.default.fileExists(atPath: rootFolder){
            do {
                try FileManager.default.removeItem(atPath: rootFolder)
            } catch {
                LCLog.d("something error when remove path")
            }
        }
    }
    
    ///取得当前用户的直播间缓存数据根目录
    class func getCurrentUserRoomCacheFolder() -> String {
        let documentsPath = NSSearchPathForDirectoriesInDomains(FileManager.SearchPathDirectory.documentDirectory, FileManager.SearchPathDomainMask.userDomainMask, true).first
        //路径拼接userId,清空直播间时移除对应的文件夹
        var myRoom = "0"
        if let user = UserInfo.currentUser() {
            myRoom = "\(user.userId ?? 0)"
        }
        return documentsPath! + "/" + k_allRoom_floder + "/" + myRoom
    }
    
}

// MARK: - 遥控器相关：微信二维码处理

private let disposeBag = DisposeBag()
extension LCTools {
    
    /// 获取微信 token
    static func getWechatToken(success:@escaping ((_ token: NSString) -> Void)) {
        
        let req: Observable<BaseRespModel<WechatTokenData>> = APISession.getWechatToken(APIURL.GET_WECHAT_Token).asObservable()
        req.subscribe(onNext: {(res: BaseRespModel<WechatTokenData>) in
            
            //判断新返回的token，为空时不覆盖当前的
            if res.code == 0 {
                if let token: WechatTokenData = res.data {
                    if var userInfo = UserInfo.currentUser() {
                        userInfo.access_token = token.access_token
                        userInfo.saveUser()
                    }
                }
            } else {
                
            }
        }).disposed(by: disposeBag)
    }
    
    //    POST https://api.weixin.qq.com/cgi-bin/wxaapp/createwxaqrcode?access_token=ACCESS_TOKEN
    
    static func createWXaqrCode(roomid: String, success: @escaping ((_ image: UIImage) -> Void)) {
        
        guard let token = (UserInfo.currentUser()?.access_token) else {
            success(UIImage())
            return
        }
        
        let path = "pages/control/control?ip=" + LPSocketHelper.getIPAddresses() + "&roomid=" + roomid
        let url: String = APIURL.POST_WeChat_Code + token
        let wid = Int(LCDevice.DIN_WIDTH(216))
        
        let param = ["page":path,"width":wid,"scene": LPSocketHelper.getIPAddresses()] as [String : Any]
        let req : Observable<WeChatRespModel> = APISession.postWeChatQrCode(url, parameters: param).asObservable()
        req.subscribe(onNext: {(model:WeChatRespModel) in
            if model.errcode == 0{
                //已结束
                guard let image: UIImage =  UIImage(data: model.buffer) else {
                    success(UIImage())
                    LCLog.d("微信二维码失败")
                    DispatchQueue.main.async {
                        HUD.showInfo("微信token失效，请稍后重试")
                    }
                    
                    if let dict = try? JSONSerialization.jsonObject(with: model.buffer!, options: []) as? [String: Any]{
                        if dict != nil && dict["code"] != nil {
                            //do something with json
                            //....
                        }
                    }
                    
                   
                    return
                }
                
                success(image)
                
            }
        }).disposed(by: disposeBag)
    }
        
}


extension LCTools {
    
    /// 存到手机的系统相册，那么使用 localIdentifier 来查询
    /// 返回这个资源在相册中的URL.string  外部播放时用
    public static func sourceUrl(path: String, callback: @escaping ( (String?) -> Void )) {
      
        guard let phasset = ZLPhotoManager.getAsset(from: path) else {
            callback(nil)
            return
        }
        ZLPhotoManager.fetchAssetFilePath(asset: phasset) { url in
           callback(url)
        }
    }
    
    public static func sourceAVAsset(path: String, needAsync: Bool = true, callback: @escaping ( (AVAsset?) -> Void )) {
        guard let phasset = ZLPhotoManager.getAsset(from: path) else {
            callback(nil)
            return
        }
        ZLPhotoManager.fetchAVAsset(forVideo: phasset, needAsync: needAsync) { asset, info in
            callback(asset)
        }
    }
    
    /// 相册图片存本地，且不检查是否存在
    public class func exportPhotoWithoutFilterLocal(for image: UIImage, fileName: String, completion: @escaping ( (String?) -> Void )) {
        let rootPath = getMaterialSourceDownloadFolder()
        let fileNameStr = fileName + "\(image.hashValue)" + ".png"
        let outputStr = rootPath + "/" + fileNameStr
        
        try? image.pngData()?.write(to: URL(fileURLWithPath: outputStr))
        
        completion(fileNameStr)
    }
    
    
}


extension LCTools {
    
    static func creatNewFolde() -> String? {
        // 删除 当前可能存在的 room config.json 文件夹
        let receivePath = getBackupAllReceiveRoomsFolder()
        // 创建一个文件夹 用来临时保存
        let timestamp = Date().timeIntervalSince1970 * 1000 * 1000
        let timestampstr = "/\(timestamp)".replacingOccurrences(of: ".", with: "")
        let rootFolder = receivePath + timestampstr
        if !FileManager.default.fileExists(atPath: rootFolder){
            do {
                try FileManager.default.createDirectory(atPath: rootFolder, withIntermediateDirectories: true, attributes: nil)
            } catch {
                LCLog.d("something error when create root doct path")
                return nil
            }
        }
        return rootFolder
    }
    
    /// 直播间备份删除
    static func removeBackupFolder(roomId: String) {
        let folder = getBackupSourceDownloadFolder()
        if let fileList = try? FileManager.default.contentsOfDirectory(atPath: folder) {
            for file in fileList where !file.contains(".zip") {
                let path = folder.appendingPathComponent(file)
                print("file \(file)")
                do {
                    try FileManager.default.removeItem(atPath: path)
                } catch {
                    print("移除文件失败 error: \(error)")
                }
            }
        }
    }
    
    // 删除所有的备份直播间
    static func removeBackupFolderAll() {
        let folder = getBackupSourceDownloadFolder()
        if let fileList = try? FileManager.default.contentsOfDirectory(atPath: folder) {
            for file in fileList {
                let path = folder.appendingPathComponent(file)
                print("file \(file)")
                do {
                    try FileManager.default.removeItem(atPath: path)
                } catch {
                    print("移除文件失败 error: \(error)")
                }
            }
        }
    }
    
    static func removeBackupRoomFolderAll() {
        let folder = getBackupAllRoomsFolder()
        if let fileList = try? FileManager.default.contentsOfDirectory(atPath: folder) {
            for file in fileList {
                let path = folder.appendingPathComponent(file)
                print("file \(file)")
                do {
                    try FileManager.default.removeItem(atPath: path)
                } catch {
                    print("移除文件失败 error: \(error)")
                }
            }
        }
    }
    
    /// 直播间备份的下载路径
    static func getBackupSourceDownloadFolderFullpath(_ name: String) -> String {
        let folder = getBackupSourceDownloadFolder()
        let path = folder + "/" + name
        if !FileManager.default.fileExists(atPath: path){
            do {
                try FileManager.default.createDirectory(atPath: path, withIntermediateDirectories: true, attributes: nil)
            } catch {
                LCLog.d("something error when create root doct path")
            }
        }
        return path
    }
    
    static func getBackupSourceDownloadFolder() -> String {
        let documentsPath = NSSearchPathForDirectoriesInDomains(FileManager.SearchPathDirectory.documentDirectory, FileManager.SearchPathDomainMask.userDomainMask, true).first
        let rootFolder = documentsPath! + "/" + k_temp_Download_floder
        if !FileManager.default.fileExists(atPath: rootFolder){
            do {
                try FileManager.default.createDirectory(atPath: rootFolder, withIntermediateDirectories: true, attributes: nil)
            } catch {
                LCLog.d("something error when create root doct path")
            }
        }
        return rootFolder
    }
    
    /// 最后的所有备份直播间数据
    static func getBackupAllRoomsFolder() -> String {
        let documentsPath = NSSearchPathForDirectoriesInDomains(FileManager.SearchPathDirectory.documentDirectory, FileManager.SearchPathDomainMask.userDomainMask, true).first
        let rootFolder = documentsPath! + "/" + k_backup_all_floder
        if !FileManager.default.fileExists(atPath: rootFolder){
            do {
                try FileManager.default.createDirectory(atPath: rootFolder, withIntermediateDirectories: true, attributes: nil)
            } catch {
                LCLog.d("something error when create root doct path")
            }
        }
        return rootFolder
    }
    
    
    /// APP收到的备份直播间数据的全路径
    static func getBackupAllReceiveRoomsFolderFullpath(_ name: String) -> String {
        let folder = getBackupAllReceiveRoomsFolder()
        return folder + "/" + name
    }
    
    /// APP收到的备份直播间数据
    static func getBackupAllReceiveRoomsFolder() -> String {
        let documentsPath = NSSearchPathForDirectoriesInDomains(FileManager.SearchPathDirectory.documentDirectory, FileManager.SearchPathDomainMask.userDomainMask, true).first
        let rootFolder = documentsPath! + "/" + k_backup_receive_floder
        if !FileManager.default.fileExists(atPath: rootFolder){
            do {
                try FileManager.default.createDirectory(atPath: rootFolder, withIntermediateDirectories: true, attributes: nil)
            } catch {
                LCLog.d("something error when create root doct path")
            }
        }
        return rootFolder
    }
    
    static func removeReceiveBackupFolderAll() {
        let folder = getBackupAllReceiveRoomsFolder()
        if let fileList = try? FileManager.default.contentsOfDirectory(atPath: folder) {
            for file in fileList {
                let path = folder.appendingPathComponent(file)
                print("file \(file)")
                do {
                    try FileManager.default.removeItem(atPath: path)
                } catch {
                    print("移除文件失败 error: \(error)")
                }
            }
        }
    }
    
    /// 将中转文件夹下的直播间zip 保存到最终的备份文件夹下
    static func copyBackUPZipToBackUpFolder(zipPath: String, zipName: String) {
        let backPath = getBackupAllRoomsFolder() + "/" + zipName
        do {
            try FileManager.default.copyItem(atPath: zipPath, toPath: backPath)
        } catch {
            LCLog.d(error.localizedDescription)
        }
    }
    
    
    static func removeZip(zipPath: String) {
        if FileManager.default.fileExists(atPath: zipPath) {
            do {
                try FileManager.default.removeItem(atPath: zipPath)
                LCLog.d("移除zip成功:\(zipPath)")
            } catch {
                LCLog.d("移除zip出错:\(zipPath)")
            }
        }
    }
    
    
    static func unzipAndCopyToBackupFolder(zipPath: String,
                                           password: String,
                                           remove: Bool = true,
                                           newFolder: String? = nil,
                                           progressHandle: @escaping (_ progress: Double) -> Void,
                                           resultHandle: @escaping (_ result: Bool) -> Void) {
        var materialPath = getBackupAllReceiveRoomsFolder()
        if let newFolder = newFolder {
            materialPath = newFolder
        }
        let queue = DispatchQueue(label: "unzip-\(password)")
        queue.async {
            SSZipArchive.unzipFile(atPath: zipPath, toDestination: materialPath, overwrite: true, password: password) { (string, info, entryNumber, total) in
                progressHandle(Double(entryNumber) / Double(total))
            } completionHandler: { (path, result, error) in
                guard error == nil else {
                    LCLog.d("解压报错：\(error)")
                    DispatchQueue.main.async {
                        resultHandle(false)
                    }
                    return
                }
                //remove zip
                if remove, FileManager.default.fileExists(atPath: zipPath) {
                    do {
                        try FileManager.default.removeItem(atPath: zipPath)
                    } catch {
                        LCLog.d("移除zip出错")
                    }
                }
                DispatchQueue.main.async {
                    resultHandle(true)
                }
            }
        }
    }
    
    
    //    直播间的备份ZIP包 保存到沙盒
    public static func saveZIP(replacePath: String, fileName: String) -> Bool {
        // 把这个文件夹清空
        LCTools.removeReceiveBackupFolderAll()
        
        let targetPath = LCTools.getBackupAllReceiveRoomsFolderFullpath(fileName)
        let copyResult = XCFileManager.copyItem(atPath: replacePath, toPath: targetPath)
        if copyResult {
            DispatchQueue.main.async {
                HUD.showSuccess("请输入密码")?.isUserInteractionEnabled = true
            }
        } else {
            HUD.showFail("资源保存失败，请在其他APP中重新选择”其他方式打开“，然后选择”快瓴中控台“")
        }
        return copyResult

    }
    
    
    static func formatFileSize(fileS: Int64)-> String {
        if (fileS < 1024) {
            return "\(fileS)B"
        }
        if (fileS >= 1024 && fileS < 1048576) {
            let size: CGFloat = CGFloat(fileS) / 1024.0
           return String(format:"%.2fKB",size)
        }
        if (fileS >= 1048576 && fileS < 1073741824) {
            let size: CGFloat = CGFloat(fileS) / 1048576.0
            return String(format:"%.2fMB",size)
        }
        if (fileS >= 1073741824) {
            let size:CGFloat = CGFloat(fileS) / 1073741824.0
            return String(format:"%.2fGB",size)
        }
        return "\(fileS)"
    }
    
}


// MARK: - 处理素材库资源的zip解密
extension LCTools {
    
    /* 多图层版本的资源保存本地的策略：
     一、存储：
     1、相册：以asset的id的md5为名称命名，并copy到getMaterialSourceDownloadFolder
     2、服务端资源：以原名称下载到getMaterialSourceDownloadFolder
     二、使用：从getMaterialSourceDownloadFolder拼接name作为完整path使用
     三、直播间打包：需要遍历图层、区域，从getMaterialSourceDownloadFolder找出所有当前直播间使用的素材，放置到直播间id文件夹内，打包上传
     */
    
    /// 获取素材库资源文件的本地完整路径
    class func getMaterialSourceLocalFullPathFromFolder(_ name: String) -> String {
        let folder = getMaterialSourceDownloadFolder()
        return folder + "/" + name
    }
    
    /// 素材库资源文件的下载路径
    class func getMaterialSourceDownloadFolder() -> String {
        let documentsPath = NSSearchPathForDirectoriesInDomains(FileManager.SearchPathDirectory.documentDirectory, FileManager.SearchPathDomainMask.userDomainMask, true).first
        let rootFolder = documentsPath! + "/" + k_sourceDownload_floder
        if !FileManager.default.fileExists(atPath: rootFolder) {
            do {
                try FileManager.default.createDirectory(atPath: rootFolder, withIntermediateDirectories: true, attributes: nil)
            } catch {
                LCLog.d("something error when create root doct path")
            }
        }
        return rootFolder
    }
    
   
    static func unzipAndCopyToMaterialSouceFolder(zipPath: String,
                                                  password: String,
                                                  progressHandle: @escaping (_ progress: Double) -> Void,
                                                  resultHandle: @escaping (_ result: Bool) -> Void) {
        let materialPath = getMaterialSourceDownloadFolder()
        DispatchQueue.global().async {
            SSZipArchive.unzipFile(atPath: zipPath, toDestination: materialPath, overwrite: true, password: password) { (string, info, entryNumber, total) in
                progressHandle(Double(entryNumber / total))
            } completionHandler: { (path, result, error) in
                guard error == nil else {
                    DispatchQueue.main.async {
                        resultHandle(false)
                    }
                    return
                }
                //remove zip
                if FileManager.default.fileExists(atPath: zipPath) {
                    do {
                        try FileManager.default.removeItem(atPath: zipPath)
                    } catch {
                        LCLog.d("移除zip出错")
                    }
                }
                DispatchQueue.main.async {
                    resultHandle(true)
                }
            }
        }
    }
    
    /// 下载视频文件
    static func downloadMovie(url: URL, name: String, closure: @escaping (Status<String>) -> Void) {
        guard let md5Name = (name as NSString).md5() else {
            DispatchQueue.main.async {
                closure(.failure(BaseError("下载失败")))
            }
            return
        }
        let folderPath = getMaterialSourceDownloadFolder()
        let extesion = name.pathExtension
        let finalPath = folderPath + "/" + md5Name + "." + extesion
        //已经存在，直接返回结果
        if FileManager.default.fileExists(atPath: finalPath) {
            LCLog.d("当前文件已存在:\(finalPath)")
            DispatchQueue.main.async {
                closure(.success(finalPath))
            }
            return
        }
        
        DispatchQueue.global().async {
            let task = URLSession.shared.dataTask(with: url) { data, _, downError in
                guard downError == nil, let data = data else {
                    DispatchQueue.main.async {
                        closure(.failure(BaseError(downError?.localizedDescription ?? "下载失败")))
                    }
                    return
                }
                do {
                    try data.write(to: URL(fileURLWithPath: finalPath))
                    DispatchQueue.main.async {
                        closure(.success(finalPath))
                    }
                } catch {
                    print("写入文件失败：\(error)")
                    DispatchQueue.main.async {
                        closure(.failure(BaseError("下载失败")))
                    }
                }
            }
            task.resume()
        }
    }
    
    /*
     1、下载zip（本地缓存判断）
     2、zip解压（解密）
     3、拿出文件
     */
    
    /// 下载zip文件并解密
    static func downloadZipSourceFrom(url: URL, source: String, fileName: String, closure: @escaping (_ path: String?, _ name: String?) -> Void) {
        
        let folderPath = getMaterialSourceDownloadFolder() //  Document/sourceDownload
        let zipPath = folderPath + "/" + source //  Document/sourceDownload/123_encrypt.zip
        
        /// 最终得到的资源绝对路径
        let finalPath = folderPath + "/" + fileName//  Document/sourceDownload/123.png
        
        //已经存在，直接返回结果
        if FileManager.default.fileExists(atPath: finalPath) {
            LCLog.d("当前文件已存在:\(finalPath)")
            DispatchQueue.main.async {
                closure(finalPath, fileName)
            }
            return
        }
        
        guard source.contains("_encrypt") else {
            LCLog.d("非加密文件，不再处理---")
            closure(nil, nil)
            return
        }
        guard let zipKey = (source as NSString).md5() else {
            LCLog.d("获取md5 失败了--")
            closure(nil, nil)
            return
        }
        do {
            try FileManager.default.createDirectory(atPath: folderPath, withIntermediateDirectories: true, attributes: nil)
        } catch {
            LCLog.d("something error when create root doct path error is: \(error)")
            DispatchQueue.main.async {
                closure(nil, nil)
            }
            return
        }
        
        DispatchQueue.global().async {
            //执行下载
            let task = URLSession.shared.dataTask(with: url) { (data, response, error) in
                guard error == nil, let zipData = data else {
                    LCLog.d("下载zip出错")
                    DispatchQueue.main.async {
                        closure(nil, nil)
                    }
                    return
                }
                do {
                    try zipData.write(to: URL(fileURLWithPath: zipPath))
                    SSZipArchive.unzipFile(atPath: zipPath, toDestination: folderPath, overwrite: true, password: zipKey , progressHandler: nil) { (path, isSuc, error) in
                        guard error == nil else {
                            closure(nil, nil)
                            return
                        }
                        //remove zip
                        if FileManager.default.fileExists(atPath: zipPath) {
                            do {
                                try FileManager.default.removeItem(atPath: zipPath)
                            } catch {
                                LCLog.d("移除zip出错")
                            }
                        }
                        
                        /* 注意：重命名：只针对MP4改写为mp4，其他不处理 */
                        if (fileName as NSString).pathExtension != "MP4" {
                            //不需要改后缀，直接返回
                            DispatchQueue.main.async {
                                closure(finalPath, fileName)
                            }
                            return
                        }
                        // 大写MP4需要重命名（如：123.MP4 -> 123.mp4）,把finalPath的xxx.MP4 移动到同文件夹到 xxx.mp4
                        var newFileName = fileName
                        let fileExt = (fileName as NSString).pathExtension.lowercased()
                        if let newPath = (fileName.removePathExtension() as NSString).appendingPathExtension(fileExt) {
                            newFileName = newPath
                        }
                        //把finalPath中的fileName 重命名
                        let newPathFloder = (finalPath as NSString).deletingLastPathComponent
                        let newPath = (newPathFloder as NSString).appendingPathComponent(newFileName)
                        do {
                            let oldUrl = URL(fileURLWithPath: finalPath)
                            let newUrl = URL(fileURLWithPath: newPath)
                            try FileManager.default.moveItem(at: oldUrl, to: newUrl)
                            //回调结果
                            DispatchQueue.main.async {
                                closure(newPath, newFileName)
                            }
                            return
                        } catch {
                            LCLog.d(error.localizedDescription)
                            //回调结果
                            DispatchQueue.main.async {
                                closure(nil, nil)
                            }
                            return
                        }
                        
                    }
                } catch {
                    DispatchQueue.main.async {
                        closure(nil, nil)
                    }
                }
            }
            task.resume()
        }
    }
    
    /// 清除本地下载的 资源目录
    class func removeMaterialSourceDownloadFolder() {
        let rootFolder = getMaterialSourceDownloadFolder()
        if FileManager.default.fileExists(atPath: rootFolder) {
            do {
                try FileManager.default.removeItem(atPath: rootFolder)
            } catch {
                LCLog.d("something error when remove path")
            }
        }
    }
    
    /// 清除某个文件（可能由于下载失败，导致文件存在，但是路径不存在）
    class func removeMaterial(source: String, fileName: String) {
        let folderPath = getMaterialSourceDownloadFolder() //  Document/sourceDownload
        let zipPath = folderPath + "/" + source //  Document/sourceDownload/123_encrypt.zip
        
        /// 最终得到的资源绝对路径
        let finalPath = folderPath + "/" + fileName//  Document/sourceDownload/123.png
        
        //已经存在
        if FileManager.default.fileExists(atPath: finalPath) {
            LCLog.d("删除文件:\(finalPath)")
            do {
                try FileManager.default.removeItem(atPath: finalPath)
            } catch {
                LCLog.d("删除文件:\(finalPath) 失败：\(error)")
            }
            return
        }
    }
}

// MARK: - Lottie文件下载

extension LCTools {
    /*Lottie文件（123.zip）的加载逻辑:
     1、下载zip到某个临时目录A （该临时目录会在每次app启动时清空）
     2、解压zip到临时目录A  xxx/A/123/ 123.json + imgs.. + info.json
     3、拼接路径（如步骤2），拿到123.json使用lottie加载，拿到info.json，取出可以修改的文本信息,
     然后当lottieView点击时根据点击区域确定需要响应的对应文本
     4、如果保存直播间，把临时路径（如步骤2）对应的文件夹（如上 ：123）移动到保存直播间的素材固定路径
     5、恢复直播间时，资源加载同步骤3
     */
    
    ///下载lottie的zip包到本地，并解压
    static func downloadLottieZipFrom(url: URL, fileName: String, closure: @escaping (_ path: String?, _ name: String) -> Void) {
        
        guard fileName.contains("_encrypt") else {
            LCLog.d("非加密文件，不再处理---")
            closure(nil, "")
            return
        }
        guard let zipKey = (fileName as NSString).md5() else {
            LCLog.d("获取md5 失败了--")
            closure(nil, "")
            return
        }
        
        //需要把_encrypt这一串删除
        let realFileName = fileName.replacingOccurrences(of: "_encrypt", with: "")
        
        let zipPath = getMaterialSourceDownloadFolder() + "/" + realFileName //  xxx/A/123.zip
        let unzipPath = getMaterialSourceDownloadFolder() + "/" + realFileName.removePathExtension() //  xxx/A/123
        let folderPath = getMaterialSourceDownloadFolder() + "/" //  xxx/A/123
        
        //解压后的文件夹名称是唯一的
        let dateF = DateFormatter.init()
        dateF.locale = Locale(identifier: "en_US_POSIX")
        dateF.timeZone = TimeZone(abbreviation: "GMT")
        dateF.dateFormat = "yyyyMMddHHmmss"
        let timeStr = dateF.string(from: Date.init())
        let newName = "\(realFileName.removePathExtension())_\(timeStr)"
        let renameUnzipPath = getMaterialSourceDownloadFolder() + "/" + newName //  xxx/A/123
        
        let task = URLSession.shared.dataTask(with: url) { (data, response, error) in
            guard error == nil, let zipData = data else {
                DispatchQueue.main.async {
                    closure(nil, "")
                }
                return
            }
            do {
                try zipData.write(to: URL(fileURLWithPath: zipPath))
                SSZipArchive.unzipFile(atPath: zipPath, toDestination: folderPath, overwrite: true, password: zipKey, progressHandler: nil) { (path, isSuc, error) in
                    guard error == nil else {
                        closure(nil, "")
                        return
                    }
                    //remove zip
                    if FileManager.default.fileExists(atPath: zipPath) {
                        do {
                            try FileManager.default.removeItem(atPath: zipPath)
                        } catch {
                            print("移除zip出错")
                        }
                    }
                    
                    //需要对解压后的文件夹重命名,保证文件夹唯一
                    do {
                        try FileManager.default.moveItem(atPath: unzipPath, toPath: renameUnzipPath)
                        DispatchQueue.main.async {
                            closure(renameUnzipPath, newName)
                        }
                    } catch {
                        print("重命名出错")
                        DispatchQueue.main.async {
                            closure(nil, "")
                        }
                    }
                    
                }
            } catch {
                DispatchQueue.main.async {
                    closure(nil, "")
                }
            }
        }
        task.resume()
    }
    
    /// 把Lottie资源从临时下载路径移动到直播间素材存储路径
    class func moveLottieFolderFromTempDownloadToRoomSource(_ name: String) {
        let folderPath = getMaterialSourceDownloadFolder() + "/" + name.removePathExtension() //  xxx/A/123
        let targetPath = getLocalFullPath(withName: name.removePathExtension())
        do {
            try FileManager.default.moveItem(atPath: folderPath, toPath: targetPath)
        } catch {
            LCLog.d("something error when create root doct path")
        }
    }
    
    
    static func checkMember() -> Bool {
        guard let _ = UserInfo.currentUser() else {
            LCTools.loginApp()
            return false
        }
        

        if !UserInfo.isMember {
            LCTools.showVipAlert()
            return false
        }
        return true
    }
    
    static func showVipAlert() {
        FreeAlert.show {
            LCTools.goMember()
        } cancelAction: {
            
        }
    }
    
    static func goMember(){
        let ovc = LimitPurchaseVC2()
        AppDelegate.curDisplayVC().navigationController?.pushViewController(ovc)
    }
    
    static func loginApp() {

        let loginCtrl = LoginVC()
        loginCtrl.needAutoBackAfterDone = true
        loginCtrl.popToRoot = true
        
        AppDelegate.curDisplayVC().navigationController?.pushViewController(loginCtrl)
        
    }
}


// MARK: - 应用内环境方法
extension LCTools {
    class func getHostUrlPath() -> String {
        var fullUrl: String = ""
        let isRelease = UserDefaults.standard.bool(forKey: LCKey.UD_ReleaseEnv)
        if isRelease {
            fullUrl = APISession.baseURLOnline
        } else {
            fullUrl = APISession.baseURLTest
        }
        return fullUrl
    }
    
    class func iLCLogin() -> Bool {
        if UserInfo.currentUser() != nil {
            return true
        }
        return false
    }
    
    func valiationLogin(popToRoot: Bool = true, complete: @escaping (() -> Void)) {
        if !LCDevice.checkNetIsOk() { return }
        /// 如果有用户信息
        if let userInfo = UserInfo.currentUser() {
            /// 1、如果没有绑定手机号 必须绑定
            if let bindPhone = userInfo.bindPhone, !bindPhone {
                goBindPhone(complete: complete)
                return
            }
            /// 2、如果需要绑定微信 但是没有绑定微信 去绑定微信
            guard let needBindPhone = userInfo.needBindWechat, let bindWechat = userInfo.bindWechat else { complete(); return }
            if needBindPhone, !bindWechat {
                goBindWechat()
                return
            }
            complete()
            return
        }
        
        /// 没有用户信息，需要去登录
        let loginCtrl = LoginVC()
        loginCtrl.needAutoBackAfterDone = true
        loginCtrl.popToRoot = popToRoot
        loginCtrl.loginSuccessBlock = {
            DispatchQueue.main.async {
                complete()
            }
        }
        let curTopVC = AppDelegate.curDisplayVC()
        DispatchQueue.main.async {
            curTopVC.navigationController?.pushViewController(loginCtrl)
        }
     }
    
    func goBindPhone(complete: @escaping (() -> Void)) {
        //微信登录，去绑定手机
        let ctrl = PhoneBindVC()
        ctrl.bindType = .bind
        ctrl.needBackAfterDone =  true
        ctrl.bindSuccessBlock = {
            if ctrl.needBackAfterDone {
                ctrl.leftButtonAction()
            }
            DispatchQueue.main.async {
                complete()
            }
        }
        let curTopVC = AppDelegate.curDisplayVC()
        curTopVC.navigationController?.pushViewController(ctrl)
    }
    
    func showUnInstallWechat() {
        let alert = UIAlertController(title: "微信提示", message: "您的手机上没有安装微信，需要您下载安装微信后，重新绑定", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "我知道了", style: .default))
        let curTopVC = AppDelegate.curDisplayVC()
        curTopVC.present(alert, animated: true)
    }
    
    func goBindWechat() {
        let curTopVC = AppDelegate.curDisplayVC()
        AlertView.show(leftOption: AlertOption.gray(title: "我再想想", action: {
            print("不想绑定微信")
        }), rightOption: AlertOption.main(title: "微信授权登录", action: { [weak self] in
            guard let self = self else { return }
            if !WXApi.isWXAppInstalled() {
                self.showUnInstallWechat()
                return
            }
            let req = SendAuthReq()
            req.scope = "snsapi_userinfo"
            req.state = "App"
            WXApi.sendAuthReq(req, viewController: curTopVC, delegate: self) { (_) in }
            UserDefaults.standard.setValue(true, forKey: LCKey.UD_wxForBind)
            UserDefaults.standard.setValue(false, forKey: LCKey.UD_wxForLogin)
        }), title: "温馨提示", message: "为了保证您的账号安全，需要绑定并校验您的微信号。")
    }
}

extension LCTools: WXApiDelegate {
    
    /// 去微信小程序 视频教程
   static func gotoWechatAppletsRecorded(userName: String, path: String) {
        // 执行微信逻辑
        /// 1、判断是否安装微信
        if !WXApi.isWXAppInstalled() {
            NotificationCenter.default.post(name: LCKey.noti_learnCenter, object: nil)
            return
        }
        
        /// 2、安装的话 跳转到小程序
        let launchMiniProgramReq = WXLaunchMiniProgramReq.object()
        launchMiniProgramReq.userName = userName
        launchMiniProgramReq.path = path
        launchMiniProgramReq.miniProgramType = .release
        WXApi.send(launchMiniProgramReq) { result in
            print("发送小程序消息：\(result)")
        }
    }
    
    // MARK: - WXApiDelegate
    func onReq(_ req: BaseReq) {
        print("xxx")
    }
    
    func didGetWeiXinResp(_ sender: Notification) {
        if !UserDefaults.standard.bool(forKey: LCKey.UD_wxForBind) {
            return
        }
        UserDefaults.standard.setValue(false, forKey: LCKey.UD_wxForBind)
        let data = sender.object as! SendAuthResp
        if let code = data.code {
            self.realHandleWxResp(code)
        }
    }
    
    @objc func realHandleWxResp(_ code: String) {
        let param = ["code": code]
        HUD.showWait()
        let req: Observable<Status<BaseRespModel<LoginRespData>>> = APISession.post(APIURL.POST_WeiXin_BIND, parameters: param) .asObservable()
        req.subscribe(onNext: { status in
            HUD.hideAllHUD()
            switch status {
            case .success(let res):
                if res.code == 0 {
                    //判断新返回的token，为空时不覆盖当前的
                    var curToken: String?
                    if let userInfo = UserInfo.currentUser() {
                        curToken = userInfo.token
                    }
                    if var user = res.data {
                        if let newToken = user.token, !newToken.isEmpty {
                        } else {
                            user.token = curToken
                        }
                        user.saveLoginUser()

                        HUD.showSuccess("绑定成功")
                    }
                } else {
                    if let mesg = res.msg {
                        HUD.showFail(mesg)
                    } else {
                        HUD.showFail("操作失败")
                    }
                }
            case .failure(let error):
                HUD.showFail(error.localizedDescription)
            case .pending: break
            }
        }).disposed(by: disposeBag)
    }
}

// MARK: - 工具方法
extension LCTools {
    // 加载本地相册的原始图片
    public class func fetchPhotoLocalOriginalImage(path: String, callback: @escaping ( (UIImage?) -> Void )) {
        guard let phasset = ZLPhotoManager.getAsset(from: path) else {
            callback(nil)
            return
        }

         ZLPhotoManager.fetchOriginalImage(for: phasset, completion: { (image, isDegraded) in
             if let image = image, !isDegraded {
                 callback(image)
             }
        })
    }
    
    //加载本地相册的原始gif
    public class func fetchPhotoLocalOriginalGif(path: String, callback: @escaping ( (Data?) -> Void )) {
        guard let phasset = ZLPhotoManager.getAsset(from: path) else {
            callback(nil)
            return
        }
        ZLPhotoManager.fetchOriginalImageData(for: phasset, completion: { (data, info, isDegraded) in
           
            if !isDegraded {
                callback(data)
            }
           
        })
    }
    
    ///相册数据的存储名称
    class func getAlbumSourceLocalCacheName(fromAsset asset: PHAsset) -> String {
        let md5Id = (asset.localIdentifier as NSString).md5()
        var typeStr = ".png"
        switch asset.mediaType {
        case .video:
            typeStr = ".mp4"
            
        case .image:
            if (asset.value(forKey: "filename") as? String)?.hasSuffix("GIF") == true {
                typeStr = ".gif"
            } else if (asset.value(forKey: "filename") as? String)?.hasSuffix("JPG") == true {
                typeStr = ".jpg"
            }
            
        default:
            break
        }
        return md5Id! + typeStr
    }
    
    ///文件data保存到本地
    class func storeData(_ data: Data, to path: String) {
        do {
            if FileManager.default.fileExists(atPath: path) {
                LCLog.d("data保存时发现该path:\(path)有文件存在，将要自动删除！")
                try FileManager.default.removeItem(atPath: path)
            }
            try data.write(to: URL(fileURLWithPath: path))
        } catch {
            LCLog.d("data保存失败:\(error)")
        }
    }
    
    class func getFormatPlayTime(secounds: TimeInterval) -> String {
        if secounds.isNaN {
            return "00:00"
        }
        var Min = Int(secounds / 60)
        let Sec = Int(secounds.truncatingRemainder(dividingBy: 60))
        var Hour = 0
        if Min>=60 {
            Hour = Int(Min / 60)
            Min = Min - Hour*60
            return String(format: "%02d:%02d:%02d", Hour, Min, Sec)
        }
        return String(format: "00:%02d:%02d", Min, Sec)
    }
    
    //获取距离当前时间多少天
    class func getDaysForNowFromTime(fromTime: String) -> Int {
        if  fromTime.count > 0 {
            let dateformatter = DateFormatter()
            dateformatter.locale = Locale(identifier: "en_US_POSIX")
            dateformatter.timeZone = TimeZone(abbreviation: "GMT")
            dateformatter.dateFormat = "yyyy-MM-dd HH:mm:ss"// 自定义时间格式

            let curTime = Date().string(withFormat: "yyyy-MM-dd HH:mm:ss")
            
            let dateFormatter = DateFormatter()
            dateFormatter.locale = Locale(identifier: "en_US_POSIX")
            dateFormatter.timeZone = TimeZone(abbreviation: "GMT")
            dateFormatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
            guard let date1 = dateFormatter.date(from: curTime),
                  let date2 = dateFormatter.date(from: fromTime) else {
                return -1
            }
            
            let calendar: Calendar = Calendar(identifier: .gregorian)
            let components = calendar.dateComponents([.day], from: date1, to: date2)
            //如果需要返回月份间隔，分钟间隔等等，只需要在dateComponents第一个参数后面加上相应的参数即可，示例如下：
            //    let components = NSCalendar.current.dateComponents([.month,.day,.hour,.minute], from: date1, to: date2)
            //        print(components.month!,components.day!,components.hour!,components.minute!)
            return components.day!
        } else {
            return 0
        }
    }
    
    class func getDateFromTime(time: String) -> Date {
        let dateformatter = DateFormatter()
        dateformatter.locale = Locale(identifier: "en_US_POSIX")
        dateformatter.timeZone = TimeZone(abbreviation: "GMT")
        //自定义日期格式
//        2023-06-13 23:59:59
        dateformatter.dateFormat="yyyy-MM-dd HH:mm:ss"
        if let str = dateformatter.date(from: time) {
            return str
        }
        return Date()
        
    }
    
    class func covertDateString(timeString: String) -> String {
        if let time = timeString.split(separator: " ").first {
            let sepretorTime = time.replacingOccurrences(of: "-", with: ".")
            return sepretorTime
        }
        let time = LCTools.getDateFromTime(time: timeString)
        return time.string(withFormat: "yyyy.MM.dd")
    }
    
    class func check90DaysExpire(time: String) -> Bool {
        // 设置北京时区(东八区)
        let beijingTimeZone = TimeZone(identifier: "Asia/Shanghai")!
        var calendar = Calendar(identifier: .gregorian)
        calendar.timeZone = beijingTimeZone

        // 设置日期格式化器
        let dateFormatter = DateFormatter()
        dateFormatter.timeZone = beijingTimeZone
        dateFormatter.dateFormat = "yyyy-MM-dd HH:mm:ss"

        // 获取当前日期
        let currentDate = Date()

        // 目标日期
        if let targetDate = dateFormatter.date(from: time) {
            // 计算天数差
            let components = calendar.dateComponents([.day], from: currentDate, to: targetDate)
            
            if let days = components.day {
                print("天数差: \(days)天")
                if days > 90 {
                    return true
                } else if days == 90 {
                    return true
                } else {
                    return false
                }
            }
        }
        
        return false
    }
    
    
    //渐变layer
    class func getgradientLayerForColors(gradientColors: [UIColor]) -> CAGradientLayer {
//        let gradientColors = [leftColor.cgColor, rightColor.cgColor]
        var colors: [CGColor] = []
        for colorItem in gradientColors {
            colors.append(colorItem.cgColor)
        }
        let gradientLocations: [NSNumber] = [0.0, 1.0]
        let gradientLayer = CAGradientLayer()
        gradientLayer.startPoint = CGPoint(x: 0, y: 0)
        gradientLayer.endPoint = CGPoint(x: 1, y: 0)
        gradientLayer.colors = colors
        gradientLayer.locations = gradientLocations
        return gradientLayer
    }
    
    /// GCD定时器倒计时
    ///
    /// - Parameters:
    ///   - timeInterval: 间隔时间
    ///   - repeatCount: 重复次数
    ///   - handler: 循环事件,闭包参数: 1.timer 2.剩余执行次数
    @discardableResult
    class func dispatchTimer(timeInterval: Double, repeatCount: Int, handler: @escaping (DispatchSourceTimer?, Int) -> Void) -> DispatchSourceTimer {
        
        if repeatCount <= 0 {
            return DispatchSource.makeTimerSource(flags: [], queue: DispatchQueue.main)
        }
        let timer = DispatchSource.makeTimerSource(flags: [], queue: DispatchQueue.main)
        var count = repeatCount
        timer.schedule(deadline: .now(), repeating: timeInterval)
        timer.setEventHandler {
            count -= 1
            DispatchQueue.main.async {
                handler(timer, count)
            }
            if count == 0 {
                timer.cancel()
            }
        }
        timer.resume()
        return timer
    }
    
    class func milliStamp() -> String {
        let now: TimeInterval = Date().timeIntervalSince1970
        let millisecond = CLongLong(round(now*1000))
        return "\(millisecond)"
    }
   
    class func secondStamp() -> String {
        let now: TimeInterval = Date().timeIntervalSince1970
        let millisecond = CLongLong(round(now))
        return "\(millisecond)"
    }
    
    class func canSetSessionPreset(capturePreset: AVCaptureSession.Preset) -> Bool {

        let session: AVCaptureSession  = AVCaptureSession()
        var inputCamera: AVCaptureDevice?
        let devices = AVCaptureDevice.DiscoverySession.init(deviceTypes: [.builtInWideAngleCamera], mediaType: .video, position: .front).devices
        devices.forEach(where: {$0.position == .front}, body: {  inputCamera = $0 })
        
        guard let inputCamera = inputCamera else { return false }
        guard let input = try? AVCaptureDeviceInput(device: inputCamera) else { return false }
        
        if session.canAddInput(input) {
            session.addInput(input)
        }
        return session.canSetSessionPreset(capturePreset)
    }
    
    class func second2String(second: Int) -> String {
        let dur = second
        switch dur {
        case 0..<60:
            return String(format: "00:00:%02d", dur)
        case 60..<3600:
            let m = dur / 60
            let s = dur % 60
            return String(format: "00:%02d:%02d", m, s)
        case 3600...:
            let h = dur / 3600
            let m = (dur % 3600) / 60
            let s = dur % 60
            return String(format: "%02d:%02d:%02d", h, m, s)
        default:
            return "00:00:00"
        }
    }
    
    class func getMinValue(orgValue: Double) -> Double {
       
        let p0: Double = 0.0
        let p180 = Double.pi
        let p360 = 2 * Double.pi
        let p90 = 0.5 * Double.pi
        let p270 = 1.5 * Double.pi
        
        
        // 差值20度可以自动吸附 0.349
        let fix: Double = p360 * 20 / 360
            
        let values = [p0, p90, p180, p270, p360]
        
        for (_, a) in values.enumerated() {
            if orgValue >= 0 {
                let diff =  fabs(a - orgValue)
                if diff < fix {
                    return a
                }
            } else {
                let diff =  fabs(a + orgValue)
                if diff < fix {
                    return -a
                }
            }
            
        }
        
        return orgValue
    }
    
}

extension LCTools {
    static var isMemberDownloading = false
    typealias videoFilePath = String
    @discardableResult
    static func checkFileExit(fileUrl: URL) -> videoFilePath? {
        guard let md5Name = (fileUrl.absoluteString as NSString).md5() else { return nil }
        let folderPath = LCTools.getMaterialSourceDownloadFolder()
        let extesion = fileUrl.absoluteString.pathExtension
        let finalPath = folderPath + "/" + md5Name + "." + extesion
        //已经存在，直接返回结果
        if FileManager.default.fileExists(atPath: finalPath) {
            return finalPath
        }
        startLoad(fileUrl: fileUrl)
        return nil
    }
    
    static func startLoad(fileUrl: URL) {
        if isMemberDownloading { return }
        isMemberDownloading = true
        GCDServices.asyncDelay(15) {
            LCTools.downloadMovie(url: fileUrl,
                                 name: fileUrl.absoluteString) { status in
                isMemberDownloading = false
                switch status {
                case .success(let filePath): print("会员中心视频缓存成功：\(filePath)")
                case .failure: print("会员中心视频缓存失败")
                default: break
                }
            }
        }
    }
}


extension LCTools {
  
    
    static func checkNickConten(text: String, color: UIColor) -> NSMutableAttributedString {
           let words = ["[用户]",
                        "【用户】",
                        "【用户1】",
                        "【用户2】",
                        "【用户3】",
                        "【用户4】",
                        "【用户5】",
                        "【用户】",
                        "【用户A】",
                        "【用户B】",
                        "【用户C】",
                        "【用户D】",
                        "【用户E】",
                        "【礼物名称】"]

           var rangeArr: [NSRange] = []
           for key in words {
              let arr = LCTools.substringRange(str: text, ofStr: key)
               rangeArr.append(contentsOf: arr)
           }

           let attString = NSMutableAttributedString(string: text,
                                                     attributes:
                                                        [NSAttributedString.Key.font: LCDevice.DIN_Font_PF_R(14),
                                                         NSAttributedString.Key.foregroundColor: color])
           //
           for range in rangeArr {
               attString.setAttributes([NSAttributedString.Key.font: LCDevice.DIN_Font_PF_R(14),
                                        NSAttributedString.Key.foregroundColor: UIColor("#6974F2")], range: range)
           }

         return attString
           
       }
    
    static func substringRange(str: String, ofStr: String) -> [NSRange] {
           var rangeArr: [NSRange] = []
           var count = 0
           var tmpStr = "\(str)"
           while tmpStr.contains(ofStr) {
               if let range = tmpStr.range(of: ofStr) {
                   var range1 = NSRange(range, in: tmpStr)
                   range1 = NSRange(location: range1.location + ofStr.count * count, length: range1.length)
                   tmpStr = tmpStr.replacingCharacters(in: range, with: "")
                   count = count + 1
                   rangeArr.append(range1)
               }
           }
           return rangeArr
       }
}

extension LCTools {
    /// 将时间戳转成分钟之前
    static func timeAgoFromMilliseconds(_ milliseconds: Int64) -> String {
        let currentTime = Date().timeIntervalSince1970
        let timeDifference = currentTime - Double(milliseconds) / 1000.0
        let minutesAgo = Int(timeDifference / 60)

        if minutesAgo < 1 {
            return ""
        } else {
            return "\(minutesAgo)分钟"
        }
    }
    
    static func formatTimestampToHourMinute(_ timestampMs: Int64) -> String {
        // 将毫秒时间戳转换为秒
        let timestampSeconds = TimeInterval(timestampMs) / 1000
        
        // 创建Date实例
        let date = Date(timeIntervalSince1970: timestampSeconds)
        
        // 使用DateFormatter格式化时间
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm"
        
        // DateFormatter默认使用设备的当前时区
        
        // 将日期转换为hh:mm格式的字符串
        let timeString = formatter.string(from: date)
        
        return timeString
    }

    /// 将冒号前面的字符变色
    static func slipMaoHaoToAttribute(string: String, color: UIColor = UIColor("#9BEAFF")) -> NSAttributedString {
        let attributedString = NSMutableAttributedString(string: string)
        if let range = string.rangeOfCharacter(from: CharacterSet(charactersIn: "：:")) {
            let nsRange = NSRange(range, in: string)
            attributedString.addAttribute(.foregroundColor, value: color, range: NSRange(location: 0, length: nsRange.location))
        }
        return attributedString
    }
    
    /// 将第一个空格前面的字符变色
    static func slipKonggeToAttribute(string: String, color: UIColor = UIColor("#9BEAFF")) -> NSAttributedString {
        let attributedString = NSMutableAttributedString(string: string)
        if let range = string.rangeOfCharacter(from: CharacterSet(charactersIn: " ")) {
            let nsRange = NSRange(range, in: string)
            attributedString.addAttribute(.foregroundColor, value: color, range: NSRange(location: 0, length: nsRange.location))
        }
        return attributedString
    }
    
    /// 将第一个空格前面的字符变色 后面的变为黄色
    static func slipKonggeAndBackYellowToAttribute(string: String, color: UIColor = UIColor("#9BEAFF")) -> NSAttributedString {
        let attributedString = NSMutableAttributedString(string: string, attributes: [.foregroundColor: UIColor("#F7B500")])
        if let range = string.rangeOfCharacter(from: CharacterSet(charactersIn: " ")) {
            let nsRange = NSRange(range, in: string)
            attributedString.addAttribute(.foregroundColor, value: color, range: NSRange(location: 0, length: nsRange.location))
        }
        return attributedString
    }
    
    /// 将第一个空格前面的字符变色 后面的变为黄色
    static func slipXiaDanAndBackYellowToAttribute(string: String, color: UIColor = UIColor("#9BEAFF")) -> NSAttributedString {
        let attributedString = NSMutableAttributedString(string: string, attributes: [.foregroundColor: UIColor("#F7B500")])
        if let range = string.rangeOfCharacter(from: CharacterSet(charactersIn: "已拍")) {
            let nsRange = NSRange(range, in: string)
            attributedString.addAttribute(.foregroundColor, value: color, range: NSRange(location: 0, length: nsRange.location))
        }
        if let range = string.rangeOfCharacter(from: CharacterSet(charactersIn: "下单")) {
            let nsRange = NSRange(range, in: string)
            attributedString.addAttribute(.foregroundColor, value: color, range: NSRange(location: 0, length: nsRange.location))
        }
        return attributedString
    }
    
    /// 是否在N分钟内
    static func isTimestampWithinMinutes(minutes: Int, timestamp: Int64) -> Bool {
        let currentTime = Int64(Date().timeIntervalSince1970 * 1000) // 当前时间戳（毫秒）
        let difference = currentTime - timestamp // 差值
        
        let result = difference <= minutes * 60 * 1000
        if difference < 0 {
            print("difference minutes: \(difference)")
        }
        return result
    }
    
    static func attributedContent(words:[String], text: String, color: UIColor) -> NSMutableAttributedString {

           var rangeArr: [NSRange] = []
           for key in words {
              let arr = LCTools.substringRange(str: text, ofStr: key)
               rangeArr.append(contentsOf: arr)
           }

           let attString = NSMutableAttributedString(string: text,
                                                     attributes:
                                                        [NSAttributedString.Key.font: LCDevice.DIN_Font_PF_R(14),
                                                         NSAttributedString.Key.foregroundColor: color])
           //
           for range in rangeArr {
               attString.setAttributes([NSAttributedString.Key.font: LCDevice.DIN_Font_PF_R(14),
                                        NSAttributedString.Key.foregroundColor: UIColor("#6974F2")], range: range)
           }

         return attString
           
       }

}



extension Int64 {
    func getFormatPlayTime() -> String {
        let min = Int(self / 60)
        let second = self % 60
        if min > 0 {
            return "\(min)分\(second)秒"
        }
        return "\(second)秒"
    }
    
    func getMinTime() -> String {
        let min = Int(self / 60)
        let second = self % 60
        if min > 0 {
            return "\(min)分钟"
        }
        return "\(second)秒"
    }
}


// MARK: - 智能互动的处理
extension LCTools {
    
    // 把文件拷贝到沙盒智能互动的文件
   static func documentSmartPicker(url: URL) {
        // 把收到的全部数据清空
        LCTools.removeReceiveBackupFolderAll()
        
        let canAccessingResource = url.startAccessingSecurityScopedResource()
        if !canAccessingResource {
            return
        }
        let coordinator = NSFileCoordinator()
        
        var error: NSError?
        var path: String?
        
        coordinator.coordinate(readingItemAt: url, error: &error) { newurl in
            
            let fileName = url.lastPathComponent
            
            let filePath = LCTools.getBackupAllReceiveRoomsFolderFullpath(fileName)
            
//            fileData?.write(toFile: filePath, atomically: true)
            do {
                try FileManager.default.copyItem(at: newurl, to: URL(fileURLWithPath: filePath))
            } catch {
                LCLog.d(error.localizedDescription)
            }
 
            path = filePath
        }
       
       LCLog.d("：error: \(error?.localizedDescription)")
        
        url.stopAccessingSecurityScopedResource()
        
        if error == nil, let path = path {
            LCSingleton.shared.showZipProgressView(path: path, type: .hudong)
        }
    }
    
    // APP收到的ZIP进行解压缩 这是解压第一层
    // path  是全路径
    // needVerify 是否需要校验 code, 如果不需要校验 空就可以
    static func unzipReceiveSmartConfig(path: String, type: ZIP_Type, progress: @escaping ((CGFloat) -> Void), completion: @escaping ( _ dismiss: Bool, _ interactions: AIBackupModel?) -> Void ) {
        
        let zipPath = path
        unzipAndCopyToBackupFolder(zipPath: zipPath, password: type.zipKey) { value in
            LCLog.d("解压config进度：\(value)")
            progress(value * 0.5)
        } resultHandle: { result in
            LCLog.d("解压config结果：\(result)")
            if !result  {
                completion( false, nil)
                return
            }
            // 解压成功会有一个config.json 文件和一个room.zip 文件 RoomBackUpModel
            let path = getBackupAllReceiveRoomsFolder()
            
            guard let configdata = try? Data(contentsOf: NSURL(fileURLWithPath: path + "/config.json") as URL) else {
                completion( false, nil)
                HUD.showFail("请选择正确的智能互动文件")
                return
            }
          
            let configstr = String(data: configdata, encoding: .utf8) ?? ""
            guard let config: RoomBackUpModel = JsonTool.string2Model(configstr) else {
                completion(false, nil)
                HUD.showFail("请选择正确的智能互动文件")
                return
                
            }
            
            // 判断最小的用户权限
            guard let user = UserInfo.currentUser(), let userid = user.userId, let leve = user.vipLevelId?.rawValue else {
                completion(false, nil)
                HUD.showFail("请先登录")
                return
            }
            
            
            var passwordKey = ("\(config.title)_\(config.roomId)_\(config.time)_\(type.appKey)" as NSString).md5()?.uppercased() ?? ""
            passwordKey = passwordKey.sha1().uppercased()
            unzipReceiveInteractionModel(path: path, password: passwordKey, canExport: "\(userid)" == config.userId) { value in
                progress(value)
            } completion: { ret, room in
                completion(ret, room)
            }
        }
    }
    
    
    // 解压 room.zip
    static func unzipReceiveInteractionModel(path: String, password: String, canExport: Bool, progress: @escaping ((CGFloat) -> Void), completion: @escaping ((Bool, AIBackupModel?) -> Void)) {
        
        unzipAndCopyToBackupFolder(zipPath: path + "/room.zip", password: password) { value in
            LCLog.d("解压room.zip进度：\(value)")
            progress(value * 0.5 + 0.5)
        } resultHandle: { result in
            LCLog.d("解压room.zip结果：\(result)")
            if !result  {
                completion(false, nil)
                return
            }
            let room = saveZIPInteractionModel2DataBase(path: path, canExport: canExport)
            if let room = room {
                completion(true, room)
            } else {
                DispatchQueue.main.async {
                    HUD.showFail("请选择正确的直播间文件")
                }
                completion(false, nil)
            }
            
        }
        
    }
    
    // 保存解压之后的文件
    static func saveZIPInteractionModel2DataBase(path: String, canExport: Bool) -> AIBackupModel? {

        // 解压成功会有一个config.json 文件和一个room.zip 解压之后变成 room wenj 文件 RoomBackUpModel
        let path = getBackupAllReceiveRoomsFolder() + "/room"
        
        guard let roomdata = try? Data(contentsOf: NSURL(fileURLWithPath: path + "/room.json") as URL) else { return nil }
        
        let configstr = String(data: roomdata, encoding: .utf8) ?? ""
        
        guard let models: AIBackupModel = JsonTool.string2Model(configstr)  else { return nil }
        
        var audioNames: [String] = []
        
        // 兼容安卓
        models.dataSource.cardList.forEach { item in
            if let replyItem = item.replyItem, let resources = replyItem.resources {
                resources.forEach { res in
                    // mp3
                    if let p = res.path, p.contains("快瓴中控台/"){
                        res.path = p.lastPathComponent
                    }
                    
                    // 视频和特效
                    if let p = res.path, p.contains("快瓴中控台") {
                        res.path = p.lastPathComponent
                        res.title = p.lastPathComponent
                    }
                    
                    if let p = res.cropImg, (p.contains("快瓴中控台/") || p.contains("快瓴中控台") ){
                        res.cropImg = nil
                    }
                }
            }
        }
        
        models.dataSource.cardList.forEach { item in
            if let replyItem = item.replyItem, replyItem.itemType == .musicLocal, let resources = replyItem.resources, !resources.isEmpty {
                resources.forEach({audioNames.append($0.path ?? "")})
            }
        }
        
        
        // 当前目录下有个res 文件 这个文件是存放的图片和视频、音频
        if let allPaths = FileManager.default.subpaths(atPath: path + "/res") {
            let tarPath = LCTools.getMaterialSourceDownloadFolder()
            
            let audioPath = LCTools.getAudioSourceFolder()
            
            allPaths.forEach { p in
                // 如果是音频文件， 需要更换位置
                if audioNames.contains(where: {$0 == p}) {
                    let staus = XCFileManager.moveFile(path + "/res/" + p, toPath: audioPath + "/", toPathIsDir: false)
                    LCLog.d("文件拷贝：音频 \(staus) 名称：\(p)")
                } else {
                    let staus = XCFileManager.moveFile(path + "/res/" + p, toPath: tarPath + "/", toPathIsDir: false)
                    LCLog.d("文件拷贝：\(staus) 名称：\(p)")
                }
                
            }
        }
        

        return models
        
    }
    
    class func getAudioSourceFolder() -> String {
        let documentsPath = NSSearchPathForDirectoriesInDomains(FileManager.SearchPathDirectory.documentDirectory, FileManager.SearchPathDomainMask.userDomainMask, true).first
        let rootFolder = documentsPath! + "/" + k_SmartAudioRecord_floder
        if !FileManager.default.fileExists(atPath: rootFolder) {
            do {
                try FileManager.default.createDirectory(atPath: rootFolder, withIntermediateDirectories: true, attributes: nil)
            } catch {
                LCLog.d("something error when create root doct path")
            }
        }
        return rootFolder
    }
    
}

