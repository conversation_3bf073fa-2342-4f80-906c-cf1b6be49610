//
//  LCLog.swift
//  livePlus
//
//  Created by iclick on 2020/11/28.
//  Copyright © 2020年 www.iclick.com. All rights reserved.
//

import UIKit

class LCLog{
    
//    #ifdef DEBUG
//    #   define ULog(fmt, ...)  { UIAlertView *alert = [[UIAlertView alloc] initWithTitle:[NSString stringWithFormat:@"%s\n [Line %d] ", __PRETTY_FUNCTION__, __LINE__] message:[NSString stringWithFormat:fmt, ##__VA_ARGS__]  delegate:nil cancelButtonTitle:@"Ok" otherButtonTitles:nil]; [alert show]; }
//    #else
//    #   define ULog(...)
//    #endif
//    class func ULog(_ messsage : String) {
//
//        let funcName : String = #function
//        let lineNum : Int = #line
//        let alert = UIAlertView.init(title: "\(funcName):\(funcName)[\(lineNum)]-\(messsage)", message: messsage, delegate: nil, cancelButtonTitle: "OK")
////        let alert = UIAlertController.init(title: "\(funcName):\(funcName)[\(lineNum)]-\(messsage)", message: messsage, preferredStyle: .alert)
//        alert.show()
//    }
    
    class func info(_ messsage : String, file : String = #file, funcName : String = #function, lineNum : Int = #line) {
        let fileName = (file as NSString).lastPathComponent
        NSLog("\(fileName):\(funcName)[\(lineNum)]-\(messsage)")
    }
    
    class func d(_ messsage : String, file : String = #file, funcName : String = #function, lineNum : Int = #line) {
        #if DEBUG
            let fileName = (file as NSString).lastPathComponent
            NSLog("\(fileName):\(funcName)[\(lineNum)]-\(messsage)")
            
        #endif
    }
    
    class func simplePrint(_ messsage : String, file : String = #file, funcName : String = #function, lineNum : Int = #line) {
        #if DEBUG
        print(messsage)
        #endif
    }

    
    class func dRect(_ rec : CGRect, file : String = #file, funcName : String = #function, lineNum : Int = #line) {
        #if DEBUG
            
            let fileName = (file as NSString).lastPathComponent
            NSLog("\(fileName):\(funcName)[\(lineNum)] rect:x=\(rec.origin.x),y=\(rec.origin.y),width=\(rec.size.width),height=\(rec.size.height)")
            
        #endif
    }
    
    class func dSize(_ size : CGSize, file : String = #file, funcName : String = #function, lineNum : Int = #line) {
        #if DEBUG
            
            let fileName = (file as NSString).lastPathComponent
            NSLog("\(fileName):\(funcName)[\(lineNum)] size:width=\(size.width),height=\(size.height)")
            
        #endif
    }
    
    class func dPoint(_ point : CGPoint, file : String = #file, funcName : String = #function, lineNum : Int = #line) {
        #if DEBUG
            
            let fileName = (file as NSString).lastPathComponent
            NSLog("\(fileName):\(funcName)[\(lineNum)] point:x=\(point.x),y=\(point.y)")
            
        #endif
    }
}

