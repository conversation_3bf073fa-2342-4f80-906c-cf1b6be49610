//
//  LCKey.swift
//  livePlus
//
//  Created by iclick on 2020/11/28.
//  Copyright © 2020年 www.iclick.com. All rights reserved.

///  存放固定的字符串，比如通知的key

import UIKit

class LCKey {
    
    static let App_universalLink = "https://ckzsapi.quickleading.com/"  //APISession.baseURLOnline + "app/"
   
    // MARK: - 第三方账号相关

    static let UM_AppKey = "682ec25bbc47b67d836e3718"

    static let Wechat_AppID = "wxa509873c882b3449"
            
    static let WXSDK_AppId = "nmM8hyKBTL659672a91b08f271809422"
    
    static let WXSDK_SecretKey = "KWDOU-P22HU-ZYLF2-3BV8L"
    
    /// 小程序路径
    static let WX_APPLET_PATH_RELEASE = "pages/index/index?tab=3"
    /// 小程序原始ID
    static let WX_APPLET_GH_ID = "gh_780cd7eab5ec"
    
    // 小程序的直播课
    static let WX_APPLET_Livestream_ID = "wx22fd5e2562263d9f"
    static let WX_APPLET_Livestream_PATH = "pages/zhibo/index"
    // 视频教程
    static let WX_APPLET_Record_PATH = "pages/index/index"
    
    // 微信客服
    static let WX_Customer_URL = "https://work.weixin.qq.com/kfid/kfc350698f15b8302c3"
    static let WX_Customer_Corpid = "wwd50064eec9beb985"
    static let WX_Customer_Path = "pages/service-chat/index"
    
    // Vip权益H5版本号
    static let Config_H5_Vip_Equity = "config_h5_vip_equity"
    
    
    // MARK: - ------ 其他字段定义
    /// 验证支付凭证请求次数
    static let ValidationPayCerCount: Int = 30
    
    /// 允许选择的视频最大时长 （10小时）
    static let MaxSelectVideoDuration: Int = 10 * 60 * 60
    
    
    // MARK: - ----- UserDefaults的key

    /// 当前是否线上环境
    static let UD_ReleaseEnv = "UD_ReleaseEnv"
    
    /// 首页协议弹窗已同意
    static let UD_PolicyAlertDone = "UD_PolicyAlertDone"

    /// 判断当前微信调起是绑定还是登录
    static let UD_wxForLogin = "UD_wxForLogin"
    static let UD_wxForBind = "UD_wxForBind"
    
    static let UD_CPU = "UD_CPU"
    

    // 保存的MOV 到MP4
    static let UD_MOV2MP4 = "UD_MOV2MP4"
                   
    
    // MARK: - ----- 通知的key (此处修改后务必同步到LivePlusPrefixHeader_pch.pch)
    
    ///通知 立刻录播
    static let noti_learnCenter = NSNotification.Name("noti_learnCenter")

    
    ///通知 我的直播间
    static let noti_gotoRoomBtnAction = NSNotification.Name("noti_gotoRoomBtnAction")

    static let noti_unreadMessage = NSNotification.Name("noti_unreadMessage")

    ///通知 退出登录
    static let noti_logout = NSNotification.Name("noti_logout")
    
    /// 通知 购买会员成功
    static let noti_PaiedMember = NSNotification.Name("noti_PaiedMember")
    
    /// 通知 在直播间去购买会员
    static let noti_gotoMember = NSNotification.Name("noti_gotoMember")
    
    
    ///通知 重新登陆了
    static let noti_reLogin = NSNotification.Name("noti_reLogin")
    
    
    ///通知 收到微信平台回调
    static let noti_didGetWeiXinResponse = NSNotification.Name("noti_didGetWeiXinResponse")
    ///通知 收到微信绑定回调
    static let noti_wechatResponse = NSNotification.Name("noti_wechatResponse")

    ///扫码
    static let noti_backupCompleteAction =   NSNotification.Name("noti_backupCompleteAction")
    
    static let OpenBackupRoomKey =   "OpenBackupRoomKey"
    
    
    // MARK: - 直播间新手引导
    /// base层新手引导
    static let UD_LiveVCNewUserAlertDoneLive2_base = "UD_LiveVCNewUserAlertDoneLive2_base_37"
    /// base层新手引导
    static let UD_LiveVCNewUserAlertDoneLive2_camera = "UD_LiveVCNewUserAlertDoneLive2_camera_37"
    /// base层新手引导
    static let UD_LiveVCNewUserAlertDoneLive2_nomarl = "UD_LiveVCNewUserAlertDoneLive2_nomarl_37"
    /// base层新手引导
    static let UD_LiveVCNewUserAlertDoneLive2_create = "UD_LiveVCNewUserAlertDoneLive2_create_37"
    ///  引导编辑内容区域
    static let UD_LiveVCNewUserAlertDoneLive2_editor = "UD_LiveVCNewUserAlertDoneLive2_editor_37"
    ///  引导手势
    static let UD_LiveVCNewUserAlertDoneLive2_gesture = "UD_LiveVCNewUserAlertDoneLive2_gesture_more_37"
    ///  新手帮助
    static let UD_LiveVCNewUserAlertDoneLive2_help = "UD_LiveVCNewUserAlertDoneLive2_gesture_help_37"
    /// 弹窗
    static let UD_LiveVCNewUserAlertDoneLive2_pop = "UD_LiveVCNewUserAlertDoneLive2_gesture_pop_37"
    
    static let noti_NetworkNotReachable = NSNotification.Name("noti_NetworkNotReachable")
    
    static let noti_NetworkReachable = NSNotification.Name("noti_NetworkReachable")
    
        
    // 保存用户选的预制音色
    static let UD_AudioSpkIdCacheKey = "UD_AudioSpkIdCache"
    
    // TTS记录
    static let UD_TTS_RecordsKey =  "UD_TTS_Records"
    
    // 弹窗 保存1 或者没保存 都是勾选状态
    static let UD_Realtime_Rlue = "UD_Realtime_Rlue"
    
    static let UD_Realtime_Open = "UD_Realtime_Open"
    
    // 克隆消耗积分
    static let UD_Point_Alert_Clone = "UD_Point_Alert_Clone"
    
    // TTS消耗积分
    static let UD_Point_Alert_TTS = "UD_Point_Alert_TTS"
    
}

