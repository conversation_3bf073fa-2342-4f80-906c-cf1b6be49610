//
//  SColor.swift
//  livePlus
//
//  Created by iclick on 2020/11/28.
//  Copyright © 2020年 www.iclick.com. All rights reserved.
//


import UIColor_Hex_Swift

class SColor{

    /// 主色 #539BF4  83/155/244
    static let main = UIColor("#539BF4")
    
    /// 标题色 #3C424C  60/66/76
    static let baseText = UIColor("#3C424C")

    /// 标签、正文色 #868A93  134/138/147
    static let baseTag = UIColor("#868A93")

    /// 未选中文字色 #A9A8AB 169/168/171
    static let baseUnText = UIColor("#A9A8AB")

    /// 提示文字灰色 #d0d1da  208/209/218
    static let baseHintGray = UIColor("#d0d1da")

    /// 提示文字红色 #ce3737  206/55/55
    static let baseHintRed = UIColor("#ce3737")

    /// 分割线色 #eeeeee   238/238/238
    static let baseLineGray = UIColor("#eeeeee")


    /// RGB 颜色
    static func RGBA(r:CGFloat,g:CGFloat,b:CGFloat,a:CGFloat = 1.0) -> UIColor{
        return UIColor.init(red: r/255.0, green: g/255.0, blue: b/255.0, alpha: a)
    }


    /// "0xbdc0b6" 颜色
    static func hexColor(hex: String) -> UIColor{
        let scanner = Scanner.init(string: hex)
        var hexNum : UInt32 = 0
        scanner.scanHexInt32(&(hexNum))
        return SColor.UIColorFromRGB(hex: hexNum)
    }

    /// 0xbdc0b6 颜色
    static func UIColorFromRGB(hex: UInt32) -> UIColor{
        let divisor = CGFloat(255)
        let red     = CGFloat((hex & 0xFF0000) >> 16) / divisor
        let green   = CGFloat((hex & 0x00FF00) >>  8) / divisor
        let blue    = CGFloat( hex & 0x0000FF       ) / divisor
        return UIColor.init(red: red, green: green, blue: blue, alpha: 1.0)
    }

    /// 取得一个随机色
    static func random() -> UIColor {
        let array = ["#5D702F","#3C9150","#93B29A","#A7C8B8","#ABDCCB","#CCE1C0","#DCEDE3","#985C26","#B98040","#D19865","#C6A874","#898360","#C7BCB2","#212121","#243246","#5A524A","#6A6968","#788382","#5C718E","#97A8C8","#79BBD8","#BDDEE7","#C8DEE7","#D4EDF2","#E6BF22","#EFD72F","#F2ED72","#F14332","#E15D63","#F6BCC7","#E9C2DC","#CFCEDA","#DCCBD0","#E7E0DC","#EDEEE8","#F5F1DB","#FFF5E9"]
        let index : Int = Int(arc4random()%UInt32(array.count))
        let randomStr = array[index]
        return UIColor("\(randomStr)")
    }

}


extension UIColor {

    ///修改当前颜色的alpha值
    func alpha(value:CGFloat) -> UIColor {

        return UIColor.init(red: self.red, green: self.green, blue: self.blue, alpha: value)
    }

}
