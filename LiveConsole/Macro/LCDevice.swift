//
//  LCDevice.swift
//  livePlus
//
//  Created by iclick on 2020/11/28.
//  Copyright © 2020年 www.iclick.com. All rights reserved.
//


import UIKit
import Photos
import CFNetwork
import CoreTelephony

class LCDevice {
    
    /// App Store评分页
    static var AppstoreStarURL = "itms-apps://itunes.apple.com/WebObjects/MZStore.woa/wa/viewContentsUserReviews?type=Purple+Software&id=" + AppstoreId
    
    /// App Store主页
    static let AppStoreUrl = "itms-apps://itunes.apple.com/app/" + AppstoreId
    
    static var AppstoreId = "6747028811"
    
    static var KBundleShortVersionString = Bundle.main.infoDictionary!["CFBundleShortVersionString"] as! String
    
    static var KFBundleVersion = Bundle.main.infoDictionary!["CFBundleVersion"] as! String
    static var KFBundleShortVersionString = Bundle.main.infoDictionary!["CFBundleShortVersionString"] as! String
    static var KFBundleAppName = Bundle.main.infoDictionary!["CFBundleDisplayName"] as! String
    static var KFBundleId = Bundle.main.infoDictionary!["CFBundleIdentifier"] as! String
    
    static var IOS_VERSION: String   = UIDevice.current.systemVersion
    
    static var screenW              = UIScreen.main.bounds.width
    static var screenH              = UIScreen.main.bounds.height
    ///TableBar 高度
    static var BOTTOM_BAR           = CGFloat(49.0) + X_BOTTOM_INSET

    static var IPHONE_3_5_INCH_WIDTH    =   CGFloat(320.0)
    static var IPHONE_4_0_INCH_WIDTH    =   CGFloat(320.0)
    static var IPHONE_4_7_INCH_WIDTH    =   CGFloat(375.0)
    static var IPHONE_5_5_INCH_WIDTH    =   CGFloat(414.0)
    
    static var IPHONE_3_5_INCH_HEIGHT   =   CGFloat(480.0)
    static var IPHONE_4_0_INCH_HEIGHT   =   CGFloat(568.0)
    static var IPHONE_4_7_INCH_HEIGHT   =   CGFloat(667.0)
    static var IPHONE_5_5_INCH_HEIGHT   =   CGFloat(736.0)
    static var IPHONE_X_INCH_HEIGHT     =   CGFloat(812.0)
        
    static var isIphone5OrSmall = screenH <= IPHONE_4_0_INCH_HEIGHT
    static var isIphone6Or7 = screenH <= IPHONE_4_7_INCH_HEIGHT && screenH > IPHONE_4_0_INCH_HEIGHT
    static var isIphonePlus = screenH <= IPHONE_5_5_INCH_HEIGHT && screenH > IPHONE_4_7_INCH_HEIGHT
    static var isIphoneX = screenH >= IPHONE_X_INCH_HEIGHT

    static var STATUS_BAR_HEIGHT = CGFloat(20.0)
    static var Nav_H = isIphoneX ? CGFloat(88.0):CGFloat(64.0)
    static var X_HAIR = isIphoneX ? CGFloat(24.0):CGFloat(0)
    static var X_BOTTOM_INSET = isIphoneX ? CGFloat(34.0):CGFloat(0.0)
    
    /* One Pixel*/
    static var DIN_ONE_PIXEL_THICKNESS       =   1.0 / UIScreen.main.scale
    static var DIN_ONE_PIXEL_ADJUST_OFFSET   =   DIN_ONE_PIXEL_THICKNESS / 2.0
    
    /// 细线的高度 1像素
    static var THIN_LINE_HEIGHT =  DIN_ONE_PIXEL_THICKNESS
    
    ///以375*812作为基准的宽度
    class func DIN_WIDTH(_ len: CGFloat) -> CGFloat {
        return len
    }

    ///以375*812作为基准的高度
    class func DIN_HEIGHT(_ len: CGFloat) -> CGFloat {
        return len
    }

    static var contrast_H = 240.0
    
    static var contrast_W = 135.0
    
    /// 获取设备唯一号 /* uuid + keychain */
    static let udid = KeychainManager.shared.udid()
    
    /// 是否有相册权限
    static func isCanVisitPhotoLibrary(resultBlock: @escaping ((_ isOk: Bool) -> Void)) {
        let status = PHPhotoLibrary.authorizationStatus()
        if status == .authorized {
            resultBlock(true)
            return
        }
        if status == .restricted || status == .denied {
            resultBlock(false)
            return
        }
        if status == .notDetermined {
            PHPhotoLibrary.requestAuthorization { (_ status) in
                DispatchQueue.main.async {
                    if status == .authorized {
                        LPStatistics.logEvent(.PREMISSION_REQUEST(.albumPremiss), eventAction: .zero)
                        resultBlock(true)
                    }
                    if status == .restricted || status == .denied {
                        LPStatistics.logEvent(.PREMISSION_REQUEST(.albumPremiss), eventAction: .one)
                        resultBlock(false)
                    }
                }
            }
        }
    }
    
}

// MARK: -  font
extension LCDevice {

    ///系统平方体 - 半粗体 Semibold
    class func DIN_Font_PF_S(_ size: CGFloat) -> UIFont {
        return UIFont(name: "PingFangSC-Semibold", size: CGFloat(DIN_WIDTH(size)))!
    }
    
    ///系统平方体 - 中粗体 Medium
    class func DIN_Font_PF_M(_ size: CGFloat) -> UIFont {
        return UIFont(name: "PingFangSC-Medium", size: CGFloat(DIN_WIDTH(size)))!
    }
    
    ///系统平方体 - Regular
    class func DIN_Font_PF_R(_ size: CGFloat) -> UIFont {
        return UIFont(name: "PingFangSC-Regular", size: CGFloat(DIN_WIDTH(size)))!
    }
    
    ///系统平方体
    class func DIN_font(_ size: CGFloat) -> UIFont {
        return UIFont.systemFont(ofSize: CGFloat(DIN_WIDTH(size)))
    }
    
    ///系统 bold
    class func DIN_Font_Bold(_ size: CGFloat) -> UIFont {
        return UIFont.boldSystemFont(ofSize: CGFloat(DIN_WIDTH(size)))
    }
    
    
    class func DIN_FONT_SIZE_CONVERSION(_ size: CGFloat) -> CGFloat {
        return (screenW / IPHONE_5_5_INCH_WIDTH) * (size)
    }
    
    ///打印所有能用的字体
    class func logAllFont() {
        let allNames = UIFont.familyNames
        for subItem in allNames {
            let fontNames = UIFont.fontNames(forFamilyName: subItem)
            for aName in fontNames {
                LCLog.d("font:---> \(aName.utf8)")
            }
        }
    }

}

// MARK: - 网络相关

extension LCDevice {
    
    //检查当前网络状态
    class func checkNetIsOk() -> Bool {
        if NetConnectionManager.shared.currentNetStatus == .notReachable {
            DispatchQueue.main.async {
                if LCDevice.checkNetRestrictedState() {
                    HUD.showInfo("当前网络连接异常，请检查后重试")
                }
            }
            return false
        }
        return true
    }
    
    //检查当前网络状态
    class func checkNetRestrictedState() -> Bool {
        // 如果首页的弹窗都没同意 就不要在提示网络错误了
       let ret = UserDefaults.standard.bool(forKey: LCKey.UD_PolicyAlertDone)
        if !ret {
            return false
        }
        // 如果网络弹窗同意了，此时用户没授权网络 也不要提示了
        let cellularData = CTCellularData()
        return cellularData.restrictedState == CTCellularDataRestrictedState.notRestricted
    }
    /**
     代理信息
     - parameter    url:    检测地址
     */
    static func ProxyInfo(_ url: URL) -> Dictionary<CFString, Any>? {
        /**对象 -> 指针*/
        func bridge<T: AnyObject>(_ obj: T) -> UnsafeMutableRawPointer {
            return Unmanaged.passUnretained(obj).toOpaque()
        }
        /**指针 -> 对象 */
        func bridge<T: AnyObject>(raw: UnsafeRawPointer) -> T {
            return Unmanaged<T>.fromOpaque(raw).takeUnretainedValue()
        }
        
        if let proxySettingsRaw = CFNetworkCopySystemProxySettings()?.toOpaque() {
            let proxySettings: CFDictionary = bridge(raw: proxySettingsRaw)
            let proxiesRaw = CFNetworkCopyProxiesForURL(url as CFURL, proxySettings)
            let proxies: NSArray = bridge(raw: proxiesRaw.toOpaque())
            if let settings = proxies[0] as? Dictionary<CFString, Any> {
                return settings
            }
        }
        
        return nil
    }
    
    ///是否使用代理
    static func isSetupProxy(_ url: URL = URL(string: "https://www.baidu.com")!) -> Bool {
        if let settings = ProxyInfo(url) {
            if let host = settings[kCFProxyTypeKey] as? String, host != kCFProxyTypeNone as String {
                return true
            }
        }
        return false
    }
    
    /// 获取WI-FI IP地址
    static var WIFI_IP: String? {
        var address: String?
        var ifaddr: UnsafeMutablePointer<ifaddrs>? = nil
        guard getifaddrs(&ifaddr) == 0 else {
            return nil
        }
        guard let firstAddr = ifaddr else {
            return nil
        }
        for ifptr in sequence(first: firstAddr, next: { $0.pointee.ifa_next }) {
            let interface = ifptr.pointee
            // Check for IPV4 or IPV6 interface
            let addrFamily = interface.ifa_addr.pointee.sa_family
            if addrFamily == UInt8(AF_INET) || addrFamily == UInt8(AF_INET6) {
                // Check interface name
                let name = String(cString: interface.ifa_name)
                if name == "en0" {
                    // Convert interface address to a human readable string
                    var addr = interface.ifa_addr.pointee
                    var hostName = [CChar](repeating: 0, count: Int(NI_MAXHOST))
                    getnameinfo(&addr, socklen_t(interface.ifa_addr.pointee.sa_len), &hostName, socklen_t(hostName.count), nil, socklen_t(0), NI_NUMERICHOST)
                    address = String(cString: hostName)
                }
            }
        }
        freeifaddrs(ifaddr)
        return address
    }

}

class KeychainManager {
    
    static let shared = KeychainManager()
    
    /// 获取设备唯一号 /* uuid + keychain */
    public func udid() -> String {
        var result: String?
        let service = Bundle.main.bundleIdentifier!
        let itemData = retrieveKeyChain(forService: service)
        if itemData == nil {
            let uuid = UIDevice.current.identifierForVendor?.uuidString
            result = uuid
            let uuidData = uuid?.data(using: .utf8)
            createKeyChain(forService: service, forData: uuidData!)
        } else {
            result = String.init(data: itemData!, encoding: .utf8)
        }
        
        return result!
    }

    // MARK: - Public methods
    public func retrieveKeyChain(forService service: String) -> Data? {
        var attributes = self.prepareAttributes(forService: service)
        attributes[kSecReturnData as String] = kCFBooleanTrue
        attributes[kSecMatchLimit as String] = kSecMatchLimitOne
        var dataTypeRef: AnyObject?
        let status = SecItemCopyMatching(attributes as CFDictionary, &dataTypeRef)
        if status == errSecSuccess { // keychain exist
            return dataTypeRef as? Data
        } else {
            return nil
        }
    }
    
    @discardableResult func createKeyChain(forService service: String, forData data: Data) -> Bool {
        var attributes = self.prepareAttributes(forService: service)
        if SecItemCopyMatching(attributes as CFDictionary, nil) == noErr { // keychain exist
            SecItemDelete(attributes as CFDictionary)
            return self.createKeyChain(forService: service, forData: data)
        } else {
            attributes[kSecValueData as String] = data
            return SecItemAdd(attributes as CFDictionary, nil) == noErr
        }
    }
    
    @discardableResult func updateKeyChain(forService service: String, toNewData data: Data) -> Bool {
        let attributes = self.prepareAttributes(forService: service)
        if SecItemCopyMatching(attributes as CFDictionary, nil) == noErr { // keychain exist
            let newAttributes = [kSecValueData: data]
            return SecItemUpdate(attributes as CFDictionary, newAttributes as CFDictionary) == errSecSuccess
        } else {
            return false
        }
    }
    
    @discardableResult func deleteKeyChain(forService service: String) -> Bool {
        let query = self.prepareAttributes(forService: service)
        if SecItemCopyMatching(query as CFDictionary, nil) == errSecSuccess { // keychain exist
            return SecItemDelete(query as CFDictionary) == errSecSuccess
        } else {
            return false
        }
    }
    
    // MARK: - private methods
    private func prepareAttributes(forService service: String) -> Dictionary<String, Any> {
        var attributes = Dictionary<String, Any>()
        attributes[kSecClass as String] = kSecClassGenericPassword
        attributes[kSecAttrAccessible as String] = kSecAttrAccessibleWhenUnlocked
        attributes[kSecAttrService as String] = service.data(using: .utf8)!
        return attributes
    }
    
}

extension LCDevice {
    static func deviceIsIPad() -> Bool {
        return UI_USER_INTERFACE_IDIOM() == .pad
    }
    
    static var isIOS16: Bool {
        if #available(iOS 16.0, *) {
            return true
        }
        return false
    }
}

