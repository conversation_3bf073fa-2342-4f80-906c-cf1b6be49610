//
//  LPPayTool.swift
//  LivePlus
//
//  Created by Brian-<PERSON> on 2021/11/9.
//

import UIKit
import SwiftyStoreKit
import StoreKit


enum InPurchasingStatus {
    case success(UInt)
    case failed(String)
}

public enum InPurchasingError: Error {
    /// 苹果支付失败
    case getAppPayFail
    /// 获取凭证失败
    case getAppStoreReceiptURLFail
    /// 获取用户信息失败
    case getUserInfoFail
    /// 服务器验证失败
    case serverValidationFail
    /// 服务端已经验证过
    case alreadlyValidation
    /// 非法凭证
    case getIllegalProof
}

class LPPayTool: NSObject {

    public static let shared = LPPayTool()
    
    var purModel: InPurchasingModel?
    
    override init() {
        super.init()
//        NotificationCenter.default.addObserver(self, selector: #selector(applicationDidEnterBackground), name: UIApplication.didEnterBackgroundNotification, object: nil)
    }
    
    // TODO: - 再要一遍内购凭证
    // app内购
    class func purchaseProduct(_ productId: String, quantity: Int = 1, atomically: Bool = false, applicationUsername: String = "", simulatesAskToBuyInSandbox: Bool = false, callBack: ((_ result: Result<String, InPurchasingError>, _ model: InPurchasingModel, _ phone: String) -> Void)? = nil) {
        guard let userInfo = UserInfo.currentUser() else {
            guard  callBack != nil else {
                return
            }
            callBack!(.failure(.getUserInfoFail), InPurchasingModel(), "")
            return
        }
        SwiftyStoreKit.purchaseProduct(productId, quantity: quantity, atomically: atomically) { result in
            LCLog.d("---pay---SwiftyStoreKit--purchaseProduct--")
            
            switch result {
            case .success(let product):
                LCTools.savePayLogInfo(message: " 付款商品：\(productId) 成功 付款价格：\(product.product.price) 开始获取凭证")
                
                //atomically false 表示走服务器获取最后支付结果
                    //                    LPPayTool.shared._callBack = callBack
                let isRelease = UserDefaults.standard.bool(forKey: LCKey.UD_ReleaseEnv)
                // 验证凭据，获取到苹果返回的交易凭据 appStoreReceiptURL iOS7.0增加的，购买交易完成后，会将凭据存放在该地址
                var receiptData: NSData = NSData()
                do {
                    try receiptData = NSData(contentsOf: Bundle.main.appStoreReceiptURL!)
                    let encodeStr: String = receiptData.base64EncodedString(options: .endLineWithLineFeed)
                    LCLog.d("---获取凭证成功---")
                    LCTools.savePayLogInfo(message: " 付款商品：\(productId) 成功 付款价格：\(product.product.price) 获取凭证成功,开始进行加加服务器校验")
                    let inPurmodel = InPurchasingModel(type: isRelease ? "1" : "0", code: encodeStr, phone: userInfo.phone ?? "", timestamp: Int(Date().timeIntervalSince1970))
                    
                    LPPayTool.shared.validationForServer(paramer: ["code": encodeStr, "type": isRelease ? "1" : "0", "phone": userInfo.phone ?? "" ], model: inPurmodel, counts: LCKey.ValidationPayCerCount) { (result, model, phone) in
                        guard  callBack != nil else {
                            return
                        }
                        LCLog.d("---pay--needsFinishTransaction--")
                        // finish 通知苹果的支付队列该交易已经完成，否者就会调用已经购买成功的支付队列，就会出现您以购买过此APP内购项目，此项目将免费恢复这句提
                        if product.needsFinishTransaction {
                            switch result {
                            case .success:
                                LCLog.d("---pay--finishTransaction--")
                                SwiftyStoreKit.finishTransaction(product.transaction)
                                LCTools.savePayLogInfo(message: " 商品：\(productId) finish")
                            default:
                                LCTools.savePayLogInfo(message: " 商品：\(productId) 无法 finish")
                                LCLog.d("")
                            }
                        }
                        callBack!(result, model, phone)
                    }
                } catch {
                    LCLog.d("---获取凭证失败---")
                    LCTools.savePayLogInfo(message: " 付款商品：\(productId) 成功 付款价格：\(product.product.price) 获取凭证失败 需要重启APP")
                    guard callBack != nil else {
                        return
                    }
                    callBack!(.failure(.getAppStoreReceiptURLFail), InPurchasingModel(), "")
                }
                LCLog.d("--Purchase Success: \(product.productId)")
            case .error(let error):
                callBack!(.failure(.getAppPayFail), InPurchasingModel(), "")
                LCTools.savePayLogInfo(message: " 付款商品：\(productId) 付款失败，错误码：\(error.code)")
                LCLog.d((error as NSError).localizedDescription)
                switch error.code {
                case .unknown: LCLog.d("---pay--该商品不可用，请选择其他商品")
                case .clientInvalid: HUD.showFail("---pay--用户Appid异常")
                case .paymentCancelled: break
                case .paymentInvalid: HUD.showFail("---pay--购买标识符无效")
                case .paymentNotAllowed:  HUD.showFail("---pay--该设备不支持支付") //print("--The device is not allowed to make the payment")
                case .storeProductNotAvailable: LCLog.d("---pay--The product is not available in the current storefront")
                case .cloudServicePermissionDenied: LCLog.d("---pay--Access to cloud service information is not allowed")
                case .cloudServiceNetworkConnectionFailed: LCLog.d("---pay--Could not connect to the network")
                case .cloudServiceRevoked: LCLog.d("---pay--User has revoked permission to use this cloud service")
                default: LCLog.d((error as NSError).localizedDescription)
                    HUD.showFail((error as NSError).localizedDescription)
                }
            }
           
        }
    }
    
    public func validationForServer(paramer: [String: Any], model: InPurchasingModel, counts: Int, callBack: ((_ result: Result<String, InPurchasingError>, _ model: InPurchasingModel, _ phone: String) -> Void)? = nil) {
        LCLog.d("---pay--validationForServer--count: \(counts)")
        LCTools.savePayLogInfo(message: "服务器校验次数\(counts)")
        MiddleRequestNet.purchasingValidation(param: paramer) {[weak self] (code, phone) in
            LCTools.savePayLogInfo(message: "服务器认证结果:\(code)")
            if code != 0 {
                if code == 8111 {
                    guard let cb = callBack else {
                        return
                    }
                    cb(.failure(.getIllegalProof), model, phone)
                    return
                }
                if counts == 0 {
                    guard let cb = callBack else {
                        return
                    }
                    cb(.failure(.serverValidationFail), model, phone)
                    return
                }
                DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
                    self?.validationForServer(paramer: paramer, model: model, counts: (counts - 1), callBack: callBack)
                }
            } else {
                LCLog.d("---pay--validationForServer--success: \(counts)")
                guard let cb = callBack else {
                    return
                }
                cb(.success("0"), model, phone)
            }
        }
    }
    
    public func completeTransactions(atomically: Bool = false) {
        print("---pay--completeTransactions--")
        // updatedTransactions
        SwiftyStoreKit.completeTransactions(atomically: atomically) { purchases in
            print("---pay--completeTransactions--SwiftyStoreKit")
            
            guard let userInfo = UserInfo.currentUser() else {
                return
            }
            for purchase in purchases {
                switch purchase.transaction.transactionState {
                case .purchased, .restored:
                    LCTools.savePayLogInfo(message: " 启动 -有没有完成的商品凭证，开始处理")
                    LCLog.d("--SwiftyStoreKit-------purchased, .restored")
                    LCTools.shared.valiationLogin {
                        let isRelease = UserDefaults.standard.bool(forKey: LCKey.UD_ReleaseEnv)
                        var receiptData: NSData = NSData()
                        do {
                            try receiptData = NSData(contentsOf: Bundle.main.appStoreReceiptURL!)
                            let encodeStr: String = receiptData.base64EncodedString(options: .endLineWithLineFeed)
                            LCLog.d("---获取凭证成功---")
                            LCTools.savePayLogInfo(message: " 启动 -没有完成的凭证开始校验")
                            let model = InPurchasingModel(type: isRelease ? "1" : "0", code: encodeStr, phone: userInfo.phone ?? "", timestamp: Int(Date().timeIntervalSince1970))
                            LPPayTool.shared.validationForServer(paramer: ["code": encodeStr, "type": isRelease ? "1" : "0", "phone": userInfo.phone ?? "" ], model: model, counts: LCKey.ValidationPayCerCount) { result,_,_  in
                                LCTools.savePayLogInfo(message: " 启动 -没有完成的凭证处理完成")
                                switch result {
                                case .success:
                                    LCLog.d("---pay--finishTransaction--")
                                    SwiftyStoreKit.finishTransaction(purchase.transaction)
                                default:
                                    LCLog.d("")
                                }
                               
                            }
                        } catch {
                            LCLog.d("---获取凭证失败---")
                            LCTools.savePayLogInfo(message: " 启动 -读取没有完成的凭证失败")
                        }
                    }
                    
                case .failed, .purchasing, .deferred:
                    LCLog.d("--SwiftyStoreKit-------error")
                    LCTools.savePayLogInfo(message: " 启动 有没有完成的商品凭证，Apple 处理失败")
                    // do nothing
                }
            }
        }
    }
     
    public func shouldAddStorePaymentHandler() {
        
        SwiftyStoreKit.shouldAddStorePaymentHandler = { (payment, product) in
            LCLog.d("---pay--shouldAddStorePaymentHandler--")
            LCTools.savePayLogInfo(message: " 启动 有未处理完成的商品:\(product.productIdentifier) 价格：\(product.price)")
            LCTools.shared.valiationLogin {
                LPPayTool.purchaseProduct(product.productIdentifier, quantity: payment.quantity, atomically: false)
            }
            return false
        }
    }
    
    // 通知服务器去校验用户的订阅信息 api/v1/order/ios/verify
    public func validationForServer() {
        guard let _ = UserInfo.currentUser() else { return }
        MiddleRequestNet.serverSubscribeVerify()
    }
    
}
