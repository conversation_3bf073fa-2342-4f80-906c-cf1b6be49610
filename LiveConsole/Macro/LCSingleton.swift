//
//  PLSingleton.swift
//  LivePlus
//
//  Created by 郭炜 on 2021/7/13.
//

import UIKit

enum AnimationOptionForCL {
    case liner
    case easyin
    case easyout
    case easyinout
}

var ReadMessageLocalIdLCKey: String {
    return "ReadMessageLocalIdLCKey11" + "\(UserInfo.currentUser()?.userId ?? 0)"
}

protocol LCSingletonWebDelegate: AnyObject {
    func webViewDidSuccess(cookies: [HTTPCookie])
    func webViewDidFail(error: Error)
}

class LCSingleton: NSObject {
    @objc static let shared = LCSingleton()
    weak var webDelegate: LCSingletonWebDelegate?
        
    
    public var isInRemoting: Bool = false
    var isEnabled: Bool = false
    var autoReplyEnable: Bool = false

    var connectedHost: String = ""
    
    // 作为遥控端时 使用
    var isVip: Bool = false
    
    // 遥控器最后选中的场景数据：处理非自动保存情况下 恢复场景数据
    public var lastRemoteScene: String? = nil
    // 是否开启了自动保存
    public var remoteAutoSave: Bool = false
    
     
    override init() {
        super.init()
       
    }
    
    public lazy var importProgressView: ImportProgressView = {
       let view = ImportProgressView(frame: CGRect(x: 0, y: 0, width: LCDevice.screenW, height: LCDevice.screenH))
        return view
    }()
    
    // 压缩的速度 每秒多少字节数 1G 一分钟的计算
    public static var zipSpeed: Int {
        1073741824 / 90
    }
    
    public static var unzipSpeed: Int {
        1073741824 / 40
    }
        
    
    // 在这里持有一个web 页面 保证不被销毁
    /// 智能场控
    lazy var qrView: CookieRequestWebView = {
        let view = CookieRequestWebView(frame: CGRect(x: 0, y: 0, width: LCDevice.screenW, height: LCDevice.screenH))
        view.delegate = self
        view.isHidden = true
        return view
    }()

}

extension LCSingleton : CookieRequestWebViewDelegate {
    func webViewDidSuccess(cookies: [HTTPCookie], extraInfo: [String : Any]) {
        self.webDelegate?.webViewDidSuccess(cookies: cookies)
        qrView.isHidden = true
    }
    
    func webViewMockUrls(request: DYRequestData) {
        
    }
    
    func userContentController(key: String) {
        
    }
    

    func showWebLogin(isHidden: Bool = true) {
        qrView.isHidden = isHidden
    }
    
    func reloadWebView() {
        guard let appDel = UIApplication.shared.delegate as? AppDelegate else { return }
        appDel.wd.addSubview(qrView)
        qrView.loadTargetPage(url: "https://anchor.douyin.com/anchor/dashboard")
    }
    
  
    
    func webViewDidFail(error: any Error) {
        self.webDelegate?.webViewDidFail(error: error)
    }
    
    
}

// 智能互动的设置项进行互动
extension LCSingleton {
    // MARK: -  显示进度
    public func showZipProgressView(path: String, type: ZIP_Type) {
        importProgressView.clear()
        self.unzipReceiveBackupSmartConfig(path: path, type: type)

    }
    
    func unzipReceiveBackupSmartConfig(path: String, type: ZIP_Type) {
        guard let fileSize = XCFileManager.sizeOfFile(atPath: path) as? Int64 else { return }
        UIApplication.shared.isIdleTimerDisabled = true
        showImportProgressView()
        importProgressView.startTimer()
        self.importProgressView(progress: 0.0, fileSize: fileSize)
        
        // 回调的是真实的进度
        LCTools.unzipReceiveSmartConfig(path: path, type: type, progress: {  [weak self]  progress in
            guard let self = self else { return }
            DispatchQueue.main.async {
                self.importProgressView(progress: progress, fileSize: fileSize)
            }
        }) {[weak self] (dismiss, rooms) in
//                    HUD.hideAllHUD()
            guard let self = self else { return }
//                    if dismiss {
//                        self.showingView.removeFromSuperview()
//                    }
            
            if !dismiss {
                DispatchQueue.main.async {
                    UIApplication.shared.isIdleTimerDisabled = false
                    self.hideImportProgressView()
                    self.importProgressView.releaseTimer()
                }
                return
            }
            guard let rooms = rooms else {
                return
            }
            DispatchQueue.main.async {
                UIApplication.shared.isIdleTimerDisabled = false
                self.importProgressView.releaseTimer()
                self.importProgressView(progress: 1.0, fileSize: fileSize)
                self.importProgressView.showSuccessView()
                self.importProgressView.closeBtnActionBlock = {[weak self] in
                    guard let self = self else { return }
                    //
                    AIReplyDataManager.shared.backupConfigModel(model: rooms.configModel)
                    AIReplyDataManager.shared.backupInteractionModel(model: rooms.dataSource)
                }
            }
        }
    }
    
    public func importProgressView(progress: CGFloat, fileSize: Int64) {
        if let sup = importProgressView.superview  {
           
        } else {
            showImportProgressView()
        }
        importProgressView.importProgressView(progress: progress, fileSize: fileSize)
    }
    
    public func hideImportProgressView() {
        importProgressView.removeFromSuperview()
    }
    
    public func showImportProgressView() {
        guard let appDel = UIApplication.shared.delegate as? AppDelegate else { return }
        appDel.wd.addSubview(importProgressView)
        
    }
}
