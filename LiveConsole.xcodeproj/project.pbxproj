// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		120FF5C72C359370B811F191 /* Pods_LiveConsole.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 9C629D9EE1C865A4E08CC3F4 /* Pods_LiveConsole.framework */; };
		AF191A182DB88C0D0066CA54 /* SmartNavigation.swift in Sources */ = {isa = PBXBuildFile; fileRef = AF191A172DB88C0D0066CA54 /* SmartNavigation.swift */; };
		AF191A1B2DB8CF520066CA54 /* SmartTextItem.swift in Sources */ = {isa = PBXBuildFile; fileRef = AF191A1A2DB8CF520066CA54 /* SmartTextItem.swift */; };
		AF191A1D2DB8EA5A0066CA54 /* SmartKeywordVC+TextReplay.swift in Sources */ = {isa = PBXBuildFile; fileRef = AF191A1C2DB8EA5A0066CA54 /* SmartKeywordVC+TextReplay.swift */; };
		AF191A1F2DB8EBC10066CA54 /* ReplyTextPlusView.swift in Sources */ = {isa = PBXBuildFile; fileRef = AF191A1E2DB8EBC10066CA54 /* ReplyTextPlusView.swift */; };
		AF98263C2E2E0FD50040BA71 /* MinimaxVoiceService.swift in Sources */ = {isa = PBXBuildFile; fileRef = AF98263A2E2E0FD50040BA71 /* MinimaxVoiceService.swift */; };
		AF98263D2E2E0FD50040BA71 /* MinimaxVoiceServiceExample.swift in Sources */ = {isa = PBXBuildFile; fileRef = AF98263B2E2E0FD50040BA71 /* MinimaxVoiceServiceExample.swift */; };
		F41483CA2DE46928008B57C0 /* NickRealtimeView.swift in Sources */ = {isa = PBXBuildFile; fileRef = F41483C92DE46928008B57C0 /* NickRealtimeView.swift */; };
		F42ACA8E2DEEB359006B4ED4 /* api_config.json in Resources */ = {isa = PBXBuildFile; fileRef = F42ACA8D2DEEB359006B4ED4 /* api_config.json */; };
		F431BBB92DB77D1F00535D51 /* HudongAlertView.swift in Sources */ = {isa = PBXBuildFile; fileRef = F431BBB82DB77D1F00535D51 /* HudongAlertView.swift */; };
		F431BBBC2DB7879800535D51 /* SmartActionVC+Danmu.swift in Sources */ = {isa = PBXBuildFile; fileRef = F431BBBB2DB7879800535D51 /* SmartActionVC+Danmu.swift */; };
		F431BBE92DB8C2AA00535D51 /* NetworkMonitorViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = F431BBC12DB8C2AA00535D51 /* NetworkMonitorViewController.swift */; };
		F431BBEA2DB8C2AA00535D51 /* WebInfoManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = F431BBC22DB8C2AA00535D51 /* WebInfoManager.swift */; };
		F431BBED2DB8C2AA00535D51 /* buyinwebjs.swift in Sources */ = {isa = PBXBuildFile; fileRef = F431BBC62DB8C2AA00535D51 /* buyinwebjs.swift */; };
		F431BBF92DB8C2AA00535D51 /* DouyinGoodsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = F431BBD42DB8C2AA00535D51 /* DouyinGoodsView.swift */; };
		F431BBFB2DB8C2AA00535D51 /* DoyinGoodsCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = F431BBD62DB8C2AA00535D51 /* DoyinGoodsCell.swift */; };
		F431BC042DB8C2AA00535D51 /* BuyinRequest.swift in Sources */ = {isa = PBXBuildFile; fileRef = F431BBE12DB8C2AA00535D51 /* BuyinRequest.swift */; };
		F431BC092DB8C3A400535D51 /* BuyinVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = F431BC082DB8C3A400535D51 /* BuyinVC.swift */; };
		F431BC0B2DB8D5C100535D51 /* BuyinModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = F431BC0A2DB8D5C100535D51 /* BuyinModel.swift */; };
		F431BC112DBB7D2F00535D51 /* DouyinAllGoodsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = F431BC102DBB7D2F00535D51 /* DouyinAllGoodsView.swift */; };
		F45FD1A12DC0D2A400F7C58B /* MineTopView2.swift in Sources */ = {isa = PBXBuildFile; fileRef = F45FD1A02DC0D2A400F7C58B /* MineTopView2.swift */; };
		F4B7056C2DBF5FCA00A8F310 /* BuyinEmptyView.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4B7056B2DBF5FCA00A8F310 /* BuyinEmptyView.swift */; };
		F4B7056E2DBF7A6B00A8F310 /* AIGiftReplyVc+TextReplay.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4B7056D2DBF7A6B00A8F310 /* AIGiftReplyVc+TextReplay.swift */; };
		F4B705702DBF7B2100A8F310 /* AIFollowReplyVc+TextReplay.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4B7056F2DBF7B2100A8F310 /* AIFollowReplyVc+TextReplay.swift */; };
		F4B814ED2DD5C17800E5C3DC /* AIConfigVC+Backup.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4B814EC2DD5C17800E5C3DC /* AIConfigVC+Backup.swift */; };
		F4B814FE2DDADB8100E5C3DC /* 播放中.gif in Resources */ = {isa = PBXBuildFile; fileRef = F4B814FD2DDADB8100E5C3DC /* 播放中.gif */; };
		F4B815002DDB187500E5C3DC /* AVPlayManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4B814FF2DDB187500E5C3DC /* AVPlayManager.swift */; };
		F4B935E22DDDC0CD00FB9162 /* SmartCloneRecordBotView.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4B935E12DDDC0AC00FB9162 /* SmartCloneRecordBotView.swift */; };
		F4B935E42DDDC9F300FB9162 /* SmartClone3View.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4B935E32DDDC9B600FB9162 /* SmartClone3View.swift */; };
		F4B935E62DDDD0F200FB9162 /* CloneStepView.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4B935E52DDDD0EE00FB9162 /* CloneStepView.swift */; };
		F4B935E82DDEFE6900FB9162 /* MemberPointsPayVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4B935E72DDEFE5E00FB9162 /* MemberPointsPayVC.swift */; };
		F4B935EA2DDF019300FB9162 /* MemberPointsPayVC+Pay.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4B935E92DDF019300FB9162 /* MemberPointsPayVC+Pay.swift */; };
		F4B935EC2DDF166500FB9162 /* PointsPayCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4B935EB2DDF166500FB9162 /* PointsPayCell.swift */; };
		F4B935EE2DE04EDA00FB9162 /* DouYinAutoSendMessage.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4B935ED2DE04EDA00FB9162 /* DouYinAutoSendMessage.swift */; };
		F4C198912D116ABF00E28E81 /* LimitMemberCard3.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4C1987A2D116ABF00E28E81 /* LimitMemberCard3.swift */; };
		F4C198942D116ABF00E28E81 /* LimitPurchaseVC+Pay.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4C1987D2D116ABF00E28E81 /* LimitPurchaseVC+Pay.swift */; };
		F4C198962D116ABF00E28E81 /* LimitPurchaseVC2.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4C1987F2D116ABF00E28E81 /* LimitPurchaseVC2.swift */; };
		F4C198982D116ABF00E28E81 /* LimitRightModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4C198812D116ABF00E28E81 /* LimitRightModel.swift */; };
		F4C198992D116ABF00E28E81 /* WechatQRCodeView.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4C198822D116ABF00E28E81 /* WechatQRCodeView.swift */; };
		F4C1989A2D116ABF00E28E81 /* LimitModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4C198842D116ABF00E28E81 /* LimitModel.swift */; };
		F4C1989B2D116ABF00E28E81 /* LimitModel+Alert.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4C198852D116ABF00E28E81 /* LimitModel+Alert.swift */; };
		F4C1989C2D116ABF00E28E81 /* LimitModel+Logo.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4C198862D116ABF00E28E81 /* LimitModel+Logo.swift */; };
		F4C1989E2D116ABF00E28E81 /* LimitModel+Operation.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4C198882D116ABF00E28E81 /* LimitModel+Operation.swift */; };
		F4C1989F2D116ABF00E28E81 /* VIPAlert.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4C198892D116ABF00E28E81 /* VIPAlert.swift */; };
		F4C198A42D12650B00E28E81 /* BaseTabBarViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4C198A32D12650B00E28E81 /* BaseTabBarViewController.swift */; };
		F4C198A82D12850F00E28E81 /* BaseNavigationController.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4C198A72D12850F00E28E81 /* BaseNavigationController.swift */; };
		F4C198D32D12A21200E28E81 /* AccountVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4C198AA2D12A21200E28E81 /* AccountVC.swift */; };
		F4C198D72D12A21200E28E81 /* FeedbackVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4C198AE2D12A21200E28E81 /* FeedbackVC.swift */; };
		F4C198D82D12A21200E28E81 /* InformationListVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4C198AF2D12A21200E28E81 /* InformationListVC.swift */; };
		F4C198DA2D12A21200E28E81 /* LogOutVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4C198B12D12A21200E28E81 /* LogOutVC.swift */; };
		F4C198DB2D12A21200E28E81 /* MineVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4C198B22D12A21200E28E81 /* MineVC.swift */; };
		F4C198DF2D12A21200E28E81 /* PhoneBindDoneVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4C198B62D12A21200E28E81 /* PhoneBindDoneVC.swift */; };
		F4C198E02D12A21200E28E81 /* PhoneBindVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4C198B72D12A21200E28E81 /* PhoneBindVC.swift */; };
		F4C198E12D12A21200E28E81 /* PrivacySettingVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4C198B82D12A21200E28E81 /* PrivacySettingVC.swift */; };
		F4C198E32D12A21200E28E81 /* SettingVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4C198BA2D12A21200E28E81 /* SettingVC.swift */; };
		F4C198E52D12A21200E28E81 /* UserInfoSettingVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4C198BC2D12A21200E28E81 /* UserInfoSettingVC.swift */; };
		F4C198E62D12A21200E28E81 /* ConfigModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4C198BE2D12A21200E28E81 /* ConfigModel.swift */; };
		F4C198E82D12A21200E28E81 /* InPurchasingModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4C198C02D12A21200E28E81 /* InPurchasingModel.swift */; };
		F4C198EA2D12A21200E28E81 /* UserInfo.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4C198C22D12A21200E28E81 /* UserInfo.swift */; };
		F4C198EC2D12A21200E28E81 /* InformationListCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4C198C52D12A21200E28E81 /* InformationListCell.swift */; };
		F4C198ED2D12A21200E28E81 /* InPurchasingBtnViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4C198C62D12A21200E28E81 /* InPurchasingBtnViewCell.swift */; };
		F4C198F02D12A21200E28E81 /* MineCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4C198C92D12A21200E28E81 /* MineCell.swift */; };
		F4C198F12D12A21200E28E81 /* MineTopView.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4C198CA2D12A21200E28E81 /* MineTopView.swift */; };
		F4C198F22D12A21200E28E81 /* MineTopView.xib in Resources */ = {isa = PBXBuildFile; fileRef = F4C198CB2D12A21200E28E81 /* MineTopView.xib */; };
		F4C198F32D12A21200E28E81 /* OpeningMemberCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4C198CC2D12A21200E28E81 /* OpeningMemberCell.swift */; };
		F4C198F52D12A21200E28E81 /* SettingCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4C198CE2D12A21200E28E81 /* SettingCell.swift */; };
		F4C198F72D12A21200E28E81 /* VipServiceView.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4C198D02D12A21200E28E81 /* VipServiceView.swift */; };
		F4C274512DCB3E0400EA07A7 /* MemberPointsVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4C274502DCB3E0400EA07A7 /* MemberPointsVC.swift */; };
		F4C274532DCB43B200EA07A7 /* PointsCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4C274522DCB43B200EA07A7 /* PointsCell.swift */; };
		F4C274562DCC465700EA07A7 /* PointsAlertView.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4C274552DCC465700EA07A7 /* PointsAlertView.swift */; };
		F4C274592DD209A900EA07A7 /* LangduNickView.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4C274582DD209A900EA07A7 /* LangduNickView.swift */; };
		F4C2745B2DD20E2500EA07A7 /* SmartActionVC+Realtime.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4C2745A2DD20E2500EA07A7 /* SmartActionVC+Realtime.swift */; };
		F4C2745D2DD20F3E00EA07A7 /* RealtimeNickAlertView.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4C2745C2DD20F3E00EA07A7 /* RealtimeNickAlertView.swift */; };
		F4C2746B2DD4784C00EA07A7 /* SmartCardTextView.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4C2746A2DD4784C00EA07A7 /* SmartCardTextView.swift */; };
		F4C2746D2DD47BAE00EA07A7 /* SmartCardTextCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4C2746C2DD47BAE00EA07A7 /* SmartCardTextCell.swift */; };
		F4D0464A2D1E84C000A57812 /* QRScanedView.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4D046492D1E84C000A57812 /* QRScanedView.swift */; };
		F4D0464C2D1E8D4E00A57812 /* LoginScanVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4D0464B2D1E8D4E00A57812 /* LoginScanVC.swift */; };
		F4D413CB2DB62BF30036646F /* VeTOSiOSSDK.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = F4D413CA2DB62BF20036646F /* VeTOSiOSSDK.framework */; };
		F4D413CF2DB62E560036646F /* BackupStepView.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4D413CD2DB62E560036646F /* BackupStepView.swift */; };
		F4D413D62DB62E8B0036646F /* BackupLoadingView.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4D413D12DB62E8A0036646F /* BackupLoadingView.swift */; };
		F4D413D92DB62E8B0036646F /* BackupPasswordView.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4D413D42DB62E8B0036646F /* BackupPasswordView.swift */; };
		F4DE7E2C2DB232650021A153 /* LPSocketHelper.m in Sources */ = {isa = PBXBuildFile; fileRef = F4DE7E2B2DB232650021A153 /* LPSocketHelper.m */; };
		F4DFC5ED2DB61E5900E003A0 /* AIAudioResultModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC59A2DB61E5900E003A0 /* AIAudioResultModel.swift */; };
		F4DFC5EE2DB61E5900E003A0 /* AiAutoReplyModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC59B2DB61E5900E003A0 /* AiAutoReplyModel.swift */; };
		F4DFC5EF2DB61E5900E003A0 /* AIReplyDataManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC59C2DB61E5900E003A0 /* AIReplyDataManager.swift */; };
		F4DFC5F02DB61E5900E003A0 /* AIReplyDataManager+Cache.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC59D2DB61E5900E003A0 /* AIReplyDataManager+Cache.swift */; };
		F4DFC5F12DB61E5900E003A0 /* AudioPathStyle.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC59E2DB61E5900E003A0 /* AudioPathStyle.swift */; };
		F4DFC5F22DB61E5900E003A0 /* AudioSpkIdModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC59F2DB61E5900E003A0 /* AudioSpkIdModel.swift */; };
		F4DFC5F32DB61E5900E003A0 /* DouyinGiftModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC5A02DB61E5900E003A0 /* DouyinGiftModel.swift */; };
		F4DFC5F42DB61E5900E003A0 /* DownloadFromBucket.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC5A12DB61E5900E003A0 /* DownloadFromBucket.swift */; };
		F4DFC5F52DB61E5900E003A0 /* InteractionModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC5A22DB61E5900E003A0 /* InteractionModel.swift */; };
		F4DFC5F62DB61E5900E003A0 /* AudioDurationHelper.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC5A42DB61E5900E003A0 /* AudioDurationHelper.swift */; };
		F4DFC5F72DB61E5900E003A0 /* AudioPermissionManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC5A52DB61E5900E003A0 /* AudioPermissionManager.swift */; };
		F4DFC5F82DB61E5900E003A0 /* AudioPlayManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC5A62DB61E5900E003A0 /* AudioPlayManager.swift */; };
		F4DFC5F92DB61E5900E003A0 /* AudioRecordManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC5A72DB61E5900E003A0 /* AudioRecordManager.swift */; };
		F4DFC5FA2DB61E5900E003A0 /* RecordCountDownLabel.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC5A82DB61E5900E003A0 /* RecordCountDownLabel.swift */; };
		F4DFC5FB2DB61E5900E003A0 /* SmartAlertView.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC5A92DB61E5900E003A0 /* SmartAlertView.swift */; };
		F4DFC5FC2DB61E5900E003A0 /* UIView+ExtensionSmart.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC5AA2DB61E5900E003A0 /* UIView+ExtensionSmart.swift */; };
		F4DFC5FD2DB61E5900E003A0 /* SmartAudioItem.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC5AC2DB61E5900E003A0 /* SmartAudioItem.swift */; };
		F4DFC5FE2DB61E5900E003A0 /* SmartKeywordItem.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC5AD2DB61E5900E003A0 /* SmartKeywordItem.swift */; };
		F4DFC5FF2DB61E5900E003A0 /* SmartTTSAudioItem.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC5AE2DB61E5900E003A0 /* SmartTTSAudioItem.swift */; };
		F4DFC6002DB61E5900E003A0 /* TTSPresetItem.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC5AF2DB61E5900E003A0 /* TTSPresetItem.swift */; };
		F4DFC6012DB61E5900E003A0 /* AIPickerProgressView.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC5B12DB61E5900E003A0 /* AIPickerProgressView.swift */; };
		F4DFC6022DB61E5900E003A0 /* AudioCloneAgreementView.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC5B22DB61E5900E003A0 /* AudioCloneAgreementView.swift */; };
		F4DFC6032DB61E5900E003A0 /* AudioRateSelectionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC5B32DB61E5900E003A0 /* AudioRateSelectionView.swift */; };
		F4DFC6042DB61E5900E003A0 /* AudioSelectionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC5B42DB61E5900E003A0 /* AudioSelectionView.swift */; };
		F4DFC6052DB61E5900E003A0 /* AudioYinSeCloneView.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC5B52DB61E5900E003A0 /* AudioYinSeCloneView.swift */; };
		F4DFC6062DB61E5900E003A0 /* AudioYinSePresetView.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC5B62DB61E5900E003A0 /* AudioYinSePresetView.swift */; };
		F4DFC6072DB61E5900E003A0 /* AudioYinSeSelectionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC5B72DB61E5900E003A0 /* AudioYinSeSelectionView.swift */; };
		F4DFC6082DB61E5900E003A0 /* KeywordListView.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC5B82DB61E5900E003A0 /* KeywordListView.swift */; };
		F4DFC6092DB61E5900E003A0 /* ReplayAudioView.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC5B92DB61E5900E003A0 /* ReplayAudioView.swift */; };
		F4DFC60A2DB61E5900E003A0 /* SmartCloneRecordTopView.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC5BA2DB61E5900E003A0 /* SmartCloneRecordTopView.swift */; };
		F4DFC60B2DB61E5900E003A0 /* SmartRecordBotView.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC5BB2DB61E5900E003A0 /* SmartRecordBotView.swift */; };
		F4DFC60C2DB61E5900E003A0 /* SmartRecordTopView.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC5BC2DB61E5900E003A0 /* SmartRecordTopView.swift */; };
		F4DFC60D2DB61E5900E003A0 /* SmartTTSBotView.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC5BD2DB61E5900E003A0 /* SmartTTSBotView.swift */; };
		F4DFC60E2DB61E5900E003A0 /* SmartTTSTopView.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC5BE2DB61E5900E003A0 /* SmartTTSTopView.swift */; };
		F4DFC60F2DB61E5900E003A0 /* SystemReplyTypeCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC5C02DB61E5900E003A0 /* SystemReplyTypeCell.swift */; };
		F4DFC6102DB61E5900E003A0 /* SystemReplyTypeView.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC5C12DB61E5900E003A0 /* SystemReplyTypeView.swift */; };
		F4DFC6112DB61E5900E003A0 /* AIBackupView.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC5C32DB61E5900E003A0 /* AIBackupView.swift */; };
		F4DFC6122DB61E5900E003A0 /* AIConfigGiftView.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC5C42DB61E5900E003A0 /* AIConfigGiftView.swift */; };
		F4DFC6132DB61E5900E003A0 /* AIConfigReplyView.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC5C52DB61E5900E003A0 /* AIConfigReplyView.swift */; };
		F4DFC6142DB61E5900E003A0 /* AIConfigSetView.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC5C62DB61E5900E003A0 /* AIConfigSetView.swift */; };
		F4DFC6152DB61E5900E003A0 /* AIConfigSKPView.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC5C72DB61E5900E003A0 /* AIConfigSKPView.swift */; };
		F4DFC6172DB61E5900E003A0 /* GiftTimeCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC5C92DB61E5900E003A0 /* GiftTimeCell.swift */; };
		F4DFC6182DB61E5900E003A0 /* GiftTimeView.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC5CA2DB61E5900E003A0 /* GiftTimeView.swift */; };
		F4DFC61A2DB61E5900E003A0 /* KeyWordInputView.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC5CC2DB61E5900E003A0 /* KeyWordInputView.swift */; };
		F4DFC61B2DB61E5900E003A0 /* SmartActionCardView.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC5CD2DB61E5900E003A0 /* SmartActionCardView.swift */; };
		F4DFC61C2DB61E5900E003A0 /* SmartActionNavigation.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC5CE2DB61E5900E003A0 /* SmartActionNavigation.swift */; };
		F4DFC61D2DB61E5900E003A0 /* SmartActionToolBar.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC5CF2DB61E5900E003A0 /* SmartActionToolBar.swift */; };
		F4DFC61E2DB61E5900E003A0 /* SmartCardAudioCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC5D02DB61E5900E003A0 /* SmartCardAudioCell.swift */; };
		F4DFC61F2DB61E5900E003A0 /* SmartCardAudioView.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC5D12DB61E5900E003A0 /* SmartCardAudioView.swift */; };
		F4DFC6202DB61E5900E003A0 /* SmartCardFollowView.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC5D22DB61E5900E003A0 /* SmartCardFollowView.swift */; };
		F4DFC6212DB61E5900E003A0 /* SmartCardGiftView.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC5D32DB61E5900E003A0 /* SmartCardGiftView.swift */; };
		F4DFC6222DB61E5900E003A0 /* SmartCardKeyWordView.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC5D42DB61E5900E003A0 /* SmartCardKeyWordView.swift */; };
		F4DFC6232DB61E5900E003A0 /* SmartCardVideoView.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC5D52DB61E5900E003A0 /* SmartCardVideoView.swift */; };
		F4DFC6242DB61E5900E003A0 /* SmartDetailFourNavigation.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC5D62DB61E5900E003A0 /* SmartDetailFourNavigation.swift */; };
		F4DFC6252DB61E5900E003A0 /* SmartDetailNavigation.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC5D72DB61E5900E003A0 /* SmartDetailNavigation.swift */; };
		F4DFC6262DB61E5900E003A0 /* SmartDetailThreeNavigation.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC5D82DB61E5900E003A0 /* SmartDetailThreeNavigation.swift */; };
		F4DFC6272DB61E5900E003A0 /* AIConfigVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC5DA2DB61E5900E003A0 /* AIConfigVC.swift */; };
		F4DFC6282DB61E5900E003A0 /* AIFollowReplyVc.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC5DB2DB61E5900E003A0 /* AIFollowReplyVc.swift */; };
		F4DFC6292DB61E5900E003A0 /* AIFollowReplyVc+AudioReply.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC5DC2DB61E5900E003A0 /* AIFollowReplyVc+AudioReply.swift */; };
		F4DFC62A2DB61E5900E003A0 /* SmartActionVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC5DD2DB61E5900E003A0 /* SmartActionVC.swift */; };
		F4DFC62B2DB61E5900E003A0 /* SmartActionVC+Backup.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC5DE2DB61E5900E003A0 /* SmartActionVC+Backup.swift */; };
		F4DFC62C2DB61E5900E003A0 /* SmartActionVC+Tool.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC5DF2DB61E5900E003A0 /* SmartActionVC+Tool.swift */; };
		F4DFC62D2DB61E5900E003A0 /* SmartCloneEditVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC5E02DB61E5900E003A0 /* SmartCloneEditVC.swift */; };
		F4DFC62E2DB61E5900E003A0 /* SmartKeywordVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC5E12DB61E5900E003A0 /* SmartKeywordVC.swift */; };
		F4DFC62F2DB61E5900E003A0 /* SmartKeywordVC+AudioReply.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC5E22DB61E5900E003A0 /* SmartKeywordVC+AudioReply.swift */; };
		F4DFC6302DB61E5900E003A0 /* SmartKeywordVC+Keyword.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC5E32DB61E5900E003A0 /* SmartKeywordVC+Keyword.swift */; };
		F4DFC6312DB61E5900E003A0 /* SmartRecordAudioVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC5E42DB61E5900E003A0 /* SmartRecordAudioVC.swift */; };
		F4DFC6322DB61E5900E003A0 /* SmartTTSAudioVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC5E52DB61E5900E003A0 /* SmartTTSAudioVC.swift */; };
		F4DFC6332DB61E5900E003A0 /* SmartTTSAudioVC+Request.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC5E62DB61E5900E003A0 /* SmartTTSAudioVC+Request.swift */; };
		F4DFC6342DB61E5900E003A0 /* AiAnswerFactory.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC5E82DB61E5900E003A0 /* AiAnswerFactory.swift */; };
		F4DFC6352DB61E5900E003A0 /* AiAnswerFactory+TTS.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC5E92DB61E5900E003A0 /* AiAnswerFactory+TTS.swift */; };
		F4DFC6362DB61E5900E003A0 /* SmartBaseView.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC5EA2DB61E5900E003A0 /* SmartBaseView.swift */; };
		F4DFC6372DB61E5900E003A0 /* SmartBaseViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC5EB2DB61E5900E003A0 /* SmartBaseViewController.swift */; };
		F4DFC6632DB61EED00E003A0 /* douyin.pb.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC6382DB61EED00E003A0 /* douyin.pb.swift */; };
		F4DFC6642DB61EED00E003A0 /* DouyinDecode.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC6392DB61EED00E003A0 /* DouyinDecode.swift */; };
		F4DFC6652DB61EED00E003A0 /* DouyinMessageData.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC63A2DB61EED00E003A0 /* DouyinMessageData.swift */; };
		F4DFC6672DB61EED00E003A0 /* LiveMessage.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC63C2DB61EED00E003A0 /* LiveMessage.swift */; };
		F4DFC6682DB61EED00E003A0 /* AnchorInfoResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC63E2DB61EED00E003A0 /* AnchorInfoResponse.swift */; };
		F4DFC6692DB61EED00E003A0 /* BaseLivingResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC63F2DB61EED00E003A0 /* BaseLivingResponse.swift */; };
		F4DFC66A2DB61EED00E003A0 /* FansAgeDistriResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC6402DB61EED00E003A0 /* FansAgeDistriResponse.swift */; };
		F4DFC66B2DB61EED00E003A0 /* FansAnalysisResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC6412DB61EED00E003A0 /* FansAnalysisResponse.swift */; };
		F4DFC66C2DB61EED00E003A0 /* FansGenderDistriResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC6422DB61EED00E003A0 /* FansGenderDistriResponse.swift */; };
		F4DFC66D2DB61EED00E003A0 /* FansInterestDistriResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC6432DB61EED00E003A0 /* FansInterestDistriResponse.swift */; };
		F4DFC66E2DB61EED00E003A0 /* FansTrendResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC6442DB61EED00E003A0 /* FansTrendResponse.swift */; };
		F4DFC66F2DB61EED00E003A0 /* HistoryDetailResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC6452DB61EED00E003A0 /* HistoryDetailResponse.swift */; };
		F4DFC6702DB61EED00E003A0 /* HistoryKeyFragmentResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC6462DB61EED00E003A0 /* HistoryKeyFragmentResponse.swift */; };
		F4DFC6712DB61EED00E003A0 /* HistoryListResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC6472DB61EED00E003A0 /* HistoryListResponse.swift */; };
		F4DFC6722DB61EED00E003A0 /* HistoryNewFansConvResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC6482DB61EED00E003A0 /* HistoryNewFansConvResponse.swift */; };
		F4DFC6732DB61EED00E003A0 /* HistoryOverviewResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC6492DB61EED00E003A0 /* HistoryOverviewResponse.swift */; };
		F4DFC6742DB61EED00E003A0 /* HistoryPayAudienceConvResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC64A2DB61EED00E003A0 /* HistoryPayAudienceConvResponse.swift */; };
		F4DFC6752DB61EED00E003A0 /* HistoryStatTopListResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC64B2DB61EED00E003A0 /* HistoryStatTopListResponse.swift */; };
		F4DFC6762DB61EED00E003A0 /* LiveDataBaseResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC64C2DB61EED00E003A0 /* LiveDataBaseResponse.swift */; };
		F4DFC6772DB61EED00E003A0 /* LiveStatusResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC64D2DB61EED00E003A0 /* LiveStatusResponse.swift */; };
		F4DFC6782DB61EED00E003A0 /* LivingConversionResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC64E2DB61EED00E003A0 /* LivingConversionResponse.swift */; };
		F4DFC6792DB61EED00E003A0 /* LivingEntranceResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC64F2DB61EED00E003A0 /* LivingEntranceResponse.swift */; };
		F4DFC67A2DB61EED00E003A0 /* LivingRealTimeResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC6502DB61EED00E003A0 /* LivingRealTimeResponse.swift */; };
		F4DFC67B2DB61EED00E003A0 /* MessageListResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC6512DB61EED00E003A0 /* MessageListResponse.swift */; };
		F4DFC67C2DB61EED00E003A0 /* SendMessageResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC6522DB61EED00E003A0 /* SendMessageResponse.swift */; };
		F4DFC67D2DB61EED00E003A0 /* api_config.enc in Resources */ = {isa = PBXBuildFile; fileRef = F4DFC6542DB61EED00E003A0 /* api_config.enc */; };
		F4DFC67E2DB61EED00E003A0 /* APIConfigurationManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC6552DB61EED00E003A0 /* APIConfigurationManager.swift */; };
		F4DFC67F2DB61EED00E003A0 /* APIStyle.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC6562DB61EED00E003A0 /* APIStyle.swift */; };
		F4DFC6802DB61EED00E003A0 /* CryptoUtil.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC6572DB61EED00E003A0 /* CryptoUtil.swift */; };
		F4DFC6812DB61EED00E003A0 /* js_config.enc in Resources */ = {isa = PBXBuildFile; fileRef = F4DFC6582DB61EED00E003A0 /* js_config.enc */; };
		F4DFC6822DB61EED00E003A0 /* LiveStatusListen.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC6592DB61EED00E003A0 /* LiveStatusListen.swift */; };
		F4DFC6832DB61EED00E003A0 /* RequestManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC65A2DB61EED00E003A0 /* RequestManager.swift */; };
		F4DFC6842DB61EED00E003A0 /* CookieInfo.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC65C2DB61EED00E003A0 /* CookieInfo.swift */; };
		F4DFC6852DB61EED00E003A0 /* CookieRequestWebView.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC65D2DB61EED00E003A0 /* CookieRequestWebView.swift */; };
		F4DFC6862DB61EED00E003A0 /* JSSingleton.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC65E2DB61EED00E003A0 /* JSSingleton.swift */; };
		F4DFC6872DB61EED00E003A0 /* NetworkMonitor.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC65F2DB61EED00E003A0 /* NetworkMonitor.swift */; };
		F4DFC6882DB61EED00E003A0 /* webjs.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC6602DB61EED00E003A0 /* webjs.swift */; };
		F4DFC6A32DB61F2C00E003A0 /* AutoReplyManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC6892DB61F2C00E003A0 /* AutoReplyManager.swift */; };
		F4DFC6A42DB61F2C00E003A0 /* DouyinMessageHanlerQueue.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC68A2DB61F2C00E003A0 /* DouyinMessageHanlerQueue.swift */; };
		F4DFC6A52DB61F2C00E003A0 /* ReplyManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC68B2DB61F2C00E003A0 /* ReplyManager.swift */; };
		F4DFC6A62DB61F2C00E003A0 /* AIGiftReplyVc.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC68D2DB61F2C00E003A0 /* AIGiftReplyVc.swift */; };
		F4DFC6A72DB61F2C00E003A0 /* AIGiftReplyVc+AudioReply.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC68E2DB61F2C00E003A0 /* AIGiftReplyVc+AudioReply.swift */; };
		F4DFC6AF2DB61F2C00E003A0 /* AIGiftReplyContentView.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC6972DB61F2C00E003A0 /* AIGiftReplyContentView.swift */; };
		F4DFC6B02DB61F2C00E003A0 /* AIGiftReplyView.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC6982DB61F2C00E003A0 /* AIGiftReplyView.swift */; };
		F4DFC6B12DB61F2C00E003A0 /* DouyinGiftCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC6992DB61F2C00E003A0 /* DouyinGiftCell.swift */; };
		F4DFC6B22DB61F2C00E003A0 /* DouyinGiftListView.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC69A2DB61F2C00E003A0 /* DouyinGiftListView.swift */; };
		F4DFC6B32DB61F2C00E003A0 /* GiftFilterCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC69B2DB61F2C00E003A0 /* GiftFilterCell.swift */; };
		F4DFC6B42DB61F2C00E003A0 /* GiftFilterView.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC69C2DB61F2C00E003A0 /* GiftFilterView.swift */; };
		F4DFC6B52DB61F2C00E003A0 /* GiftReplyAudioView.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC69D2DB61F2C00E003A0 /* GiftReplyAudioView.swift */; };
		F4DFC6B62DB61F2C00E003A0 /* GiftReplyItemView.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC69E2DB61F2C00E003A0 /* GiftReplyItemView.swift */; };
		F4DFC6BC2DB6240900E003A0 /* AreaItemModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4DFC6BB2DB6240900E003A0 /* AreaItemModel.swift */; };
		FB3E340E2B8446BE00106FC7 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = FB3E2C312B8446BB00106FC7 /* Assets.xcassets */; };
		FB3E34132B8446BE00106FC7 /* LaunchScreenJ.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = FB3E2C362B8446BB00106FC7 /* LaunchScreenJ.storyboard */; };
		FB3E34142B8446BE00106FC7 /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = FB3E2C382B8446BB00106FC7 /* Main.storyboard */; };
		FB3E346D2B8446BF00106FC7 /* StatisticsEvents.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2C9B2B8446BB00106FC7 /* StatisticsEvents.swift */; };
		FB3E346E2B8446BF00106FC7 /* LPStatistics.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2C9C2B8446BB00106FC7 /* LPStatistics.swift */; };
		FB3E346F2B8446BF00106FC7 /* LXReorderableCollectionViewFlowLayout.m in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2C9F2B8446BB00106FC7 /* LXReorderableCollectionViewFlowLayout.m */; };
		FB3E34702B8446BF00106FC7 /* BRResultModel.m in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2CA22B8446BB00106FC7 /* BRResultModel.m */; };
		FB3E34712B8446BF00106FC7 /* BRStringPickerView.m in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2CA52B8446BB00106FC7 /* BRStringPickerView.m */; };
		FB3E34722B8446BF00106FC7 /* BRDatePickerView+BR.m in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2CA82B8446BB00106FC7 /* BRDatePickerView+BR.m */; };
		FB3E34732B8446BF00106FC7 /* NSDate+BRPickerView.m in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2CAA2B8446BB00106FC7 /* NSDate+BRPickerView.m */; };
		FB3E34742B8446BF00106FC7 /* BRDatePickerView.m in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2CAB2B8446BB00106FC7 /* BRDatePickerView.m */; };
		FB3E34752B8446BF00106FC7 /* BRPickerView.bundle in Resources */ = {isa = PBXBuildFile; fileRef = FB3E2CB12B8446BB00106FC7 /* BRPickerView.bundle */; };
		FB3E34762B8446BF00106FC7 /* BRPickerStyle.m in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2CB22B8446BB00106FC7 /* BRPickerStyle.m */; };
		FB3E34772B8446BF00106FC7 /* BRBaseView.m in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2CB32B8446BB00106FC7 /* BRBaseView.m */; };
		FB3E34782B8446BF00106FC7 /* NSBundle+BRPickerView.m in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2CB42B8446BB00106FC7 /* NSBundle+BRPickerView.m */; };
		FB3E34792B8446BF00106FC7 /* BRAddressModel.m in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2CB82B8446BB00106FC7 /* BRAddressModel.m */; };
		FB3E347A2B8446BF00106FC7 /* BRAddressPickerView.m in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2CBB2B8446BB00106FC7 /* BRAddressPickerView.m */; };
		FB3E347B2B8446BF00106FC7 /* CircleButton.m in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2CBD2B8446BB00106FC7 /* CircleButton.m */; };
		FB3E347C2B8446BF00106FC7 /* IrregularButton.m in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2CBE2B8446BB00106FC7 /* IrregularButton.m */; };
		FB3E347D2B8446BF00106FC7 /* XSPDispatchGroup.m in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2CC42B8446BB00106FC7 /* XSPDispatchGroup.m */; };
		FB3E347E2B8446BF00106FC7 /* XSDispatchQueue.m in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2CC62B8446BB00106FC7 /* XSDispatchQueue.m */; };
		FB3E34802B8446BF00106FC7 /* CYCropCornerView.m in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2CC92B8446BB00106FC7 /* CYCropCornerView.m */; };
		FB3E34822B8446BF00106FC7 /* IFVSRSecurityPolicy.m in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2CCF2B8446BB00106FC7 /* IFVSRSecurityPolicy.m */; };
		FB3E34832B8446BF00106FC7 /* IFVConstants.m in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2CD32B8446BB00106FC7 /* IFVConstants.m */; };
		FB3E34842B8446BF00106FC7 /* IFVProxyConnect.m in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2CD72B8446BB00106FC7 /* IFVProxyConnect.m */; };
		FB3E34852B8446BF00106FC7 /* IFVRunLoopThread.m in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2CDA2B8446BB00106FC7 /* IFVRunLoopThread.m */; };
		FB3E34862B8446BF00106FC7 /* IFVPinningSecurityPolicy.m in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2CDC2B8446BB00106FC7 /* IFVPinningSecurityPolicy.m */; };
		FB3E34872B8446BF00106FC7 /* IFVDelegateController.m in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2CE02B8446BB00106FC7 /* IFVDelegateController.m */; };
		FB3E34882B8446BF00106FC7 /* IFVRandom.m in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2CE32B8446BB00106FC7 /* IFVRandom.m */; };
		FB3E34892B8446BF00106FC7 /* IFVSIMDHelpers.m in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2CE52B8446BB00106FC7 /* IFVSIMDHelpers.m */; };
		FB3E348A2B8446BF00106FC7 /* IFVLog.m in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2CE62B8446BB00106FC7 /* IFVLog.m */; };
		FB3E348B2B8446BF00106FC7 /* IFVMutex.m in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2CE92B8446BB00106FC7 /* IFVMutex.m */; };
		FB3E348C2B8446BF00106FC7 /* IFVURLUtilities.m in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2CEB2B8446BB00106FC7 /* IFVURLUtilities.m */; };
		FB3E348D2B8446BF00106FC7 /* IFVError.m in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2CEC2B8446BB00106FC7 /* IFVError.m */; };
		FB3E348E2B8446BF00106FC7 /* IFVHTTPConnectMessage.m in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2CED2B8446BB00106FC7 /* IFVHTTPConnectMessage.m */; };
		FB3E348F2B8446BF00106FC7 /* IFVHash.m in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2CF12B8446BB00106FC7 /* IFVHash.m */; };
		FB3E34902B8446BF00106FC7 /* IFVIOConsumerPool.m in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2CF32B8446BB00106FC7 /* IFVIOConsumerPool.m */; };
		FB3E34912B8446BF00106FC7 /* IFVIOConsumer.m in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2CF42B8446BB00106FC7 /* IFVIOConsumer.m */; };
		FB3E34922B8446BF00106FC7 /* IFVWebSocket.m in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2CFA2B8446BB00106FC7 /* IFVWebSocket.m */; };
		FB3E34932B8446BF00106FC7 /* NSRunLoop+IFVWebSocket.m in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2CFC2B8446BB00106FC7 /* NSRunLoop+IFVWebSocket.m */; };
		FB3E34942B8446BF00106FC7 /* NSURLRequest+IFVWebSocket.m in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2CFD2B8446BB00106FC7 /* NSURLRequest+IFVWebSocket.m */; };
		FB3E34952B8446BF00106FC7 /* YYFPSLabel.m in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2D002B8446BB00106FC7 /* YYFPSLabel.m */; };
		FB3E34962B8446BF00106FC7 /* ZLPhotoBrowser.bundle in Resources */ = {isa = PBXBuildFile; fileRef = FB3E2D032B8446BB00106FC7 /* ZLPhotoBrowser.bundle */; };
		FB3E34972B8446BF00106FC7 /* ZLImagePreviewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2D052B8446BB00106FC7 /* ZLImagePreviewController.swift */; };
		FB3E34982B8446BF00106FC7 /* ZLAlbumCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2D062B8446BB00106FC7 /* ZLAlbumCell.swift */; };
		FB3E34992B8446BF00106FC7 /* ZLPhotoManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2D072B8446BB00106FC7 /* ZLPhotoManager.swift */; };
		FB3E349A2B8446BF00106FC7 /* ZLPhotoModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2D082B8446BB00106FC7 /* ZLPhotoModel.swift */; };
		FB3E349B2B8446BF00106FC7 /* ZLPhotoPreviewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2D092B8446BB00106FC7 /* ZLPhotoPreviewCell.swift */; };
		FB3E349C2B8446BF00106FC7 /* ZLProgressHUD.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2D0A2B8446BB00106FC7 /* ZLProgressHUD.swift */; };
		FB3E349D2B8446BF00106FC7 /* ZLFetchImageOperation.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2D0B2B8446BB00106FC7 /* ZLFetchImageOperation.swift */; };
		FB3E349E2B8446BF00106FC7 /* ZLAlbumListController.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2D0C2B8446BB00106FC7 /* ZLAlbumListController.swift */; };
		FB3E349F2B8446BF00106FC7 /* ZLEmbedAlbumListView.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2D0D2B8446BB00106FC7 /* ZLEmbedAlbumListView.swift */; };
		FB3E34A02B8446BF00106FC7 /* ZLLanguageDefine.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2D0E2B8446BB00106FC7 /* ZLLanguageDefine.swift */; };
		FB3E34A12B8446BF00106FC7 /* ZLAlbumListView.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2D0F2B8446BB00106FC7 /* ZLAlbumListView.swift */; };
		FB3E34A22B8446BF00106FC7 /* ZLPhotoPreviewSheet.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2D102B8446BB00106FC7 /* ZLPhotoPreviewSheet.swift */; };
		FB3E34A32B8446BF00106FC7 /* ZLAlbumListCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2D112B8446BB00106FC7 /* ZLAlbumListCell.swift */; };
		FB3E34A42B8446BF00106FC7 /* ZLPhotoPreviewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2D122B8446BB00106FC7 /* ZLPhotoPreviewController.swift */; };
		FB3E34A52B8446BF00106FC7 /* ZLImageNavController.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2D132B8446BB00106FC7 /* ZLImageNavController.swift */; };
		FB3E34A62B8446BF00106FC7 /* ZLThumbnailViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2D142B8446BB00106FC7 /* ZLThumbnailViewController.swift */; };
		FB3E34A72B8446BF00106FC7 /* ZLVideoManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2D152B8446BB00106FC7 /* ZLVideoManager.swift */; };
		FB3E34A82B8446BF00106FC7 /* ZLAddPhotoCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2D162B8446BB00106FC7 /* ZLAddPhotoCell.swift */; };
		FB3E34A92B8446BF00106FC7 /* ZLGeneralDefine.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2D172B8446BB00106FC7 /* ZLGeneralDefine.swift */; };
		FB3E34AA2B8446BF00106FC7 /* ZLThumbnailPhotoCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2D182B8446BB00106FC7 /* ZLThumbnailPhotoCell.swift */; };
		FB3E34AB2B8446BF00106FC7 /* ZLProgressView.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2D192B8446BB00106FC7 /* ZLProgressView.swift */; };
		FB3E34AC2B8446BF00106FC7 /* ZLPhotoConfiguration.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2D1A2B8446BB00106FC7 /* ZLPhotoConfiguration.swift */; };
		FB3E34AD2B8446BF00106FC7 /* ZLPhotoBrowser.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2D1B2B8446BB00106FC7 /* ZLPhotoBrowser.swift */; };
		FB3E34AE2B8446BF00106FC7 /* ZLAlbumListModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2D1C2B8446BB00106FC7 /* ZLAlbumListModel.swift */; };
		FB3E34AF2B8446BF00106FC7 /* ZLCameraCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2D1D2B8446BB00106FC7 /* ZLCameraCell.swift */; };
		FB3E34B02B8446BF00106FC7 /* ZLCustomCamera.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2D1F2B8446BB00106FC7 /* ZLCustomCamera.swift */; };
		FB3E34B12B8446BF00106FC7 /* ZLPhotoPreviewAnimatedTransition.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2D212B8446BB00106FC7 /* ZLPhotoPreviewAnimatedTransition.swift */; };
		FB3E34B22B8446BF00106FC7 /* ZLClipImageDismissAnimatedTransition.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2D222B8446BB00106FC7 /* ZLClipImageDismissAnimatedTransition.swift */; };
		FB3E34B32B8446BF00106FC7 /* ZLPhotoPreviewPopInteractiveTransition.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2D232B8446BB00106FC7 /* ZLPhotoPreviewPopInteractiveTransition.swift */; };
		FB3E34B42B8446BF00106FC7 /* UIControl+ZLPhotoBrowser.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2D252B8446BB00106FC7 /* UIControl+ZLPhotoBrowser.swift */; };
		FB3E34B52B8446BF00106FC7 /* Bundle+ZLPhotoBrowser.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2D262B8446BB00106FC7 /* Bundle+ZLPhotoBrowser.swift */; };
		FB3E34B62B8446BF00106FC7 /* UIImage+ZLPhotoBrowser.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2D272B8446BB00106FC7 /* UIImage+ZLPhotoBrowser.swift */; };
		FB3E34B72B8446BF00106FC7 /* Int+ZLPhotoBrowser.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2D282B8446BB00106FC7 /* Int+ZLPhotoBrowser.swift */; };
		FB3E34B82B8446BF00106FC7 /* String+ZLPhotoBrowser.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2D292B8446BB00106FC7 /* String+ZLPhotoBrowser.swift */; };
		FB3E34B92B8446BF00106FC7 /* Cell+ZLPhotoBrowser.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2D2A2B8446BB00106FC7 /* Cell+ZLPhotoBrowser.swift */; };
		FB3E34BA2B8446BF00106FC7 /* Array+ZLPhotoBrowser.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2D2B2B8446BB00106FC7 /* Array+ZLPhotoBrowser.swift */; };
		FB3E34BB2B8446BF00106FC7 /* CGFloat+ZLPhotoBrowser.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2D2C2B8446BB00106FC7 /* CGFloat+ZLPhotoBrowser.swift */; };
		FB3E34BC2B8446BF00106FC7 /* PHAsset+ZLPhotoBrowser.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2D2D2B8446BB00106FC7 /* PHAsset+ZLPhotoBrowser.swift */; };
		FB3E34BD2B8446BF00106FC7 /* UIColor+ZLPhotoBrowser.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2D2E2B8446BB00106FC7 /* UIColor+ZLPhotoBrowser.swift */; };
		FB3E34BE2B8446BF00106FC7 /* ZLInputTextViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2D302B8446BB00106FC7 /* ZLInputTextViewController.swift */; };
		FB3E34BF2B8446BF00106FC7 /* ZLClipImageViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2D312B8446BB00106FC7 /* ZLClipImageViewController.swift */; };
		FB3E34C02B8446BF00106FC7 /* ZLImageStickerView.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2D322B8446BB00106FC7 /* ZLImageStickerView.swift */; };
		FB3E34C12B8446BF00106FC7 /* ZLEditImageViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2D332B8446BB00106FC7 /* ZLEditImageViewController.swift */; };
		FB3E34C22B8446BF00106FC7 /* ZLFilter.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2D342B8446BB00106FC7 /* ZLFilter.swift */; };
		FB3E34C32B8446BF00106FC7 /* ZLTextStickerView.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2D352B8446BB00106FC7 /* ZLTextStickerView.swift */; };
		FB3E34C42B8446BF00106FC7 /* ZLEditVideoViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2D362B8446BB00106FC7 /* ZLEditVideoViewController.swift */; };
		FB3E34C52B8446BF00106FC7 /* ZXImageBrowser.m in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2D372B8446BB00106FC7 /* ZXImageBrowser.m */; };
		FB3E34C62B8446BF00106FC7 /* TFHpple.m in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2D3B2B8446BB00106FC7 /* TFHpple.m */; };
		FB3E34C72B8446BF00106FC7 /* TFHppleElement.m in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2D3E2B8446BB00106FC7 /* TFHppleElement.m */; };
		FB3E34C82B8446BF00106FC7 /* XPathQuery.m in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2D3F2B8446BB00106FC7 /* XPathQuery.m */; };
		FB3E34CB2B8446BF00106FC7 /* BaseVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2D442B8446BC00106FC7 /* BaseVC.swift */; };
		FB3E34CD2B8446BF00106FC7 /* SRouteMediator.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2D462B8446BC00106FC7 /* SRouteMediator.swift */; };
		FB3E34CE2B8446BF00106FC7 /* LCTools.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2D482B8446BC00106FC7 /* LCTools.swift */; };
		FB3E34CF2B8446BF00106FC7 /* LCPayTool.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2D492B8446BC00106FC7 /* LCPayTool.swift */; };
		FB3E34D02B8446BF00106FC7 /* LCDevice.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2D4A2B8446BC00106FC7 /* LCDevice.swift */; };
		FB3E34D22B8446BF00106FC7 /* SColor.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2D4C2B8446BC00106FC7 /* SColor.swift */; };
		FB3E34D32B8446BF00106FC7 /* LCKey.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2D4D2B8446BC00106FC7 /* LCKey.swift */; };
		FB3E34D42B8446BF00106FC7 /* LCSingleton.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2D4E2B8446BC00106FC7 /* LCSingleton.swift */; };
		FB3E34D62B8446BF00106FC7 /* LCLog.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2D512B8446BC00106FC7 /* LCLog.swift */; };
		FB3E351B2B8446BF00106FC7 /* LiveScanVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2DAA2B8446BD00106FC7 /* LiveScanVC.swift */; };
		FB3E36912B8446C000106FC7 /* MainLoginVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2F762B8446BD00106FC7 /* MainLoginVC.swift */; };
		FB3E36922B8446C000106FC7 /* LoginVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2F772B8446BD00106FC7 /* LoginVC.swift */; };
		FB3E36942B8446C000106FC7 /* VerifyModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E2F7B2B8446BD00106FC7 /* VerifyModel.swift */; };
		FB3E379C2B8446C000106FC7 /* DataBaseManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E31092B8446BE00106FC7 /* DataBaseManager.swift */; };
		FB3E379F2B8446C000106FC7 /* UIImage+CVPixelBuffer.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E310F2B8446BE00106FC7 /* UIImage+CVPixelBuffer.swift */; };
		FB3E37A02B8446C000106FC7 /* StringExt.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E31112B8446BE00106FC7 /* StringExt.swift */; };
		FB3E37A12B8446C000106FC7 /* ButtonExt.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E31132B8446BE00106FC7 /* ButtonExt.swift */; };
		FB3E37A22B8446C000106FC7 /* UIViewExt.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E31142B8446BE00106FC7 /* UIViewExt.swift */; };
		FB3E37A42B8446C000106FC7 /* UIFont+AdjustFont.m in Sources */ = {isa = PBXBuildFile; fileRef = FB3E31162B8446BE00106FC7 /* UIFont+AdjustFont.m */; };
		FB3E37A52B8446C000106FC7 /* AVAssetExt.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E31172B8446BE00106FC7 /* AVAssetExt.swift */; };
		FB3E37A62B8446C000106FC7 /* NSURL+Hook.m in Sources */ = {isa = PBXBuildFile; fileRef = FB3E31182B8446BE00106FC7 /* NSURL+Hook.m */; };
		FB3E37A72B8446C000106FC7 /* ArrayExt.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E31192B8446BE00106FC7 /* ArrayExt.swift */; };
		FB3E37A82B8446C000106FC7 /* LPOperators.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E311A2B8446BE00106FC7 /* LPOperators.swift */; };
		FB3E37A92B8446C000106FC7 /* NSMutableArray+Hook.m in Sources */ = {isa = PBXBuildFile; fileRef = FB3E311B2B8446BE00106FC7 /* NSMutableArray+Hook.m */; };
		FB3E37AA2B8446C000106FC7 /* CGImage+CVPixelBuffer.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E311C2B8446BE00106FC7 /* CGImage+CVPixelBuffer.swift */; };
		FB3E37AB2B8446C000106FC7 /* UIResponder+Router.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E311D2B8446BE00106FC7 /* UIResponder+Router.swift */; };
		FB3E37AC2B8446C000106FC7 /* UIImageExt.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E311F2B8446BE00106FC7 /* UIImageExt.swift */; };
		FB3E37AF2B8446C000106FC7 /* FWRefreshHeader.m in Sources */ = {isa = PBXBuildFile; fileRef = FB3E31272B8446BE00106FC7 /* FWRefreshHeader.m */; };
		FB3E37B02B8446C000106FC7 /* SourceRefreshHeader.m in Sources */ = {isa = PBXBuildFile; fileRef = FB3E312A2B8446BE00106FC7 /* SourceRefreshHeader.m */; };
		FB3E37B12B8446C000106FC7 /* NetConnectionManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E312D2B8446BE00106FC7 /* NetConnectionManager.swift */; };
		FB3E37B22B8446C000106FC7 /* MiddleRequestNet.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E312E2B8446BE00106FC7 /* MiddleRequestNet.swift */; };
		FB3E37B82B8446C000106FC7 /* Result.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E31352B8446BE00106FC7 /* Result.swift */; };
		FB3E37B92B8446C000106FC7 /* APISession.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E31362B8446BE00106FC7 /* APISession.swift */; };
		FB3E37BA2B8446C000106FC7 /* BaseRespModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E31372B8446BE00106FC7 /* BaseRespModel.swift */; };
		FB3E37BB2B8446C000106FC7 /* APIURL.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E31382B8446BE00106FC7 /* APIURL.swift */; };
		FB3E37BC2B8446C000106FC7 /* GCDTimer.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E313A2B8446BE00106FC7 /* GCDTimer.swift */; };
		FB3E37BD2B8446C000106FC7 /* LPTimer.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E313B2B8446BE00106FC7 /* LPTimer.swift */; };
		FB3E37BE2B8446C000106FC7 /* HapTicFeedback.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E313C2B8446BE00106FC7 /* HapTicFeedback.swift */; };
		FB3E37BF2B8446C000106FC7 /* CustomURLProtocol.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E313F2B8446BE00106FC7 /* CustomURLProtocol.swift */; };
		FB3E37C02B8446C000106FC7 /* WebProgressLine.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E31402B8446BE00106FC7 /* WebProgressLine.swift */; };
		FB3E37C12B8446C000106FC7 /* WebVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E31412B8446BE00106FC7 /* WebVC.swift */; };
		FB3E37C32B8446C000106FC7 /* ParserTextField.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E31452B8446BE00106FC7 /* ParserTextField.swift */; };
		FB3E37C42B8446C000106FC7 /* GWReParser.m in Sources */ = {isa = PBXBuildFile; fileRef = FB3E31462B8446BE00106FC7 /* GWReParser.m */; };
		FB3E37C62B8446C000106FC7 /* JFRandom.m in Sources */ = {isa = PBXBuildFile; fileRef = FB3E314D2B8446BE00106FC7 /* JFRandom.m */; };
		FB3E37C72B8446C000106FC7 /* JFBCrypt.m in Sources */ = {isa = PBXBuildFile; fileRef = FB3E314E2B8446BE00106FC7 /* JFBCrypt.m */; };
		FB3E37C82B8446C000106FC7 /* XCFileManager.m in Sources */ = {isa = PBXBuildFile; fileRef = FB3E31502B8446BE00106FC7 /* XCFileManager.m */; };
		FB3E37C92B8446C000106FC7 /* CustomSlider.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E31532B8446BE00106FC7 /* CustomSlider.swift */; };
		FB3E37CA2B8446C000106FC7 /* SevenSwitch.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E31552B8446BE00106FC7 /* SevenSwitch.swift */; };
		FB3E37CC2B8446C000106FC7 /* MyCustomSliderView.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E31572B8446BE00106FC7 /* MyCustomSliderView.swift */; };
		FB3E37CF2B8446C000106FC7 /* VersionUpdateAlert.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E315A2B8446BE00106FC7 /* VersionUpdateAlert.swift */; };
		FB3E37D12B8446C000106FC7 /* CustomLayoutButton.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E315C2B8446BE00106FC7 /* CustomLayoutButton.swift */; };
		FB3E37D22B8446C000106FC7 /* LLAlertView.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E315D2B8446BE00106FC7 /* LLAlertView.swift */; };
		FB3E37D32B8446C000106FC7 /* HUD.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E315E2B8446BE00106FC7 /* HUD.swift */; };
		FB3E37D42B8446C000106FC7 /* NormalItemSelectSheet.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E315F2B8446BE00106FC7 /* NormalItemSelectSheet.swift */; };
		FB3E37D62B8446C000106FC7 /* AlertView.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E31612B8446BE00106FC7 /* AlertView.swift */; };
		FB3E37D72B8446C000106FC7 /* GestureGuideInfoView.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E31622B8446BE00106FC7 /* GestureGuideInfoView.swift */; };
		FB3E37D82B8446C000106FC7 /* PolicyAlert.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E31632B8446BE00106FC7 /* PolicyAlert.swift */; };
		FB3E37D92B8446C000106FC7 /* LiveRoomGuideView.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E31642B8446BE00106FC7 /* LiveRoomGuideView.swift */; };
		FB3E37DA2B8446C000106FC7 /* InputAlert.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E31652B8446BE00106FC7 /* InputAlert.swift */; };
		FB3E39662B8446C200106FC7 /* ESPThread.m in Sources */ = {isa = PBXBuildFile; fileRef = FB3E33EC2B8446BE00106FC7 /* ESPThread.m */; };
		FB3E39672B8446C200106FC7 /* FFPopup.m in Sources */ = {isa = PBXBuildFile; fileRef = FB3E33ED2B8446BE00106FC7 /* FFPopup.m */; };
		FB3E39682B8446C200106FC7 /* Throttler.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E33EF2B8446BE00106FC7 /* Throttler.swift */; };
		FB3E39692B8446C200106FC7 /* LPImageUtils.m in Sources */ = {isa = PBXBuildFile; fileRef = FB3E33F12B8446BE00106FC7 /* LPImageUtils.m */; };
		FB3E396F2B8446C200106FC7 /* GCDServices.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E33F92B8446BE00106FC7 /* GCDServices.swift */; };
		FB3E39702B8446C200106FC7 /* Debouncer.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB3E33FA2B8446BE00106FC7 /* Debouncer.swift */; };
		FB8043862B677FD200D9D456 /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB8043852B677FD200D9D456 /* AppDelegate.swift */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		7C8DA1C39BB13F9E1A2D41B3 /* Pods-liveplusexplore.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-liveplusexplore.debug.xcconfig"; path = "Target Support Files/Pods-liveplusexplore/Pods-liveplusexplore.debug.xcconfig"; sourceTree = "<group>"; };
		86C3122487679839A4909852 /* Pods-liveplusexplore.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-liveplusexplore.release.xcconfig"; path = "Target Support Files/Pods-liveplusexplore/Pods-liveplusexplore.release.xcconfig"; sourceTree = "<group>"; };
		8B70208A5C7FCD58B162B9A2 /* Pods-LiveConsole.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-LiveConsole.debug.xcconfig"; path = "Target Support Files/Pods-LiveConsole/Pods-LiveConsole.debug.xcconfig"; sourceTree = "<group>"; };
		9C629D9EE1C865A4E08CC3F4 /* Pods_LiveConsole.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_LiveConsole.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		AF191A172DB88C0D0066CA54 /* SmartNavigation.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SmartNavigation.swift; sourceTree = "<group>"; };
		AF191A1A2DB8CF520066CA54 /* SmartTextItem.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SmartTextItem.swift; sourceTree = "<group>"; };
		AF191A1C2DB8EA5A0066CA54 /* SmartKeywordVC+TextReplay.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "SmartKeywordVC+TextReplay.swift"; sourceTree = "<group>"; };
		AF191A1E2DB8EBC10066CA54 /* ReplyTextPlusView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ReplyTextPlusView.swift; sourceTree = "<group>"; };
		AF98263A2E2E0FD50040BA71 /* MinimaxVoiceService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MinimaxVoiceService.swift; sourceTree = "<group>"; };
		AF98263B2E2E0FD50040BA71 /* MinimaxVoiceServiceExample.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MinimaxVoiceServiceExample.swift; sourceTree = "<group>"; };
		F41483C92DE46928008B57C0 /* NickRealtimeView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NickRealtimeView.swift; sourceTree = "<group>"; };
		F42ACA8D2DEEB359006B4ED4 /* api_config.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = api_config.json; sourceTree = "<group>"; };
		F431BBB82DB77D1F00535D51 /* HudongAlertView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = HudongAlertView.swift; sourceTree = "<group>"; };
		F431BBBB2DB7879800535D51 /* SmartActionVC+Danmu.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "SmartActionVC+Danmu.swift"; sourceTree = "<group>"; };
		F431BBC12DB8C2AA00535D51 /* NetworkMonitorViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = NetworkMonitorViewController.swift; sourceTree = "<group>"; };
		F431BBC22DB8C2AA00535D51 /* WebInfoManager.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = WebInfoManager.swift; sourceTree = "<group>"; };
		F431BBC62DB8C2AA00535D51 /* buyinwebjs.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = buyinwebjs.swift; sourceTree = "<group>"; };
		F431BBD42DB8C2AA00535D51 /* DouyinGoodsView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = DouyinGoodsView.swift; sourceTree = "<group>"; };
		F431BBD62DB8C2AA00535D51 /* DoyinGoodsCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = DoyinGoodsCell.swift; sourceTree = "<group>"; };
		F431BBE12DB8C2AA00535D51 /* BuyinRequest.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = BuyinRequest.swift; sourceTree = "<group>"; };
		F431BC082DB8C3A400535D51 /* BuyinVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BuyinVC.swift; sourceTree = "<group>"; };
		F431BC0A2DB8D5C100535D51 /* BuyinModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BuyinModel.swift; sourceTree = "<group>"; };
		F431BC102DBB7D2F00535D51 /* DouyinAllGoodsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DouyinAllGoodsView.swift; sourceTree = "<group>"; };
		F45FD1A02DC0D2A400F7C58B /* MineTopView2.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MineTopView2.swift; sourceTree = "<group>"; };
		F4B7056B2DBF5FCA00A8F310 /* BuyinEmptyView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BuyinEmptyView.swift; sourceTree = "<group>"; };
		F4B7056D2DBF7A6B00A8F310 /* AIGiftReplyVc+TextReplay.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "AIGiftReplyVc+TextReplay.swift"; sourceTree = "<group>"; };
		F4B7056F2DBF7B2100A8F310 /* AIFollowReplyVc+TextReplay.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "AIFollowReplyVc+TextReplay.swift"; sourceTree = "<group>"; };
		F4B814EC2DD5C17800E5C3DC /* AIConfigVC+Backup.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "AIConfigVC+Backup.swift"; sourceTree = "<group>"; };
		F4B814FD2DDADB8100E5C3DC /* 播放中.gif */ = {isa = PBXFileReference; lastKnownFileType = image.gif; path = "播放中.gif"; sourceTree = "<group>"; };
		F4B814FF2DDB187500E5C3DC /* AVPlayManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AVPlayManager.swift; sourceTree = "<group>"; };
		F4B935E12DDDC0AC00FB9162 /* SmartCloneRecordBotView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SmartCloneRecordBotView.swift; sourceTree = "<group>"; };
		F4B935E32DDDC9B600FB9162 /* SmartClone3View.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SmartClone3View.swift; sourceTree = "<group>"; };
		F4B935E52DDDD0EE00FB9162 /* CloneStepView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CloneStepView.swift; sourceTree = "<group>"; };
		F4B935E72DDEFE5E00FB9162 /* MemberPointsPayVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MemberPointsPayVC.swift; sourceTree = "<group>"; };
		F4B935E92DDF019300FB9162 /* MemberPointsPayVC+Pay.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "MemberPointsPayVC+Pay.swift"; sourceTree = "<group>"; };
		F4B935EB2DDF166500FB9162 /* PointsPayCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PointsPayCell.swift; sourceTree = "<group>"; };
		F4B935ED2DE04EDA00FB9162 /* DouYinAutoSendMessage.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DouYinAutoSendMessage.swift; sourceTree = "<group>"; };
		F4C1987A2D116ABF00E28E81 /* LimitMemberCard3.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = LimitMemberCard3.swift; sourceTree = "<group>"; };
		F4C1987D2D116ABF00E28E81 /* LimitPurchaseVC+Pay.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = "LimitPurchaseVC+Pay.swift"; sourceTree = "<group>"; };
		F4C1987F2D116ABF00E28E81 /* LimitPurchaseVC2.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = LimitPurchaseVC2.swift; sourceTree = "<group>"; };
		F4C198812D116ABF00E28E81 /* LimitRightModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = LimitRightModel.swift; sourceTree = "<group>"; };
		F4C198822D116ABF00E28E81 /* WechatQRCodeView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = WechatQRCodeView.swift; sourceTree = "<group>"; };
		F4C198842D116ABF00E28E81 /* LimitModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = LimitModel.swift; sourceTree = "<group>"; };
		F4C198852D116ABF00E28E81 /* LimitModel+Alert.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = "LimitModel+Alert.swift"; sourceTree = "<group>"; };
		F4C198862D116ABF00E28E81 /* LimitModel+Logo.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = "LimitModel+Logo.swift"; sourceTree = "<group>"; };
		F4C198882D116ABF00E28E81 /* LimitModel+Operation.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = "LimitModel+Operation.swift"; sourceTree = "<group>"; };
		F4C198892D116ABF00E28E81 /* VIPAlert.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = VIPAlert.swift; sourceTree = "<group>"; };
		F4C198A32D12650B00E28E81 /* BaseTabBarViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = BaseTabBarViewController.swift; sourceTree = "<group>"; };
		F4C198A72D12850F00E28E81 /* BaseNavigationController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BaseNavigationController.swift; sourceTree = "<group>"; };
		F4C198AA2D12A21200E28E81 /* AccountVC.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AccountVC.swift; sourceTree = "<group>"; };
		F4C198AE2D12A21200E28E81 /* FeedbackVC.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = FeedbackVC.swift; sourceTree = "<group>"; };
		F4C198AF2D12A21200E28E81 /* InformationListVC.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = InformationListVC.swift; sourceTree = "<group>"; };
		F4C198B12D12A21200E28E81 /* LogOutVC.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = LogOutVC.swift; sourceTree = "<group>"; };
		F4C198B22D12A21200E28E81 /* MineVC.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MineVC.swift; sourceTree = "<group>"; };
		F4C198B62D12A21200E28E81 /* PhoneBindDoneVC.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = PhoneBindDoneVC.swift; sourceTree = "<group>"; };
		F4C198B72D12A21200E28E81 /* PhoneBindVC.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = PhoneBindVC.swift; sourceTree = "<group>"; };
		F4C198B82D12A21200E28E81 /* PrivacySettingVC.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = PrivacySettingVC.swift; sourceTree = "<group>"; };
		F4C198BA2D12A21200E28E81 /* SettingVC.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SettingVC.swift; sourceTree = "<group>"; };
		F4C198BC2D12A21200E28E81 /* UserInfoSettingVC.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = UserInfoSettingVC.swift; sourceTree = "<group>"; };
		F4C198BE2D12A21200E28E81 /* ConfigModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ConfigModel.swift; sourceTree = "<group>"; };
		F4C198C02D12A21200E28E81 /* InPurchasingModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = InPurchasingModel.swift; sourceTree = "<group>"; };
		F4C198C22D12A21200E28E81 /* UserInfo.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = UserInfo.swift; sourceTree = "<group>"; };
		F4C198C52D12A21200E28E81 /* InformationListCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = InformationListCell.swift; sourceTree = "<group>"; };
		F4C198C62D12A21200E28E81 /* InPurchasingBtnViewCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = InPurchasingBtnViewCell.swift; sourceTree = "<group>"; };
		F4C198C92D12A21200E28E81 /* MineCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MineCell.swift; sourceTree = "<group>"; };
		F4C198CA2D12A21200E28E81 /* MineTopView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MineTopView.swift; sourceTree = "<group>"; };
		F4C198CB2D12A21200E28E81 /* MineTopView.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = MineTopView.xib; sourceTree = "<group>"; };
		F4C198CC2D12A21200E28E81 /* OpeningMemberCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = OpeningMemberCell.swift; sourceTree = "<group>"; };
		F4C198CE2D12A21200E28E81 /* SettingCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SettingCell.swift; sourceTree = "<group>"; };
		F4C198D02D12A21200E28E81 /* VipServiceView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = VipServiceView.swift; sourceTree = "<group>"; };
		F4C274502DCB3E0400EA07A7 /* MemberPointsVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MemberPointsVC.swift; sourceTree = "<group>"; };
		F4C274522DCB43B200EA07A7 /* PointsCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PointsCell.swift; sourceTree = "<group>"; };
		F4C274552DCC465700EA07A7 /* PointsAlertView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PointsAlertView.swift; sourceTree = "<group>"; };
		F4C274582DD209A900EA07A7 /* LangduNickView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LangduNickView.swift; sourceTree = "<group>"; };
		F4C2745A2DD20E2500EA07A7 /* SmartActionVC+Realtime.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "SmartActionVC+Realtime.swift"; sourceTree = "<group>"; };
		F4C2745C2DD20F3E00EA07A7 /* RealtimeNickAlertView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RealtimeNickAlertView.swift; sourceTree = "<group>"; };
		F4C2746A2DD4784C00EA07A7 /* SmartCardTextView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SmartCardTextView.swift; sourceTree = "<group>"; };
		F4C2746C2DD47BAE00EA07A7 /* SmartCardTextCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SmartCardTextCell.swift; sourceTree = "<group>"; };
		F4D046492D1E84C000A57812 /* QRScanedView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = QRScanedView.swift; sourceTree = "<group>"; };
		F4D0464B2D1E8D4E00A57812 /* LoginScanVC.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = LoginScanVC.swift; sourceTree = "<group>"; };
		F4D413CA2DB62BF20036646F /* VeTOSiOSSDK.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = VeTOSiOSSDK.framework; sourceTree = "<group>"; };
		F4D413CD2DB62E560036646F /* BackupStepView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = BackupStepView.swift; sourceTree = "<group>"; };
		F4D413D12DB62E8A0036646F /* BackupLoadingView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = BackupLoadingView.swift; sourceTree = "<group>"; };
		F4D413D42DB62E8B0036646F /* BackupPasswordView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = BackupPasswordView.swift; sourceTree = "<group>"; };
		F4DE7E2A2DB232650021A153 /* LPSocketHelper.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = LPSocketHelper.h; sourceTree = "<group>"; };
		F4DE7E2B2DB232650021A153 /* LPSocketHelper.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = LPSocketHelper.m; sourceTree = "<group>"; };
		F4DFC59A2DB61E5900E003A0 /* AIAudioResultModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AIAudioResultModel.swift; sourceTree = "<group>"; };
		F4DFC59B2DB61E5900E003A0 /* AiAutoReplyModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AiAutoReplyModel.swift; sourceTree = "<group>"; };
		F4DFC59C2DB61E5900E003A0 /* AIReplyDataManager.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AIReplyDataManager.swift; sourceTree = "<group>"; };
		F4DFC59D2DB61E5900E003A0 /* AIReplyDataManager+Cache.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = "AIReplyDataManager+Cache.swift"; sourceTree = "<group>"; };
		F4DFC59E2DB61E5900E003A0 /* AudioPathStyle.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AudioPathStyle.swift; sourceTree = "<group>"; };
		F4DFC59F2DB61E5900E003A0 /* AudioSpkIdModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AudioSpkIdModel.swift; sourceTree = "<group>"; };
		F4DFC5A02DB61E5900E003A0 /* DouyinGiftModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = DouyinGiftModel.swift; sourceTree = "<group>"; };
		F4DFC5A12DB61E5900E003A0 /* DownloadFromBucket.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = DownloadFromBucket.swift; sourceTree = "<group>"; };
		F4DFC5A22DB61E5900E003A0 /* InteractionModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = InteractionModel.swift; sourceTree = "<group>"; };
		F4DFC5A42DB61E5900E003A0 /* AudioDurationHelper.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AudioDurationHelper.swift; sourceTree = "<group>"; };
		F4DFC5A52DB61E5900E003A0 /* AudioPermissionManager.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AudioPermissionManager.swift; sourceTree = "<group>"; };
		F4DFC5A62DB61E5900E003A0 /* AudioPlayManager.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AudioPlayManager.swift; sourceTree = "<group>"; };
		F4DFC5A72DB61E5900E003A0 /* AudioRecordManager.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AudioRecordManager.swift; sourceTree = "<group>"; };
		F4DFC5A82DB61E5900E003A0 /* RecordCountDownLabel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = RecordCountDownLabel.swift; sourceTree = "<group>"; };
		F4DFC5A92DB61E5900E003A0 /* SmartAlertView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SmartAlertView.swift; sourceTree = "<group>"; };
		F4DFC5AA2DB61E5900E003A0 /* UIView+ExtensionSmart.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = "UIView+ExtensionSmart.swift"; sourceTree = "<group>"; };
		F4DFC5AC2DB61E5900E003A0 /* SmartAudioItem.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SmartAudioItem.swift; sourceTree = "<group>"; };
		F4DFC5AD2DB61E5900E003A0 /* SmartKeywordItem.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SmartKeywordItem.swift; sourceTree = "<group>"; };
		F4DFC5AE2DB61E5900E003A0 /* SmartTTSAudioItem.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SmartTTSAudioItem.swift; sourceTree = "<group>"; };
		F4DFC5AF2DB61E5900E003A0 /* TTSPresetItem.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = TTSPresetItem.swift; sourceTree = "<group>"; };
		F4DFC5B12DB61E5900E003A0 /* AIPickerProgressView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AIPickerProgressView.swift; sourceTree = "<group>"; };
		F4DFC5B22DB61E5900E003A0 /* AudioCloneAgreementView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AudioCloneAgreementView.swift; sourceTree = "<group>"; };
		F4DFC5B32DB61E5900E003A0 /* AudioRateSelectionView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AudioRateSelectionView.swift; sourceTree = "<group>"; };
		F4DFC5B42DB61E5900E003A0 /* AudioSelectionView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AudioSelectionView.swift; sourceTree = "<group>"; };
		F4DFC5B52DB61E5900E003A0 /* AudioYinSeCloneView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AudioYinSeCloneView.swift; sourceTree = "<group>"; };
		F4DFC5B62DB61E5900E003A0 /* AudioYinSePresetView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AudioYinSePresetView.swift; sourceTree = "<group>"; };
		F4DFC5B72DB61E5900E003A0 /* AudioYinSeSelectionView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AudioYinSeSelectionView.swift; sourceTree = "<group>"; };
		F4DFC5B82DB61E5900E003A0 /* KeywordListView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = KeywordListView.swift; sourceTree = "<group>"; };
		F4DFC5B92DB61E5900E003A0 /* ReplayAudioView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ReplayAudioView.swift; sourceTree = "<group>"; };
		F4DFC5BA2DB61E5900E003A0 /* SmartCloneRecordTopView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SmartCloneRecordTopView.swift; sourceTree = "<group>"; };
		F4DFC5BB2DB61E5900E003A0 /* SmartRecordBotView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SmartRecordBotView.swift; sourceTree = "<group>"; };
		F4DFC5BC2DB61E5900E003A0 /* SmartRecordTopView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SmartRecordTopView.swift; sourceTree = "<group>"; };
		F4DFC5BD2DB61E5900E003A0 /* SmartTTSBotView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SmartTTSBotView.swift; sourceTree = "<group>"; };
		F4DFC5BE2DB61E5900E003A0 /* SmartTTSTopView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SmartTTSTopView.swift; sourceTree = "<group>"; };
		F4DFC5C02DB61E5900E003A0 /* SystemReplyTypeCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SystemReplyTypeCell.swift; sourceTree = "<group>"; };
		F4DFC5C12DB61E5900E003A0 /* SystemReplyTypeView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SystemReplyTypeView.swift; sourceTree = "<group>"; };
		F4DFC5C32DB61E5900E003A0 /* AIBackupView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AIBackupView.swift; sourceTree = "<group>"; };
		F4DFC5C42DB61E5900E003A0 /* AIConfigGiftView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AIConfigGiftView.swift; sourceTree = "<group>"; };
		F4DFC5C52DB61E5900E003A0 /* AIConfigReplyView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AIConfigReplyView.swift; sourceTree = "<group>"; };
		F4DFC5C62DB61E5900E003A0 /* AIConfigSetView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AIConfigSetView.swift; sourceTree = "<group>"; };
		F4DFC5C72DB61E5900E003A0 /* AIConfigSKPView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AIConfigSKPView.swift; sourceTree = "<group>"; };
		F4DFC5C92DB61E5900E003A0 /* GiftTimeCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = GiftTimeCell.swift; sourceTree = "<group>"; };
		F4DFC5CA2DB61E5900E003A0 /* GiftTimeView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = GiftTimeView.swift; sourceTree = "<group>"; };
		F4DFC5CC2DB61E5900E003A0 /* KeyWordInputView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = KeyWordInputView.swift; sourceTree = "<group>"; };
		F4DFC5CD2DB61E5900E003A0 /* SmartActionCardView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SmartActionCardView.swift; sourceTree = "<group>"; };
		F4DFC5CE2DB61E5900E003A0 /* SmartActionNavigation.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SmartActionNavigation.swift; sourceTree = "<group>"; };
		F4DFC5CF2DB61E5900E003A0 /* SmartActionToolBar.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SmartActionToolBar.swift; sourceTree = "<group>"; };
		F4DFC5D02DB61E5900E003A0 /* SmartCardAudioCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SmartCardAudioCell.swift; sourceTree = "<group>"; };
		F4DFC5D12DB61E5900E003A0 /* SmartCardAudioView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SmartCardAudioView.swift; sourceTree = "<group>"; };
		F4DFC5D22DB61E5900E003A0 /* SmartCardFollowView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SmartCardFollowView.swift; sourceTree = "<group>"; };
		F4DFC5D32DB61E5900E003A0 /* SmartCardGiftView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SmartCardGiftView.swift; sourceTree = "<group>"; };
		F4DFC5D42DB61E5900E003A0 /* SmartCardKeyWordView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SmartCardKeyWordView.swift; sourceTree = "<group>"; };
		F4DFC5D52DB61E5900E003A0 /* SmartCardVideoView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SmartCardVideoView.swift; sourceTree = "<group>"; };
		F4DFC5D62DB61E5900E003A0 /* SmartDetailFourNavigation.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SmartDetailFourNavigation.swift; sourceTree = "<group>"; };
		F4DFC5D72DB61E5900E003A0 /* SmartDetailNavigation.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SmartDetailNavigation.swift; sourceTree = "<group>"; };
		F4DFC5D82DB61E5900E003A0 /* SmartDetailThreeNavigation.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SmartDetailThreeNavigation.swift; sourceTree = "<group>"; };
		F4DFC5DA2DB61E5900E003A0 /* AIConfigVC.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AIConfigVC.swift; sourceTree = "<group>"; };
		F4DFC5DB2DB61E5900E003A0 /* AIFollowReplyVc.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AIFollowReplyVc.swift; sourceTree = "<group>"; };
		F4DFC5DC2DB61E5900E003A0 /* AIFollowReplyVc+AudioReply.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = "AIFollowReplyVc+AudioReply.swift"; sourceTree = "<group>"; };
		F4DFC5DD2DB61E5900E003A0 /* SmartActionVC.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SmartActionVC.swift; sourceTree = "<group>"; };
		F4DFC5DE2DB61E5900E003A0 /* SmartActionVC+Backup.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = "SmartActionVC+Backup.swift"; sourceTree = "<group>"; };
		F4DFC5DF2DB61E5900E003A0 /* SmartActionVC+Tool.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = "SmartActionVC+Tool.swift"; sourceTree = "<group>"; };
		F4DFC5E02DB61E5900E003A0 /* SmartCloneEditVC.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SmartCloneEditVC.swift; sourceTree = "<group>"; };
		F4DFC5E12DB61E5900E003A0 /* SmartKeywordVC.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SmartKeywordVC.swift; sourceTree = "<group>"; };
		F4DFC5E22DB61E5900E003A0 /* SmartKeywordVC+AudioReply.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = "SmartKeywordVC+AudioReply.swift"; sourceTree = "<group>"; };
		F4DFC5E32DB61E5900E003A0 /* SmartKeywordVC+Keyword.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = "SmartKeywordVC+Keyword.swift"; sourceTree = "<group>"; };
		F4DFC5E42DB61E5900E003A0 /* SmartRecordAudioVC.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SmartRecordAudioVC.swift; sourceTree = "<group>"; };
		F4DFC5E52DB61E5900E003A0 /* SmartTTSAudioVC.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SmartTTSAudioVC.swift; sourceTree = "<group>"; };
		F4DFC5E62DB61E5900E003A0 /* SmartTTSAudioVC+Request.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = "SmartTTSAudioVC+Request.swift"; sourceTree = "<group>"; };
		F4DFC5E82DB61E5900E003A0 /* AiAnswerFactory.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AiAnswerFactory.swift; sourceTree = "<group>"; };
		F4DFC5E92DB61E5900E003A0 /* AiAnswerFactory+TTS.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = "AiAnswerFactory+TTS.swift"; sourceTree = "<group>"; };
		F4DFC5EA2DB61E5900E003A0 /* SmartBaseView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SmartBaseView.swift; sourceTree = "<group>"; };
		F4DFC5EB2DB61E5900E003A0 /* SmartBaseViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SmartBaseViewController.swift; sourceTree = "<group>"; };
		F4DFC6382DB61EED00E003A0 /* douyin.pb.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = douyin.pb.swift; sourceTree = "<group>"; };
		F4DFC6392DB61EED00E003A0 /* DouyinDecode.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = DouyinDecode.swift; sourceTree = "<group>"; };
		F4DFC63A2DB61EED00E003A0 /* DouyinMessageData.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = DouyinMessageData.swift; sourceTree = "<group>"; };
		F4DFC63C2DB61EED00E003A0 /* LiveMessage.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = LiveMessage.swift; sourceTree = "<group>"; };
		F4DFC63E2DB61EED00E003A0 /* AnchorInfoResponse.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AnchorInfoResponse.swift; sourceTree = "<group>"; };
		F4DFC63F2DB61EED00E003A0 /* BaseLivingResponse.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = BaseLivingResponse.swift; sourceTree = "<group>"; };
		F4DFC6402DB61EED00E003A0 /* FansAgeDistriResponse.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = FansAgeDistriResponse.swift; sourceTree = "<group>"; };
		F4DFC6412DB61EED00E003A0 /* FansAnalysisResponse.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = FansAnalysisResponse.swift; sourceTree = "<group>"; };
		F4DFC6422DB61EED00E003A0 /* FansGenderDistriResponse.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = FansGenderDistriResponse.swift; sourceTree = "<group>"; };
		F4DFC6432DB61EED00E003A0 /* FansInterestDistriResponse.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = FansInterestDistriResponse.swift; sourceTree = "<group>"; };
		F4DFC6442DB61EED00E003A0 /* FansTrendResponse.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = FansTrendResponse.swift; sourceTree = "<group>"; };
		F4DFC6452DB61EED00E003A0 /* HistoryDetailResponse.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = HistoryDetailResponse.swift; sourceTree = "<group>"; };
		F4DFC6462DB61EED00E003A0 /* HistoryKeyFragmentResponse.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = HistoryKeyFragmentResponse.swift; sourceTree = "<group>"; };
		F4DFC6472DB61EED00E003A0 /* HistoryListResponse.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = HistoryListResponse.swift; sourceTree = "<group>"; };
		F4DFC6482DB61EED00E003A0 /* HistoryNewFansConvResponse.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = HistoryNewFansConvResponse.swift; sourceTree = "<group>"; };
		F4DFC6492DB61EED00E003A0 /* HistoryOverviewResponse.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = HistoryOverviewResponse.swift; sourceTree = "<group>"; };
		F4DFC64A2DB61EED00E003A0 /* HistoryPayAudienceConvResponse.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = HistoryPayAudienceConvResponse.swift; sourceTree = "<group>"; };
		F4DFC64B2DB61EED00E003A0 /* HistoryStatTopListResponse.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = HistoryStatTopListResponse.swift; sourceTree = "<group>"; };
		F4DFC64C2DB61EED00E003A0 /* LiveDataBaseResponse.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = LiveDataBaseResponse.swift; sourceTree = "<group>"; };
		F4DFC64D2DB61EED00E003A0 /* LiveStatusResponse.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = LiveStatusResponse.swift; sourceTree = "<group>"; };
		F4DFC64E2DB61EED00E003A0 /* LivingConversionResponse.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = LivingConversionResponse.swift; sourceTree = "<group>"; };
		F4DFC64F2DB61EED00E003A0 /* LivingEntranceResponse.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = LivingEntranceResponse.swift; sourceTree = "<group>"; };
		F4DFC6502DB61EED00E003A0 /* LivingRealTimeResponse.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = LivingRealTimeResponse.swift; sourceTree = "<group>"; };
		F4DFC6512DB61EED00E003A0 /* MessageListResponse.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MessageListResponse.swift; sourceTree = "<group>"; };
		F4DFC6522DB61EED00E003A0 /* SendMessageResponse.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SendMessageResponse.swift; sourceTree = "<group>"; };
		F4DFC6542DB61EED00E003A0 /* api_config.enc */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = api_config.enc; sourceTree = "<group>"; };
		F4DFC6552DB61EED00E003A0 /* APIConfigurationManager.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = APIConfigurationManager.swift; sourceTree = "<group>"; };
		F4DFC6562DB61EED00E003A0 /* APIStyle.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = APIStyle.swift; sourceTree = "<group>"; };
		F4DFC6572DB61EED00E003A0 /* CryptoUtil.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CryptoUtil.swift; sourceTree = "<group>"; };
		F4DFC6582DB61EED00E003A0 /* js_config.enc */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = js_config.enc; sourceTree = "<group>"; };
		F4DFC6592DB61EED00E003A0 /* LiveStatusListen.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = LiveStatusListen.swift; sourceTree = "<group>"; };
		F4DFC65A2DB61EED00E003A0 /* RequestManager.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = RequestManager.swift; sourceTree = "<group>"; };
		F4DFC65C2DB61EED00E003A0 /* CookieInfo.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CookieInfo.swift; sourceTree = "<group>"; };
		F4DFC65D2DB61EED00E003A0 /* CookieRequestWebView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CookieRequestWebView.swift; sourceTree = "<group>"; };
		F4DFC65E2DB61EED00E003A0 /* JSSingleton.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = JSSingleton.swift; sourceTree = "<group>"; };
		F4DFC65F2DB61EED00E003A0 /* NetworkMonitor.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = NetworkMonitor.swift; sourceTree = "<group>"; };
		F4DFC6602DB61EED00E003A0 /* webjs.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = webjs.swift; sourceTree = "<group>"; };
		F4DFC6892DB61F2C00E003A0 /* AutoReplyManager.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AutoReplyManager.swift; sourceTree = "<group>"; };
		F4DFC68A2DB61F2C00E003A0 /* DouyinMessageHanlerQueue.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = DouyinMessageHanlerQueue.swift; sourceTree = "<group>"; };
		F4DFC68B2DB61F2C00E003A0 /* ReplyManager.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ReplyManager.swift; sourceTree = "<group>"; };
		F4DFC68D2DB61F2C00E003A0 /* AIGiftReplyVc.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AIGiftReplyVc.swift; sourceTree = "<group>"; };
		F4DFC68E2DB61F2C00E003A0 /* AIGiftReplyVc+AudioReply.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = "AIGiftReplyVc+AudioReply.swift"; sourceTree = "<group>"; };
		F4DFC6972DB61F2C00E003A0 /* AIGiftReplyContentView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AIGiftReplyContentView.swift; sourceTree = "<group>"; };
		F4DFC6982DB61F2C00E003A0 /* AIGiftReplyView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AIGiftReplyView.swift; sourceTree = "<group>"; };
		F4DFC6992DB61F2C00E003A0 /* DouyinGiftCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = DouyinGiftCell.swift; sourceTree = "<group>"; };
		F4DFC69A2DB61F2C00E003A0 /* DouyinGiftListView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = DouyinGiftListView.swift; sourceTree = "<group>"; };
		F4DFC69B2DB61F2C00E003A0 /* GiftFilterCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = GiftFilterCell.swift; sourceTree = "<group>"; };
		F4DFC69C2DB61F2C00E003A0 /* GiftFilterView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = GiftFilterView.swift; sourceTree = "<group>"; };
		F4DFC69D2DB61F2C00E003A0 /* GiftReplyAudioView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = GiftReplyAudioView.swift; sourceTree = "<group>"; };
		F4DFC69E2DB61F2C00E003A0 /* GiftReplyItemView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = GiftReplyItemView.swift; sourceTree = "<group>"; };
		F4DFC6BB2DB6240900E003A0 /* AreaItemModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AreaItemModel.swift; sourceTree = "<group>"; };
		F8303A47D0B3A8E3C973E9DD /* Pods-LiveConsole.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-LiveConsole.release.xcconfig"; path = "Target Support Files/Pods-LiveConsole/Pods-LiveConsole.release.xcconfig"; sourceTree = "<group>"; };
		FB37BA852B88793A002ADEFA /* LiveConsoleRelease.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = LiveConsoleRelease.entitlements; sourceTree = "<group>"; };
		FB37BA862B887986002ADEFA /* LiveConsoleDebug.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = LiveConsoleDebug.entitlements; sourceTree = "<group>"; };
		FB3E2C312B8446BB00106FC7 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		FB3E2C372B8446BB00106FC7 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreenJ.storyboard; sourceTree = "<group>"; };
		FB3E2C392B8446BB00106FC7 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		FB3E2C9B2B8446BB00106FC7 /* StatisticsEvents.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = StatisticsEvents.swift; sourceTree = "<group>"; };
		FB3E2C9C2B8446BB00106FC7 /* LPStatistics.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = LPStatistics.swift; sourceTree = "<group>"; };
		FB3E2C9E2B8446BB00106FC7 /* LXReorderableCollectionViewFlowLayout.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = LXReorderableCollectionViewFlowLayout.h; sourceTree = "<group>"; };
		FB3E2C9F2B8446BB00106FC7 /* LXReorderableCollectionViewFlowLayout.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = LXReorderableCollectionViewFlowLayout.m; sourceTree = "<group>"; };
		FB3E2CA22B8446BB00106FC7 /* BRResultModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BRResultModel.m; sourceTree = "<group>"; };
		FB3E2CA32B8446BB00106FC7 /* BRStringPickerView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BRStringPickerView.h; sourceTree = "<group>"; };
		FB3E2CA42B8446BB00106FC7 /* BRResultModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BRResultModel.h; sourceTree = "<group>"; };
		FB3E2CA52B8446BB00106FC7 /* BRStringPickerView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BRStringPickerView.m; sourceTree = "<group>"; };
		FB3E2CA62B8446BB00106FC7 /* BRPickerView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BRPickerView.h; sourceTree = "<group>"; };
		FB3E2CA82B8446BB00106FC7 /* BRDatePickerView+BR.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "BRDatePickerView+BR.m"; sourceTree = "<group>"; };
		FB3E2CA92B8446BB00106FC7 /* BRDatePickerView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BRDatePickerView.h; sourceTree = "<group>"; };
		FB3E2CAA2B8446BB00106FC7 /* NSDate+BRPickerView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSDate+BRPickerView.m"; sourceTree = "<group>"; };
		FB3E2CAB2B8446BB00106FC7 /* BRDatePickerView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BRDatePickerView.m; sourceTree = "<group>"; };
		FB3E2CAC2B8446BB00106FC7 /* BRDatePickerView+BR.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "BRDatePickerView+BR.h"; sourceTree = "<group>"; };
		FB3E2CAD2B8446BB00106FC7 /* NSDate+BRPickerView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSDate+BRPickerView.h"; sourceTree = "<group>"; };
		FB3E2CAF2B8446BB00106FC7 /* NSBundle+BRPickerView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSBundle+BRPickerView.h"; sourceTree = "<group>"; };
		FB3E2CB02B8446BB00106FC7 /* BRPickerViewMacro.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BRPickerViewMacro.h; sourceTree = "<group>"; };
		FB3E2CB12B8446BB00106FC7 /* BRPickerView.bundle */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.plug-in"; path = BRPickerView.bundle; sourceTree = "<group>"; };
		FB3E2CB22B8446BB00106FC7 /* BRPickerStyle.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BRPickerStyle.m; sourceTree = "<group>"; };
		FB3E2CB32B8446BB00106FC7 /* BRBaseView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BRBaseView.m; sourceTree = "<group>"; };
		FB3E2CB42B8446BB00106FC7 /* NSBundle+BRPickerView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSBundle+BRPickerView.m"; sourceTree = "<group>"; };
		FB3E2CB52B8446BB00106FC7 /* BRBaseView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BRBaseView.h; sourceTree = "<group>"; };
		FB3E2CB62B8446BB00106FC7 /* BRPickerStyle.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BRPickerStyle.h; sourceTree = "<group>"; };
		FB3E2CB82B8446BB00106FC7 /* BRAddressModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BRAddressModel.m; sourceTree = "<group>"; };
		FB3E2CB92B8446BB00106FC7 /* BRAddressPickerView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BRAddressPickerView.h; sourceTree = "<group>"; };
		FB3E2CBA2B8446BB00106FC7 /* BRAddressModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BRAddressModel.h; sourceTree = "<group>"; };
		FB3E2CBB2B8446BB00106FC7 /* BRAddressPickerView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BRAddressPickerView.m; sourceTree = "<group>"; };
		FB3E2CBD2B8446BB00106FC7 /* CircleButton.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CircleButton.m; sourceTree = "<group>"; };
		FB3E2CBE2B8446BB00106FC7 /* IrregularButton.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = IrregularButton.m; sourceTree = "<group>"; };
		FB3E2CBF2B8446BB00106FC7 /* CircleButton.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CircleButton.h; sourceTree = "<group>"; };
		FB3E2CC02B8446BB00106FC7 /* IrregularButton.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IrregularButton.h; sourceTree = "<group>"; };
		FB3E2CC12B8446BB00106FC7 /* ZXImageBrowser.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ZXImageBrowser.h; sourceTree = "<group>"; };
		FB3E2CC32B8446BB00106FC7 /* XSDispatchQueue.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = XSDispatchQueue.h; sourceTree = "<group>"; };
		FB3E2CC42B8446BB00106FC7 /* XSPDispatchGroup.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = XSPDispatchGroup.m; sourceTree = "<group>"; };
		FB3E2CC52B8446BB00106FC7 /* XSDispatchGroup.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = XSDispatchGroup.h; sourceTree = "<group>"; };
		FB3E2CC62B8446BB00106FC7 /* XSDispatchQueue.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = XSDispatchQueue.m; sourceTree = "<group>"; };
		FB3E2CC92B8446BB00106FC7 /* CYCropCornerView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CYCropCornerView.m; sourceTree = "<group>"; };
		FB3E2CCB2B8446BB00106FC7 /* CYCropCornerView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CYCropCornerView.h; sourceTree = "<group>"; };
		FB3E2CCE2B8446BB00106FC7 /* IFVWebSocket.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IFVWebSocket.h; sourceTree = "<group>"; };
		FB3E2CCF2B8446BB00106FC7 /* IFVSRSecurityPolicy.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = IFVSRSecurityPolicy.m; sourceTree = "<group>"; };
		FB3E2CD02B8446BB00106FC7 /* NSRunLoop+IFVWebSocket.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSRunLoop+IFVWebSocket.h"; sourceTree = "<group>"; };
		FB3E2CD22B8446BB00106FC7 /* NSRunLoop+IFVWebSocketPrivate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSRunLoop+IFVWebSocketPrivate.h"; sourceTree = "<group>"; };
		FB3E2CD32B8446BB00106FC7 /* IFVConstants.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = IFVConstants.m; sourceTree = "<group>"; };
		FB3E2CD42B8446BB00106FC7 /* NSURLRequest+IFVWebSocketPrivate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSURLRequest+IFVWebSocketPrivate.h"; sourceTree = "<group>"; };
		FB3E2CD62B8446BB00106FC7 /* IFVProxyConnect.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IFVProxyConnect.h; sourceTree = "<group>"; };
		FB3E2CD72B8446BB00106FC7 /* IFVProxyConnect.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = IFVProxyConnect.m; sourceTree = "<group>"; };
		FB3E2CD92B8446BB00106FC7 /* IFVRunLoopThread.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IFVRunLoopThread.h; sourceTree = "<group>"; };
		FB3E2CDA2B8446BB00106FC7 /* IFVRunLoopThread.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = IFVRunLoopThread.m; sourceTree = "<group>"; };
		FB3E2CDC2B8446BB00106FC7 /* IFVPinningSecurityPolicy.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = IFVPinningSecurityPolicy.m; sourceTree = "<group>"; };
		FB3E2CDD2B8446BB00106FC7 /* IFVPinningSecurityPolicy.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IFVPinningSecurityPolicy.h; sourceTree = "<group>"; };
		FB3E2CDF2B8446BB00106FC7 /* IFVDelegateController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IFVDelegateController.h; sourceTree = "<group>"; };
		FB3E2CE02B8446BB00106FC7 /* IFVDelegateController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = IFVDelegateController.m; sourceTree = "<group>"; };
		FB3E2CE22B8446BB00106FC7 /* IFVURLUtilities.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IFVURLUtilities.h; sourceTree = "<group>"; };
		FB3E2CE32B8446BB00106FC7 /* IFVRandom.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = IFVRandom.m; sourceTree = "<group>"; };
		FB3E2CE42B8446BB00106FC7 /* IFVError.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IFVError.h; sourceTree = "<group>"; };
		FB3E2CE52B8446BB00106FC7 /* IFVSIMDHelpers.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = IFVSIMDHelpers.m; sourceTree = "<group>"; };
		FB3E2CE62B8446BB00106FC7 /* IFVLog.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = IFVLog.m; sourceTree = "<group>"; };
		FB3E2CE72B8446BB00106FC7 /* IFVHTTPConnectMessage.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IFVHTTPConnectMessage.h; sourceTree = "<group>"; };
		FB3E2CE82B8446BB00106FC7 /* IFVHash.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IFVHash.h; sourceTree = "<group>"; };
		FB3E2CE92B8446BB00106FC7 /* IFVMutex.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = IFVMutex.m; sourceTree = "<group>"; };
		FB3E2CEA2B8446BB00106FC7 /* IFVRandom.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IFVRandom.h; sourceTree = "<group>"; };
		FB3E2CEB2B8446BB00106FC7 /* IFVURLUtilities.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = IFVURLUtilities.m; sourceTree = "<group>"; };
		FB3E2CEC2B8446BB00106FC7 /* IFVError.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = IFVError.m; sourceTree = "<group>"; };
		FB3E2CED2B8446BB00106FC7 /* IFVHTTPConnectMessage.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = IFVHTTPConnectMessage.m; sourceTree = "<group>"; };
		FB3E2CEE2B8446BB00106FC7 /* IFVLog.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IFVLog.h; sourceTree = "<group>"; };
		FB3E2CEF2B8446BB00106FC7 /* IFVSIMDHelpers.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IFVSIMDHelpers.h; sourceTree = "<group>"; };
		FB3E2CF02B8446BB00106FC7 /* IFVMutex.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IFVMutex.h; sourceTree = "<group>"; };
		FB3E2CF12B8446BB00106FC7 /* IFVHash.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = IFVHash.m; sourceTree = "<group>"; };
		FB3E2CF32B8446BB00106FC7 /* IFVIOConsumerPool.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = IFVIOConsumerPool.m; sourceTree = "<group>"; };
		FB3E2CF42B8446BB00106FC7 /* IFVIOConsumer.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = IFVIOConsumer.m; sourceTree = "<group>"; };
		FB3E2CF52B8446BB00106FC7 /* IFVIOConsumerPool.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IFVIOConsumerPool.h; sourceTree = "<group>"; };
		FB3E2CF62B8446BB00106FC7 /* IFVIOConsumer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IFVIOConsumer.h; sourceTree = "<group>"; };
		FB3E2CF72B8446BB00106FC7 /* IFVConstants.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IFVConstants.h; sourceTree = "<group>"; };
		FB3E2CF82B8446BB00106FC7 /* NSURLRequest+IFVWebSocket.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSURLRequest+IFVWebSocket.h"; sourceTree = "<group>"; };
		FB3E2CF92B8446BB00106FC7 /* IFVSRSecurityPolicy.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IFVSRSecurityPolicy.h; sourceTree = "<group>"; };
		FB3E2CFA2B8446BB00106FC7 /* IFVWebSocket.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = IFVWebSocket.m; sourceTree = "<group>"; };
		FB3E2CFB2B8446BB00106FC7 /* IFVSocketRocket.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IFVSocketRocket.h; sourceTree = "<group>"; };
		FB3E2CFC2B8446BB00106FC7 /* NSRunLoop+IFVWebSocket.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSRunLoop+IFVWebSocket.m"; sourceTree = "<group>"; };
		FB3E2CFD2B8446BB00106FC7 /* NSURLRequest+IFVWebSocket.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSURLRequest+IFVWebSocket.m"; sourceTree = "<group>"; };
		FB3E2CFF2B8446BB00106FC7 /* YYFPSLabel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = YYFPSLabel.h; sourceTree = "<group>"; };
		FB3E2D002B8446BB00106FC7 /* YYFPSLabel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = YYFPSLabel.m; sourceTree = "<group>"; };
		FB3E2D022B8446BB00106FC7 /* ZLPhotoBrowser.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ZLPhotoBrowser.h; sourceTree = "<group>"; };
		FB3E2D032B8446BB00106FC7 /* ZLPhotoBrowser.bundle */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.plug-in"; path = ZLPhotoBrowser.bundle; sourceTree = "<group>"; };
		FB3E2D052B8446BB00106FC7 /* ZLImagePreviewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ZLImagePreviewController.swift; sourceTree = "<group>"; };
		FB3E2D062B8446BB00106FC7 /* ZLAlbumCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ZLAlbumCell.swift; sourceTree = "<group>"; };
		FB3E2D072B8446BB00106FC7 /* ZLPhotoManager.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ZLPhotoManager.swift; sourceTree = "<group>"; };
		FB3E2D082B8446BB00106FC7 /* ZLPhotoModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ZLPhotoModel.swift; sourceTree = "<group>"; };
		FB3E2D092B8446BB00106FC7 /* ZLPhotoPreviewCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ZLPhotoPreviewCell.swift; sourceTree = "<group>"; };
		FB3E2D0A2B8446BB00106FC7 /* ZLProgressHUD.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ZLProgressHUD.swift; sourceTree = "<group>"; };
		FB3E2D0B2B8446BB00106FC7 /* ZLFetchImageOperation.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ZLFetchImageOperation.swift; sourceTree = "<group>"; };
		FB3E2D0C2B8446BB00106FC7 /* ZLAlbumListController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ZLAlbumListController.swift; sourceTree = "<group>"; };
		FB3E2D0D2B8446BB00106FC7 /* ZLEmbedAlbumListView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ZLEmbedAlbumListView.swift; sourceTree = "<group>"; };
		FB3E2D0E2B8446BB00106FC7 /* ZLLanguageDefine.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ZLLanguageDefine.swift; sourceTree = "<group>"; };
		FB3E2D0F2B8446BB00106FC7 /* ZLAlbumListView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ZLAlbumListView.swift; sourceTree = "<group>"; };
		FB3E2D102B8446BB00106FC7 /* ZLPhotoPreviewSheet.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ZLPhotoPreviewSheet.swift; sourceTree = "<group>"; };
		FB3E2D112B8446BB00106FC7 /* ZLAlbumListCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ZLAlbumListCell.swift; sourceTree = "<group>"; };
		FB3E2D122B8446BB00106FC7 /* ZLPhotoPreviewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ZLPhotoPreviewController.swift; sourceTree = "<group>"; };
		FB3E2D132B8446BB00106FC7 /* ZLImageNavController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ZLImageNavController.swift; sourceTree = "<group>"; };
		FB3E2D142B8446BB00106FC7 /* ZLThumbnailViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ZLThumbnailViewController.swift; sourceTree = "<group>"; };
		FB3E2D152B8446BB00106FC7 /* ZLVideoManager.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ZLVideoManager.swift; sourceTree = "<group>"; };
		FB3E2D162B8446BB00106FC7 /* ZLAddPhotoCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ZLAddPhotoCell.swift; sourceTree = "<group>"; };
		FB3E2D172B8446BB00106FC7 /* ZLGeneralDefine.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ZLGeneralDefine.swift; sourceTree = "<group>"; };
		FB3E2D182B8446BB00106FC7 /* ZLThumbnailPhotoCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ZLThumbnailPhotoCell.swift; sourceTree = "<group>"; };
		FB3E2D192B8446BB00106FC7 /* ZLProgressView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ZLProgressView.swift; sourceTree = "<group>"; };
		FB3E2D1A2B8446BB00106FC7 /* ZLPhotoConfiguration.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ZLPhotoConfiguration.swift; sourceTree = "<group>"; };
		FB3E2D1B2B8446BB00106FC7 /* ZLPhotoBrowser.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ZLPhotoBrowser.swift; sourceTree = "<group>"; };
		FB3E2D1C2B8446BB00106FC7 /* ZLAlbumListModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ZLAlbumListModel.swift; sourceTree = "<group>"; };
		FB3E2D1D2B8446BB00106FC7 /* ZLCameraCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ZLCameraCell.swift; sourceTree = "<group>"; };
		FB3E2D1F2B8446BB00106FC7 /* ZLCustomCamera.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ZLCustomCamera.swift; sourceTree = "<group>"; };
		FB3E2D212B8446BB00106FC7 /* ZLPhotoPreviewAnimatedTransition.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ZLPhotoPreviewAnimatedTransition.swift; sourceTree = "<group>"; };
		FB3E2D222B8446BB00106FC7 /* ZLClipImageDismissAnimatedTransition.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ZLClipImageDismissAnimatedTransition.swift; sourceTree = "<group>"; };
		FB3E2D232B8446BB00106FC7 /* ZLPhotoPreviewPopInteractiveTransition.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ZLPhotoPreviewPopInteractiveTransition.swift; sourceTree = "<group>"; };
		FB3E2D252B8446BB00106FC7 /* UIControl+ZLPhotoBrowser.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = "UIControl+ZLPhotoBrowser.swift"; sourceTree = "<group>"; };
		FB3E2D262B8446BB00106FC7 /* Bundle+ZLPhotoBrowser.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = "Bundle+ZLPhotoBrowser.swift"; sourceTree = "<group>"; };
		FB3E2D272B8446BB00106FC7 /* UIImage+ZLPhotoBrowser.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = "UIImage+ZLPhotoBrowser.swift"; sourceTree = "<group>"; };
		FB3E2D282B8446BB00106FC7 /* Int+ZLPhotoBrowser.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = "Int+ZLPhotoBrowser.swift"; sourceTree = "<group>"; };
		FB3E2D292B8446BB00106FC7 /* String+ZLPhotoBrowser.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = "String+ZLPhotoBrowser.swift"; sourceTree = "<group>"; };
		FB3E2D2A2B8446BB00106FC7 /* Cell+ZLPhotoBrowser.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = "Cell+ZLPhotoBrowser.swift"; sourceTree = "<group>"; };
		FB3E2D2B2B8446BB00106FC7 /* Array+ZLPhotoBrowser.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = "Array+ZLPhotoBrowser.swift"; sourceTree = "<group>"; };
		FB3E2D2C2B8446BB00106FC7 /* CGFloat+ZLPhotoBrowser.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = "CGFloat+ZLPhotoBrowser.swift"; sourceTree = "<group>"; };
		FB3E2D2D2B8446BB00106FC7 /* PHAsset+ZLPhotoBrowser.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = "PHAsset+ZLPhotoBrowser.swift"; sourceTree = "<group>"; };
		FB3E2D2E2B8446BB00106FC7 /* UIColor+ZLPhotoBrowser.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = "UIColor+ZLPhotoBrowser.swift"; sourceTree = "<group>"; };
		FB3E2D302B8446BB00106FC7 /* ZLInputTextViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ZLInputTextViewController.swift; sourceTree = "<group>"; };
		FB3E2D312B8446BB00106FC7 /* ZLClipImageViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ZLClipImageViewController.swift; sourceTree = "<group>"; };
		FB3E2D322B8446BB00106FC7 /* ZLImageStickerView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ZLImageStickerView.swift; sourceTree = "<group>"; };
		FB3E2D332B8446BB00106FC7 /* ZLEditImageViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ZLEditImageViewController.swift; sourceTree = "<group>"; };
		FB3E2D342B8446BB00106FC7 /* ZLFilter.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ZLFilter.swift; sourceTree = "<group>"; };
		FB3E2D352B8446BB00106FC7 /* ZLTextStickerView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ZLTextStickerView.swift; sourceTree = "<group>"; };
		FB3E2D362B8446BB00106FC7 /* ZLEditVideoViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ZLEditVideoViewController.swift; sourceTree = "<group>"; };
		FB3E2D372B8446BB00106FC7 /* ZXImageBrowser.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ZXImageBrowser.m; sourceTree = "<group>"; };
		FB3E2D3A2B8446BB00106FC7 /* TFHppleElement.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TFHppleElement.h; sourceTree = "<group>"; };
		FB3E2D3B2B8446BB00106FC7 /* TFHpple.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TFHpple.m; sourceTree = "<group>"; };
		FB3E2D3C2B8446BB00106FC7 /* XPathQuery.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = XPathQuery.h; sourceTree = "<group>"; };
		FB3E2D3D2B8446BB00106FC7 /* TFHpple.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TFHpple.h; sourceTree = "<group>"; };
		FB3E2D3E2B8446BB00106FC7 /* TFHppleElement.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TFHppleElement.m; sourceTree = "<group>"; };
		FB3E2D3F2B8446BB00106FC7 /* XPathQuery.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = XPathQuery.m; sourceTree = "<group>"; };
		FB3E2D442B8446BC00106FC7 /* BaseVC.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = BaseVC.swift; sourceTree = "<group>"; };
		FB3E2D462B8446BC00106FC7 /* SRouteMediator.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SRouteMediator.swift; sourceTree = "<group>"; };
		FB3E2D482B8446BC00106FC7 /* LCTools.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = LCTools.swift; sourceTree = "<group>"; };
		FB3E2D492B8446BC00106FC7 /* LCPayTool.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = LCPayTool.swift; sourceTree = "<group>"; };
		FB3E2D4A2B8446BC00106FC7 /* LCDevice.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = LCDevice.swift; sourceTree = "<group>"; };
		FB3E2D4C2B8446BC00106FC7 /* SColor.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SColor.swift; sourceTree = "<group>"; };
		FB3E2D4D2B8446BC00106FC7 /* LCKey.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = LCKey.swift; sourceTree = "<group>"; };
		FB3E2D4E2B8446BC00106FC7 /* LCSingleton.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = LCSingleton.swift; sourceTree = "<group>"; };
		FB3E2D512B8446BC00106FC7 /* LCLog.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = LCLog.swift; sourceTree = "<group>"; };
		FB3E2DAA2B8446BD00106FC7 /* LiveScanVC.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = LiveScanVC.swift; sourceTree = "<group>"; };
		FB3E2F762B8446BD00106FC7 /* MainLoginVC.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MainLoginVC.swift; sourceTree = "<group>"; };
		FB3E2F772B8446BD00106FC7 /* LoginVC.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = LoginVC.swift; sourceTree = "<group>"; };
		FB3E2F7B2B8446BD00106FC7 /* VerifyModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = VerifyModel.swift; sourceTree = "<group>"; };
		FB3E31092B8446BE00106FC7 /* DataBaseManager.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = DataBaseManager.swift; sourceTree = "<group>"; };
		FB3E310B2B8446BE00106FC7 /* ESPThread.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ESPThread.h; sourceTree = "<group>"; };
		FB3E310F2B8446BE00106FC7 /* UIImage+CVPixelBuffer.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = "UIImage+CVPixelBuffer.swift"; sourceTree = "<group>"; };
		FB3E31102B8446BE00106FC7 /* NSURL+Hook.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSURL+Hook.h"; sourceTree = "<group>"; };
		FB3E31112B8446BE00106FC7 /* StringExt.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = StringExt.swift; sourceTree = "<group>"; };
		FB3E31122B8446BE00106FC7 /* NSMutableArray+Hook.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSMutableArray+Hook.h"; sourceTree = "<group>"; };
		FB3E31132B8446BE00106FC7 /* ButtonExt.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ButtonExt.swift; sourceTree = "<group>"; };
		FB3E31142B8446BE00106FC7 /* UIViewExt.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = UIViewExt.swift; sourceTree = "<group>"; };
		FB3E31162B8446BE00106FC7 /* UIFont+AdjustFont.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIFont+AdjustFont.m"; sourceTree = "<group>"; };
		FB3E31172B8446BE00106FC7 /* AVAssetExt.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AVAssetExt.swift; sourceTree = "<group>"; };
		FB3E31182B8446BE00106FC7 /* NSURL+Hook.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSURL+Hook.m"; sourceTree = "<group>"; };
		FB3E31192B8446BE00106FC7 /* ArrayExt.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ArrayExt.swift; sourceTree = "<group>"; };
		FB3E311A2B8446BE00106FC7 /* LPOperators.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = LPOperators.swift; sourceTree = "<group>"; };
		FB3E311B2B8446BE00106FC7 /* NSMutableArray+Hook.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSMutableArray+Hook.m"; sourceTree = "<group>"; };
		FB3E311C2B8446BE00106FC7 /* CGImage+CVPixelBuffer.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = "CGImage+CVPixelBuffer.swift"; sourceTree = "<group>"; };
		FB3E311D2B8446BE00106FC7 /* UIResponder+Router.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = "UIResponder+Router.swift"; sourceTree = "<group>"; };
		FB3E311E2B8446BE00106FC7 /* UIFont+AdjustFont.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIFont+AdjustFont.h"; sourceTree = "<group>"; };
		FB3E311F2B8446BE00106FC7 /* UIImageExt.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = UIImageExt.swift; sourceTree = "<group>"; };
		FB3E31272B8446BE00106FC7 /* FWRefreshHeader.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FWRefreshHeader.m; sourceTree = "<group>"; };
		FB3E31282B8446BE00106FC7 /* SourceRefreshHeader.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SourceRefreshHeader.h; sourceTree = "<group>"; };
		FB3E31292B8446BE00106FC7 /* FWRefreshHeader.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FWRefreshHeader.h; sourceTree = "<group>"; };
		FB3E312A2B8446BE00106FC7 /* SourceRefreshHeader.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SourceRefreshHeader.m; sourceTree = "<group>"; };
		FB3E312B2B8446BE00106FC7 /* route.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = route.h; sourceTree = "<group>"; };
		FB3E312D2B8446BE00106FC7 /* NetConnectionManager.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = NetConnectionManager.swift; sourceTree = "<group>"; };
		FB3E312E2B8446BE00106FC7 /* MiddleRequestNet.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MiddleRequestNet.swift; sourceTree = "<group>"; };
		FB3E31352B8446BE00106FC7 /* Result.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Result.swift; sourceTree = "<group>"; };
		FB3E31362B8446BE00106FC7 /* APISession.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = APISession.swift; sourceTree = "<group>"; };
		FB3E31372B8446BE00106FC7 /* BaseRespModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = BaseRespModel.swift; sourceTree = "<group>"; };
		FB3E31382B8446BE00106FC7 /* APIURL.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = APIURL.swift; sourceTree = "<group>"; };
		FB3E313A2B8446BE00106FC7 /* GCDTimer.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = GCDTimer.swift; sourceTree = "<group>"; };
		FB3E313B2B8446BE00106FC7 /* LPTimer.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = LPTimer.swift; sourceTree = "<group>"; };
		FB3E313C2B8446BE00106FC7 /* HapTicFeedback.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = HapTicFeedback.swift; sourceTree = "<group>"; };
		FB3E313D2B8446BE00106FC7 /* FFPopup.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FFPopup.h; sourceTree = "<group>"; };
		FB3E313F2B8446BE00106FC7 /* CustomURLProtocol.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CustomURLProtocol.swift; sourceTree = "<group>"; };
		FB3E31402B8446BE00106FC7 /* WebProgressLine.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = WebProgressLine.swift; sourceTree = "<group>"; };
		FB3E31412B8446BE00106FC7 /* WebVC.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = WebVC.swift; sourceTree = "<group>"; };
		FB3E31442B8446BE00106FC7 /* GWReParser.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GWReParser.h; sourceTree = "<group>"; };
		FB3E31452B8446BE00106FC7 /* ParserTextField.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ParserTextField.swift; sourceTree = "<group>"; };
		FB3E31462B8446BE00106FC7 /* GWReParser.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GWReParser.m; sourceTree = "<group>"; };
		FB3E31492B8446BE00106FC7 /* JFBCrypt.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JFBCrypt.h; sourceTree = "<group>"; };
		FB3E314A2B8446BE00106FC7 /* JFRandom.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JFRandom.h; sourceTree = "<group>"; };
		FB3E314B2B8446BE00106FC7 /* JFMacros.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JFMacros.h; sourceTree = "<group>"; };
		FB3E314C2B8446BE00106FC7 /* JFGC.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JFGC.h; sourceTree = "<group>"; };
		FB3E314D2B8446BE00106FC7 /* JFRandom.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JFRandom.m; sourceTree = "<group>"; };
		FB3E314E2B8446BE00106FC7 /* JFBCrypt.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JFBCrypt.m; sourceTree = "<group>"; };
		FB3E31502B8446BE00106FC7 /* XCFileManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = XCFileManager.m; sourceTree = "<group>"; };
		FB3E31512B8446BE00106FC7 /* XCFileManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = XCFileManager.h; sourceTree = "<group>"; };
		FB3E31532B8446BE00106FC7 /* CustomSlider.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CustomSlider.swift; sourceTree = "<group>"; };
		FB3E31552B8446BE00106FC7 /* SevenSwitch.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SevenSwitch.swift; sourceTree = "<group>"; };
		FB3E31572B8446BE00106FC7 /* MyCustomSliderView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MyCustomSliderView.swift; sourceTree = "<group>"; };
		FB3E315A2B8446BE00106FC7 /* VersionUpdateAlert.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = VersionUpdateAlert.swift; sourceTree = "<group>"; };
		FB3E315C2B8446BE00106FC7 /* CustomLayoutButton.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CustomLayoutButton.swift; sourceTree = "<group>"; };
		FB3E315D2B8446BE00106FC7 /* LLAlertView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = LLAlertView.swift; sourceTree = "<group>"; };
		FB3E315E2B8446BE00106FC7 /* HUD.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = HUD.swift; sourceTree = "<group>"; };
		FB3E315F2B8446BE00106FC7 /* NormalItemSelectSheet.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = NormalItemSelectSheet.swift; sourceTree = "<group>"; };
		FB3E31612B8446BE00106FC7 /* AlertView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AlertView.swift; sourceTree = "<group>"; };
		FB3E31622B8446BE00106FC7 /* GestureGuideInfoView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = GestureGuideInfoView.swift; sourceTree = "<group>"; };
		FB3E31632B8446BE00106FC7 /* PolicyAlert.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = PolicyAlert.swift; sourceTree = "<group>"; };
		FB3E31642B8446BE00106FC7 /* LiveRoomGuideView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = LiveRoomGuideView.swift; sourceTree = "<group>"; };
		FB3E31652B8446BE00106FC7 /* InputAlert.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = InputAlert.swift; sourceTree = "<group>"; };
		FB3E33EC2B8446BE00106FC7 /* ESPThread.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ESPThread.m; sourceTree = "<group>"; };
		FB3E33ED2B8446BE00106FC7 /* FFPopup.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FFPopup.m; sourceTree = "<group>"; };
		FB3E33EF2B8446BE00106FC7 /* Throttler.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Throttler.swift; sourceTree = "<group>"; };
		FB3E33F12B8446BE00106FC7 /* LPImageUtils.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = LPImageUtils.m; sourceTree = "<group>"; };
		FB3E33F62B8446BE00106FC7 /* LPImageUtils.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = LPImageUtils.h; sourceTree = "<group>"; };
		FB3E33F92B8446BE00106FC7 /* GCDServices.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = GCDServices.swift; sourceTree = "<group>"; };
		FB3E33FA2B8446BE00106FC7 /* Debouncer.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Debouncer.swift; sourceTree = "<group>"; };
		FB3E39722B84847800106FC7 /* LiveConsole-Bridging-Header.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "LiveConsole-Bridging-Header.h"; sourceTree = "<group>"; };
		FB8043822B677FD200D9D456 /* LiveConsole.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = LiveConsole.app; sourceTree = BUILT_PRODUCTS_DIR; };
		FB8043852B677FD200D9D456 /* AppDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		FB8043932B677FD400D9D456 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		FB80437F2B677FD200D9D456 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				120FF5C72C359370B811F191 /* Pods_LiveConsole.framework in Frameworks */,
				F4D413CB2DB62BF30036646F /* VeTOSiOSSDK.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		D297576DEFA655F5C5F52B35 /* Pods */ = {
			isa = PBXGroup;
			children = (
				7C8DA1C39BB13F9E1A2D41B3 /* Pods-liveplusexplore.debug.xcconfig */,
				86C3122487679839A4909852 /* Pods-liveplusexplore.release.xcconfig */,
				8B70208A5C7FCD58B162B9A2 /* Pods-LiveConsole.debug.xcconfig */,
				F8303A47D0B3A8E3C973E9DD /* Pods-LiveConsole.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
		E6E888001A8070322E889D73 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				9C629D9EE1C865A4E08CC3F4 /* Pods_LiveConsole.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		F431BBC32DB8C2AA00535D51 /* CookieManager */ = {
			isa = PBXGroup;
			children = (
				F431BBC12DB8C2AA00535D51 /* NetworkMonitorViewController.swift */,
				F431BBC22DB8C2AA00535D51 /* WebInfoManager.swift */,
			);
			path = CookieManager;
			sourceTree = "<group>";
		};
		F431BBC72DB8C2AA00535D51 /* DouyinJS */ = {
			isa = PBXGroup;
			children = (
				F431BBC62DB8C2AA00535D51 /* buyinwebjs.swift */,
			);
			path = DouyinJS;
			sourceTree = "<group>";
		};
		F431BBDA2DB8C2AA00535D51 /* View */ = {
			isa = PBXGroup;
			children = (
				F431BBD42DB8C2AA00535D51 /* DouyinGoodsView.swift */,
				F431BC102DBB7D2F00535D51 /* DouyinAllGoodsView.swift */,
				F431BBD62DB8C2AA00535D51 /* DoyinGoodsCell.swift */,
				F4B7056B2DBF5FCA00A8F310 /* BuyinEmptyView.swift */,
			);
			path = View;
			sourceTree = "<group>";
		};
		F431BBE02DB8C2AA00535D51 /* Douyin */ = {
			isa = PBXGroup;
			children = (
				F431BBC72DB8C2AA00535D51 /* DouyinJS */,
				F431BBDA2DB8C2AA00535D51 /* View */,
			);
			path = Douyin;
			sourceTree = "<group>";
		};
		F4C198832D116ABF00E28E81 /* PurchaseLimit */ = {
			isa = PBXGroup;
			children = (
				F4C1987A2D116ABF00E28E81 /* LimitMemberCard3.swift */,
				F4C1987D2D116ABF00E28E81 /* LimitPurchaseVC+Pay.swift */,
				F4C1987F2D116ABF00E28E81 /* LimitPurchaseVC2.swift */,
				F4C198812D116ABF00E28E81 /* LimitRightModel.swift */,
				F4C198822D116ABF00E28E81 /* WechatQRCodeView.swift */,
			);
			path = PurchaseLimit;
			sourceTree = "<group>";
		};
		F4C1988D2D116ABF00E28E81 /* LimitManager */ = {
			isa = PBXGroup;
			children = (
				F4C198832D116ABF00E28E81 /* PurchaseLimit */,
				F4C198842D116ABF00E28E81 /* LimitModel.swift */,
				F4C198852D116ABF00E28E81 /* LimitModel+Alert.swift */,
				F4C198862D116ABF00E28E81 /* LimitModel+Logo.swift */,
				F4C198882D116ABF00E28E81 /* LimitModel+Operation.swift */,
				F4C198892D116ABF00E28E81 /* VIPAlert.swift */,
			);
			path = LimitManager;
			sourceTree = "<group>";
		};
		F4C198BD2D12A21200E28E81 /* Controller */ = {
			isa = PBXGroup;
			children = (
				F4D0464B2D1E8D4E00A57812 /* LoginScanVC.swift */,
				F4C198AA2D12A21200E28E81 /* AccountVC.swift */,
				F4C198AE2D12A21200E28E81 /* FeedbackVC.swift */,
				F4C198AF2D12A21200E28E81 /* InformationListVC.swift */,
				F4C198B12D12A21200E28E81 /* LogOutVC.swift */,
				F4C198B22D12A21200E28E81 /* MineVC.swift */,
				F4C198B62D12A21200E28E81 /* PhoneBindDoneVC.swift */,
				F4C198B72D12A21200E28E81 /* PhoneBindVC.swift */,
				F4C198B82D12A21200E28E81 /* PrivacySettingVC.swift */,
				F4C198BA2D12A21200E28E81 /* SettingVC.swift */,
				F4C198BC2D12A21200E28E81 /* UserInfoSettingVC.swift */,
				F4C274502DCB3E0400EA07A7 /* MemberPointsVC.swift */,
				F4B935E72DDEFE5E00FB9162 /* MemberPointsPayVC.swift */,
				F4B935E92DDF019300FB9162 /* MemberPointsPayVC+Pay.swift */,
			);
			path = Controller;
			sourceTree = "<group>";
		};
		F4C198C32D12A21200E28E81 /* Model */ = {
			isa = PBXGroup;
			children = (
				F4C198BE2D12A21200E28E81 /* ConfigModel.swift */,
				F4C198C02D12A21200E28E81 /* InPurchasingModel.swift */,
				F4C198C22D12A21200E28E81 /* UserInfo.swift */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		F4C198D12D12A21200E28E81 /* View */ = {
			isa = PBXGroup;
			children = (
				F4C198C52D12A21200E28E81 /* InformationListCell.swift */,
				F4C198C62D12A21200E28E81 /* InPurchasingBtnViewCell.swift */,
				F4C198C92D12A21200E28E81 /* MineCell.swift */,
				F4C198CA2D12A21200E28E81 /* MineTopView.swift */,
				F4C198CB2D12A21200E28E81 /* MineTopView.xib */,
				F4C198CC2D12A21200E28E81 /* OpeningMemberCell.swift */,
				F4C198CE2D12A21200E28E81 /* SettingCell.swift */,
				F4C198D02D12A21200E28E81 /* VipServiceView.swift */,
				F45FD1A02DC0D2A400F7C58B /* MineTopView2.swift */,
				F4C274522DCB43B200EA07A7 /* PointsCell.swift */,
				F4C274552DCC465700EA07A7 /* PointsAlertView.swift */,
				F4B935EB2DDF166500FB9162 /* PointsPayCell.swift */,
			);
			path = View;
			sourceTree = "<group>";
		};
		F4C198D22D12A21200E28E81 /* Mine */ = {
			isa = PBXGroup;
			children = (
				F4C198BD2D12A21200E28E81 /* Controller */,
				F4C198C32D12A21200E28E81 /* Model */,
				F4C198D12D12A21200E28E81 /* View */,
			);
			path = Mine;
			sourceTree = "<group>";
		};
		F4D413CE2DB62E560036646F /* Backup */ = {
			isa = PBXGroup;
			children = (
				F431BBB82DB77D1F00535D51 /* HudongAlertView.swift */,
				F4D413D12DB62E8A0036646F /* BackupLoadingView.swift */,
				F4D413D42DB62E8B0036646F /* BackupPasswordView.swift */,
				F4D413CD2DB62E560036646F /* BackupStepView.swift */,
			);
			path = Backup;
			sourceTree = "<group>";
		};
		F4DFC5982DB61DCF00E003A0 /* Hudong */ = {
			isa = PBXGroup;
			children = (
				F4D413CE2DB62E560036646F /* Backup */,
				F4DFC6A22DB61F2C00E003A0 /* Trade */,
				F4DFC5EC2DB61E5900E003A0 /* SmartAction */,
			);
			path = Hudong;
			sourceTree = "<group>";
		};
		F4DFC5992DB61DED00E003A0 /* Jiangjie */ = {
			isa = PBXGroup;
			children = (
				F431BBC32DB8C2AA00535D51 /* CookieManager */,
				F431BBE02DB8C2AA00535D51 /* Douyin */,
				F431BC082DB8C3A400535D51 /* BuyinVC.swift */,
				F431BC0A2DB8D5C100535D51 /* BuyinModel.swift */,
				F431BBE12DB8C2AA00535D51 /* BuyinRequest.swift */,
			);
			path = Jiangjie;
			sourceTree = "<group>";
		};
		F4DFC5A32DB61E5900E003A0 /* Model */ = {
			isa = PBXGroup;
			children = (
				F4DFC59A2DB61E5900E003A0 /* AIAudioResultModel.swift */,
				F4DFC59B2DB61E5900E003A0 /* AiAutoReplyModel.swift */,
				F4DFC59C2DB61E5900E003A0 /* AIReplyDataManager.swift */,
				F4DFC59D2DB61E5900E003A0 /* AIReplyDataManager+Cache.swift */,
				F4DFC59E2DB61E5900E003A0 /* AudioPathStyle.swift */,
				F4DFC59F2DB61E5900E003A0 /* AudioSpkIdModel.swift */,
				F4DFC5A02DB61E5900E003A0 /* DouyinGiftModel.swift */,
				F4DFC5A12DB61E5900E003A0 /* DownloadFromBucket.swift */,
				F4DFC5A22DB61E5900E003A0 /* InteractionModel.swift */,
				F4DFC6BB2DB6240900E003A0 /* AreaItemModel.swift */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		F4DFC5AB2DB61E5900E003A0 /* Plugins */ = {
			isa = PBXGroup;
			children = (
				F4DFC5A42DB61E5900E003A0 /* AudioDurationHelper.swift */,
				F4DFC5A52DB61E5900E003A0 /* AudioPermissionManager.swift */,
				F4DFC5A62DB61E5900E003A0 /* AudioPlayManager.swift */,
				F4B814FF2DDB187500E5C3DC /* AVPlayManager.swift */,
				F4DFC5A72DB61E5900E003A0 /* AudioRecordManager.swift */,
				F4DFC5A82DB61E5900E003A0 /* RecordCountDownLabel.swift */,
				F4DFC5A92DB61E5900E003A0 /* SmartAlertView.swift */,
				F4DFC5AA2DB61E5900E003A0 /* UIView+ExtensionSmart.swift */,
			);
			path = Plugins;
			sourceTree = "<group>";
		};
		F4DFC5B02DB61E5900E003A0 /* Item */ = {
			isa = PBXGroup;
			children = (
				F4DFC5AC2DB61E5900E003A0 /* SmartAudioItem.swift */,
				AF191A1A2DB8CF520066CA54 /* SmartTextItem.swift */,
				F4DFC5AD2DB61E5900E003A0 /* SmartKeywordItem.swift */,
				F4DFC5AE2DB61E5900E003A0 /* SmartTTSAudioItem.swift */,
				F4DFC5AF2DB61E5900E003A0 /* TTSPresetItem.swift */,
			);
			path = Item;
			sourceTree = "<group>";
		};
		F4DFC5BF2DB61E5900E003A0 /* Keyword */ = {
			isa = PBXGroup;
			children = (
				F4DFC5B02DB61E5900E003A0 /* Item */,
				F4DFC5B12DB61E5900E003A0 /* AIPickerProgressView.swift */,
				F4DFC5B22DB61E5900E003A0 /* AudioCloneAgreementView.swift */,
				F4DFC5B32DB61E5900E003A0 /* AudioRateSelectionView.swift */,
				F4DFC5B42DB61E5900E003A0 /* AudioSelectionView.swift */,
				F4DFC5B52DB61E5900E003A0 /* AudioYinSeCloneView.swift */,
				F4DFC5B62DB61E5900E003A0 /* AudioYinSePresetView.swift */,
				F4DFC5B72DB61E5900E003A0 /* AudioYinSeSelectionView.swift */,
				F4DFC5B82DB61E5900E003A0 /* KeywordListView.swift */,
				F4DFC5B92DB61E5900E003A0 /* ReplayAudioView.swift */,
				AF191A1E2DB8EBC10066CA54 /* ReplyTextPlusView.swift */,
				F4DFC5BA2DB61E5900E003A0 /* SmartCloneRecordTopView.swift */,
				F4DFC5BB2DB61E5900E003A0 /* SmartRecordBotView.swift */,
				F4B935E12DDDC0AC00FB9162 /* SmartCloneRecordBotView.swift */,
				F4B935E32DDDC9B600FB9162 /* SmartClone3View.swift */,
				F4DFC5BC2DB61E5900E003A0 /* SmartRecordTopView.swift */,
				F4DFC5BD2DB61E5900E003A0 /* SmartTTSBotView.swift */,
				F4DFC5BE2DB61E5900E003A0 /* SmartTTSTopView.swift */,
				F4B935E52DDDD0EE00FB9162 /* CloneStepView.swift */,
			);
			path = Keyword;
			sourceTree = "<group>";
		};
		F4DFC5C22DB61E5900E003A0 /* System */ = {
			isa = PBXGroup;
			children = (
				F4DFC5C02DB61E5900E003A0 /* SystemReplyTypeCell.swift */,
				F4DFC5C12DB61E5900E003A0 /* SystemReplyTypeView.swift */,
				F4C2746C2DD47BAE00EA07A7 /* SmartCardTextCell.swift */,
			);
			path = System;
			sourceTree = "<group>";
		};
		F4DFC5D92DB61E5900E003A0 /* View */ = {
			isa = PBXGroup;
			children = (
				F4DFC5BF2DB61E5900E003A0 /* Keyword */,
				F4DFC5C22DB61E5900E003A0 /* System */,
				F4DFC5C32DB61E5900E003A0 /* AIBackupView.swift */,
				F4DFC5C42DB61E5900E003A0 /* AIConfigGiftView.swift */,
				F4DFC5C52DB61E5900E003A0 /* AIConfigReplyView.swift */,
				F4DFC5C62DB61E5900E003A0 /* AIConfigSetView.swift */,
				F4DFC5C72DB61E5900E003A0 /* AIConfigSKPView.swift */,
				F4DFC5C92DB61E5900E003A0 /* GiftTimeCell.swift */,
				F4DFC5CA2DB61E5900E003A0 /* GiftTimeView.swift */,
				F4DFC5CC2DB61E5900E003A0 /* KeyWordInputView.swift */,
				F4DFC5CD2DB61E5900E003A0 /* SmartActionCardView.swift */,
				F4DFC5CE2DB61E5900E003A0 /* SmartActionNavigation.swift */,
				F4DFC5CF2DB61E5900E003A0 /* SmartActionToolBar.swift */,
				F4DFC5D02DB61E5900E003A0 /* SmartCardAudioCell.swift */,
				F4DFC5D12DB61E5900E003A0 /* SmartCardAudioView.swift */,
				F4DFC5D22DB61E5900E003A0 /* SmartCardFollowView.swift */,
				F4DFC5D32DB61E5900E003A0 /* SmartCardGiftView.swift */,
				F4C2746A2DD4784C00EA07A7 /* SmartCardTextView.swift */,
				F4DFC5D42DB61E5900E003A0 /* SmartCardKeyWordView.swift */,
				F4DFC5D52DB61E5900E003A0 /* SmartCardVideoView.swift */,
				F4DFC5D62DB61E5900E003A0 /* SmartDetailFourNavigation.swift */,
				F4DFC5D72DB61E5900E003A0 /* SmartDetailNavigation.swift */,
				AF191A172DB88C0D0066CA54 /* SmartNavigation.swift */,
				F4DFC5D82DB61E5900E003A0 /* SmartDetailThreeNavigation.swift */,
				F4C274582DD209A900EA07A7 /* LangduNickView.swift */,
				F41483C92DE46928008B57C0 /* NickRealtimeView.swift */,
				F4C2745C2DD20F3E00EA07A7 /* RealtimeNickAlertView.swift */,
			);
			path = View;
			sourceTree = "<group>";
		};
		F4DFC5E72DB61E5900E003A0 /* ViewController */ = {
			isa = PBXGroup;
			children = (
				F4DFC5DA2DB61E5900E003A0 /* AIConfigVC.swift */,
				F4B814EC2DD5C17800E5C3DC /* AIConfigVC+Backup.swift */,
				F4DFC5DB2DB61E5900E003A0 /* AIFollowReplyVc.swift */,
				F4DFC5DC2DB61E5900E003A0 /* AIFollowReplyVc+AudioReply.swift */,
				F4B7056F2DBF7B2100A8F310 /* AIFollowReplyVc+TextReplay.swift */,
				F4DFC5DD2DB61E5900E003A0 /* SmartActionVC.swift */,
				F4C2745A2DD20E2500EA07A7 /* SmartActionVC+Realtime.swift */,
				F4DFC5DE2DB61E5900E003A0 /* SmartActionVC+Backup.swift */,
				F4DFC5DF2DB61E5900E003A0 /* SmartActionVC+Tool.swift */,
				F431BBBB2DB7879800535D51 /* SmartActionVC+Danmu.swift */,
				F4DFC5E02DB61E5900E003A0 /* SmartCloneEditVC.swift */,
				F4DFC5E12DB61E5900E003A0 /* SmartKeywordVC.swift */,
				F4DFC5E22DB61E5900E003A0 /* SmartKeywordVC+AudioReply.swift */,
				AF191A1C2DB8EA5A0066CA54 /* SmartKeywordVC+TextReplay.swift */,
				F4DFC5E32DB61E5900E003A0 /* SmartKeywordVC+Keyword.swift */,
				F4DFC5E42DB61E5900E003A0 /* SmartRecordAudioVC.swift */,
				F4DFC5E52DB61E5900E003A0 /* SmartTTSAudioVC.swift */,
				F4DFC5E62DB61E5900E003A0 /* SmartTTSAudioVC+Request.swift */,
			);
			path = ViewController;
			sourceTree = "<group>";
		};
		F4DFC5EC2DB61E5900E003A0 /* SmartAction */ = {
			isa = PBXGroup;
			children = (
				F4DFC5A32DB61E5900E003A0 /* Model */,
				F4DFC5AB2DB61E5900E003A0 /* Plugins */,
				F4DFC5D92DB61E5900E003A0 /* View */,
				F4DFC5E72DB61E5900E003A0 /* ViewController */,
				F4DFC5E82DB61E5900E003A0 /* AiAnswerFactory.swift */,
				F4DFC5E92DB61E5900E003A0 /* AiAnswerFactory+TTS.swift */,
				F4DFC5EA2DB61E5900E003A0 /* SmartBaseView.swift */,
				F4DFC5EB2DB61E5900E003A0 /* SmartBaseViewController.swift */,
			);
			path = SmartAction;
			sourceTree = "<group>";
		};
		F4DFC63D2DB61EED00E003A0 /* DouYinDanmaku */ = {
			isa = PBXGroup;
			children = (
				F4B935ED2DE04EDA00FB9162 /* DouYinAutoSendMessage.swift */,
				F4DFC6382DB61EED00E003A0 /* douyin.pb.swift */,
				F4DFC6392DB61EED00E003A0 /* DouyinDecode.swift */,
				F4DFC63A2DB61EED00E003A0 /* DouyinMessageData.swift */,
				F4DFC63C2DB61EED00E003A0 /* LiveMessage.swift */,
			);
			path = DouYinDanmaku;
			sourceTree = "<group>";
		};
		F4DFC6532DB61EED00E003A0 /* Models */ = {
			isa = PBXGroup;
			children = (
				F4DFC63E2DB61EED00E003A0 /* AnchorInfoResponse.swift */,
				F4DFC63F2DB61EED00E003A0 /* BaseLivingResponse.swift */,
				F4DFC6402DB61EED00E003A0 /* FansAgeDistriResponse.swift */,
				F4DFC6412DB61EED00E003A0 /* FansAnalysisResponse.swift */,
				F4DFC6422DB61EED00E003A0 /* FansGenderDistriResponse.swift */,
				F4DFC6432DB61EED00E003A0 /* FansInterestDistriResponse.swift */,
				F4DFC6442DB61EED00E003A0 /* FansTrendResponse.swift */,
				F4DFC6452DB61EED00E003A0 /* HistoryDetailResponse.swift */,
				F4DFC6462DB61EED00E003A0 /* HistoryKeyFragmentResponse.swift */,
				F4DFC6472DB61EED00E003A0 /* HistoryListResponse.swift */,
				F4DFC6482DB61EED00E003A0 /* HistoryNewFansConvResponse.swift */,
				F4DFC6492DB61EED00E003A0 /* HistoryOverviewResponse.swift */,
				F4DFC64A2DB61EED00E003A0 /* HistoryPayAudienceConvResponse.swift */,
				F4DFC64B2DB61EED00E003A0 /* HistoryStatTopListResponse.swift */,
				F4DFC64C2DB61EED00E003A0 /* LiveDataBaseResponse.swift */,
				F4DFC64D2DB61EED00E003A0 /* LiveStatusResponse.swift */,
				F4DFC64E2DB61EED00E003A0 /* LivingConversionResponse.swift */,
				F4DFC64F2DB61EED00E003A0 /* LivingEntranceResponse.swift */,
				F4DFC6502DB61EED00E003A0 /* LivingRealTimeResponse.swift */,
				F4DFC6512DB61EED00E003A0 /* MessageListResponse.swift */,
				F4DFC6522DB61EED00E003A0 /* SendMessageResponse.swift */,
			);
			path = Models;
			sourceTree = "<group>";
		};
		F4DFC65B2DB61EED00E003A0 /* APIKit */ = {
			isa = PBXGroup;
			children = (
				F4DFC63D2DB61EED00E003A0 /* DouYinDanmaku */,
				F4DFC6532DB61EED00E003A0 /* Models */,
				F4DFC6542DB61EED00E003A0 /* api_config.enc */,
				F42ACA8D2DEEB359006B4ED4 /* api_config.json */,
				F4DFC6552DB61EED00E003A0 /* APIConfigurationManager.swift */,
				F4DFC6562DB61EED00E003A0 /* APIStyle.swift */,
				F4DFC6572DB61EED00E003A0 /* CryptoUtil.swift */,
				F4DFC6582DB61EED00E003A0 /* js_config.enc */,
				F4DFC6592DB61EED00E003A0 /* LiveStatusListen.swift */,
				F4DFC65A2DB61EED00E003A0 /* RequestManager.swift */,
			);
			path = APIKit;
			sourceTree = "<group>";
		};
		F4DFC6612DB61EED00E003A0 /* WebView */ = {
			isa = PBXGroup;
			children = (
				F4DFC65C2DB61EED00E003A0 /* CookieInfo.swift */,
				F4DFC65D2DB61EED00E003A0 /* CookieRequestWebView.swift */,
				F4DFC65E2DB61EED00E003A0 /* JSSingleton.swift */,
				F4DFC65F2DB61EED00E003A0 /* NetworkMonitor.swift */,
				F4DFC6602DB61EED00E003A0 /* webjs.swift */,
			);
			path = WebView;
			sourceTree = "<group>";
		};
		F4DFC6622DB61EED00E003A0 /* MockAPI */ = {
			isa = PBXGroup;
			children = (
				F4DFC65B2DB61EED00E003A0 /* APIKit */,
				F4DFC6612DB61EED00E003A0 /* WebView */,
			);
			path = MockAPI;
			sourceTree = "<group>";
		};
		F4DFC68C2DB61F2C00E003A0 /* Model */ = {
			isa = PBXGroup;
			children = (
				F4DFC6892DB61F2C00E003A0 /* AutoReplyManager.swift */,
				F4DFC68A2DB61F2C00E003A0 /* DouyinMessageHanlerQueue.swift */,
				F4DFC68B2DB61F2C00E003A0 /* ReplyManager.swift */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		F4DFC6962DB61F2C00E003A0 /* VC */ = {
			isa = PBXGroup;
			children = (
				F4DFC68D2DB61F2C00E003A0 /* AIGiftReplyVc.swift */,
				F4DFC68E2DB61F2C00E003A0 /* AIGiftReplyVc+AudioReply.swift */,
				F4B7056D2DBF7A6B00A8F310 /* AIGiftReplyVc+TextReplay.swift */,
			);
			path = VC;
			sourceTree = "<group>";
		};
		F4DFC6A12DB61F2C00E003A0 /* View */ = {
			isa = PBXGroup;
			children = (
				F4DFC6972DB61F2C00E003A0 /* AIGiftReplyContentView.swift */,
				F4DFC6982DB61F2C00E003A0 /* AIGiftReplyView.swift */,
				F4DFC6992DB61F2C00E003A0 /* DouyinGiftCell.swift */,
				F4DFC69A2DB61F2C00E003A0 /* DouyinGiftListView.swift */,
				F4DFC69B2DB61F2C00E003A0 /* GiftFilterCell.swift */,
				F4DFC69C2DB61F2C00E003A0 /* GiftFilterView.swift */,
				F4DFC69D2DB61F2C00E003A0 /* GiftReplyAudioView.swift */,
				F4DFC69E2DB61F2C00E003A0 /* GiftReplyItemView.swift */,
			);
			path = View;
			sourceTree = "<group>";
		};
		F4DFC6A22DB61F2C00E003A0 /* Trade */ = {
			isa = PBXGroup;
			children = (
				F4DFC68C2DB61F2C00E003A0 /* Model */,
				F4DFC6962DB61F2C00E003A0 /* VC */,
				F4DFC6A12DB61F2C00E003A0 /* View */,
			);
			path = Trade;
			sourceTree = "<group>";
		};
		FB3E2C272B8446BB00106FC7 /* Resource */ = {
			isa = PBXGroup;
			children = (
				F4B814FD2DDADB8100E5C3DC /* 播放中.gif */,
				F4D413CA2DB62BF20036646F /* VeTOSiOSSDK.framework */,
				FB3E2C312B8446BB00106FC7 /* Assets.xcassets */,
				FB3E2C362B8446BB00106FC7 /* LaunchScreenJ.storyboard */,
				FB3E2C382B8446BB00106FC7 /* Main.storyboard */,
			);
			path = Resource;
			sourceTree = "<group>";
		};
		FB3E2C992B8446BB00106FC7 /* Vendors */ = {
			isa = PBXGroup;
			children = (
				FB3E2CA02B8446BB00106FC7 /* BRPickerView */,
				FB3E2CBC2B8446BB00106FC7 /* CircleButton */,
				FB3E2CC82B8446BB00106FC7 /* CYImageCrop */,
				FB3E2CC22B8446BB00106FC7 /* Dispatch */,
				FB3E2D392B8446BB00106FC7 /* Hpple */,
				FB3E2CCD2B8446BB00106FC7 /* LPSocketRocket */,
				FB3E2C9D2B8446BB00106FC7 /* RecoderCollectionLayout */,
				FB3E2C9A2B8446BB00106FC7 /* Statistics */,
				FB3E2CFE2B8446BB00106FC7 /* YYFPSLabel */,
				FB3E2D012B8446BB00106FC7 /* ZLImagePicker */,
				FB3E2CC12B8446BB00106FC7 /* ZXImageBrowser.h */,
				FB3E2D372B8446BB00106FC7 /* ZXImageBrowser.m */,
			);
			path = Vendors;
			sourceTree = "<group>";
		};
		FB3E2C9A2B8446BB00106FC7 /* Statistics */ = {
			isa = PBXGroup;
			children = (
				FB3E2C9B2B8446BB00106FC7 /* StatisticsEvents.swift */,
				FB3E2C9C2B8446BB00106FC7 /* LPStatistics.swift */,
			);
			path = Statistics;
			sourceTree = "<group>";
		};
		FB3E2C9D2B8446BB00106FC7 /* RecoderCollectionLayout */ = {
			isa = PBXGroup;
			children = (
				FB3E2C9E2B8446BB00106FC7 /* LXReorderableCollectionViewFlowLayout.h */,
				FB3E2C9F2B8446BB00106FC7 /* LXReorderableCollectionViewFlowLayout.m */,
			);
			path = RecoderCollectionLayout;
			sourceTree = "<group>";
		};
		FB3E2CA02B8446BB00106FC7 /* BRPickerView */ = {
			isa = PBXGroup;
			children = (
				FB3E2CA12B8446BB00106FC7 /* StringPickerView */,
				FB3E2CA62B8446BB00106FC7 /* BRPickerView.h */,
				FB3E2CA72B8446BB00106FC7 /* DatePickerView */,
				FB3E2CAE2B8446BB00106FC7 /* Base */,
				FB3E2CB72B8446BB00106FC7 /* AddressPickerView */,
			);
			path = BRPickerView;
			sourceTree = "<group>";
		};
		FB3E2CA12B8446BB00106FC7 /* StringPickerView */ = {
			isa = PBXGroup;
			children = (
				FB3E2CA22B8446BB00106FC7 /* BRResultModel.m */,
				FB3E2CA32B8446BB00106FC7 /* BRStringPickerView.h */,
				FB3E2CA42B8446BB00106FC7 /* BRResultModel.h */,
				FB3E2CA52B8446BB00106FC7 /* BRStringPickerView.m */,
			);
			path = StringPickerView;
			sourceTree = "<group>";
		};
		FB3E2CA72B8446BB00106FC7 /* DatePickerView */ = {
			isa = PBXGroup;
			children = (
				FB3E2CA82B8446BB00106FC7 /* BRDatePickerView+BR.m */,
				FB3E2CA92B8446BB00106FC7 /* BRDatePickerView.h */,
				FB3E2CAA2B8446BB00106FC7 /* NSDate+BRPickerView.m */,
				FB3E2CAB2B8446BB00106FC7 /* BRDatePickerView.m */,
				FB3E2CAC2B8446BB00106FC7 /* BRDatePickerView+BR.h */,
				FB3E2CAD2B8446BB00106FC7 /* NSDate+BRPickerView.h */,
			);
			path = DatePickerView;
			sourceTree = "<group>";
		};
		FB3E2CAE2B8446BB00106FC7 /* Base */ = {
			isa = PBXGroup;
			children = (
				FB3E2CAF2B8446BB00106FC7 /* NSBundle+BRPickerView.h */,
				FB3E2CB02B8446BB00106FC7 /* BRPickerViewMacro.h */,
				FB3E2CB12B8446BB00106FC7 /* BRPickerView.bundle */,
				FB3E2CB22B8446BB00106FC7 /* BRPickerStyle.m */,
				FB3E2CB32B8446BB00106FC7 /* BRBaseView.m */,
				FB3E2CB42B8446BB00106FC7 /* NSBundle+BRPickerView.m */,
				FB3E2CB52B8446BB00106FC7 /* BRBaseView.h */,
				FB3E2CB62B8446BB00106FC7 /* BRPickerStyle.h */,
			);
			path = Base;
			sourceTree = "<group>";
		};
		FB3E2CB72B8446BB00106FC7 /* AddressPickerView */ = {
			isa = PBXGroup;
			children = (
				FB3E2CB82B8446BB00106FC7 /* BRAddressModel.m */,
				FB3E2CB92B8446BB00106FC7 /* BRAddressPickerView.h */,
				FB3E2CBA2B8446BB00106FC7 /* BRAddressModel.h */,
				FB3E2CBB2B8446BB00106FC7 /* BRAddressPickerView.m */,
			);
			path = AddressPickerView;
			sourceTree = "<group>";
		};
		FB3E2CBC2B8446BB00106FC7 /* CircleButton */ = {
			isa = PBXGroup;
			children = (
				FB3E2CBF2B8446BB00106FC7 /* CircleButton.h */,
				FB3E2CBD2B8446BB00106FC7 /* CircleButton.m */,
				FB3E2CC02B8446BB00106FC7 /* IrregularButton.h */,
				FB3E2CBE2B8446BB00106FC7 /* IrregularButton.m */,
			);
			path = CircleButton;
			sourceTree = "<group>";
		};
		FB3E2CC22B8446BB00106FC7 /* Dispatch */ = {
			isa = PBXGroup;
			children = (
				FB3E2CC32B8446BB00106FC7 /* XSDispatchQueue.h */,
				FB3E2CC42B8446BB00106FC7 /* XSPDispatchGroup.m */,
				FB3E2CC52B8446BB00106FC7 /* XSDispatchGroup.h */,
				FB3E2CC62B8446BB00106FC7 /* XSDispatchQueue.m */,
			);
			path = Dispatch;
			sourceTree = "<group>";
		};
		FB3E2CC82B8446BB00106FC7 /* CYImageCrop */ = {
			isa = PBXGroup;
			children = (
				FB3E2CC92B8446BB00106FC7 /* CYCropCornerView.m */,
				FB3E2CCB2B8446BB00106FC7 /* CYCropCornerView.h */,
			);
			path = CYImageCrop;
			sourceTree = "<group>";
		};
		FB3E2CCD2B8446BB00106FC7 /* LPSocketRocket */ = {
			isa = PBXGroup;
			children = (
				FB3E2CFB2B8446BB00106FC7 /* IFVSocketRocket.h */,
				FB3E2CF92B8446BB00106FC7 /* IFVSRSecurityPolicy.h */,
				FB3E2CCF2B8446BB00106FC7 /* IFVSRSecurityPolicy.m */,
				FB3E2CCE2B8446BB00106FC7 /* IFVWebSocket.h */,
				FB3E2CFA2B8446BB00106FC7 /* IFVWebSocket.m */,
				FB3E2CD12B8446BB00106FC7 /* Internal */,
				FB3E2CD02B8446BB00106FC7 /* NSRunLoop+IFVWebSocket.h */,
				FB3E2CFC2B8446BB00106FC7 /* NSRunLoop+IFVWebSocket.m */,
				FB3E2CF82B8446BB00106FC7 /* NSURLRequest+IFVWebSocket.h */,
				FB3E2CFD2B8446BB00106FC7 /* NSURLRequest+IFVWebSocket.m */,
			);
			path = LPSocketRocket;
			sourceTree = "<group>";
		};
		FB3E2CD12B8446BB00106FC7 /* Internal */ = {
			isa = PBXGroup;
			children = (
				FB3E2CD22B8446BB00106FC7 /* NSRunLoop+IFVWebSocketPrivate.h */,
				FB3E2CD32B8446BB00106FC7 /* IFVConstants.m */,
				FB3E2CD42B8446BB00106FC7 /* NSURLRequest+IFVWebSocketPrivate.h */,
				FB3E2CD52B8446BB00106FC7 /* Proxy */,
				FB3E2CD82B8446BB00106FC7 /* RunLoop */,
				FB3E2CDB2B8446BB00106FC7 /* Security */,
				FB3E2CDE2B8446BB00106FC7 /* Delegate */,
				FB3E2CE12B8446BB00106FC7 /* Utilities */,
				FB3E2CF22B8446BB00106FC7 /* IOConsumer */,
				FB3E2CF72B8446BB00106FC7 /* IFVConstants.h */,
			);
			path = Internal;
			sourceTree = "<group>";
		};
		FB3E2CD52B8446BB00106FC7 /* Proxy */ = {
			isa = PBXGroup;
			children = (
				FB3E2CD62B8446BB00106FC7 /* IFVProxyConnect.h */,
				FB3E2CD72B8446BB00106FC7 /* IFVProxyConnect.m */,
			);
			path = Proxy;
			sourceTree = "<group>";
		};
		FB3E2CD82B8446BB00106FC7 /* RunLoop */ = {
			isa = PBXGroup;
			children = (
				FB3E2CD92B8446BB00106FC7 /* IFVRunLoopThread.h */,
				FB3E2CDA2B8446BB00106FC7 /* IFVRunLoopThread.m */,
			);
			path = RunLoop;
			sourceTree = "<group>";
		};
		FB3E2CDB2B8446BB00106FC7 /* Security */ = {
			isa = PBXGroup;
			children = (
				FB3E2CDC2B8446BB00106FC7 /* IFVPinningSecurityPolicy.m */,
				FB3E2CDD2B8446BB00106FC7 /* IFVPinningSecurityPolicy.h */,
			);
			path = Security;
			sourceTree = "<group>";
		};
		FB3E2CDE2B8446BB00106FC7 /* Delegate */ = {
			isa = PBXGroup;
			children = (
				FB3E2CDF2B8446BB00106FC7 /* IFVDelegateController.h */,
				FB3E2CE02B8446BB00106FC7 /* IFVDelegateController.m */,
			);
			path = Delegate;
			sourceTree = "<group>";
		};
		FB3E2CE12B8446BB00106FC7 /* Utilities */ = {
			isa = PBXGroup;
			children = (
				FB3E2CE22B8446BB00106FC7 /* IFVURLUtilities.h */,
				FB3E2CE32B8446BB00106FC7 /* IFVRandom.m */,
				FB3E2CE42B8446BB00106FC7 /* IFVError.h */,
				FB3E2CE52B8446BB00106FC7 /* IFVSIMDHelpers.m */,
				FB3E2CE62B8446BB00106FC7 /* IFVLog.m */,
				FB3E2CE72B8446BB00106FC7 /* IFVHTTPConnectMessage.h */,
				FB3E2CE82B8446BB00106FC7 /* IFVHash.h */,
				FB3E2CE92B8446BB00106FC7 /* IFVMutex.m */,
				FB3E2CEA2B8446BB00106FC7 /* IFVRandom.h */,
				FB3E2CEB2B8446BB00106FC7 /* IFVURLUtilities.m */,
				FB3E2CEC2B8446BB00106FC7 /* IFVError.m */,
				FB3E2CED2B8446BB00106FC7 /* IFVHTTPConnectMessage.m */,
				FB3E2CEE2B8446BB00106FC7 /* IFVLog.h */,
				FB3E2CEF2B8446BB00106FC7 /* IFVSIMDHelpers.h */,
				FB3E2CF02B8446BB00106FC7 /* IFVMutex.h */,
				FB3E2CF12B8446BB00106FC7 /* IFVHash.m */,
			);
			path = Utilities;
			sourceTree = "<group>";
		};
		FB3E2CF22B8446BB00106FC7 /* IOConsumer */ = {
			isa = PBXGroup;
			children = (
				FB3E2CF32B8446BB00106FC7 /* IFVIOConsumerPool.m */,
				FB3E2CF42B8446BB00106FC7 /* IFVIOConsumer.m */,
				FB3E2CF52B8446BB00106FC7 /* IFVIOConsumerPool.h */,
				FB3E2CF62B8446BB00106FC7 /* IFVIOConsumer.h */,
			);
			path = IOConsumer;
			sourceTree = "<group>";
		};
		FB3E2CFE2B8446BB00106FC7 /* YYFPSLabel */ = {
			isa = PBXGroup;
			children = (
				FB3E2CFF2B8446BB00106FC7 /* YYFPSLabel.h */,
				FB3E2D002B8446BB00106FC7 /* YYFPSLabel.m */,
			);
			path = YYFPSLabel;
			sourceTree = "<group>";
		};
		FB3E2D012B8446BB00106FC7 /* ZLImagePicker */ = {
			isa = PBXGroup;
			children = (
				FB3E2D022B8446BB00106FC7 /* ZLPhotoBrowser.h */,
				FB3E2D032B8446BB00106FC7 /* ZLPhotoBrowser.bundle */,
				FB3E2D042B8446BB00106FC7 /* General */,
				FB3E2D1E2B8446BB00106FC7 /* Camera */,
				FB3E2D202B8446BB00106FC7 /* Animation */,
				FB3E2D242B8446BB00106FC7 /* Extensions */,
				FB3E2D2F2B8446BB00106FC7 /* Edit */,
			);
			path = ZLImagePicker;
			sourceTree = "<group>";
		};
		FB3E2D042B8446BB00106FC7 /* General */ = {
			isa = PBXGroup;
			children = (
				FB3E2D052B8446BB00106FC7 /* ZLImagePreviewController.swift */,
				FB3E2D062B8446BB00106FC7 /* ZLAlbumCell.swift */,
				FB3E2D072B8446BB00106FC7 /* ZLPhotoManager.swift */,
				FB3E2D082B8446BB00106FC7 /* ZLPhotoModel.swift */,
				FB3E2D092B8446BB00106FC7 /* ZLPhotoPreviewCell.swift */,
				FB3E2D0A2B8446BB00106FC7 /* ZLProgressHUD.swift */,
				FB3E2D0B2B8446BB00106FC7 /* ZLFetchImageOperation.swift */,
				FB3E2D0C2B8446BB00106FC7 /* ZLAlbumListController.swift */,
				FB3E2D0D2B8446BB00106FC7 /* ZLEmbedAlbumListView.swift */,
				FB3E2D0E2B8446BB00106FC7 /* ZLLanguageDefine.swift */,
				FB3E2D0F2B8446BB00106FC7 /* ZLAlbumListView.swift */,
				FB3E2D102B8446BB00106FC7 /* ZLPhotoPreviewSheet.swift */,
				FB3E2D112B8446BB00106FC7 /* ZLAlbumListCell.swift */,
				FB3E2D122B8446BB00106FC7 /* ZLPhotoPreviewController.swift */,
				FB3E2D132B8446BB00106FC7 /* ZLImageNavController.swift */,
				FB3E2D142B8446BB00106FC7 /* ZLThumbnailViewController.swift */,
				FB3E2D152B8446BB00106FC7 /* ZLVideoManager.swift */,
				FB3E2D162B8446BB00106FC7 /* ZLAddPhotoCell.swift */,
				FB3E2D172B8446BB00106FC7 /* ZLGeneralDefine.swift */,
				FB3E2D182B8446BB00106FC7 /* ZLThumbnailPhotoCell.swift */,
				FB3E2D192B8446BB00106FC7 /* ZLProgressView.swift */,
				FB3E2D1A2B8446BB00106FC7 /* ZLPhotoConfiguration.swift */,
				FB3E2D1B2B8446BB00106FC7 /* ZLPhotoBrowser.swift */,
				FB3E2D1C2B8446BB00106FC7 /* ZLAlbumListModel.swift */,
				FB3E2D1D2B8446BB00106FC7 /* ZLCameraCell.swift */,
			);
			path = General;
			sourceTree = "<group>";
		};
		FB3E2D1E2B8446BB00106FC7 /* Camera */ = {
			isa = PBXGroup;
			children = (
				FB3E2D1F2B8446BB00106FC7 /* ZLCustomCamera.swift */,
			);
			path = Camera;
			sourceTree = "<group>";
		};
		FB3E2D202B8446BB00106FC7 /* Animation */ = {
			isa = PBXGroup;
			children = (
				FB3E2D212B8446BB00106FC7 /* ZLPhotoPreviewAnimatedTransition.swift */,
				FB3E2D222B8446BB00106FC7 /* ZLClipImageDismissAnimatedTransition.swift */,
				FB3E2D232B8446BB00106FC7 /* ZLPhotoPreviewPopInteractiveTransition.swift */,
			);
			path = Animation;
			sourceTree = "<group>";
		};
		FB3E2D242B8446BB00106FC7 /* Extensions */ = {
			isa = PBXGroup;
			children = (
				FB3E2D252B8446BB00106FC7 /* UIControl+ZLPhotoBrowser.swift */,
				FB3E2D262B8446BB00106FC7 /* Bundle+ZLPhotoBrowser.swift */,
				FB3E2D272B8446BB00106FC7 /* UIImage+ZLPhotoBrowser.swift */,
				FB3E2D282B8446BB00106FC7 /* Int+ZLPhotoBrowser.swift */,
				FB3E2D292B8446BB00106FC7 /* String+ZLPhotoBrowser.swift */,
				FB3E2D2A2B8446BB00106FC7 /* Cell+ZLPhotoBrowser.swift */,
				FB3E2D2B2B8446BB00106FC7 /* Array+ZLPhotoBrowser.swift */,
				FB3E2D2C2B8446BB00106FC7 /* CGFloat+ZLPhotoBrowser.swift */,
				FB3E2D2D2B8446BB00106FC7 /* PHAsset+ZLPhotoBrowser.swift */,
				FB3E2D2E2B8446BB00106FC7 /* UIColor+ZLPhotoBrowser.swift */,
			);
			path = Extensions;
			sourceTree = "<group>";
		};
		FB3E2D2F2B8446BB00106FC7 /* Edit */ = {
			isa = PBXGroup;
			children = (
				FB3E2D302B8446BB00106FC7 /* ZLInputTextViewController.swift */,
				FB3E2D312B8446BB00106FC7 /* ZLClipImageViewController.swift */,
				FB3E2D322B8446BB00106FC7 /* ZLImageStickerView.swift */,
				FB3E2D332B8446BB00106FC7 /* ZLEditImageViewController.swift */,
				FB3E2D342B8446BB00106FC7 /* ZLFilter.swift */,
				FB3E2D352B8446BB00106FC7 /* ZLTextStickerView.swift */,
				FB3E2D362B8446BB00106FC7 /* ZLEditVideoViewController.swift */,
			);
			path = Edit;
			sourceTree = "<group>";
		};
		FB3E2D392B8446BB00106FC7 /* Hpple */ = {
			isa = PBXGroup;
			children = (
				FB3E2D3D2B8446BB00106FC7 /* TFHpple.h */,
				FB3E2D3B2B8446BB00106FC7 /* TFHpple.m */,
				FB3E2D3A2B8446BB00106FC7 /* TFHppleElement.h */,
				FB3E2D3E2B8446BB00106FC7 /* TFHppleElement.m */,
				FB3E2D3C2B8446BB00106FC7 /* XPathQuery.h */,
				FB3E2D3F2B8446BB00106FC7 /* XPathQuery.m */,
			);
			path = Hpple;
			sourceTree = "<group>";
		};
		FB3E2D432B8446BC00106FC7 /* Base */ = {
			isa = PBXGroup;
			children = (
				F4C198A32D12650B00E28E81 /* BaseTabBarViewController.swift */,
				FB3E2D442B8446BC00106FC7 /* BaseVC.swift */,
				FB3E2D462B8446BC00106FC7 /* SRouteMediator.swift */,
				F4C198A72D12850F00E28E81 /* BaseNavigationController.swift */,
			);
			path = Base;
			sourceTree = "<group>";
		};
		FB3E2D472B8446BC00106FC7 /* Macro */ = {
			isa = PBXGroup;
			children = (
				FB3E2D482B8446BC00106FC7 /* LCTools.swift */,
				FB3E2D492B8446BC00106FC7 /* LCPayTool.swift */,
				FB3E2D4A2B8446BC00106FC7 /* LCDevice.swift */,
				FB3E2D4C2B8446BC00106FC7 /* SColor.swift */,
				FB3E2D4D2B8446BC00106FC7 /* LCKey.swift */,
				FB3E2D4E2B8446BC00106FC7 /* LCSingleton.swift */,
				FB3E2D512B8446BC00106FC7 /* LCLog.swift */,
			);
			path = Macro;
			sourceTree = "<group>";
		};
		FB3E2D522B8446BD00106FC7 /* Module */ = {
			isa = PBXGroup;
			children = (
				F4DFC5992DB61DED00E003A0 /* Jiangjie */,
				F4DFC5982DB61DCF00E003A0 /* Hudong */,
				F4C198D22D12A21200E28E81 /* Mine */,
				F4C1988D2D116ABF00E28E81 /* LimitManager */,
				FB3E2DA22B8446BD00106FC7 /* LiveRoom */,
				FB3E2F732B8446BD00106FC7 /* Login */,
			);
			path = Module;
			sourceTree = "<group>";
		};
		FB3E2DA22B8446BD00106FC7 /* LiveRoom */ = {
			isa = PBXGroup;
			children = (
				FB3E2DA72B8446BD00106FC7 /* Controller */,
			);
			path = LiveRoom;
			sourceTree = "<group>";
		};
		FB3E2DA72B8446BD00106FC7 /* Controller */ = {
			isa = PBXGroup;
			children = (
				FB3E2DAA2B8446BD00106FC7 /* LiveScanVC.swift */,
			);
			path = Controller;
			sourceTree = "<group>";
		};
		FB3E2F732B8446BD00106FC7 /* Login */ = {
			isa = PBXGroup;
			children = (
				FB3E2F742B8446BD00106FC7 /* Controller */,
				FB3E2F792B8446BD00106FC7 /* Login2.0 */,
			);
			path = Login;
			sourceTree = "<group>";
		};
		FB3E2F742B8446BD00106FC7 /* Controller */ = {
			isa = PBXGroup;
			children = (
				FB3E2F762B8446BD00106FC7 /* MainLoginVC.swift */,
				FB3E2F772B8446BD00106FC7 /* LoginVC.swift */,
			);
			path = Controller;
			sourceTree = "<group>";
		};
		FB3E2F792B8446BD00106FC7 /* Login2.0 */ = {
			isa = PBXGroup;
			children = (
				F4D046492D1E84C000A57812 /* QRScanedView.swift */,
				FB3E2F7A2B8446BD00106FC7 /* Model */,
			);
			path = Login2.0;
			sourceTree = "<group>";
		};
		FB3E2F7A2B8446BD00106FC7 /* Model */ = {
			isa = PBXGroup;
			children = (
				FB3E2F7B2B8446BD00106FC7 /* VerifyModel.swift */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		FB3E31042B8446BE00106FC7 /* Public */ = {
			isa = PBXGroup;
			children = (
				F4DE7E2A2DB232650021A153 /* LPSocketHelper.h */,
				F4DE7E2B2DB232650021A153 /* LPSocketHelper.m */,
				FB3E31082B8446BE00106FC7 /* DataBase */,
				FB3E310B2B8446BE00106FC7 /* ESPThread.h */,
				FB3E33EC2B8446BE00106FC7 /* ESPThread.m */,
				FB3E310D2B8446BE00106FC7 /* Extension */,
				FB3E313D2B8446BE00106FC7 /* FFPopup.h */,
				FB3E33ED2B8446BE00106FC7 /* FFPopup.m */,
				FB3E314F2B8446BE00106FC7 /* FileManager */,
				FB3E33EE2B8446BE00106FC7 /* Helper */,
				FB3E31482B8446BE00106FC7 /* JFBCrypt */,
				FB3E312C2B8446BE00106FC7 /* Networking */,
				FB3E31262B8446BE00106FC7 /* Refresh */,
				FB3E312B2B8446BE00106FC7 /* route.h */,
				FB3E31432B8446BE00106FC7 /* TextField */,
				FB3E31392B8446BE00106FC7 /* Timer */,
				FB3E31522B8446BE00106FC7 /* View */,
				FB3E313E2B8446BE00106FC7 /* WebView */,
			);
			path = Public;
			sourceTree = "<group>";
		};
		FB3E31082B8446BE00106FC7 /* DataBase */ = {
			isa = PBXGroup;
			children = (
				FB3E31092B8446BE00106FC7 /* DataBaseManager.swift */,
			);
			path = DataBase;
			sourceTree = "<group>";
		};
		FB3E310D2B8446BE00106FC7 /* Extension */ = {
			isa = PBXGroup;
			children = (
				FB3E31192B8446BE00106FC7 /* ArrayExt.swift */,
				FB3E31172B8446BE00106FC7 /* AVAssetExt.swift */,
				FB3E31132B8446BE00106FC7 /* ButtonExt.swift */,
				FB3E311C2B8446BE00106FC7 /* CGImage+CVPixelBuffer.swift */,
				FB3E311A2B8446BE00106FC7 /* LPOperators.swift */,
				FB3E31122B8446BE00106FC7 /* NSMutableArray+Hook.h */,
				FB3E311B2B8446BE00106FC7 /* NSMutableArray+Hook.m */,
				FB3E31102B8446BE00106FC7 /* NSURL+Hook.h */,
				FB3E31182B8446BE00106FC7 /* NSURL+Hook.m */,
				FB3E31112B8446BE00106FC7 /* StringExt.swift */,
				FB3E311E2B8446BE00106FC7 /* UIFont+AdjustFont.h */,
				FB3E31162B8446BE00106FC7 /* UIFont+AdjustFont.m */,
				FB3E310F2B8446BE00106FC7 /* UIImage+CVPixelBuffer.swift */,
				FB3E311F2B8446BE00106FC7 /* UIImageExt.swift */,
				FB3E311D2B8446BE00106FC7 /* UIResponder+Router.swift */,
				FB3E31142B8446BE00106FC7 /* UIViewExt.swift */,
			);
			path = Extension;
			sourceTree = "<group>";
		};
		FB3E31262B8446BE00106FC7 /* Refresh */ = {
			isa = PBXGroup;
			children = (
				FB3E31292B8446BE00106FC7 /* FWRefreshHeader.h */,
				FB3E31272B8446BE00106FC7 /* FWRefreshHeader.m */,
				FB3E31282B8446BE00106FC7 /* SourceRefreshHeader.h */,
				FB3E312A2B8446BE00106FC7 /* SourceRefreshHeader.m */,
			);
			path = Refresh;
			sourceTree = "<group>";
		};
		FB3E312C2B8446BE00106FC7 /* Networking */ = {
			isa = PBXGroup;
			children = (
				AF98263A2E2E0FD50040BA71 /* MinimaxVoiceService.swift */,
				AF98263B2E2E0FD50040BA71 /* MinimaxVoiceServiceExample.swift */,
				FB3E312D2B8446BE00106FC7 /* NetConnectionManager.swift */,
				FB3E312E2B8446BE00106FC7 /* MiddleRequestNet.swift */,
				FB3E31352B8446BE00106FC7 /* Result.swift */,
				FB3E31362B8446BE00106FC7 /* APISession.swift */,
				FB3E31372B8446BE00106FC7 /* BaseRespModel.swift */,
				FB3E31382B8446BE00106FC7 /* APIURL.swift */,
			);
			path = Networking;
			sourceTree = "<group>";
		};
		FB3E31392B8446BE00106FC7 /* Timer */ = {
			isa = PBXGroup;
			children = (
				FB3E313A2B8446BE00106FC7 /* GCDTimer.swift */,
				FB3E313B2B8446BE00106FC7 /* LPTimer.swift */,
				FB3E313C2B8446BE00106FC7 /* HapTicFeedback.swift */,
			);
			path = Timer;
			sourceTree = "<group>";
		};
		FB3E313E2B8446BE00106FC7 /* WebView */ = {
			isa = PBXGroup;
			children = (
				FB3E313F2B8446BE00106FC7 /* CustomURLProtocol.swift */,
				FB3E31402B8446BE00106FC7 /* WebProgressLine.swift */,
				FB3E31412B8446BE00106FC7 /* WebVC.swift */,
			);
			path = WebView;
			sourceTree = "<group>";
		};
		FB3E31432B8446BE00106FC7 /* TextField */ = {
			isa = PBXGroup;
			children = (
				FB3E31442B8446BE00106FC7 /* GWReParser.h */,
				FB3E31462B8446BE00106FC7 /* GWReParser.m */,
				FB3E31452B8446BE00106FC7 /* ParserTextField.swift */,
			);
			path = TextField;
			sourceTree = "<group>";
		};
		FB3E31482B8446BE00106FC7 /* JFBCrypt */ = {
			isa = PBXGroup;
			children = (
				FB3E31492B8446BE00106FC7 /* JFBCrypt.h */,
				FB3E314E2B8446BE00106FC7 /* JFBCrypt.m */,
				FB3E314C2B8446BE00106FC7 /* JFGC.h */,
				FB3E314B2B8446BE00106FC7 /* JFMacros.h */,
				FB3E314A2B8446BE00106FC7 /* JFRandom.h */,
				FB3E314D2B8446BE00106FC7 /* JFRandom.m */,
			);
			path = JFBCrypt;
			sourceTree = "<group>";
		};
		FB3E314F2B8446BE00106FC7 /* FileManager */ = {
			isa = PBXGroup;
			children = (
				FB3E31502B8446BE00106FC7 /* XCFileManager.m */,
				FB3E31512B8446BE00106FC7 /* XCFileManager.h */,
			);
			path = FileManager;
			sourceTree = "<group>";
		};
		FB3E31522B8446BE00106FC7 /* View */ = {
			isa = PBXGroup;
			children = (
				FB3E31612B8446BE00106FC7 /* AlertView.swift */,
				FB3E315C2B8446BE00106FC7 /* CustomLayoutButton.swift */,
				FB3E31532B8446BE00106FC7 /* CustomSlider.swift */,
				FB3E31622B8446BE00106FC7 /* GestureGuideInfoView.swift */,
				FB3E315E2B8446BE00106FC7 /* HUD.swift */,
				FB3E31652B8446BE00106FC7 /* InputAlert.swift */,
				FB3E31642B8446BE00106FC7 /* LiveRoomGuideView.swift */,
				FB3E315D2B8446BE00106FC7 /* LLAlertView.swift */,
				FB3E31572B8446BE00106FC7 /* MyCustomSliderView.swift */,
				FB3E315F2B8446BE00106FC7 /* NormalItemSelectSheet.swift */,
				FB3E31632B8446BE00106FC7 /* PolicyAlert.swift */,
				FB3E31552B8446BE00106FC7 /* SevenSwitch.swift */,
				FB3E315A2B8446BE00106FC7 /* VersionUpdateAlert.swift */,
			);
			path = View;
			sourceTree = "<group>";
		};
		FB3E33EE2B8446BE00106FC7 /* Helper */ = {
			isa = PBXGroup;
			children = (
				FB3E33FA2B8446BE00106FC7 /* Debouncer.swift */,
				FB3E33F92B8446BE00106FC7 /* GCDServices.swift */,
				FB3E33F62B8446BE00106FC7 /* LPImageUtils.h */,
				FB3E33F12B8446BE00106FC7 /* LPImageUtils.m */,
				FB3E33EF2B8446BE00106FC7 /* Throttler.swift */,
			);
			path = Helper;
			sourceTree = "<group>";
		};
		FB8043792B677FD200D9D456 = {
			isa = PBXGroup;
			children = (
				FB8043842B677FD200D9D456 /* LiveConsole */,
				FB8043832B677FD200D9D456 /* Products */,
				D297576DEFA655F5C5F52B35 /* Pods */,
				E6E888001A8070322E889D73 /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		FB8043832B677FD200D9D456 /* Products */ = {
			isa = PBXGroup;
			children = (
				FB8043822B677FD200D9D456 /* LiveConsole.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		FB8043842B677FD200D9D456 /* LiveConsole */ = {
			isa = PBXGroup;
			children = (
				F4DFC6622DB61EED00E003A0 /* MockAPI */,
				FB37BA862B887986002ADEFA /* LiveConsoleDebug.entitlements */,
				FB37BA852B88793A002ADEFA /* LiveConsoleRelease.entitlements */,
				FB3E2D432B8446BC00106FC7 /* Base */,
				FB3E2D472B8446BC00106FC7 /* Macro */,
				FB3E2D522B8446BD00106FC7 /* Module */,
				FB3E31042B8446BE00106FC7 /* Public */,
				FB3E2C272B8446BB00106FC7 /* Resource */,
				FB3E2C992B8446BB00106FC7 /* Vendors */,
				FB8043852B677FD200D9D456 /* AppDelegate.swift */,
				FB8043932B677FD400D9D456 /* Info.plist */,
				FB3E39722B84847800106FC7 /* LiveConsole-Bridging-Header.h */,
			);
			path = LiveConsole;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		FB8043812B677FD200D9D456 /* LiveConsole */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = FB8043962B677FD400D9D456 /* Build configuration list for PBXNativeTarget "LiveConsole" */;
			buildPhases = (
				4634D58C015ADB1368563612 /* [CP] Check Pods Manifest.lock */,
				FB80437E2B677FD200D9D456 /* Sources */,
				FB80437F2B677FD200D9D456 /* Frameworks */,
				FB8043802B677FD200D9D456 /* Resources */,
				74CEBFFA612BDE7066021F91 /* [CP] Embed Pods Frameworks */,
				E948AE196D7E21C068C68C33 /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = LiveConsole;
			productName = liveplusexplore;
			productReference = FB8043822B677FD200D9D456 /* LiveConsole.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		FB80437A2B677FD200D9D456 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1420;
				LastUpgradeCheck = 1420;
				TargetAttributes = {
					FB8043812B677FD200D9D456 = {
						CreatedOnToolsVersion = 14.2;
					};
				};
			};
			buildConfigurationList = FB80437D2B677FD200D9D456 /* Build configuration list for PBXProject "LiveConsole" */;
			compatibilityVersion = "Xcode 9.3";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = FB8043792B677FD200D9D456;
			productRefGroup = FB8043832B677FD200D9D456 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				FB8043812B677FD200D9D456 /* LiveConsole */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		FB8043802B677FD200D9D456 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F4DFC67D2DB61EED00E003A0 /* api_config.enc in Resources */,
				FB3E34132B8446BE00106FC7 /* LaunchScreenJ.storyboard in Resources */,
				F42ACA8E2DEEB359006B4ED4 /* api_config.json in Resources */,
				FB3E34752B8446BF00106FC7 /* BRPickerView.bundle in Resources */,
				FB3E340E2B8446BE00106FC7 /* Assets.xcassets in Resources */,
				F4DFC6812DB61EED00E003A0 /* js_config.enc in Resources */,
				FB3E34142B8446BE00106FC7 /* Main.storyboard in Resources */,
				F4B814FE2DDADB8100E5C3DC /* 播放中.gif in Resources */,
				FB3E34962B8446BF00106FC7 /* ZLPhotoBrowser.bundle in Resources */,
				F4C198F22D12A21200E28E81 /* MineTopView.xib in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		4634D58C015ADB1368563612 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-LiveConsole-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		74CEBFFA612BDE7066021F91 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-LiveConsole/Pods-LiveConsole-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			inputPaths = (
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-LiveConsole/Pods-LiveConsole-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-LiveConsole/Pods-LiveConsole-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		E948AE196D7E21C068C68C33 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-LiveConsole/Pods-LiveConsole-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			inputPaths = (
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-LiveConsole/Pods-LiveConsole-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-LiveConsole/Pods-LiveConsole-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		FB80437E2B677FD200D9D456 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				FB3E34B82B8446BF00106FC7 /* String+ZLPhotoBrowser.swift in Sources */,
				FB3E349C2B8446BF00106FC7 /* ZLProgressHUD.swift in Sources */,
				FB3E34BF2B8446BF00106FC7 /* ZLClipImageViewController.swift in Sources */,
				F4C2746B2DD4784C00EA07A7 /* SmartCardTextView.swift in Sources */,
				F4DFC6202DB61E5900E003A0 /* SmartCardFollowView.swift in Sources */,
				FB3E34A12B8446BF00106FC7 /* ZLAlbumListView.swift in Sources */,
				F4B935E22DDDC0CD00FB9162 /* SmartCloneRecordBotView.swift in Sources */,
				FB3E34742B8446BF00106FC7 /* BRDatePickerView.m in Sources */,
				F4DFC6772DB61EED00E003A0 /* LiveStatusResponse.swift in Sources */,
				FB3E34B02B8446BF00106FC7 /* ZLCustomCamera.swift in Sources */,
				FB3E34A62B8446BF00106FC7 /* ZLThumbnailViewController.swift in Sources */,
				FB3E37D22B8446C000106FC7 /* LLAlertView.swift in Sources */,
				FB3E34AD2B8446BF00106FC7 /* ZLPhotoBrowser.swift in Sources */,
				F4DFC60A2DB61E5900E003A0 /* SmartCloneRecordTopView.swift in Sources */,
				F4DFC6302DB61E5900E003A0 /* SmartKeywordVC+Keyword.swift in Sources */,
				FB3E34CF2B8446BF00106FC7 /* LCPayTool.swift in Sources */,
				FB3E37B22B8446C000106FC7 /* MiddleRequestNet.swift in Sources */,
				F4DFC5F92DB61E5900E003A0 /* AudioRecordManager.swift in Sources */,
				F4DFC6322DB61E5900E003A0 /* SmartTTSAudioVC.swift in Sources */,
				FB3E37A82B8446C000106FC7 /* LPOperators.swift in Sources */,
				FB3E347D2B8446BF00106FC7 /* XSPDispatchGroup.m in Sources */,
				F4DFC6062DB61E5900E003A0 /* AudioYinSePresetView.swift in Sources */,
				FB3E36942B8446C000106FC7 /* VerifyModel.swift in Sources */,
				F4DFC67A2DB61EED00E003A0 /* LivingRealTimeResponse.swift in Sources */,
				F4DFC62F2DB61E5900E003A0 /* SmartKeywordVC+AudioReply.swift in Sources */,
				FB3E37D62B8446C000106FC7 /* AlertView.swift in Sources */,
				F4DE7E2C2DB232650021A153 /* LPSocketHelper.m in Sources */,
				F4DFC6752DB61EED00E003A0 /* HistoryStatTopListResponse.swift in Sources */,
				F4DFC5EE2DB61E5900E003A0 /* AiAutoReplyModel.swift in Sources */,
				FB3E34CB2B8446BF00106FC7 /* BaseVC.swift in Sources */,
				F4C274512DCB3E0400EA07A7 /* MemberPointsVC.swift in Sources */,
				F4DFC6B62DB61F2C00E003A0 /* GiftReplyItemView.swift in Sources */,
				F4DFC6232DB61E5900E003A0 /* SmartCardVideoView.swift in Sources */,
				FB3E379C2B8446C000106FC7 /* DataBaseManager.swift in Sources */,
				FB3E37C92B8446C000106FC7 /* CustomSlider.swift in Sources */,
				FB3E37BB2B8446C000106FC7 /* APIURL.swift in Sources */,
				FB3E34912B8446BF00106FC7 /* IFVIOConsumer.m in Sources */,
				F4DFC67B2DB61EED00E003A0 /* MessageListResponse.swift in Sources */,
				F4DFC5EF2DB61E5900E003A0 /* AIReplyDataManager.swift in Sources */,
				F4DFC6B32DB61F2C00E003A0 /* GiftFilterCell.swift in Sources */,
				F4DFC6132DB61E5900E003A0 /* AIConfigReplyView.swift in Sources */,
				FB3E37A22B8446C000106FC7 /* UIViewExt.swift in Sources */,
				FB3E34C52B8446BF00106FC7 /* ZXImageBrowser.m in Sources */,
				F431BC112DBB7D2F00535D51 /* DouyinAllGoodsView.swift in Sources */,
				F4C198A82D12850F00E28E81 /* BaseNavigationController.swift in Sources */,
				FB3E349D2B8446BF00106FC7 /* ZLFetchImageOperation.swift in Sources */,
				FB3E37A42B8446C000106FC7 /* UIFont+AdjustFont.m in Sources */,
				F4DFC61C2DB61E5900E003A0 /* SmartActionNavigation.swift in Sources */,
				F4C198E62D12A21200E28E81 /* ConfigModel.swift in Sources */,
				F4DFC6762DB61EED00E003A0 /* LiveDataBaseResponse.swift in Sources */,
				F4DFC66F2DB61EED00E003A0 /* HistoryDetailResponse.swift in Sources */,
				F4DFC6332DB61E5900E003A0 /* SmartTTSAudioVC+Request.swift in Sources */,
				F4B935EC2DDF166500FB9162 /* PointsPayCell.swift in Sources */,
				F4DFC6B42DB61F2C00E003A0 /* GiftFilterView.swift in Sources */,
				FB3E34822B8446BF00106FC7 /* IFVSRSecurityPolicy.m in Sources */,
				FB3E37C42B8446C000106FC7 /* GWReParser.m in Sources */,
				F4C198982D116ABF00E28E81 /* LimitRightModel.swift in Sources */,
				F4DFC6282DB61E5900E003A0 /* AIFollowReplyVc.swift in Sources */,
				F431BC0B2DB8D5C100535D51 /* BuyinModel.swift in Sources */,
				FB3E37C72B8446C000106FC7 /* JFBCrypt.m in Sources */,
				F4DFC60F2DB61E5900E003A0 /* SystemReplyTypeCell.swift in Sources */,
				F4C2745B2DD20E2500EA07A7 /* SmartActionVC+Realtime.swift in Sources */,
				F4B935E42DDDC9F300FB9162 /* SmartClone3View.swift in Sources */,
				FB3E37B92B8446C000106FC7 /* APISession.swift in Sources */,
				AF191A1D2DB8EA5A0066CA54 /* SmartKeywordVC+TextReplay.swift in Sources */,
				F4DFC62B2DB61E5900E003A0 /* SmartActionVC+Backup.swift in Sources */,
				FB3E37A12B8446C000106FC7 /* ButtonExt.swift in Sources */,
				F4DFC6112DB61E5900E003A0 /* AIBackupView.swift in Sources */,
				FB3E34BC2B8446BF00106FC7 /* PHAsset+ZLPhotoBrowser.swift in Sources */,
				FB3E34C72B8446BF00106FC7 /* TFHppleElement.m in Sources */,
				FB3E37B12B8446C000106FC7 /* NetConnectionManager.swift in Sources */,
				F4DFC6292DB61E5900E003A0 /* AIFollowReplyVc+AudioReply.swift in Sources */,
				F4C198F72D12A21200E28E81 /* VipServiceView.swift in Sources */,
				FB3E34AA2B8446BF00106FC7 /* ZLThumbnailPhotoCell.swift in Sources */,
				FB3E34842B8446BF00106FC7 /* IFVProxyConnect.m in Sources */,
				FB3E37D32B8446C000106FC7 /* HUD.swift in Sources */,
				FB3E347E2B8446BF00106FC7 /* XSDispatchQueue.m in Sources */,
				F4DFC66E2DB61EED00E003A0 /* FansTrendResponse.swift in Sources */,
				FB3E34772B8446BF00106FC7 /* BRBaseView.m in Sources */,
				F4C1989A2D116ABF00E28E81 /* LimitModel.swift in Sources */,
				F4B815002DDB187500E5C3DC /* AVPlayManager.swift in Sources */,
				F4C198912D116ABF00E28E81 /* LimitMemberCard3.swift in Sources */,
				F4DFC66B2DB61EED00E003A0 /* FansAnalysisResponse.swift in Sources */,
				FB3E348A2B8446BF00106FC7 /* IFVLog.m in Sources */,
				FB3E37C12B8446C000106FC7 /* WebVC.swift in Sources */,
				F4B705702DBF7B2100A8F310 /* AIFollowReplyVc+TextReplay.swift in Sources */,
				F4DFC6A52DB61F2C00E003A0 /* ReplyManager.swift in Sources */,
				F4DFC5F22DB61E5900E003A0 /* AudioSpkIdModel.swift in Sources */,
				F4DFC6072DB61E5900E003A0 /* AudioYinSeSelectionView.swift in Sources */,
				FB3E34CE2B8446BF00106FC7 /* LCTools.swift in Sources */,
				FB3E34D22B8446BF00106FC7 /* SColor.swift in Sources */,
				F4DFC6632DB61EED00E003A0 /* douyin.pb.swift in Sources */,
				F4C2745D2DD20F3E00EA07A7 /* RealtimeNickAlertView.swift in Sources */,
				F4C198E32D12A21200E28E81 /* SettingVC.swift in Sources */,
				F4C198E82D12A21200E28E81 /* InPurchasingModel.swift in Sources */,
				FB3E39662B8446C200106FC7 /* ESPThread.m in Sources */,
				FB3E34D62B8446BF00106FC7 /* LCLog.swift in Sources */,
				F4C198D32D12A21200E28E81 /* AccountVC.swift in Sources */,
				F4DFC5F02DB61E5900E003A0 /* AIReplyDataManager+Cache.swift in Sources */,
				F4DFC60E2DB61E5900E003A0 /* SmartTTSTopView.swift in Sources */,
				FB3E34B22B8446BF00106FC7 /* ZLClipImageDismissAnimatedTransition.swift in Sources */,
				F4DFC60B2DB61E5900E003A0 /* SmartRecordBotView.swift in Sources */,
				FB3E348D2B8446BF00106FC7 /* IFVError.m in Sources */,
				F4C198992D116ABF00E28E81 /* WechatQRCodeView.swift in Sources */,
				FB3E37D92B8446C000106FC7 /* LiveRoomGuideView.swift in Sources */,
				F4DFC6152DB61E5900E003A0 /* AIConfigSKPView.swift in Sources */,
				FB3E34BA2B8446BF00106FC7 /* Array+ZLPhotoBrowser.swift in Sources */,
				FB3E348B2B8446BF00106FC7 /* IFVMutex.m in Sources */,
				F4C1989B2D116ABF00E28E81 /* LimitModel+Alert.swift in Sources */,
				FB3E348E2B8446BF00106FC7 /* IFVHTTPConnectMessage.m in Sources */,
				F4DFC6262DB61E5900E003A0 /* SmartDetailThreeNavigation.swift in Sources */,
				F4DFC6862DB61EED00E003A0 /* JSSingleton.swift in Sources */,
				FB3E37A02B8446C000106FC7 /* StringExt.swift in Sources */,
				F4DFC6A32DB61F2C00E003A0 /* AutoReplyManager.swift in Sources */,
				F431BBBC2DB7879800535D51 /* SmartActionVC+Danmu.swift in Sources */,
				FB3E349A2B8446BF00106FC7 /* ZLPhotoModel.swift in Sources */,
				F4DFC62D2DB61E5900E003A0 /* SmartCloneEditVC.swift in Sources */,
				FB3E37C02B8446C000106FC7 /* WebProgressLine.swift in Sources */,
				F4C198A42D12650B00E28E81 /* BaseTabBarViewController.swift in Sources */,
				F4DFC6702DB61EED00E003A0 /* HistoryKeyFragmentResponse.swift in Sources */,
				F4B7056C2DBF5FCA00A8F310 /* BuyinEmptyView.swift in Sources */,
				FB3E34952B8446BF00106FC7 /* YYFPSLabel.m in Sources */,
				F4C198E12D12A21200E28E81 /* PrivacySettingVC.swift in Sources */,
				FB3E34A82B8446BF00106FC7 /* ZLAddPhotoCell.swift in Sources */,
				F4DFC6252DB61E5900E003A0 /* SmartDetailNavigation.swift in Sources */,
				F4DFC67C2DB61EED00E003A0 /* SendMessageResponse.swift in Sources */,
				FB3E34B92B8446BF00106FC7 /* Cell+ZLPhotoBrowser.swift in Sources */,
				F4D0464A2D1E84C000A57812 /* QRScanedView.swift in Sources */,
				FB3E34A02B8446BF00106FC7 /* ZLLanguageDefine.swift in Sources */,
				FB3E37D72B8446C000106FC7 /* GestureGuideInfoView.swift in Sources */,
				F431BBED2DB8C2AA00535D51 /* buyinwebjs.swift in Sources */,
				FB3E34AC2B8446BF00106FC7 /* ZLPhotoConfiguration.swift in Sources */,
				FB3E39672B8446C200106FC7 /* FFPopup.m in Sources */,
				F4C1989E2D116ABF00E28E81 /* LimitModel+Operation.swift in Sources */,
				F4DFC6742DB61EED00E003A0 /* HistoryPayAudienceConvResponse.swift in Sources */,
				FB3E37DA2B8446C000106FC7 /* InputAlert.swift in Sources */,
				FB3E37A62B8446C000106FC7 /* NSURL+Hook.m in Sources */,
				FB3E34932B8446BF00106FC7 /* NSRunLoop+IFVWebSocket.m in Sources */,
				FB3E34BB2B8446BF00106FC7 /* CGFloat+ZLPhotoBrowser.swift in Sources */,
				F431BC042DB8C2AA00535D51 /* BuyinRequest.swift in Sources */,
				FB3E34852B8446BF00106FC7 /* IFVRunLoopThread.m in Sources */,
				F4DFC5FF2DB61E5900E003A0 /* SmartTTSAudioItem.swift in Sources */,
				F4DFC5F32DB61E5900E003A0 /* DouyinGiftModel.swift in Sources */,
				F4DFC6012DB61E5900E003A0 /* AIPickerProgressView.swift in Sources */,
				F4DFC6792DB61EED00E003A0 /* LivingEntranceResponse.swift in Sources */,
				F4DFC6212DB61E5900E003A0 /* SmartCardGiftView.swift in Sources */,
				F4DFC5FB2DB61E5900E003A0 /* SmartAlertView.swift in Sources */,
				F4B7056E2DBF7A6B00A8F310 /* AIGiftReplyVc+TextReplay.swift in Sources */,
				F4B814ED2DD5C17800E5C3DC /* AIConfigVC+Backup.swift in Sources */,
				F4DFC6832DB61EED00E003A0 /* RequestManager.swift in Sources */,
				FB3E34972B8446BF00106FC7 /* ZLImagePreviewController.swift in Sources */,
				FB3E37A72B8446C000106FC7 /* ArrayExt.swift in Sources */,
				F4DFC67F2DB61EED00E003A0 /* APIStyle.swift in Sources */,
				F4DFC6692DB61EED00E003A0 /* BaseLivingResponse.swift in Sources */,
				FB3E351B2B8446BF00106FC7 /* LiveScanVC.swift in Sources */,
				FB3E349F2B8446BF00106FC7 /* ZLEmbedAlbumListView.swift in Sources */,
				FB3E34D42B8446BF00106FC7 /* LCSingleton.swift in Sources */,
				F4DFC6882DB61EED00E003A0 /* webjs.swift in Sources */,
				FB3E37C82B8446C000106FC7 /* XCFileManager.m in Sources */,
				F4DFC6712DB61EED00E003A0 /* HistoryListResponse.swift in Sources */,
				F4DFC60D2DB61E5900E003A0 /* SmartTTSBotView.swift in Sources */,
				F4DFC6B52DB61F2C00E003A0 /* GiftReplyAudioView.swift in Sources */,
				F4DFC66A2DB61EED00E003A0 /* FansAgeDistriResponse.swift in Sources */,
				FB3E346E2B8446BF00106FC7 /* LPStatistics.swift in Sources */,
				FB3E34A42B8446BF00106FC7 /* ZLPhotoPreviewController.swift in Sources */,
				F4D413CF2DB62E560036646F /* BackupStepView.swift in Sources */,
				F4DFC6042DB61E5900E003A0 /* AudioSelectionView.swift in Sources */,
				FB3E34922B8446BF00106FC7 /* IFVWebSocket.m in Sources */,
				FB3E34832B8446BF00106FC7 /* IFVConstants.m in Sources */,
				FB3E37B82B8446C000106FC7 /* Result.swift in Sources */,
				F4C198DF2D12A21200E28E81 /* PhoneBindDoneVC.swift in Sources */,
				F4DFC6B22DB61F2C00E003A0 /* DouyinGiftListView.swift in Sources */,
				F4DFC6A42DB61F2C00E003A0 /* DouyinMessageHanlerQueue.swift in Sources */,
				F4C198DA2D12A21200E28E81 /* LogOutVC.swift in Sources */,
				F4DFC6342DB61E5900E003A0 /* AiAnswerFactory.swift in Sources */,
				FB3E396F2B8446C200106FC7 /* GCDServices.swift in Sources */,
				F4DFC6B02DB61F2C00E003A0 /* AIGiftReplyView.swift in Sources */,
				F4DFC66D2DB61EED00E003A0 /* FansInterestDistriResponse.swift in Sources */,
				FB3E37D12B8446C000106FC7 /* CustomLayoutButton.swift in Sources */,
				FB3E39692B8446C200106FC7 /* LPImageUtils.m in Sources */,
				F4B935E82DDEFE6900FB9162 /* MemberPointsPayVC.swift in Sources */,
				FB3E34A72B8446BF00106FC7 /* ZLVideoManager.swift in Sources */,
				F4DFC6362DB61E5900E003A0 /* SmartBaseView.swift in Sources */,
				FB3E34982B8446BF00106FC7 /* ZLAlbumCell.swift in Sources */,
				F4DFC6652DB61EED00E003A0 /* DouyinMessageData.swift in Sources */,
				F4DFC6782DB61EED00E003A0 /* LivingConversionResponse.swift in Sources */,
				F4DFC61D2DB61E5900E003A0 /* SmartActionToolBar.swift in Sources */,
				F4DFC5FA2DB61E5900E003A0 /* RecordCountDownLabel.swift in Sources */,
				F4DFC66C2DB61EED00E003A0 /* FansGenderDistriResponse.swift in Sources */,
				F4C198DB2D12A21200E28E81 /* MineVC.swift in Sources */,
				FB3E37BC2B8446C000106FC7 /* GCDTimer.swift in Sources */,
				FB3E34A52B8446BF00106FC7 /* ZLImageNavController.swift in Sources */,
				FB3E349E2B8446BF00106FC7 /* ZLAlbumListController.swift in Sources */,
				FB3E37AB2B8446C000106FC7 /* UIResponder+Router.swift in Sources */,
				FB3E39682B8446C200106FC7 /* Throttler.swift in Sources */,
				F4DFC6BC2DB6240900E003A0 /* AreaItemModel.swift in Sources */,
				FB3E347C2B8446BF00106FC7 /* IrregularButton.m in Sources */,
				F4DFC6022DB61E5900E003A0 /* AudioCloneAgreementView.swift in Sources */,
				F4DFC5F42DB61E5900E003A0 /* DownloadFromBucket.swift in Sources */,
				F4DFC62E2DB61E5900E003A0 /* SmartKeywordVC.swift in Sources */,
				F4C198F52D12A21200E28E81 /* SettingCell.swift in Sources */,
				FB3E349B2B8446BF00106FC7 /* ZLPhotoPreviewCell.swift in Sources */,
				F4C198E02D12A21200E28E81 /* PhoneBindVC.swift in Sources */,
				FB3E34802B8446BF00106FC7 /* CYCropCornerView.m in Sources */,
				F4C198F12D12A21200E28E81 /* MineTopView.swift in Sources */,
				F4DFC5F82DB61E5900E003A0 /* AudioPlayManager.swift in Sources */,
				FB3E34712B8446BF00106FC7 /* BRStringPickerView.m in Sources */,
				F4DFC5F12DB61E5900E003A0 /* AudioPathStyle.swift in Sources */,
				F431BBE92DB8C2AA00535D51 /* NetworkMonitorViewController.swift in Sources */,
				F4C1989F2D116ABF00E28E81 /* VIPAlert.swift in Sources */,
				FB3E348F2B8446BF00106FC7 /* IFVHash.m in Sources */,
				F4DFC6B12DB61F2C00E003A0 /* DouyinGiftCell.swift in Sources */,
				FB3E34882B8446BF00106FC7 /* IFVRandom.m in Sources */,
				FB3E34B42B8446BF00106FC7 /* UIControl+ZLPhotoBrowser.swift in Sources */,
				F4DFC6872DB61EED00E003A0 /* NetworkMonitor.swift in Sources */,
				FB3E34D02B8446BF00106FC7 /* LCDevice.swift in Sources */,
				F4DFC6A72DB61F2C00E003A0 /* AIGiftReplyVc+AudioReply.swift in Sources */,
				F431BBF92DB8C2AA00535D51 /* DouyinGoodsView.swift in Sources */,
				FB3E34CD2B8446BF00106FC7 /* SRouteMediator.swift in Sources */,
				FB3E37BA2B8446C000106FC7 /* BaseRespModel.swift in Sources */,
				F431BC092DB8C3A400535D51 /* BuyinVC.swift in Sources */,
				FB3E34C12B8446BF00106FC7 /* ZLEditImageViewController.swift in Sources */,
				FB3E37AA2B8446C000106FC7 /* CGImage+CVPixelBuffer.swift in Sources */,
				FB3E34892B8446BF00106FC7 /* IFVSIMDHelpers.m in Sources */,
				F4DFC6802DB61EED00E003A0 /* CryptoUtil.swift in Sources */,
				F4DFC6642DB61EED00E003A0 /* DouyinDecode.swift in Sources */,
				FB3E34B62B8446BF00106FC7 /* UIImage+ZLPhotoBrowser.swift in Sources */,
				FB3E34AF2B8446BF00106FC7 /* ZLCameraCell.swift in Sources */,
				FB3E37B02B8446C000106FC7 /* SourceRefreshHeader.m in Sources */,
				F4DFC6122DB61E5900E003A0 /* AIConfigGiftView.swift in Sources */,
				FB3E37A52B8446C000106FC7 /* AVAssetExt.swift in Sources */,
				FB3E37AC2B8446C000106FC7 /* UIImageExt.swift in Sources */,
				F4C1989C2D116ABF00E28E81 /* LimitModel+Logo.swift in Sources */,
				FB3E36922B8446C000106FC7 /* LoginVC.swift in Sources */,
				FB3E34AB2B8446BF00106FC7 /* ZLProgressView.swift in Sources */,
				FB3E39702B8446C200106FC7 /* Debouncer.swift in Sources */,
				FB3E36912B8446C000106FC7 /* MainLoginVC.swift in Sources */,
				F4C2746D2DD47BAE00EA07A7 /* SmartCardTextCell.swift in Sources */,
				FB3E347A2B8446BF00106FC7 /* BRAddressPickerView.m in Sources */,
				F4DFC6172DB61E5900E003A0 /* GiftTimeCell.swift in Sources */,
				AF98263C2E2E0FD50040BA71 /* MinimaxVoiceService.swift in Sources */,
				AF98263D2E2E0FD50040BA71 /* MinimaxVoiceServiceExample.swift in Sources */,
				FB3E34862B8446BF00106FC7 /* IFVPinningSecurityPolicy.m in Sources */,
				FB3E34942B8446BF00106FC7 /* NSURLRequest+IFVWebSocket.m in Sources */,
				F431BBEA2DB8C2AA00535D51 /* WebInfoManager.swift in Sources */,
				FB3E34B72B8446BF00106FC7 /* Int+ZLPhotoBrowser.swift in Sources */,
				FB3E34A22B8446BF00106FC7 /* ZLPhotoPreviewSheet.swift in Sources */,
				FB3E37C62B8446C000106FC7 /* JFRandom.m in Sources */,
				F4DFC6372DB61E5900E003A0 /* SmartBaseViewController.swift in Sources */,
				F4C198F32D12A21200E28E81 /* OpeningMemberCell.swift in Sources */,
				AF191A1B2DB8CF520066CA54 /* SmartTextItem.swift in Sources */,
				F4DFC6002DB61E5900E003A0 /* TTSPresetItem.swift in Sources */,
				AF191A1F2DB8EBC10066CA54 /* ReplyTextPlusView.swift in Sources */,
				FB3E34792B8446BF00106FC7 /* BRAddressModel.m in Sources */,
				F4B935E62DDDD0F200FB9162 /* CloneStepView.swift in Sources */,
				FB3E34722B8446BF00106FC7 /* BRDatePickerView+BR.m in Sources */,
				FB3E34992B8446BF00106FC7 /* ZLPhotoManager.swift in Sources */,
				F4DFC61A2DB61E5900E003A0 /* KeyWordInputView.swift in Sources */,
				F4DFC60C2DB61E5900E003A0 /* SmartRecordTopView.swift in Sources */,
				FB3E34C42B8446BF00106FC7 /* ZLEditVideoViewController.swift in Sources */,
				FB3E34BE2B8446BF00106FC7 /* ZLInputTextViewController.swift in Sources */,
				F4DFC5FE2DB61E5900E003A0 /* SmartKeywordItem.swift in Sources */,
				FB3E348C2B8446BF00106FC7 /* IFVURLUtilities.m in Sources */,
				FB3E346F2B8446BF00106FC7 /* LXReorderableCollectionViewFlowLayout.m in Sources */,
				FB3E37AF2B8446C000106FC7 /* FWRefreshHeader.m in Sources */,
				F4DFC6092DB61E5900E003A0 /* ReplayAudioView.swift in Sources */,
				F4DFC5F52DB61E5900E003A0 /* InteractionModel.swift in Sources */,
				F4C274562DCC465700EA07A7 /* PointsAlertView.swift in Sources */,
				F4DFC62A2DB61E5900E003A0 /* SmartActionVC.swift in Sources */,
				F431BBB92DB77D1F00535D51 /* HudongAlertView.swift in Sources */,
				FB3E34B52B8446BF00106FC7 /* Bundle+ZLPhotoBrowser.swift in Sources */,
				FB3E37C32B8446C000106FC7 /* ParserTextField.swift in Sources */,
				F4B935EE2DE04EDA00FB9162 /* DouYinAutoSendMessage.swift in Sources */,
				FB3E37BF2B8446C000106FC7 /* CustomURLProtocol.swift in Sources */,
				F4D413D92DB62E8B0036646F /* BackupPasswordView.swift in Sources */,
				FB3E34902B8446BF00106FC7 /* IFVIOConsumerPool.m in Sources */,
				F4DFC6082DB61E5900E003A0 /* KeywordListView.swift in Sources */,
				F4DFC6272DB61E5900E003A0 /* AIConfigVC.swift in Sources */,
				F4B935EA2DDF019300FB9162 /* MemberPointsPayVC+Pay.swift in Sources */,
				F4DFC6142DB61E5900E003A0 /* AIConfigSetView.swift in Sources */,
				F4C198D72D12A21200E28E81 /* FeedbackVC.swift in Sources */,
				F4C274532DCB43B200EA07A7 /* PointsCell.swift in Sources */,
				F4C198EC2D12A21200E28E81 /* InformationListCell.swift in Sources */,
				FB3E34A92B8446BF00106FC7 /* ZLGeneralDefine.swift in Sources */,
				FB3E34732B8446BF00106FC7 /* NSDate+BRPickerView.m in Sources */,
				F4DFC6672DB61EED00E003A0 /* LiveMessage.swift in Sources */,
				F4C198942D116ABF00E28E81 /* LimitPurchaseVC+Pay.swift in Sources */,
				FB3E346D2B8446BF00106FC7 /* StatisticsEvents.swift in Sources */,
				F4DFC6852DB61EED00E003A0 /* CookieRequestWebView.swift in Sources */,
				FB3E379F2B8446C000106FC7 /* UIImage+CVPixelBuffer.swift in Sources */,
				F4DFC6AF2DB61F2C00E003A0 /* AIGiftReplyContentView.swift in Sources */,
				F4DFC62C2DB61E5900E003A0 /* SmartActionVC+Tool.swift in Sources */,
				FB3E34C32B8446BF00106FC7 /* ZLTextStickerView.swift in Sources */,
				FB3E37BD2B8446C000106FC7 /* LPTimer.swift in Sources */,
				F4C198EA2D12A21200E28E81 /* UserInfo.swift in Sources */,
				F4DFC5F62DB61E5900E003A0 /* AudioDurationHelper.swift in Sources */,
				F4DFC6052DB61E5900E003A0 /* AudioYinSeCloneView.swift in Sources */,
				FB3E34D32B8446BF00106FC7 /* LCKey.swift in Sources */,
				FB3E34702B8446BF00106FC7 /* BRResultModel.m in Sources */,
				F4DFC6222DB61E5900E003A0 /* SmartCardKeyWordView.swift in Sources */,
				F4C198E52D12A21200E28E81 /* UserInfoSettingVC.swift in Sources */,
				F4C198962D116ABF00E28E81 /* LimitPurchaseVC2.swift in Sources */,
				F4DFC6732DB61EED00E003A0 /* HistoryOverviewResponse.swift in Sources */,
				F4DFC6682DB61EED00E003A0 /* AnchorInfoResponse.swift in Sources */,
				FB3E34BD2B8446BF00106FC7 /* UIColor+ZLPhotoBrowser.swift in Sources */,
				F4DFC6312DB61E5900E003A0 /* SmartRecordAudioVC.swift in Sources */,
				FB3E34C22B8446BF00106FC7 /* ZLFilter.swift in Sources */,
				F4DFC61E2DB61E5900E003A0 /* SmartCardAudioCell.swift in Sources */,
				F4DFC6842DB61EED00E003A0 /* CookieInfo.swift in Sources */,
				FB3E34B32B8446BF00106FC7 /* ZLPhotoPreviewPopInteractiveTransition.swift in Sources */,
				FB3E34762B8446BF00106FC7 /* BRPickerStyle.m in Sources */,
				FB3E37CA2B8446C000106FC7 /* SevenSwitch.swift in Sources */,
				F4DFC6722DB61EED00E003A0 /* HistoryNewFansConvResponse.swift in Sources */,
				F4DFC61B2DB61E5900E003A0 /* SmartActionCardView.swift in Sources */,
				F4C274592DD209A900EA07A7 /* LangduNickView.swift in Sources */,
				F4DFC5FD2DB61E5900E003A0 /* SmartAudioItem.swift in Sources */,
				F4DFC5ED2DB61E5900E003A0 /* AIAudioResultModel.swift in Sources */,
				FB3E37D82B8446C000106FC7 /* PolicyAlert.swift in Sources */,
				FB3E34872B8446BF00106FC7 /* IFVDelegateController.m in Sources */,
				F4DFC6102DB61E5900E003A0 /* SystemReplyTypeView.swift in Sources */,
				F4DFC6822DB61EED00E003A0 /* LiveStatusListen.swift in Sources */,
				F4DFC6242DB61E5900E003A0 /* SmartDetailFourNavigation.swift in Sources */,
				FB8043862B677FD200D9D456 /* AppDelegate.swift in Sources */,
				FB3E34782B8446BF00106FC7 /* NSBundle+BRPickerView.m in Sources */,
				FB3E37CC2B8446C000106FC7 /* MyCustomSliderView.swift in Sources */,
				F4DFC67E2DB61EED00E003A0 /* APIConfigurationManager.swift in Sources */,
				FB3E34AE2B8446BF00106FC7 /* ZLAlbumListModel.swift in Sources */,
				F4D413D62DB62E8B0036646F /* BackupLoadingView.swift in Sources */,
				F4C198D82D12A21200E28E81 /* InformationListVC.swift in Sources */,
				F4DFC5FC2DB61E5900E003A0 /* UIView+ExtensionSmart.swift in Sources */,
				F4DFC6182DB61E5900E003A0 /* GiftTimeView.swift in Sources */,
				FB3E34C82B8446BF00106FC7 /* XPathQuery.m in Sources */,
				FB3E37A92B8446C000106FC7 /* NSMutableArray+Hook.m in Sources */,
				FB3E34C62B8446BF00106FC7 /* TFHpple.m in Sources */,
				FB3E37CF2B8446C000106FC7 /* VersionUpdateAlert.swift in Sources */,
				F431BBFB2DB8C2AA00535D51 /* DoyinGoodsCell.swift in Sources */,
				F4D0464C2D1E8D4E00A57812 /* LoginScanVC.swift in Sources */,
				FB3E347B2B8446BF00106FC7 /* CircleButton.m in Sources */,
				FB3E34A32B8446BF00106FC7 /* ZLAlbumListCell.swift in Sources */,
				F41483CA2DE46928008B57C0 /* NickRealtimeView.swift in Sources */,
				FB3E37BE2B8446C000106FC7 /* HapTicFeedback.swift in Sources */,
				F4C198F02D12A21200E28E81 /* MineCell.swift in Sources */,
				FB3E37D42B8446C000106FC7 /* NormalItemSelectSheet.swift in Sources */,
				F4C198ED2D12A21200E28E81 /* InPurchasingBtnViewCell.swift in Sources */,
				F4DFC6032DB61E5900E003A0 /* AudioRateSelectionView.swift in Sources */,
				F4DFC6352DB61E5900E003A0 /* AiAnswerFactory+TTS.swift in Sources */,
				F4DFC61F2DB61E5900E003A0 /* SmartCardAudioView.swift in Sources */,
				F45FD1A12DC0D2A400F7C58B /* MineTopView2.swift in Sources */,
				F4DFC6A62DB61F2C00E003A0 /* AIGiftReplyVc.swift in Sources */,
				AF191A182DB88C0D0066CA54 /* SmartNavigation.swift in Sources */,
				F4DFC5F72DB61E5900E003A0 /* AudioPermissionManager.swift in Sources */,
				FB3E34C02B8446BF00106FC7 /* ZLImageStickerView.swift in Sources */,
				FB3E34B12B8446BF00106FC7 /* ZLPhotoPreviewAnimatedTransition.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXVariantGroup section */
		FB3E2C362B8446BB00106FC7 /* LaunchScreenJ.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				FB3E2C372B8446BB00106FC7 /* Base */,
			);
			name = LaunchScreenJ.storyboard;
			sourceTree = "<group>";
		};
		FB3E2C382B8446BB00106FC7 /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				FB3E2C392B8446BB00106FC7 /* Base */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		FB8043942B677FD400D9D456 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 16.2;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		FB8043952B677FD400D9D456 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 16.2;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		FB8043972B677FD400D9D456 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 8B70208A5C7FCD58B162B9A2 /* Pods-LiveConsole.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = LiveConsole/LiveConsoleDebug.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = PTXAN56R77;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/LiveConsole/Resource/TencentSDK/YTSDK",
					"$(PROJECT_DIR)/LiveConsole/Resource/TencentSDK/LiteSDK",
					"$(PROJECT_DIR)/LiveConsole/Resource/TencentSDK/LanSongAISDK",
					"$(PROJECT_DIR)/LiveConsole/Resource",
				);
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = LiveConsole/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "快瓴中控台";
				INFOPLIST_KEY_LSApplicationCategoryType = "";
				INFOPLIST_KEY_LSSupportsOpeningDocumentsInPlace = YES;
				INFOPLIST_KEY_NSCameraUsageDescription = "使用“快瓴中控台”进行扫描二维码，需要开启相机权限以采集摄像头画面";
				INFOPLIST_KEY_NSLocalNetworkUsageDescription = "想要访问本地局域网";
				INFOPLIST_KEY_NSLocationWhenInUseUsageDescription = "使用“快瓴中控台”进行遥控时，需要开启定位权限";
				INFOPLIST_KEY_NSMicrophoneUsageDescription = "使用“快瓴中控台”进行录音时，需要开启麦克风权限以采集声音";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "使用“快瓴中控台”进行扫描二维码，需要开启相机权限以采集摄像头画面";
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreenJ;
				INFOPLIST_KEY_UIMainStoryboardFile = Main;
				INFOPLIST_KEY_UIRequiredDeviceCapabilities = armv7;
				INFOPLIST_KEY_UIRequiresFullScreen = YES;
				INFOPLIST_KEY_UIStatusBarStyle = UIStatusBarStyleLightContent;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UIUserInterfaceStyle = Light;
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.quickleading.ailivecontrol;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OBJC_BRIDGING_HEADER = "$(SRCROOT)/LiveConsole/LiveConsole-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		FB8043982B677FD400D9D456 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = F8303A47D0B3A8E3C973E9DD /* Pods-LiveConsole.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = LiveConsole/LiveConsoleRelease.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = PTXAN56R77;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/LiveConsole/Resource/TencentSDK/YTSDK",
					"$(PROJECT_DIR)/LiveConsole/Resource/TencentSDK/LiteSDK",
					"$(PROJECT_DIR)/LiveConsole/Resource/TencentSDK/LanSongAISDK",
					"$(PROJECT_DIR)/LiveConsole/Resource",
				);
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = LiveConsole/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "快瓴中控台";
				INFOPLIST_KEY_LSApplicationCategoryType = "";
				INFOPLIST_KEY_LSSupportsOpeningDocumentsInPlace = YES;
				INFOPLIST_KEY_NSCameraUsageDescription = "使用“快瓴中控台”进行扫描二维码，需要开启相机权限以采集摄像头画面";
				INFOPLIST_KEY_NSLocalNetworkUsageDescription = "想要访问本地局域网";
				INFOPLIST_KEY_NSLocationWhenInUseUsageDescription = "使用“快瓴中控台”进行遥控时，需要开启定位权限";
				INFOPLIST_KEY_NSMicrophoneUsageDescription = "使用“快瓴中控台”进行录音时，需要开启麦克风权限以采集声音";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "使用“快瓴中控台”进行扫描二维码，需要开启相机权限以采集摄像头画面";
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreenJ;
				INFOPLIST_KEY_UIMainStoryboardFile = Main;
				INFOPLIST_KEY_UIRequiredDeviceCapabilities = armv7;
				INFOPLIST_KEY_UIRequiresFullScreen = YES;
				INFOPLIST_KEY_UIStatusBarStyle = UIStatusBarStyleLightContent;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UIUserInterfaceStyle = Light;
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.quickleading.ailivecontrol;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OBJC_BRIDGING_HEADER = "$(SRCROOT)/LiveConsole/LiveConsole-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		FB80437D2B677FD200D9D456 /* Build configuration list for PBXProject "LiveConsole" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				FB8043942B677FD400D9D456 /* Debug */,
				FB8043952B677FD400D9D456 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		FB8043962B677FD400D9D456 /* Build configuration list for PBXNativeTarget "LiveConsole" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				FB8043972B677FD400D9D456 /* Debug */,
				FB8043982B677FD400D9D456 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = FB80437A2B677FD200D9D456 /* Project object */;
}
